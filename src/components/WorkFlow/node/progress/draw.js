import { append as svgAppend, create as svgCreate, attr as svgAttr } from 'tiny-svg'
import { getSpendTimeText, isValidTime } from '../../utils/timeFormatter'

/**
 * 进度显示模块 - 使用延迟处理确保元素已完全渲染
 */
function ProgressModule(eventBus, elementRegistry) {
  // 监听导入完成事件
  eventBus.on('import.done', function() {
    // 延迟处理，确保所有元素都已渲染
    setTimeout(function() {
      addProgressToAllElements()
    }, 100)
  })

  function addProgressToAllElements() {
    const elements = elementRegistry.getAll()

    elements.forEach(function(element) {
      if (element.businessObject && element.businessObject.$attrs) {
        const attrs = element.businessObject.$attrs

        if (attrs.showProgress) {
          addProgressOverlay(element, attrs)
        }
      }
    })
  }

  function addProgressOverlay(element, attrs) {
    const gfx = elementRegistry.getGraphics(element)
    if (!gfx) return

    const spendTime = attrs.spendTime
    const currentNode = attrs.currentNode

    // 添加当前节点高亮
    if (currentNode) {
      addCurrentNodeHighlight(gfx, element)
    }

    // 添加耗时显示
    if (isValidTime(spendTime)) {
      addSpendTimeDisplay(gfx, element, spendTime)
    }
  }

  function addCurrentNodeHighlight(gfx, element) {
    // 添加高亮边框
    const highlightRect = svgCreate('rect')
    svgAttr(highlightRect, {
      x: -4,
      y: -4,
      width: element.width + 8,
      height: element.height + 8,
      rx: 6,
      ry: 6,
      fill: 'none',
      stroke: '#ff6b35',
      'stroke-width': 2,
      'stroke-dasharray': '8,4',
      opacity: 0.8
    })

    // 添加动画
    const animate = svgCreate('animate')
    svgAttr(animate, {
      attributeName: 'stroke-dashoffset',
      values: '0;12',
      dur: '2s',
      repeatCount: 'indefinite'
    })

    svgAppend(highlightRect, animate)
    svgAppend(gfx, highlightRect)
  }

  function addSpendTimeDisplay(gfx, element, spendTime) {
    const timeText = getSpendTimeText(spendTime, true)

    // 计算位置
    const fontSize = 10
    const padding = 6
    const textWidth = Math.max(timeText.length * (fontSize * 0.6) + padding * 2, 40)
    const textHeight = 18
    const bgX = (element.width - textWidth) / 2
    const bgY = element.height + 4

    // 创建背景
    const textBg = svgCreate('rect')
    svgAttr(textBg, {
      x: bgX,
      y: bgY,
      width: textWidth,
      height: textHeight,
      rx: 3,
      ry: 3,
      fill: 'rgba(62, 123, 250, 0.9)',
      stroke: 'rgba(62, 123, 250, 1)',
      'stroke-width': 1
    })

    // 创建文本
    const text = svgCreate('text')
    svgAttr(text, {
      x: element.width / 2,
      y: bgY + textHeight / 2 + fontSize / 3,
      'text-anchor': 'middle',
      'font-family': 'Arial, sans-serif',
      'font-size': fontSize + 'px',
      fill: '#ffffff',
      'font-weight': '500'
    })
    text.textContent = timeText

    svgAppend(gfx, textBg)
    svgAppend(gfx, text)
  }
}

ProgressModule.$inject = ['eventBus', 'elementRegistry']

export default [
  {
    __init__: ['progressModule'],
    progressModule: ['type', ProgressModule]
  }
]
