import { createStore } from 'vuex'
import getters from './getters'
import app from './modules/app'
import user from './modules/user'
import code from './modules/code'
import project from './modules/project'
import theme from './modules/theme'

import publicData from './modules/publicData'
import chat from './modules/chat'
import downLoadProgress from './modules/downLoadProgress'

const store = createStore({
  modules: {
    app,
    user,
    code,
    project,
    theme,
    publicData,
    downLoadProgress,
    chat
  },
  getters: {
    ...getters
  }
})

export default store
