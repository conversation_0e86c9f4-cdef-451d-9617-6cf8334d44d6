<template>
  <div v-if="!multiLine" class="text-tooltip">
    <el-tooltip class="item" effect="dark" :disabled="isShowTooltip" :content="content" placement="top">
      <p class="over-flow" :class="className" @mouseover="onMouseOver(refName)">
        {{ label }}:<span :ref="refName" style="padding-left: 4px;">{{ content||' ' }}</span>
      </p>
      <!-- <slot /> -->
      <slot name="content" />
    </el-tooltip>
  </div>
  <div v-else class="multi-text-tooltip">
    <el-tooltip class="item" effect="dark" :disabled="isShowMultiLineTooltip" :content="content" placement="top">
      <p class="multi-over-flow" :class="className">
        <span class="multi-over-flow-label">
          {{ label }}:
        </span>
        <span :ref="refName" class="multi-over-flow-content" @mouseover="omMouseOverMulti(refName)">
          <span>{{ content ||'' }}</span>
        </span>
      </p>
      <slot name="content" />
    </el-tooltip>
  </div>
</template>

<script>
export default {
  name: 'TextTooltip',
  props: {
    // 显示的文字内容
    content: {
      type: String,
      default: () => {
        return ''
      }
    },
    // 外层框的样式，在传入的这个类名中设置文字显示的宽度
    className: {
      type: String,
      default: () => {
        return ''
      }
    },
    // 为页面文字标识（如在同一页面中调用多次组件，此参数不可重复）
    refName: {
      type: String,
      default: () => {
        return ''
      }
    },
    label: {
      type: String,
      default: () => {
        return ''
      }
    },
    multiLine: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isShowTooltip: true,
      isShowMultiLineTooltip: true
    }
  },
  methods: {
    onMouseOver(str) {
      const parentWidth = this.$refs[str].parentNode.offsetWidth
      const contentWidth = this.$refs[str].offsetWidth
      // 判断是否开启tooltip功能
      if (contentWidth > parentWidth) {
        this.isShowTooltip = false
      } else {
        this.isShowTooltip = true
      }
    },
    omMouseOverMulti(str) {
      const parentHeight = this.$refs[str].offsetHeight
      const contentHeight = this.$refs[str].children?.[0]?.offsetHeight
      // 判断是否开启tooltip功能
      if (parentHeight > contentHeight) {
        this.isShowMultiLineTooltip = true
      } else {
        this.isShowMultiLineTooltip = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.over-flow {
  position: relative;
  // top: 10px;
  margin: 8px 0 0 0;
  // line-height: 1;
  overflow: hidden; //
  white-space: nowrap;
  text-overflow: ellipsis;
  line-height: 20px;
}
.multi-over-flow {
  display: flex;
  margin-bottom: 0;
  margin-top: 4px;
  &-{
    &label {
      line-height: 20px;
    }
    &content {
      height: 60px;
      padding-left: 4px;
      flex: 1;
      display: -webkit-box;
      overflow: hidden;
      white-space: normal!important;
      text-overflow: ellipsis;
      word-wrap: break-word;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      line-height: 20px;
    }
  }
}
</style>
