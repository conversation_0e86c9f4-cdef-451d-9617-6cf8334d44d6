<template>
  <el-dialog
    :title="title"
    v-model:value="visible"
    width="30%"
    :before-close="onClose"
    :close-on-click-modal="false"
  >
    <el-form
      ref="treeFormRef"
      :model="treeForm"
      :rules="rulesFormRules"
      label-position="top"
    >
      <el-form-item label="名称" prop="name">
        <el-input v-model.trim="treeForm.name" placeholder="请输入名称" />
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <div class="dialog-footer">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="save"
          >确定</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import { editTestPlanTree, addTestPlanTree } from '@/api/vone/testplan'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    // 节点数据
    data: {
      type: Object,
      default: () => ({}),
    },
    // 弹窗操作类型
    menuType: {
      type: String,
      default: 'add',
    },
  },
  data() {
    return {
      saveLoading: false,
      title: '添加分组',
      treeForm: {
        name: '',
      },
      rulesFormRules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' },
          {
            pattern: '[^ ]+',
            message: '名称不能为空格',
          },
          {
            max: 128,
            message: '名称不能超过128个字符',
          },
        ],
      },
    }
  },
  watch: {
    visible(v) {
      if (!v) return
      this.title = this.menuType === 'edit' ? '编辑分组名称' : '新增分组'
      this.treeForm.name = this.menuType === 'edit' ? this.data.name : ''
    },
  },
  methods: {
    async save() {
      try {
        await this.$refs.treeFormRef.validate()
      } catch (error) {
        return
      }
      this.menuType === 'add' ? this.addNodeTree() : this.eidtNodeTree()
    },
    // 新增节点
    async addNodeTree() {
      this.saveLoading = true
      const params = {
        state: true,
        name: this.treeForm.name,
        parentId: this.data.id || '0',
      }
      const res = await addTestPlanTree(params).catch(() => {
        this.saveLoading = false
      })
      if (res.isSuccess) {
        this.$message.success('新增成功')
        $emit(this, 'success')
        this.onClose()
      }
      this.saveLoading = false
    },
    // 修改节点名称
    async eidtNodeTree() {
      const params = {
        state: true,
        id: this.data.id,

        name: this.treeForm.name,
        parentId: this.data.parentId,
      }
      const res = await editTestPlanTree(params)
      if (res.isSuccess) {
        this.$message.success('修改成功')
        $emit(this, 'success')
        this.onClose()
      }
    },
    onClose() {
      this.treeForm.name = ''
      this.$nextTick(() => {
        this.$refs.treeFormRef.resetFields()
        $emit(this, 'update:visible', false)
      })
    },
  },
  emits: ['update:visible', 'success'],
}
</script>
