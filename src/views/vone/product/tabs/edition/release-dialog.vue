<template>
  <el-dialog
    class="dialogContainer"
    title="版本发布"
    :model-value="visible"
    width="35%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="close"
  >
    <p>
      <el-icon><el-icon-warning-outline /></el-icon>
    </p>
    <p>
      共发布内容 216 条，发布至
      <el-tag type="success">PRD</el-tag>
      环境
    </p>
    <el-form ref="appForm" :model="appForm">
      <el-form-item label="发布时间">
        <el-date-picker
          v-model="appForm.date"
          type="date"
          placeholder="选择日期"
        />
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <span class="dialog-footer">
        <el-button size="small" @click="$emit('update:visible', false)"
          >取消</el-button
        >
        <el-button type="primary" size="small" @click="save">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { WarningOutline as ElIconWarningOutline } from "@element-plus/icons-vue";
import { $on, $off, $once, $emit } from "../../../../../utils/gogocodeTransfer";
export default {
  components: {
    ElIconWarningOutline,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      appForm: {
        date: "",
      },
    };
  },
  mounted() {},
  methods: {
    // 保存
    save() {
      this.$message.success("发布成功");
      this.close();
      $emit(this, "success");
    },
    close() {
      this.$refs.appForm.resetFields();
      $emit(this, "update:visible", false);
    },
  },
  emits: ["update:visible", "success"],
};
</script>
