<template>
  <div>
    <div v-loading="pageLoading" class="boxContent">
      <!-- 父需求/ -->
      <div v-if="typeCode == 'ISSUE' && parentTable.length" style="margin-bottom: 16px">
        <el-row style="margin-bottom: 10px">
          <el-col :span="6">
            <div class="boxTitle">父需求</div>
          </el-col>
        </el-row>
        <needTable
          :table-data="parentTable"
          :project-id="projectId"
          is-parent
          :type-code="typeCode"
          :all-project-id="allProjectId"
          @success="getRelationTable"
        />
      </div>

      <!-- 需求拆分 -->
      <div v-if="typeCode == 'ISSUE'" style="margin-bottom: 16px">
        <el-row type="flex" style="align-items: center; margin-bottom: 12px">
          <el-col :span="6">
            <div class="boxTitle">需求拆分</div>
          </el-col>
          <el-col :span="18" style="text-align: right">
            <el-button class="con-issue" type="text" icon="el-icon-link" @click="addTask = 1">关联</el-button>
            <el-button v-if="isShowCreateBtn" class="con-issue" type="text" icon="el-icon-plus" @click="handleClickAdd"
              >新增</el-button
            >
          </el-col>
        </el-row>

        <simpleAddIssue
          v-if="addTask == 0"
          no-file
          :type-code="'ISSUE'"
          :issue-id="issueId"
          no-epic
          style="width: 100%"
          @success="init"
          @cancel="addTask = null"
        />
        <el-form :model="taskForm">
          <el-row v-if="addTask == 1" :gutter="12" class="mt-10" type="flex">
            <el-col :span="6">
              <el-form-item prop="projectId">
                <el-select
                  v-model="taskForm.projectId"
                  filterable
                  style="width: 100%"
                  :disabled="$route.params.id ? true : false"
                  class="projectStl"
                  @change="changeProject"
                  @focus="setOptionWidth"
                >
                  <el-option
                    v-for="item in projectIdList"
                    :key="item.key"
                    :label="item.name"
                    :value="item.id"
                    :style="{ width: selectOptionWidth }"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="18">
              <el-form-item prop="taskId">
                <el-select
                  v-model.trim="taskForm.relationIssue"
                  placeholder="请输入需求名称"
                  clearable
                  filterable
                  remote
                  multiple
                  :remote-method="getRequirementList"
                  :loading="requireLoading"
                  class="requireSelect"
                  @focus="setOptionWidth"
                >
                  <el-option
                    v-for="ele in issueList"
                    :key="ele.id"
                    :value="ele.id"
                    :label="ele.name"
                    :disabled="ele.disabled"
                    :style="{ width: selectOptionWidth }"
                    :title="ele.name"
                  >
                    {{ `${ele.code}   ${ele.name}` }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <div v-if="addTask == 1" class="minbtns" style="text-align: right; margin-top: -16px">
            <el-button class="miniBtn" @click="addTask = null">取消</el-button>
            <el-button class="miniBtn" type="primary" style="margin-left: 8px" @click="saveTask">确定</el-button>
          </div>
        </el-form>

        <div>
          <div v-if="!childrenTable.length" class="empty-container">
            <svg-icon icon-class="zanwu" class-name="empty-svg" />
            暂无关联内容
          </div>
          <el-table v-else ref="childrenTable" class="vone-table voneNobg-table" :data="childrenTable">
            <template>
              <el-table-column
                prop="code"
                label="需求"
                show-overflow-tooltip
                min-width="250"
                fixed="left"
                class-name="name_col"
              >
                <template slot-scope="scope">
                  <a class="table_title" @click="toRouter(scope.row)">
                    <span v-if="scope.row.echoMap && scope.row.echoMap.typeCode">
                      <i
                        :class="`iconfont ${scope.row.echoMap.typeCode.icon}`"
                        :style="{ color: `${scope.row.echoMap.typeCode ? scope.row.echoMap.typeCode.color : '#ccc'}` }"
                      />
                    </span>
                    {{ scope.row.code }} - {{ scope.row.name }}
                  </a>
                </template>
              </el-table-column>

              <!-- <el-table-column prop="projectId" label="项目" show-overflow-tooltip min-width="70">
                <template slot-scope="scope">
                  <el-tag v-if="$route.params.id && $route.params.id != scope.row.projectId" effect="plain" type="info">
                    外部
                  </el-tag>
                </template>
              </el-table-column> -->

              <el-table-column prop="handleBy" label="处理人" show-overflow-tooltip min-width="150">
                <template slot-scope="scope">
                  <span>
                    <vone-user-avatar
                      :avatar-path="getUserInfo(scope.row) ? getUserInfo(scope.row).avatarPath : ''"
                      :name="getUserInfo(scope.row) ? getUserInfo(scope.row).name : ''"
                    />
                    <!-- <vone-remote-user v-model="scope.row.handleBy" :project-id="$route.params&&$route.params.id" class="remoteuser" :default-data="[scope.row.echoMap.handleBy]" :disabled="scope.row.stateCode == 'DONE' || !$permission('project_issue_edit')" @change="workitemChange(row,$event,'handleBy')" /> -->
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="stateCode" label="状态" show-overflow-tooltip min-width="110">
                <template slot-scope="scope">
                  <el-tag v-if="scope.row.echoMap.stateCode" type="success">
                    {{ scope.row.echoMap.stateCode.name }}
                  </el-tag>

                  <el-tag v-else-if="scope.row.echoMap.state" type="success">
                    {{ scope.row.echoMap.state.name }}
                  </el-tag>

                  <el-tag v-else type="success">
                    {{ scope.row.stateCode }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="planEtime" label="计划完成时间" show-overflow-tooltip min-width="165">
                <template slot-scope="scope">
                  {{ scope.row.planEtime || '未设置' }}
                </template>
              </el-table-column>

              <el-table-column fixed="right" label="操作" width="80">
                <template slot-scope="scope">
                  <div style="display: flex">
                    <el-button
                      style="padding: 0 10px; min-width: 20px"
                      type="text"
                      :disabled="scope.row.stateCode == 'DONE' || !$permission('project_issue_edit')"
                      icon="iconfont el-icon-application-edit"
                      @click="gotoEditI(scope.row)"
                    />
                    <el-divider direction="vertical" />
                    <el-button
                      style="padding: 0 10px; min-width: 20px"
                      type="text"
                      icon="iconfont el-icon-icon-line-jiechuguanlian"
                      @click="deleteRelation(scope.row)"
                    />
                  </div>
                </template>
              </el-table-column>
            </template>
          </el-table>
        </div>
      </div>

      <!-- 用户需求-------------------------------------------------------------- -->
      <div>
        <el-row type="flex" style="align-items: center; margin-bottom: 12px">
          <el-col :span="6">
            <div class="boxTitle">关联关系</div>
          </el-col>
          <el-col :span="18" style="text-align: right">
            <el-button class="con-issue" type="text" icon="el-icon-link" @click="addRelation = true">关联</el-button>
          </el-col>
        </el-row>
      </div>
      <el-form ref="issueTab" :model="issueTab" :rules="relateRules">
        <el-row v-if="addRelation" :gutter="8" class="mt-10" type="flex" justify="space-between">
          <el-col :span="4">
            <el-form-item prop="relation">
              <el-select v-model="issueTab.relation" placeholder="请选择依赖关系">
                <el-option label="关联" value="RELATION" />
                <el-option label="前置依赖" value="DEPEND" />
                <el-option label="后置影响" value="AFFECT" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item prop="projectId">
              <el-select
                v-model="issueTab.projectId"
                filterable
                style="width: 100%"
                @change="changeProject"
                @focus="setOptionWidth"
              >
                <el-option
                  v-for="item in projectIdList"
                  :key="item.key"
                  :label="item.name"
                  :value="item.id"
                  :style="{ width: selectOptionWidth }"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item prop="relationIssue">
              <el-select
                v-model.trim="issueTab.relationIssue"
                placeholder="请输入需求名称"
                clearable
                filterable
                remote
                :remote-method="getRequirementList"
                :loading="requireLoading"
                class="requireSelect"
                @focus="setOptionWidth"
              >
                <el-option
                  v-for="ele in issueList"
                  :key="ele.id"
                  :value="ele.id"
                  :label="ele.name"
                  :disabled="ele.disabled"
                  :style="{ width: selectOptionWidth }"
                  :title="ele.name"
                >
                  {{ `${ele.code}   ${ele.name}` }}
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <div v-if="addRelation" class="minbtns" style="text-align: right; margin-top: -16px">
          <el-button class="miniBtn" @click="addRelation = false">取消</el-button>
          <el-button class="miniBtn" type="primary" style="margin-left: 8px" @click="addRelationShip">保存</el-button>
        </div>
      </el-form>

      <div v-if="!connectTable.length && !dependTable.length && !affectTable.length" class="empty-container">
        <svg-icon icon-class="zanwu" class-name="empty-svg" />
        暂无关联内容
      </div>

      <el-collapse v-else v-model="activeNames">
        <!-- 关联/------------------- -->
        <el-collapse-item v-if="connectTable.length" name="RELATION">
          <template slot="title">
            <i class="iconfont el-icon-yibiaoban-shuxingyanse colorOrange" />
            <span class="centerText"> 关联 </span>
            <span class="centerNum">
              {{ connectTable.length }}
            </span>
          </template>
          <needTable
            :table-data="connectTable"
            :project-id="projectId"
            :type-code="typeCode"
            :all-project-id="allProjectId"
            @success="getRelationTable"
          />
        </el-collapse-item>
        <!-- 前置依赖--------------------- -->
        <el-collapse-item v-if="dependTable.length" name="DEPEND">
          <template slot="title">
            <i class="iconfont el-icon-yibiaoban-shuxingyanse colorGreen" />
            <span class="centerText"> 前置依赖 </span>
            <span class="centerNum">
              {{ dependTable.length }}
            </span>
          </template>
          <needTable
            :table-data="dependTable"
            :project-id="projectId"
            :type-code="typeCode"
            :all-project-id="allProjectId"
            @success="getRelationTable"
          />
        </el-collapse-item>
        <!-- 后置影响-------------------------- -->
        <el-collapse-item v-if="affectTable.length" name="AFFECT">
          <template slot="title">
            <i class="iconfont el-icon-yibiaoban-shuxingyanse colorPerple" />
            <span class="centerText"> 后置影响 </span>
            <span class="centerNum">
              {{ affectTable.length }}
            </span>
          </template>
          <needTable
            :table-data="affectTable"
            :project-id="projectId"
            :type-code="typeCode"
            :all-project-id="allProjectId"
            @success="getRelationTable"
          />
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script>
import {
  apiAlmProjectRelation,
  apiAlmAddDepend,
  apiAlmPutDepend,
  apiAlmRequirementNoPage,
  iderTorequire
} from '@/api/vone/project/issue'
import { apiAlmProjectNoPage } from '@/api/vone/project/index'
import { debounce } from 'lodash'
import needTable from './need-table.vue'
import simpleAddIssue from './simple-add.vue'
import { apiAlmCorrelatedRequirement } from '@/api/vone/project/issue'

export default {
  name: 'IssueToIssue',
  components: {
    needTable,
    simpleAddIssue
  },
  props: {
    issueInfo: {
      type: Object,
      default: null
    },
    issueId: {
      type: String,
      default: undefined
    },
    projectId: {
      type: String,
      default: null
    },
    tabName: {
      type: String,
      default: null
    },
    typeCode: {
      type: String,
      default: null
    },
    // 拆分子项是否弹框
    showPopupForSplit: {
      type: Boolean,
      default: false
    },
    /**
     * 是否显示新增
     */
    isShowCreateBtn: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      pageLoading: false,
      activeNames: ['RELATION', 'DEPEND', 'AFFECT'],
      projectIdList: [], // 项目
      issueTab: {
        relation: 'RELATION'
      },
      addRelation: false,
      tableLoading: false,
      createSimple: false,
      typeId: '',
      formData: {},

      taskList: [],
      relateRules: {
        relation: [{ required: true, message: '请选择依赖关系', trigger: 'change' }],
        projectId: [{ required: true, message: '请选择项目', trigger: 'change' }],
        relationIssue: [{ required: true, message: '请选择需求', trigger: 'change' }]
      },
      loading: false,
      affectTable: [], // 影响
      connectTable: [], // 关联
      dependTable: [], // 依赖
      relationTable: [],
      parentTable: [], // 父需求
      childrenTable: [], // 拆分
      issueList: [],
      selectOptionWidth: '',
      requireLoading: false,
      addTask: null,
      taskForm: {
        taskId: []
      },
      allProjectId: []
    }
  },
  computed: {
    getUserInfo() {
      return function (row) {
        return row?.handleBy && row?.echoMap?.handleBy
      }
    }
  },
  watch: {
    issueId() {
      this.getRelationTable()
      this.getProjectList()
      this.getChildrenTable() // 需求拆分
    },
    immediate: true
  },

  mounted() {
    console.log(this.projectId)
    this.$set(this.taskForm, 'projectId', this.projectId || this.$route.params.id)

    this.getRelationTable()
    this.getProjectList()
    this.getChildrenTable() // 需求拆分
  },
  methods: {
    handleClickAdd() {
      if (this.showPopupForSplit) {
        this.$emit('add-child', 'issue')
      } else {
        this.addTask = 0
      }
    },
    init() {
      this.addTask = null
      this.getChildrenTable()
      this.$emit('initList')
    },
    creactIssueSuccess() {
      this.getChildrenTable()
      this.$emit('initList')
    },
    // 下拉框弹出时，设置弹框的宽度
    setOptionWidth(event) {
      this.$nextTick(() => {
        this.selectOptionWidth = event.target.parentNode.parentNode.clientWidth + 'px'
      })
    },
    // 需求拆分
    async getChildrenTable() {
      if (!this.issueId) {
        return
      }
      this.pageLoading = true
      const res = await iderTorequire({
        parentId: this.issueId
      })
      this.pageLoading = false
      if (!res.isSuccess) {
        return
      }
      this.childrenTable = res.data
    },
    // 归属项目
    async getProjectList() {
      this.pageLoading = true
      const res = await apiAlmProjectNoPage()
      this.pageLoading = false

      if (!res.isSuccess) {
        return
      }
      this.projectIdList = res.data
      this.allProjectId = res.data.map(r => r.id)
    },
    changeProject(val) {
      this.$set(this.issueTab, 'relationIssue', '')
      this.issueList = []
    },

    // 查需求列表
    getRequirementList: debounce(async function (query, projectId) {
      try {
        this.requireLoading = true
        const res = await apiAlmRequirementNoPage({
          name: query,
          projectId: this.issueTab.projectId || this.taskForm.projectId
        })
        this.requireLoading = false
        if (!res.isSuccess) {
          return
        }

        res.data.forEach(element => {
          element.disabled = element.id == this.issueId
        })

        this.issueList = res.data
      } catch (e) {
        this.requireLoading = false
      }
    }, 1000),

    // 查询依赖关系
    async getRelationTable() {
      if (!this.issueId) {
        return
      }
      this.pageLoading = true

      const res = await apiAlmProjectRelation('ISSUE', this.issueId)
      this.pageLoading = false
      if (!res.isSuccess) {
        return
      }

      this.connectTable = res.data && res.data.RELATION.length ? res.data.RELATION : []

      this.affectTable = res.data && res.data.AFFECT.length ? res.data.AFFECT : []

      this.dependTable = res.data && res.data.DEPEND.length ? res.data.DEPEND : []
      this.parentTable = res.data && res.data.PARENT.length ? res.data.PARENT : []
    },

    // 添加依赖关系
    async addRelationShip() {
      try {
        await this.$refs.issueTab.validate()
      } catch (error) {
        return
      }
      const { isSuccess, msg } = await apiAlmAddDepend({
        fromId: this.issueId,
        fromType: 'ISSUE',
        relationType: this.issueTab.relation,
        toId: this.issueTab.relationIssue,
        toType: 'ISSUE'
      })
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.$message.success(msg)
      this.getRelationTable()
      this.addRelation = false
      this.$set(this.issueTab, 'relation', 'DEPEND')
      this.$set(this.issueTab, 'relationIssue', '')
    },
    // 修改依赖关系
    async EditState(row) {
      const { isSuccess, msg } = await apiAlmPutDepend(row.relationId, row.relationType)
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.$message.success(msg)
      this.getRelationTable()
    },
    async saveTask() {
      this.saveLoading = true

      const res = await apiAlmCorrelatedRequirement({
        correlatedType: 'ADD',
        requirementId: this.issueId,
        requirementIds: this.taskForm.relationIssue
      })
      this.saveLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('创建成功')
      this.getChildrenTable()
      this.addTask = null
      this.$set(this.taskForm, 'relationIssue', '')
    },
    gotoEditI(val) {
      this.toProjectIssue(val, 'edit')
    },
    toRouter(val) {
      const activeApp = this.$route.meta.activeApp

      if (activeApp == 'project') {
        this.toProjectIssue(val, 'info')
      } else {
        this.toRequireCenter(val)
      }
    },
    // 跳转至项目
    toProjectIssue(val, type) {
      if (val.projectId && !this.allProjectId.includes(val.projectId)) {
        // this.$confirm('当前登陆账号没有该项目的查看权限,将跳转至需求中心, 是否继续?', '提示', {
        //   confirmButtonText: '确定',
        //   cancelButtonText: '取消',
        //   type: 'warning'
        // }).then(() => {
        //   this.toRequireCenter(val)
        // }).catch(() => {

        // })
        this.$message.warning('当前登陆账号没有该项目的查看权限,请联系项目经理授权')
        return
      }
      const newpage = this.$router.resolve({
        path: `/project/issue/${val.code}/${val.typeCode}/${val.projectId}`,
        query: {
          showDialog: true,
          queryId: val.id,
          rowTypeCode: val.typeCode,
          stateCode: val.stateCode,
          projectId: val.projectId,
          type: type
        }
      })
      window.open(newpage.href, '_blank')
    },
    // 跳转至需求中心
    toRequireCenter(val) {
      const newpage = this.$router.resolve({
        path: `/reqmcenter/require/requireList`,
        query: {
          showDialog: true,
          queryId: val.id,
          rowTypeCode: val.typeCode,
          stateCode: val.stateCode,
          projectId: val.projectId
        }
      })
      window.open(newpage.href, '_blank')
    },
    // 需求拆分解除关系
    async deleteRelation(row) {
      this.$confirm(`你确定要取消与 【${row.name}】的关系吗?`, '取消关联', {
        confirmButtonText: '确认',
        type: 'warning',
        closeOnClickModal: false
      })
        .then(async () => {
          const { isSuccess, msg } = await apiAlmCorrelatedRequirement({
            correlatedType: 'DELETE',
            requirementId: this.issueId,
            requirementIds: [row.id]
          })
          if (!isSuccess) {
            this.$message.error(msg)
            return
          }
          this.$message.success(msg)
          this.getChildrenTable()
        })
        .catch(() => {})
    }
  }
}
</script>
<style lang="scss" scoped>
.i-text {
  color: var(--main-theme-color);
  cursor: pointer;
}
.con-issue {
  font-weight: 400;
  font-size: 14px;
  padding: 0 12x;
  min-width: auto;
  font-weight: 400;
}

.boxContent {
  // padding: 5px 16px;
  .headerTotal {
    background: var(--disabled-bg-color);
    border-radius: 50%;
    padding: 0 5px;
    color: var(--main-font-color);
  }
  .centerText {
    font-size: 14px;
    font-weight: 400;
    margin: 0 5px;
  }
  .centerNum {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 20px;
    height: 15px;
    background: #ebeef5;
    border-radius: 14px;
    color: var(--main-font-color);
    font-size: 14px;
    font-weight: 400;
  }
  .boxTitle {
    font-weight: 500;
    color: #202124;
  }
}
.mt-10 {
  margin-top: 10px;
  display: flex;
  gap: 12px;
  div {
    padding: 0 !important;
  }
  .projectStl {
    :deep(.el-input__inner) {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    :deep(.el-input__suffix) {
      right: 2px;
    }
  }
  .btnCty {
    height: 32px;
    display: flex;
    align-items: center;
    text-align: right;
    justify-content: flex-end;
  }
}
.colorOrange {
  color: #ffd591;
}
.colorGreen {
  color: #b7eb8f;
}
.colorPerple {
  color: #adc6ff;
}
:deep(.el-collapse-item__header) {
  padding: 0;
}
:deep(.el-collapse-item__content) {
  padding: 0;
}
:deep(.el-button + .el-button) {
  margin: 0px;
}
.minbtns {
  :deep(.el-button + .el-button) {
    margin: 12px 0px 12px 12px;
  }
}

.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 0 8px;
  height: calc(15vh);
  .empty-svg {
    width: 50px;
    height: 40px;
  }
}
:deep(.el-button) {
  height: 22px;
  line-height: 22px;
}

:deep(.el-collapse-item__header) {
  height: 40px;

  padding: 0px 10px;
}
:deep(.el-collapse-item) {
  border: 1rem solid var(--solid-border-color);
  box-shadow: var(--main-bg-shadow);
  margin-bottom: 10px;
}
:deep(.el-select .el-input .el-input__inner) {
  border: none;
}
</style>
