import request from '@/utils/axios-api'
import requestUtils from '@/utils/requestUtils'

// 查询用户列表
export function getUserList(data) {
  return request({
    url: `/api/base/base/user/page`,
    method: 'post',
    data
  })
}
// 根据用户查询详情
export function getUserDetail(id) {
  return request({
    url: `/api/base/base/user/${id}`,
    method: 'get'
  })
}
// 根据用户查询详情
export function getUserById(id) {
  return request({
    url: `/api/testm/testProductCase/getUserById/${id}`,
    method: 'post'
  })
}
// 修改用户
export function editUser(data) {
  return request({
    url: `/api/base/base/user`,
    method: 'put',
    data
  })
}
// 新增用户
export function addUser(data) {
  return request({
    url: `/api/base/base/user`,
    method: data.id ? 'put' : 'post',
    data
  })
}
// 删除用户
export function deleteUser(data) {
  return request({
    url: `/api/base/base/user`,
    method: 'delete',
    data
  })
}

// 用户查询--不分页
export function apiBaseAllUserNoPage(data) {
  return request({
    url: `/api/base/base/user/query`,
    method: 'post',
    data
  })
}

// 修改用户密码
export function apiBaseUserNewPwd(data) {
  return request({
    url: `/api/base/base/user/reset`,
    method: 'POST',
    data
  })
}

// 用户管理--导出
export function apiBaseUserExport(data) {
  return request({
    url: `/api/base/base/user/export`,
    method: 'post',
    data
  })
}

// 用户管理--导入
export function apiBaseUserImport(data) {
  return request({
    url: `/api/base/base/user/import`,
    method: 'post',
    data: requestUtils.fileFormData(data)
  })
}

// 修改用户状态
export function updateStateById(userId, state) {
  return request({
    url: `/api/base/base/user/updateStateById/${userId}/${state}`,
    method: 'put'
  })
}

// 下载用户导入模板
export function downloadImportTemplate() {
  return request({
    url: `/api/base/base/user/downloadImportTemplate`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json; charset=UTF-8'
    },
    responseType: 'blob' // 在请求中加上这一行，特别重要
  })
}

// 企业设置
export function apiBaseCompanySetting(data) {
  return request({
    url: `/api/base/tenantPlatformConfig/save`,
    method: 'post',
    data
  })
}
// 修改密码

export function putPassword(data) {
  return request({
    url: `/api/base/base/user/password`,
    method: 'put',
    data
  })
}
// 根据用户id查询用户信息
export function getUsersDetail(ids) {
  return request({
    url: `/api/base/base/user/findUserById?ids=${ids}`,
    method: 'get'
  })
}
// 获取组织机构下子机构和当前组织结构的用户
export function getOrgUser(data) {
  return request({
    url: `/api/base/base/org/getOrgUser`,
    method: 'get',
    data
  })
}

// 获取当前登陆用户人数和最大用户人数
export function getMaxUserNum(params) {
  return request({
    url: `/api/base/base/user/maxUserNum`,
    method: 'get',
    data: params
  })
}

// 同步OA用户
export function syncUser() {
  return request({
    url: `/api/base/base/user/sync`,
    method: 'get'
  })
}
