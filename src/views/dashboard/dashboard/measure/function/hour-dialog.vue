<template>
  <el-dialog
    :close-on-click-modal="false"
    title="数据详情"
    v-model:value="visible"
    width="800px"
    :before-close="close"
  >
    <div>
      <vxe-table
        ref="issue-table"
        class="vone-vxe-table"
        border
        resizable
        min-height="200"
        max-height="300"
        show-overflow="tooltip"
        :loading="tableLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column title="工时日期" field="fillingTime" width="130">
          <template #default="{ row }">
            <span v-if="!row.groupType">{{
              dayjs(row.fillingTime).format("YYYY-MM-DD")
            }}</span>
            <div v-else>
              {{ row.name }}
              <span class="count-num">
                {{ row.count }}
              </span>
            </div>
          </template>
        </vxe-column>
        <vxe-column
          title="工作项名称"
          field="name"
          min-width="200"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <div v-if="!row.groupType">
              <span v-if="queryType != 'hour'">
                <el-icon class="iconfont" style="color: rgb(135, 145, 250)"
                  ><el-icon-icon-xuqiu
                /></el-icon>
                <el-icon class="iconfont" style="color: rgb(250, 107, 87)"
                  ><el-icon-icon-quexian
                /></el-icon>
                <el-icon class="iconfont" style="color: rgb(62, 123, 250)"
                  ><el-icon-icon-renwu
                /></el-icon>
                <span v-if="row.echoMap">
                  {{
                    row.echoMap.biz
                      ? row.echoMap.biz.name
                      : !row.groupType
                      ? "工作项已不存在"
                      : ""
                  }}
                </span>
              </span>
              <span v-else>
                <el-icon class="iconfont" style="color: rgb(135, 145, 250)"
                  ><el-icon-icon-xuqiu
                /></el-icon>
                <el-icon class="iconfont" style="color: rgb(250, 107, 87)"
                  ><el-icon-icon-quexian
                /></el-icon>
                <el-icon class="iconfont" style="color: rgb(62, 123, 250)"
                  ><el-icon-icon-renwu
                /></el-icon>
                <span>
                  {{
                    row.name ? row.name : !row.groupType ? "工作项已不存在" : ""
                  }}
                </span>
              </span>
            </div>
          </template>
        </vxe-column>
        <vxe-column
          :title="queryType != 'hour' ? '填报工时' : '预估工时'"
          width="90"
        >
          <template #default="{ row }">
            <span v-if="queryType != 'hour'">{{
              row.duration ? row.duration + "h" : ""
            }}</span>
            <span v-else>{{
              row.estimateHour ? row.estimateHour + "h" : ""
            }}</span>
          </template>
        </vxe-column>
        <vxe-column title="填报日期" field="createTime" width="180">
          <template #default="scope">
            {{ scope.row.createTime }}
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <vone-pagination
      ref="pagination"
      :total="tableData.total"
      style="position: static"
      @update="getInitTableData"
    />
    <template v-slot:footer>
      <div class="dialog-footer">
        <el-button @click="close">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import {
  IconXuqiu as ElIconIconXuqiu,
  IconQuexian as ElIconIconQuexian,
  IconRenwu as ElIconIconRenwu,
} from "@element-plus/icons-vue";
import { $on, $off, $once, $emit } from "../../../../../utils/gogocodeTransfer";
import { getHourDetail, getCheckHourDetail } from "@/api/vone/dashboard/index";
import dayjs from "dayjs";
export default {
  components: {
    ElIconIconXuqiu,
    ElIconIconQuexian,
    ElIconIconRenwu,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    queryType: {
      type: String,
      default: "",
    },
    layout: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      tableData: {},
      tableLoading: false,
      prioritList: [],
    };
  },
  computed: {},
  mounted() {
    // if (this.queryType == 'hour') {
    //   this.getInitTableData()
    // } else {
    //   this.getTableData()
    // }
    this.getTableData();
  },
  methods: {
    // 初始化进入页面列表
    async getInitTableData(e, type) {
      const tableAttr = this.$refs?.pagination?.pageObj || {
        current: 1,
        size: 20,
      };
      const sortObj = this.$refs.searchForm?.sortObj;
      const params = {
        ...tableAttr,
        ...sortObj,
        extra: {
          ...this.extraData,
        },
        model: {
          projectIds: this.layout.projectId || [],
          orgIds: this.layout.orgId || [],
          productsetIds: this.layout.productId || [],
          startDate: dayjs(this.layout.time && this.layout.time[0]).format(
            "YYYY-MM-DD"
          ),
          endDate: dayjs(this.layout.time && this.layout.time[1]).format(
            "YYYY-MM-DD"
          ),
        },
      };

      this.tableLoading = true;
      // this.$set(this.formData, 'projectId', this.$route.params.id)

      const res = await getHourDetail(params);
      this.tableLoading = false;
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      this.tableData = res.data;
    },
    async getTableData() {
      const tableAttr = this.$refs?.pagination?.pageObj || {
        current: 1,
        size: 20,
      };
      const sortObj = this.$refs.searchForm?.sortObj;
      const params = {
        ...tableAttr,
        ...sortObj,
        extra: {
          ...this.extraData,
        },
        model: {
          projectIds: this.layout.projectId || [],
          orgIds: this.layout.orgId || [],
          productsetIds: this.layout.productId || [],
          startDate: dayjs(this.layout.time && this.layout.time[0]).format(
            "YYYY-MM-DD"
          ),
          endDate: dayjs(this.layout.time && this.layout.time[1]).format(
            "YYYY-MM-DD"
          ),
        },
      };

      this.tableLoading = true;
      // this.$set(this.formData, 'projectId', this.$route.params.id)
      let res = null;
      if (this.queryType == "hour") {
        res = await getHourDetail(params);
      } else {
        res = await getCheckHourDetail(params);
      }
      this.tableLoading = false;
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      this.tableData = res.data;
    },
    close() {
      $emit(this, "update:visible", false);
    },
    goto(row) {
      const newpage = this.$router.resolve({
        path: `/project/defect/${row.code}/${row.typeCode}/${row.projectId}`,
      });
      window.open(newpage.href, "_blank");
    },
  },
  emits: ["update:visible"],
};
</script>

<style lang="scss" scoped>
.topBox {
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  color: #2c2e36;
  width: 180px;
  a {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
  }
  span {
    margin-left: 8px;
    font-size: 26px;
    font-style: normal;
    font-weight: 700;
  }
}
:deep(.vxe-table--body-wrapper) {
  height: 200px !important;
}
</style>
