<template>
  <el-table
    ref="product-case-records"
    v-loading="tableLoading"
    class="vone-table"
    table-key="product-case-records"
    row-key="id"
    :data="tableData"
  >
    <el-table-column
      label="计划名称"
      prop="name"
      min-width="320px"
      show-overflow-tooltip
    >
      <template v-slot="{ row }">
        <div>
          <el-icon
            class="iconfont"
            style="color: var(--main-theme-color, #3e7bfa); margin-right: 4px"
            ><el-icon-testcase
          /></el-icon>
          <span>{{ row.name }}</span>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="状态" prop="stateId" show-overflow-tooltip>
      <template v-slot="{ row }">
        <div>
          {{
            row.stateId == 0 ? "未开始" : row.stateId == 1 ? "进行中" : "已完成"
          }}
        </div>
      </template>
    </el-table-column>
    <el-table-column label="类型" prop="type" show-overflow-tooltip>
      <template v-slot="{ row }">
        <div>
          {{
            row.type == "system"
              ? "系统测试"
              : row.type == "smoke"
              ? "冒烟测试"
              : "回归测试"
          }}
        </div>
      </template>
    </el-table-column>
    <el-table-column
      label="维护人"
      prop="execBy"
      width="150"
      show-overflow-tooltip
    >
      <template v-slot="{ row }">
        <span v-if="row.leadingBy && row.echoMap && row.echoMap.leadingBy">
          <vone-user-avatar
            :avatar-path="row.echoMap.leadingBy.avatarPath"
            :name="row.echoMap.leadingBy.name"
          />
        </span>
        <span v-else> {{ row.leadingBy }}</span>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import { Testcase as ElIconTestcase } from "@element-plus/icons-vue";
import { getArchiveCase } from "@/api/vone/testplan";
export default {
  components: {
    ElIconTestcase,
  },
  props: {
    data: {
      type: Object,
      default: () => {},
    },
    tableShow: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      tableLoading: false,
      tableData: [],
      tableOptions: {},
      formData: {},
      statusAllList: [
        // 下拉框状态
        {
          value: "system",
          label: "成功",
          class: "el-icon-success",
          color: "#3CB540",
        },
        {
          value: "smoke",
          label: "失败",
          class: "el-icon-error",
          color: "#FA6B57",
        },
        {
          value: "block",
          label: "阻塞",
          class: "el-icon-tips-minus-circle-fill",
          color: "#FFBF47",
        },
        {
          value: "skip",
          label: "跳过",
          class: "iconfont el-icon-shijian1",
          color: "#BD7FFA",
        },
      ],
    };
  },
  methods: {
    async getTableData() {
      this.tableLoading = true;

      // const params = {}

      // const tableAttr = this.$refs['product-case-records'].exportTableQueryData()
      // params = {
      //   ...tableAttr,
      //   extra: {},
      //   model: { ...this.formData }
      // }
      const res = await getArchiveCase(
        this.data.updateTime,
        this.data.updatedBy
      );
      this.tableLoading = false;
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }

      this.tableData = res.data;
    },
  },
};
</script>
