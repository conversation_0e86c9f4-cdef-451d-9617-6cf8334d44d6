import G6 from '@antv/g6'

const tooltip = new G6.Tooltip({
  offsetX: 20,
  offsetY: 20,
  fixToNode: true,
  // 允许出现 tooltip 的 item 类型
  itemTypes: ['node'],
  // 自定义 tooltip 内容
  getContent: e => {
    const outDiv = document.createElement('div')
    outDiv.style.width = 'fit-content'
    outDiv.style.padding = '8px 12px'

    const item = e.item.getModel()
    if (item.depth === 2) {
      outDiv.innerHTML = `
      <ul>
        <li>主机名称: ${item.name || '-'}</li>
        <li>地址: ${item.hostIp || '-'} </li>
        <li>CPU核数: ${item.hostCpuNum || '-'}</li>
        <li>内存大小: ${item.hostMemSize || '-'}MB</li>
        <li>硬盘大小: ${item.hostHdSize || '-'}GB</li>
      </ul>`
    } else {
      if (item.classify === 'dbComponents') {
        outDiv.innerHTML = `
      <ul>
        <li>IP地址: ${item.serverIp || '-'}</li>
        <li>组件版本: ${item.appComponentsType || '-'}</li>
        <li>安装路径: ${item.installationPath || '-'}</li>
        <li>端口: ${item.serverPort || ''}</li>
      </ul>`
      } else {
        outDiv.innerHTML = `
      <ul>
        <li>IP地址: ${item.serverIp || '-'}</li>
        <li>组件版本: ${item.appComponentsType || '-'}</li>
        <li>安装版本: ${item.deployVersion || '-'}</li>
        <li>端口: ${item.serverPort || ''}</li>
        <li>工程包名称: ${item.productName || '-'}</li>
        <li>模块名称: ${item.moduleName || '-'}</li>
      </ul>`
      }
    }
    return outDiv
  },
  // 是否显示
  shouldBegin(e) {
    return e.item.getModel().depth > 1
  }
})

export default tooltip
