<template>
  <div>
    <el-dialog :visible.sync="visible" :before-close="onClose" :close-on-click-modal="false" destroy-on-close width="80%" top="5vh" v-on="$listeners">
      <div slot="title">趋势报表</div>
      <div class="header">
        <div class="titleText">

          <i class="el-icon-warning-outline" />针对多个产品或用例库场景
        </div>
        <el-select
          v-model="productId"
          clearable
          filterable
          multiple
          collapse-tags
          placeholder="请选择用例库"
          class="filterSelect"
          popper-class="multipleSelect"
          @focus="setOptionWidth"
          @change="changeProduct"
        >
          <el-option
            v-for="item in libraryList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
            :style="{width:selectOptionWidth}"
          >
            <svg class="icon" aria-hidden="true">
              <use xlink:href="#el-application-filejia2" />
            </svg>
            <span style="margin-left:4px;">{{ item.name }}</span>
          </el-option>
        </el-select>
      </div>
      <el-row style="margin-bottom: 10px;" :gutter="12">
        <el-col :span="12">
          <lineChart ref="caseLineChart" :title="'用例新增周折线图'" type="case" :library-id="productId" />
        </el-col>
        <el-col :span="12">
          <barChart ref="caseBarChart" :title="'用例新增按人分布'" type="case" :library-id="productId" />
        </el-col>
      </el-row>
      <el-row style="margin-bottom: 10px;" :gutter="12">
        <el-col :span="12">
          <lineChart ref="planLineChart" :title="'计划新增周折线图'" type="plan" :library-id="productId" />
        </el-col>
        <el-col :span="12">
          <barChart ref="planBarChart" :title="'计划新增按人分布'" type="plan" :library-id="productId" />
        </el-col>
      </el-row>
      <el-row :gutter="12">
        <el-col :span="12">
          <lineChart ref="bugLineChart" :title="'缺陷新增周折线图'" type="bug" :library-id="productId" />
        </el-col>
        <el-col :span="12">
          <barChart ref="bugBarChart" :title="'缺陷新增按人分布'" type="bug" :library-id="productId" />
        </el-col>
      </el-row>

      <div slot="footer">
        <el-button :loading="saveLoading" @click="onClose">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import BarChart from './bar-chart.vue'
import lineChart from './line-chart.vue'

export default {
  components: {
    lineChart,
    BarChart
  },
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    id: {
      type: String,
      default: null
    },
    // 选中的用例库id
    selectedId: {
      type: Array,
      default: () => ([])
    },
    // 产品用例库列表
    libraryList: {
      type: Array,
      default: () => ([])
    }
  },
  data() {
    return {
      saveLoading: false,
      productList: [],
      productId: [],
      selectOptionWidth: ''
    }
  },
  mounted() {
    // this.productId = this.selectedId
  },
  methods: {
    onClose() {
      this.$emit('update:visible', false)
    },
    // 下拉框弹出时，设置弹框的宽度
    setOptionWidth(event) {
      this.$nextTick(() => {
        this.selectOptionWidth = event.target.parentNode.parentNode.clientWidth + 'px'
      })
    },
    changeProduct(val) {
      // const ids = Array.isArray(val) ? val.join() : val
      this.$refs?.caseLineChart.getData(val)
      this.$refs?.caseBarChart.getData(val)
      this.$refs?.bugLineChart.getData(val)
      this.$refs?.bugBarChart.getData(val)
      this.$refs?.planLineChart.getData(val)
      this.$refs?.planBarChart.getData(val)
    }
  }
}
</script>

<style lang="scss" scoped>
.titleText {
  color: var(--auxiliary-font-color);
  font-size: 11px;
}
::v-deep .el-col-12 {
  height: 200px;
}
.header{
	display: flex;
	justify-content:space-between;
  align-items: center;
  margin-bottom: 12px;
}
::v-deep {
  .el-dialog__body {
   padding: 16px;
  }
  .el-input__inner{
  border: none;
  background: #F5F6FA !important;
  color: #4882FA !important;
}
}
.filterSelect {
  width: 30%;
}
::v-deep .el-dialog__wrapper {
  // z-index: 9999!important;
}
</style>
