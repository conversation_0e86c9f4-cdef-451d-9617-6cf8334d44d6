
import { apiAlmProjectNoPage } from '@/api/vone/project/index'
// 查询所有机构

const projectMixin = {
  data() {
    return {
      allProjectList: [],
      projectMap: {}
    }
  },

  mounted() {
    this.getProjectList()
  },
  methods: {
    // 查询所有项目
    async getProjectList() {
      const res = await apiAlmProjectNoPage()
      if (!res.isSuccess) {
        console.warn(res.msg)
        return
      }
      this.allProjectList = res.data
      this.projectMap = res.data.reduce((r, v) => (r[v.id] = v.name) && r, {})
    }

  }
}

export default projectMixin
