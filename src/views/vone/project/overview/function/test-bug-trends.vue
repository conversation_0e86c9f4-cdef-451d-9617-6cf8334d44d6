<template>
  <vone-echarts-card title="缺陷趋势" :name="name">
    <!-- 缺陷趋势 -->
    <vone-echarts v-if="trendsData" :options="options" height="300px" />
    <vone-empty v-else style="height: 300px" />
  </vone-echarts-card>
</template>

<script>
import { getProjectDefectTrends } from '@/api/vone/project/overview'
export default {
  props: {
    name: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      trendsData: null,
      options: {},
    }
  },
  mounted() {
    this.getProjectOptions()
  },
  methods: {
    async getProjectOptions() {
      const { data, isSuccess, msg } = await getProjectDefectTrends(
        this.$route.params.id
      )
      if (!isSuccess) {
        this.$message.error(msg)
        return
      }
      this.trendsData = data
      this.options = {
        color: ['#7486eb', '#6ad2a8', '#FFBF47'],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          backgroundColor: '#fff',
          borderColor: 'none',
          extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);', // 附加阴影样式
          textStyle: {
            color: '#53565C',
          },
        },
        legend: {
          x: 'right', // 居右显示
          itemHeight: 12,
          itemWidth: 24,
          borderRadius: 5,
          data: [
            {
              name: '总数',
              icon: 'rect',
            },
            {
              name: '新增',
              icon: 'rect',
            },
            {
              name: '修复',
              icon: 'rect',
            },
          ],
          textStyle: {
            // 图例文字的样式
            color: '#8A8F99',
          },
        },
        grid: {
          left: '5%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: data.date,
          boundaryGap: false,
          splitLine: {
            show: false, // 去掉网格线
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#8A8F99', // 更改坐标轴文字颜色
            },
          },
          axisLine: {
            lineStyle: {
              color: '#EBEEF5', // 更改坐标轴颜色
            },
          },
        },
        yAxis: [
          {
            type: 'value',
            minInterval: 1,
            axisTick: {
              // y轴刻度线
              show: false,
            },
            axisLine: {
              show: false, // 不显示坐标轴轴线
            },
            splitLine: {
              // 网格线
              lineStyle: {
                type: 'dashed', // 设置网格线类型 dotted：虚线   solid:实线
                width: 1,
                color: '#EBEEF5',
              },
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#8A8F99', // 更改坐标轴文字颜色
              },
            },
          },
        ],
        series: [
          {
            name: '总数',
            type: 'line',
            symbol: 'none',
            data: data.total,
          },
          {
            name: '新增',
            type: 'line',
            symbol: 'none',
            data: data.add,
            smooth: false, // 关键点，为true是不支持虚线的，实线就用true
            itemStyle: {
              lineStyle: {
                width: 2,
                type: 'dotted', // 'dotted'虚线 'solid'实线
              },
            },
          },
          {
            name: '修复',
            type: 'line',
            symbol: 'none',
            data: data.close,
            smooth: false, // 关键点，为true是不支持虚线的，实线就用true
            itemStyle: {
              lineStyle: {
                width: 2,
                type: 'dotted', // 'dotted'虚线 'solid'实线
              },
            },
          },
        ],
      }
    },
  },
}
</script>
