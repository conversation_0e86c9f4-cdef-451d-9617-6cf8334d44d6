<template>
  <div style="width: 100%; height: 180px">
    <div class="title">
      {{ title }}
      <el-tooltip placement="right">
        <template v-slot:content>
          <div>
            <span v-if="title == '需求概览'">
              待处理：根据软件需求创建的时间,统计软件需求在当前时间段内的数量。<br />
              1.创建时间大于当前月开始日期并且小于当前月结束日期。<br />
              处理中：根据软件需求创建的时间,统计软件需求在当前时间段内的数量。<br />
              1.创建时间小于当前月开始日期并且(实际完成日期大于当前月开始日期或者状态为
              “未完成”的软件需求数量)。<br />
              已完成：统计周期内状态为"已完成"的需求数量，根据软件需求实际完成的时间，统计软件需求在当前时间段内的数量。<br />
              1.实际完成时间大于当前月开始日期并且小于当前月结束日期。
            </span>
            <span v-if="title == '任务概览'">
              未开始：通过任务创建时间与任务结束时间来计算是否属于当前统计时间段内任务状态阶段为未开始的数量。<br />
              1、创建时间小于当前月开始日期并且（结束时间大于当前月开始日期或者结束时间为空）；<br />
              2、创建时间大于当前月开始日期并且小于当前月结束日期。<br />
              进行中：通过任务处理的时间，统计任务在当前时间段内的数量。<br />
              1.创建时间小于当前月开始日期并且（实际完成日期大于当前月开始日期或者未完成的任务数量）。<br />
              已完成：通过任务实际完成时间来计算是否属于当前统计时间段内的数量。<br />
              1、实际完成时间大于当前月开始日期并且小于当前月结束白期。
            </span>
            <span v-if="title == '缺陷概览'">
              待修复：通过缺陷创建时间与缺陷结束时间来计算是否属于当前统计时间段内缺陷状态为待处理的数量。<br />
              1、创建时间小于当前月开始日期并且(结束时间大于当前月开始日期或者结束时间为空)；<br />
              2、创建时间大于当前月开始日期并且小于当前月结束日期。<br />
              修复中:
              通过缺陷结束时间与结束状态来计算是否属于当前统计时间没内缺陷状态为处理中的数量。<br />
              1、缺陷结束时间大于当前月开始日期并且小于当前月结束日期；<br />
              2、缺陷结束状态为“已修复”。<br />
              已修复：通过缺陷结束时间与结束状态来计算是否属于当前统计时间段内缺陷状态为已完成的数量。<br />
              1、缺陷结束时间大于当前月开始日期并且小于当前月结束日期；<br />
              2、缺陷结束状态为“已关闭”。
            </span>
            <span v-if="title == '工时概览'">
              预估：按工作项预估工时对统计时间段内的所有预估工时求和。<br />
              登记：按工时填报日期对统计时间段内的所有登记工时求和。<br />
              完成：工作项状态处于“已完成”阶段的预估工时求和。
            </span>
          </div>
        </template>
        <el-icon
          class="iconfont"
          style="color: var(--font-disabled-color); margin-left: 4px"
          ><el-icon-tips-info-circle
        /></el-icon>
      </el-tooltip>
    </div>
    <vone-echarts height="120px" :options="options" @chartClick="chartClick" />
    <detailDialog
      v-bind="detailParams"
      v-if="detailParams.visible"
      v-model:value="detailParams.visible"
      :type="detailParams.type"
      :layout="layout"
      :query-type="detailParams.queryType"
      :project-id="projectId"
      :org-id="orgId"
    />
  </div>
</template>

<script>
import { TipsInfoCircle as ElIconTipsInfoCircle } from "@element-plus/icons-vue";
import detailDialog from "./detail-dialog.vue";
export default {
  components: {
    detailDialog,
    ElIconTipsInfoCircle,
  },
  props: {
    title: {
      type: String,
      default: "",
    },
    layout: {
      type: Object,
      default: () => {},
    },
    data: {
      type: Array,
      default: () => [],
    },
    projectId: {
      type: String,
      default: "",
    },
    orgId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      options: {
        color: ["#99A0AC", "#3E7BFA", "#18BF82"],
        tooltip: {
          trigger: "item",
          axisPointer: {
            type: "shadow",
          },
          backgroundColor: "#fff",
          borderColor: "none",
          extraCssText: "box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);", // 附加阴影样式
        },
        legend: {
          orient: "vertical",
          left: "right",
          textStyle: {
            // 图例文字的样式
            color: "#777F8E",
          },
          itemHeight: 8,
          itemWidth: 8,
          icon: "circle",
        },
        series: [
          {
            type: "pie",
            center: ["40%", "50%"],
            radius: ["50%", "90%"],
            avoidLabelOverlap: true,
            data: [],
            label: {
              // show: false,
              position: "inside",
              formatter: "{c}",
              color: "#2C2E36",
            },
            labelLine: {
              show: false,
            },
          },
        ],
      },
      detailParams: {
        visible: false,
      },
      countData: {},
    };
  },
  watch: {
    data: {
      handler(val) {
        this.options.series[0].data = val;
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {
    if (this.title == "工时概览") {
      this.options.color = ["#3E7BFA", "#18BF82", "#F7A14A"];
    }
  },
  methods: {
    chartClick(params) {
      let type = "";
      let queryType = "";
      switch (this.title) {
        case "需求概览":
          type = "issue";
          queryType =
            params.name == "待处理"
              ? 0
              : params.name == "处理中"
              ? 1
              : params.name == "已完成"
              ? 2
              : "";
          break;
        case "任务概览":
          type = "task";
          queryType =
            params.name == "未开始"
              ? 0
              : params.name == "进行中"
              ? 1
              : params.name == "已完成"
              ? 2
              : "";
          break;
        case "缺陷概览":
          type = "bug";
          queryType =
            params.name == "待修复"
              ? 0
              : params.name == "修复中"
              ? 1
              : params.name == "已修复"
              ? 2
              : "";
          break;
        case "工时概览":
          type =
            params.name == "预估"
              ? "estimateHour"
              : params.name == "登记"
              ? "checkHour"
              : params.name == "完成"
              ? "completeHour"
              : "";
          break;
        default:
          break;
      }
      this.detailParams = { visible: true, type, queryType };
    },
  },
};
</script>

<style lang="scss" scoped>
.title {
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 22px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}
</style>
