<template>
  <div>
    <vone-search-wrapper>
      <template v-slot:search>
        <vone-search-dynamic
          table-search-key="baseUserTable"
          :model="formData"
          :table-ref="$refs['baseUserTable']"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="名称" prop="name">
                <el-input v-model="formData.name" placeholder="请输入名称" />
              </el-form-item>
            </el-col>
          </el-row>
        </vone-search-dynamic>
      </template>
    </vone-search-wrapper>
    <div :style="{ height: $tableHeight }">
      <vxe-table
        ref="baseUserTable"
        class="vone-vxe-table"
        border
        height="auto"
        show-overflow="tooltip"
        :loading="pageLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column title="编号" field="code" min-width="150" />
        <vxe-column title="工作项" field="typeCode" min-width="150">
          <template v-slot="scope">
            <span
              v-if="
                scope.row.typeCode &&
                scope.row.echoMap &&
                scope.row.echoMap.typeCode
              "
            >
              {{ scope.row.echoMap.typeCode.classify.desc }}
            </span>
          </template>
        </vxe-column>
        <vxe-column title="项目" field="projectId" min-width="150">
          <template v-slot="scope">
            <span
              v-if="
                scope.row.projectId &&
                scope.row.echoMap &&
                scope.row.echoMap.projectId
              "
            >
              {{ scope.row.echoMap.projectId.name }}
            </span>
          </template>
        </vxe-column>
        <vxe-column title="所属迭代" field="planId" min-width="150">
          <template v-slot="scope">
            <span
              v-if="
                scope.row.planId &&
                scope.row.echoMap &&
                scope.row.echoMap.planId
              "
            >
              {{ scope.row.echoMap.planId.name }}
            </span>
            <span class="disText"> 未关联迭代 </span>
          </template>
        </vxe-column>
        <vxe-column title="负责人" field="leadingBy" min-width="150">
          <template v-slot="scope">
            <vone-user-avatar
              :avatar-path="
                getUserInfo(scope.row) ? getUserInfo(scope.row).avatarPath : ''
              "
              :name="getUserInfo(scope.row) ? getUserInfo(scope.row).name : ''"
            />
          </template>
        </vxe-column>
        <vxe-column title="状态" field="stateCode" min-width="150">
          <template v-slot="scope">
            <span
              v-if="
                scope.row.stateCode &&
                scope.row.echoMap &&
                scope.row.echoMap.stateCode
              "
            >
              <div
                :style="{
                  textAlign: 'center',
                  borderRadius: '5px',
                  border: `1px solid ${scope.row.echoMap.stateCode.color}`,
                  width: '80px',
                  color: `${scope.row.echoMap.stateCode.color}`,
                }"
              >
                {{ scope.row.echoMap.stateCode.name }}
              </div>
            </span>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <vone-pagination
      ref="pagination"
      :total="tableData.total"
      @update="getInitTableData"
    />
  </div>
</template>

<script>
import { apiAlmIdeaPage } from '@/api/vone/reqmcenter/idea'
import { requirementPage } from '@/api/vone/reqmcenter/require'
export default {
  props: {
    currentType: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      formData: {
        // productId: [this.$route.params.id]
      },
      pageLoading: false,
      tableData: {},
      tableOptions: {},
    }
  },
  computed: {
    getUserInfo() {
      return function (row) {
        return row?.leadingBy && row?.echoMap?.leadingBy
      }
    },
  },
  watch: {
    currentType: {
      handler() {
        this.getInitTableData()
      },
      deep: true,
      immediate: true,
    },
  },

  methods: {
    async getInitTableData() {
      this.pageLoading = true

      const pageObj = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
      const params = {
        ...pageObj,
        extra: {},
        model: { ...this.formData },
      }
      const res =
        this.currentType == 'idea'
          ? await apiAlmIdeaPage(params)
          : this.currentType == 'issue'
          ? await requirementPage(params)
          : await requirementPage(params)
      this.tableLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      this.tableData = res.data
    },
  },
}
</script>

<style lang="scss" scoped>
.disText {
  color: var(--placeholder-color);
}
</style>
