<template>
  <div class="menu">

    <div class="menu-title">
      <span>

        <svg v-if="projectTypeCode == 'AGILE'" class="icon" aria-hidden="true">
          <use xlink:href="#el-icon-icon-minjie1" />
        </svg>
        <svg v-else-if="projectTypeCode == 'WALL'" class="icon" aria-hidden="true">
          <use xlink:href="#el-icon-icon-pubu1" />
        </svg>
        <svg v-else-if="projectTypeCode == 'DEVOPS'" class="icon" aria-hidden="true">
          <use xlink:href="#el-icon-project-devops" />
        </svg>
        <svg v-else-if="projectTypeCode == 'CAR'" class="icon" aria-hidden="true">
          <use xlink:href="#el-icon-project-car-model" />
        </svg>
        <svg v-else-if="projectTypeCode == 'PLATFORM'" class="icon" aria-hidden="true">
          <use xlink:href="#el-icon-project-platform" />
        </svg>
        <svg v-else class="icon" aria-hidden="true">
          <use xlink:href="#el-icon-ceshixiangmu" />
        </svg>

      </span>

      <span class="text-over"> {{ projectInfo.name }}
      </span>

      <i v-if="!iconFlag" class="iconfont el-icon-direction-down" />
      <i v-else class="iconfont el-icon-direction-up" />
    </div>
    <el-select ref="select" v-model="projectId" filterable placeholder="切换项目" @change="projectChange" @visible-change="(v) => visibleChange(v, 'select', selectClick)">
      <el-option v-for="item in projectList" :key="item.id" :label="item.name" :value="item.id">
        <div>
          <span>

            <svg v-if="item.typeCode == 'AGILE'" class="icon" aria-hidden="true">
              <use xlink:href="#el-icon-icon-minjie1" />
            </svg>
            <svg v-else-if="item.typeCode == 'WALL'" class="icon" aria-hidden="true">
              <use xlink:href="#el-icon-icon-pubu1" />
            </svg>
            <svg v-else-if="item.typeCode == 'DEVOPS'" class="icon" aria-hidden="true">
              <use xlink:href="#el-icon-project-devops" />
            </svg>
            <svg v-else-if="item.typeCode == 'CAR'" class="icon" aria-hidden="true">
              <use xlink:href="#el-icon-project-car-model" />
            </svg>
            <svg v-else-if="item.typeCode == 'PLATFORM'" class="icon" aria-hidden="true">
              <use xlink:href="#el-icon-project-platform" />
            </svg>
            <svg v-else class="icon" aria-hidden="true">
              <use xlink:href="#el-icon-ceshixiangmu" />
            </svg>

          </span>

          &nbsp;&nbsp;
          <span>{{ item.name }}</span>
        </div>
      </el-option>
    </el-select>
    <span class="solid" />
  </div>
</template>

<script>

import { apiAlmProjectNoPage, apiProjectInfo } from '@/api/vone/project/index'
import { apiProjectAuth } from '@/api/vone/project/index'
import { getPermission, getRouter } from '@/utils/auth'
import { cloneDeep } from 'lodash'
import { setPermission } from '@/utils/auth'
// import pathToRegexp from 'path-to-regexp'
export default {
  data() {
    return {
      projectId: '',
      projectTypeCode: '',
      projectKey: '',
      projectList: [],
      projectInfo: {},
      projectsList: [],
      projectsMap: {},
      iconFlag: false,
      searchTitle: ''
    }
  },
  watch: {
    $route: function() {
      this.projectTypeCode = this.$route.params.projectTypeCode
      this.projectKey = this.$route.params.projectKey
      this.projectId = this.$route.params.id
      if (this.projectKey) {
        this.getProjectInfo()
      }
      this.getProjectList()
      this.searchTitle = '查看全部项目'
    }
  },
  mounted() {
    this.projectTypeCode = this.$route.params.projectTypeCode
    this.projectKey = this.$route.params.projectKey
    this.projectId = this.$route.params.id

    this.getProjectInfo()
    this.getProjectList()
    this.searchTitle = '查看全部项目'
  },
  beforeDestroy() {
    // 清除列表缓存
    this.$store.commit('project/UPDATE_LIST', [])
  },
  methods: {
    async getProjectInfo() {
      if (!this.projectKey) {
        return
      }
      const { data, msg, isSuccess } = await apiProjectInfo(
        this.projectId
      )
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      if (data) {
        this.projectInfo = data
      }
    },
    async getProjectList() {
      const { isSuccess, data } = await apiAlmProjectNoPage()
      if (!isSuccess) {
        return
      }
      this.projectList = data
      // 保存当前查询的项目列表
      this.$store.commit('project/UPDATE_LIST', data)
    },

    async projectChange(projectId) {
      const { data, isSuccess, msg } = await apiProjectAuth(projectId)
      const jumpRouter = cloneDeep(data)
      if (!isSuccess) {
        this.$message.error(msg)
        return
      }
      if (!isSuccess) {
        return this.$message('获取用户权限失败，请重新登录')
      }
      if (!data.length) {
        this.$message.warning('当前登录用户【项目角色】查看当前项目信息权限不足,请联系项目经理授权')
        return
      }

      // -------------------------------------------------------------------------------
      // 处理按钮权限
      const hasPermissionList = [] // 接口返回的用来接收新的按钮权限数组
      var findPermission = function(V) {
        V.forEach(item => { // 把传入的数组循环遍历
          if (item.meta.isButton === true) {
            hasPermissionList.push(item.meta.code) // item.meta.isButton 为true 把id添加到新数组
          }
          if (item.children) {
            findPermission(item.children) // 递归调用自身
          }
        })
      }
      findPermission(data) // 调用函数

      const permission = getPermission() // 从登录接口获取的权限按钮数据

      const allPermision = [...new Set([...permission, ...hasPermissionList])] // 去重

      setPermission(allPermision)

      // -------------------------------------------------------------------------------

      const routerMenu = cloneDeep(getRouter())
      const projectSettingMenu = cloneDeep(routerMenu.find(ele => ele.name == 'project').children.find(ele => ele.name == 'project_view'))

      routerMenu.map(item => {
        if (item.name == 'project') {
          item.children = []
          item.children.push(projectSettingMenu)
          item.children = [...item.children, ...data]
        }
      })

      this.$store.commit('user/set_router', routerMenu)

      // this.$store.commit('user/set_projectMenu', data)

      const projectTypeCode = this.projectList.find(
        (item) => item.id == projectId
      ).typeCode

      const code = this.projectList.find(
        (item) => item.id == projectId
      ).code

      // 保存路由信息
      this.$store.dispatch('project/itemProject', projectTypeCode)

      const firstMenuName = jumpRouter[0]?.children[0]?.name || jumpRouter[0]?.name
      this.$router.push({
        name: firstMenuName,
        params: { projectKey: code, projectTypeCode: projectTypeCode, id: projectId }
      })

      this.getProjectInfo()
    },
    visibleChange(visible, refName, onClick) {
      this.iconFlag = visible
      if (visible) {
        const ref = this.$refs[refName]
        let popper = ref.$refs.popper
        if (popper.$el) popper = popper.$el
        if (
          !Array.from(popper.children).some(
            (v) => v.className === 'el-cascader-menu__list'
          )
        ) {
          const el = document.createElement('ul')
          el.className = 'el-cascader-menu__list'
          el.style =
            'border-top: solid 1px #e4e7ed; padding:0; color: #606266;'
          el.innerHTML =
            `<li class="el-cascader-node color-info" style="height: 38px; line-height: 38px">
            <span class="el-cascader-node__label">` +
            this.searchTitle +
            `</span>
            <i class="el-icon-arrow-right el-cascader-node__postfix"/>
            </li>`
          popper.appendChild(el)
          el.onclick = () => {
            // 底部按钮的点击事件 点击后想触发的逻辑也可以直接写在这
            onClick && onClick()
            // 以下代码实现点击后弹层隐藏 不需要可以删掉
            // if (ref.toggleDropDownVisible) {
            //   ref.toggleDropDownVisible(false);
            // } else {
            //   ref.visible = false;
            // }
          }
        }
      }
    },
    selectClick() {
      this.$router.push({
        name: 'project_view'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@use "@/styles/variables.scss" as *;
.icon {
  font-size: 20px
}
.menu {
  position: relative;
  display: flex;
  align-items: center;
  height: 48px;
  margin-left: 5px;
  .menu-title {
    max-width: 214px;
    padding: 0 8px;
    display: flex;
    align-items: center;
    flex: 1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    span {
      margin-right: 4px;
    }
    .iconfont {
      margin-left: 6px;
      color: var(--font-second-color);
    }
    .text-over {
      min-width: 56rem;
    }
  }
  .el-select {
    opacity: 0;
    position: absolute;
    top: 6px;
    left: 0;
    z-index: 99;
    cursor: pointer;
  }
  i {
    font-weight: normal;
    &.project-icon {
      font-size: 18px;
    }
  }
}
.solid {
  height: 16px;
  width: 1px;
  background: var(--solid-border-color);
}
</style>
