<template>
  <vone-echarts-card :title="'版本计划'" :height="'100%'">
    <div class="vone-version-step">
      <el-steps>
        <el-step v-for="(item, index) in stepData" :key="index" :title="item.name">
          <div slot="icon" style="width: 100%; height:100%">
            <div v-if="item.stateId == '1'" class="icons unstart">
              <span />
            </div>
            <div v-if="item.stateId == '2'" class="icons planning">
              <span />
            </div>
            <div v-if="item.stateId == '3'" class="icons success">
              <i class="iconfont el-icon-diedai-yiwancheng-light" />
            </div>
            <div v-if="item.stateId == '4'" class="icons error">
              <i class="iconfont el-icon-tips-exclamation-circle-fill" />
            </div>
          </div>
          <div slot="description" style="width:150px">{{ item.createTime.substring(0,11) }}</div>
        </el-step>
      </el-steps>
    </div>
  </vone-echarts-card>
</template>
<script>
import { getProductVersionList } from '@/api/vone/product/index'
export default {
  props: {
    formInfo: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      stepData: []
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    getData() {
      getProductVersionList({
        productId: this.formInfo.projectId
      }).then(res => {
        if (res.isSuccess) {
          this.stepData = res.data
        } else {
          this.$message.warning(res.msg)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.vone-version-step{
	margin-top: 60px;
  padding:0 40px;
  ::v-deep .el-step__icon.is-text {
    border: none;
  }
	::v-deep .el-step__description {
		position: absolute;
		top: -20px;
		width: 100%;
    color:#777F8E
	}
  ::v-deep .el-step__title {
    color: #202124;
  }
	.el-step {
		position: relative;
	}
	.today {
		position: absolute;
    text-align: center;
    top: 0px;
    left: 50%;
		a {
			display: inline-block;
			width: 15px;
			height: 15px;
			border: 2px solid;
			border-radius: 50%;
			margin-top: 10px;
			background: var(--main-bg-color,#fff);
		}
	}
  .icons {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  .unstart {
    background: #ADB0B8;
    text-align: center;
    line-height: 22px;
    span {
      display: inline-block;
      width: 9px;
      height:9px;
      border-radius: 50%;
      background: var(--main-bg-color,#fff);
    }
  }
  .planning {
     background: #ffbf47;
    text-align: center;
    line-height: 22px;
    span {
      display: inline-block;
      width: 9px;
      height:9px;
      border-radius: 50%;
      background: var(--main-bg-color,#fff);
    }
  }
  .success .iconfont{
    color: #3CB540;
    font-size: 24px;
  }
  .error .iconfont{
    color: #FA6B57;
    font-size: 24px;
  }
}
</style>
