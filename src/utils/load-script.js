const loadScript = async(url, id) => {
  return new Promise((resolve, reject) => {
    try {
      if (document.getElementById(id)) {
        // @ts-ignore
        if (window.DocsAPI) return resolve(null)

        const intervalHandler = setInterval(() => {
          // @ts-ignore
          if (!window.DocsAPI) return

          clearInterval(intervalHandler)

          return resolve(null)
        }, 500)
      } else {
        const script = document.createElement('script')
        script.setAttribute('type', 'text/javascript')
        script.setAttribute('id', id)

        script.onload = resolve
        script.onerror = reject

        script.src = url
        script.async = true
        document.body.appendChild(script)
      }
    } catch (e) {
      console.error(e)
    }
  })
}

export default loadScript

// 如何使用
// let url = this.url;
// if (!url!.endsWith("/")) url += "/";
// const docApiUrl = `${url}web-apps/apps/api/documents/api.js`;
// loadScript(docApiUrl, "onlyoffice-api-script")
// 	.then(() => this.onLoad())
// 	.catch((err) => console.error(err));
