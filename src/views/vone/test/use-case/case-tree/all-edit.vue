<!-- 新增用例卡片 -->
<template>
  <el-dialog title="批量编辑" :visible="visible" top="5vh" :before-close="onClose" :close-on-click-modal="false">
    <div class="addWrap">
      <el-form ref="caseFormRef" :model="caseForm" label-position="top">

        <el-form-item prop="tabInfo">
          <span slot="label">
            标签
            <el-tooltip style="diaplay:inline;margin-left:5px" effect="dark" content="选择标签会追加到原有标签" placement="top">
              <i class="iconfont el-icon-icon-line-shuoming" />
            </el-tooltip>

          </span>

          <tagSelect v-model="caseForm.tabInfo" :multiple-limit="3" />

        </el-form-item>
        <el-form-item label="优先级" prop="priority" class="label_line">
          <vone-icon-select v-model="caseForm.priority" :data="prioritList" placeholder="请选择优先级" filterable clearable class="select level-select">
            <el-option v-for="item in prioritList" :key="item.code" :label="item.name" :value="item.code">
              <div style="display:flex;align-items:center;">
                <i :class="`iconfont ${item.icon}`" :style="{color: item.color, fontSize: '16px',paddingRight:'6px'}" />
                <span>{{ item.name }}</span>
              </div>
            </el-option>
          </vone-icon-select>
        </el-form-item>
        <el-form-item label="所属分组">
          <el-popover
            ref="groupPopover"
            placement="top-start"
            title="选择分组"
            trigger="click"
            popper-class="groupPopper"
          >
            <div class="popoverMain">
              <el-tree
                ref="tree"
                class="filter-tree"
                :load="loadNode"
                lazy
                node-key="id"
                :highlight-current="true"
                :expand-on-click-node="false"
                @node-click="nodeClick"
              >
                <div slot-scope="{ node, data }" class="custom-tree-node">
                  <span :title="data.name">
                    <!-- 文件图标 -->
                    <i
                      v-if="node.level === 1"
                      class="iconfont el-icon-cangku"
                      style="color: var(--main-theme-color,#3e7bfa)"
                    />
                    <svg v-else class="icon" aria-hidden="true">
                      <use xlink:href="#el-application-filejia2" />
                    </svg>
                    <el-button style="padding:0;min-width:0;color:#202124" :disabled="data.parentId=='0'" type="text" class="treeNode">{{ data.name }}</el-button>
                  </span>
                </div>
              </el-tree>
            </div>
            <div slot="reference" class="referenceEmit" style="cursor: pointer">
              <svg v-if="newTreeName" class="icon" aria-hidden="true">
                <use xlink:href="#el-application-filejia2" />
              </svg>
              <span class="treeName" :title="newTreeName">{{ newTreeName }}</span>

            </div>
          </el-popover>

        </el-form-item>

        <el-form-item label="关联需求">
          <el-select
            v-model="caseForm.requirementId"

            placeholder="请输入需求名称"
            clearable
            filterable
            remote
            :remote-method="getRequirementList"
            :loading="requireLoading"
            class="requireSelect"
            @focus="setOptionWidth"
          >
            <el-option v-for="ele in requirementList" :key="ele.id" :value="ele.id" :label="ele.name" :style="{width:selectOptionWidth}">
              {{ `${ele.code}   ${ele.name}` }}
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="前置条件" prop="prerequisite">
          <el-input v-model="caseForm.prerequisite" placeholder="请输入前置条件" type="textarea" />
        </el-form-item>

        <el-form-item label="测试意图" prop="intent">
          <el-input v-model="caseForm.intent" placeholder="请输入意图" />
        </el-form-item>

      </el-form>

    </div>
    <div slot="footer">
      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="submit">确定</el-button>
    </div>
  </el-dialog>

</template>

<script>

import { updateAllParam, updateAllByLibraryId, getFiveRequirement, getTreeByLibrary } from '@/api/vone/testmanage/case'

import _, { debounce } from 'lodash'

import tagSelect from '@/views/vone/test/components/tag-select.vue'
// 重置表单属性
const defaultForm = () => {
  return {
    version: 'v1.0',
    treeId: '',
    name: '', // 用例名称
    caseKey: '', // 用例标识
    leadingBy: '', // 负责人
    requirementId: '',
    intent: '', // 测试意图
    prerequisite: '', // 前置条件
    stepType: 'text', // 步骤类型 text/subclause
    testStep: '', // 测试步骤
    expectedResult: '', // 预期结果
    negative: 'true', // 用例性质
    priority: null, // 用例级别
    // comment: '', // 备注
    issueKeys: [],
    tabInfo: []

  }
}
export default {
  components: { tagSelect },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 弹窗类型
    dialogType: {
      type: String,
      default: 'add'
    },
    // 选中的用例数据
    infoData: {
      type: Object,
      default: () => ({})
    },
    // 选中的用例id
    ids: {
      type: Array,
      default: () => ([])
    },
    edit: {
      type: String,
      default: ''
    },
    libraryId: {
      type: String,
      default: ''
    },
    // 选中节点数据
    nodeData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      selectOptionWidth: '',
      saveLoading: false,
      requireLoading: false,
      caseForm: defaultForm(),
      newTreeName: '',
      currentTree: {
        name: '',
        id: ''
      }, // 选中节点数据
      requirementList: [], // 需求列表
      // 优先级
      prioritList: [
        {
          name: '最高',
          code: 5,
          icon: 'el-icon-icon-dengji-zuigao2',
          color: '#FA6A69'
        }, {
          name: '较高',
          code: 4,
          icon: 'el-icon-icon-dengji-jiaogao2',
          color: '#FA8669'
        }, {
          name: '普通',
          code: 3,
          icon: 'el-icon-icon-dengji-putong2',
          color: 'var(--main-theme-color,#3e7bfa)'
        }, {
          name: '较低',
          code: 2,
          icon: 'el-icon-icon-dengji-jiaodi2',
          color: '#5ACC5E'
        }, {
          name: '最低',
          code: 1,
          icon: 'el-icon-icon-dengji-zuidi2',
          color: '#4ECF95'
        }
      ]
    }
  },
  mounted() {
    this.getRequirementList()
  },
  methods: {
    // 下拉框弹出时，设置弹框的宽度
    setOptionWidth(event) {
      this.$nextTick(() => {
        this.$refs.groupPopover.updatePopper()
        this.selectOptionWidth = event.target.parentNode.parentNode.clientWidth + 'px'
      })
    },
    async loadNode(node, resolve) {
      // 节点id
      const rootId = node.level == 0 ? 0 : node.data.id

      const res = await getTreeByLibrary(this.$route.params.caseId || this.libraryId, rootId)
      resolve(res.data)
    },
    // 分组树点击
    nodeClick(val) {
      if (val.parentId == '0') return
      this.currentTree = val
      this.newTreeName = val.name
      // 关闭分组弹窗
      this.$refs['groupPopover'].doClose()
    },
    // 重置
    onReseat() {
      this.caseForm = defaultForm()
    },
    submit() {
      this.editCase()
    },
    // 查需求列表
    getRequirementList: debounce(async function(query) {
      try {
        const params = {}
        if (this.$route.params.id) {
          params.projectId = [this.$route.params.id]
        } else {
          params.productId = this.$route.params.productInfoIds.split(',')
        }
        this.requireLoading = true
        const res = await getFiveRequirement({ ...params, name: query })
        this.requireLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.requirementList = res.data
      } catch (e) {
        this.requireLoading = false
      }
    }, 1000),

    // 编辑用例
    async editCase() {
      try {
        await this.$refs.caseFormRef.validate()
        this.saveLoading = true
        const obj = _.pick(this.caseForm, ['tabInfo', 'prerequisite', 'priority', 'intent', 'requirementId'])
        let params

        if (!this.edit) { //  下拉操作批量编辑
          params = {
            ...obj,
            treeId: this.currentTree?.id,
            ids: this.ids
          }
        } else {
          params = {
            ...obj,
            treeId: this.currentTree?.id,
            libraryId: this.libraryId
          }
        }
        const res = this.edit ? await updateAllByLibraryId(params) : await updateAllParam(params)
        this.saveLoading = false
        if (res.isSuccess) {
          this.$message.success('修改成功')
          this.$emit('success')
          this.onClose()
        }
      } catch (e) {
        this.saveLoading = false
      }
    },
    onClose() {
      this.caseForm = defaultForm()

      this.$emit('update:visible', false)
    }
  }
}
</script>
<style lang='scss' scoped>
::v-deep .el-button.is-disabled{
  border: none;
}
@mixin ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.addWrap {

  // 新增用例样式
  ::v-deep form {
    overflow-y: overlay;
    overflow-x: hidden;

    .el-form-item--small {
      margin-bottom: 16px;
    }

  }
}
// 节点树样式
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  & > span {
    flex: 1 1 auto;
    display: flex;
    align-items: center;
  }

  .treeNode {
    margin-left: 6px;
    // flex: 1 1 auto;
    @include ellipsis;
  }
}

.popoverMain {
	min-height: 100px;
  max-height: 300px;
  overflow-y: auto;
}
.referenceEmit {
  display: flex;
  align-items: center;
	height:32px;
	border: 1px solid #C1C8D6;
	border-radius: 2px;

  .treeName {
    max-width: 95%;
    @include ellipsis;
  }
}
.footer {
  // position: relative;
  // padding-top: 16px;
  // height: 45px;
  // text-align: center;
}

::v-deep.level-select {
  .el-input__prefix {
    display: flex;
    align-items: center;
  }
}
.requireSelect {
  ::v-deep .el-input__inner {
    padding-right: 30px;
    @include ellipsis;
  }
}
// 暗色主题样式
.custom-theme-dark {
  .footer {
    border-color: #495266;
  }
}
</style>
<style lang="scss">
.custom-theme-dark {

  ::-webkit-scrollbar-corner,
  ::-webkit-resizer {
    color: #e6e9f0;
    background-color: #252933;
  }
}
.groupPopper{
	width: 44%;
}

</style>
