<template>
  <div class="editor-aomao-placeholder">
    <el-alert
      title="EditorAomao 组件暂时不可用"
      type="warning"
      description="该组件在Vue3升级过程中遇到兼容性问题，已暂时禁用。请使用其他编辑器组件。"
      show-icon
      :closable="false"
    />
    <p class="mt-20">
      <strong>建议：</strong>可以使用 @wangeditor/editor-for-vue 作为替代方案。
    </p>
  </div>
</template>

<script>
export default {
  name: 'EditorAomao'
}
</script>

<style scoped>
.editor-aomao-placeholder {
  padding: 20px;
  border: 1px dashed #ddd;
  border-radius: 4px;
  min-height: 200px;
}
.mt-20 {
  margin-top: 20px;
}
</style>
