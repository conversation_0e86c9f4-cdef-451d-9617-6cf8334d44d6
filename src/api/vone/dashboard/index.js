import request from '@/utils/axios-api'

// 根据用户查询工作台
export function searchMyDashboard(data) {
  return request({
    url: '/api/tm-base/base/dashboard/myDashboard',
    method: 'get'
  })
}

// 根据工作台id查询工作台
export function searchDashboard(id) {
  return request({
    url: `/api/tm-base/base/dashboard/all/${id}`,
    method: 'get'
  })
}

// 操作工作台
export function operationDashboard(data, method) {
  return request({
    url: `/api/tm-base/base/dashboard`,
    method: method,
    data
  })
}

// 查询默认工作台
export function searchDefaultDashboard() {
  return request({
    url: `/api/tm-base/base/dashboard/findByTemplate`,
    method: 'get'
  })
}

// 将模板新增为工作台信息
export function setTemplate(data) {
  return request({
    url: `/api/tm-base/base/dashboard/saveTemplate`,
    method: 'post',
    data
  })
}

// 查询项目基本信息
export function searchProjectBasic(projectId) {
  return request({
    url: `/api/alm/alm/project/findDetailByProjectId/` + projectId,
    method: 'get'
  })
}
// 根据模板生成工作台信息
export function setTemplateWork(id, data) {
  return request({
    url: `/api/tm-base/base/dashboard/template/` + id,
    method: 'post',
    data
  })
}

// 获取仪表盘列表
export function getFilterList(data) {
  return request({
    url: `/api/base/base/tableView/table`,
    method: 'get',
    data
  })
}

// 查询自定义饼图数据
export function getChartData(data) {
  return request({
    url: `/api/alm/alm/issueItem/statistics`,
    method: 'post',
    data
  })
}

// 设置默认模板
export function setDashboardDefault(id, data) {
  return request({
    url: `/api/tm-base/base/dashboard/default/` + id,
    method: 'PUT',
    data
  })
}

// 查询任务统计组件
export function getTaskCount(data) {
  return request({
    url: `/api/insight/insight/taskComponent/findCountByQuery`,
    method: 'post',
    data
  })
}

// 任务统计查询详情
export function getTaskDetail(data) {
  return request({
    url: `/api/alm/alm/task/findPageCountByQuery`,
    method: 'post',
    data
  })
}
// 工时统计
export function getHourCount(data) {
  return request({
    url: `/api/insight/insight/taskComponent/findCountHourByQuery`,
    method: 'post',
    data
  })
}
// 预估工时下钻
export function getHourDetail(data) {
  return request({
    url: `/api/alm/alm/task/findHourPageByQuery`,
    method: 'post',
    data
  })
}

// 登记工时下钻
export function getCheckHourDetail(data) {
  return request({
    url: `/api/alm/alm/workingHoursInfo/findCountHourPageByQuery`,
    method: 'post',
    data
  })
}

// 查询缺陷统计
export function getBugCount(data) {
  return request({
    url: `/api/insight/insight/bugComponent/findBugCountComponentByQuery`,
    method: 'post',
    data
  })
}

// 缺陷统计下钻
export function getBugDetail(data) {
  return request({
    url: `/api/insight/insight/bugComponent/findBugCountComponentByQuery/page`,
    method: 'post',
    data
  })
}

// 缺陷饼图
export function getBugChart(data) {
  return request({
    url: `/api/insight/insight/bugComponent/findBugTypeComponentByQuery`,
    method: 'post',
    data
  })
}

// 缺陷饼图下钻
export function getBugPaiDetail(data) {
  return request({
    url: `/api/insight/insight/bugComponent/findBugTypeComponentByQuery/page`,
    method: 'post',
    data
  })
}

// 缺陷趋势图
export function getBugTrendChart(data) {
  return request({
    url: `/api/insight/insight/bugComponent/findBugTrendComponentByQuery`,
    method: 'post',
    data
  })
}

// 趋势下钻
export function getBugTrendDetail(data) {
  return request({
    url: `/api/insight/insight/bugComponent/findBugTrendComponentByQuery/page`,
    method: 'post',
    data
  })
}

// 需求统计
export function getDemandCount(data) {
  return request({
    url: `/api/insight/insight/requirementComponent/findRequirementCountComponentByQuery`,
    method: 'post',
    data
  })
}

// 需求统计下钻
export function getDemandDetail(data) {
  return request({
    url: `/api/insight/insight/requirementComponent/findRequirementCountComponentByQuery/page`,
    method: 'post',
    data
  })
}
// 需求饼图
export function getDeamndChart(data) {
  return request({
    url: `/api/insight/insight/requirementComponent/findRequirementPriorityComponentByQuery`,
    method: 'post',
    data
  })
}

// 需求饼图下钻
export function getDemandPaiDetail(data) {
  return request({
    url: `/api/insight/insight/requirementComponent/findRequirementPriorityComponentByQuery/page`,
    method: 'post',
    data
  })
}
// 需求趋势图
export function getDemandTrendChart(data) {
  return request({
    url: `/api/insight/insight/requirementComponent/findRequirementTrendComponentByQuery`,
    method: 'post',
    data
  })
}

// 需求趋势下钻
export function getDemandTrendDetail(data) {
  return request({
    url: `/api/insight/insight/requirementComponent/findRequirementTrendComponentByQuery/page`,
    method: 'post',
    data
  })
}

// 流水线构建部署统计
export function getPipCount(data) {
  return request({
    url: `/api/insight/insight/pipelineComponent/queryPipelineCount`,
    method: 'post',
    data
  })
}

// 流水线下钻

export function getPipDetail(data) {
  return request({
    url: `/api/pipeline/pipeline/pipelineBuildHistory/queryPipelineCountPage`,
    method: 'post',
    data
  })
}

// 完成工时
export function getCompleteHour(data) {
  return request({
    url: `/api/alm/alm/workingHoursInfo/findComponentCountHourPageByQuery`,
    method: 'post',
    data
  })
}

// 任务下钻
export function getTaskData(data) {
  return request({
    url: `/api/insight/insight/taskComponent/findTaskByQuery/page`,
    method: 'post',
    data
  })
}
