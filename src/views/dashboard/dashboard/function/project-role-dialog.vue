<template>
  <el-dialog
    :visible="visible"
    :before-close="close"
    title="编辑部件"
    :close-on-click-modal="false"
    width="600px"
   
  >
    <el-form ref="form" :model="form" :rules="formRule">
      <el-form-item prop="name" label="部件名称">
        <el-input ref="name" v-model.trim="form.name" placeholder="请输入" />
      </el-form-item>
      <el-form-item prop="projectId" label="项目">
        <el-select v-model="form.projectId" filterable @change="changeproject">
          <el-option v-for="(item,index) in projectIdList" :key="index" :label="item.name" :value="item.id">{{ item.name }}</el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="roleId" label="角色">
        <el-select v-model="form.roleId" filterable>
          <el-option v-for="(item,index) in roleList" :key="index" :label="item.name" :value="item.id">{{ item.name }}</el-option>
        </el-select>
      </el-form-item>

      <el-form-item prop="viewType" label="展示方式">
        <el-select v-model="form.viewType" placeholder="请选择" filterable>
          <el-option v-for="(item,index) in typeList" :key="index" :value="item.key" :label="item.name" />
        </el-select>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="submit">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getFilterList } from '@/api/vone/dashboard'
import { getProjectRole } from '@/api/vone/project/setting'
import {
  apiAlmProjectNoPage
} from '@/api/vone/project/index'
import _ from 'lodash'
export default {
  props: {
    isEditForm: Boolean,
    visible: Boolean,
    formInfo: {
      type: Object,
      default: () => {}
    },
    layout: {
      type: [Object, Array],
      default: null
    }
  },
  data() {
    return {
      form: {
        y: 'count(*)',
        name: this.formInfo.title,
        viewType: 'bar'
      },
      roleList: [],
      formRule: {
        name: [{ required: true, message: '请输入名称' }],
        projectId: [{ required: true, message: '请选择项目' }],
        viewType: [{ required: true, message: '请选择展示方式' }],
        roleId: [{ required: true, message: '请选择角色' }]
      },
      filterList: [],
      projectIdList: [],
      typeList: [
        {
          key: 'bar',
          name: '柱状图'
        },
        {
          key: 'pie',
          name: '饼图'
        }
      ],
      saveLoading: false
    }
  },
  mounted() {
    if (this.isEditForm) {
      this.form = this.formInfo
      this.getRole(this.formInfo.projectId)
    }
    this.getList()
    this.getProjectList()
  },
  methods: {
    close() {
      this.$emit('update:visible', false)
      this.$refs.form.resetFields()
    },
    getList() {
      getFilterList({
        table: 'work_item_entity'
      }).then(res => {
        if (res.isSuccess) {
          this.filterList = res.data
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    // 项目
    async getProjectList() {
      const res = await apiAlmProjectNoPage()

      if (!res.isSuccess) {
        console.warn(res.msg)
        return
      }
      this.projectIdList = res.data
    },
    changeproject(val) {
      this.getRole(val)
    },
    async getRole(val) {
      const res = await getProjectRole(val)

      if (!res.isSuccess) {
        console.warn(res.msg)
        return
      }
      this.roleList = res.data
    },

    async submit() {
      try {
        await this.$refs.form.validate()
      } catch (e) {
        return
      }
      const layouts = _.cloneDeep(this.layout)
      this.$set(this.form, 'roleName', this.roleList.find(item => item.id === this.form.roleId).name)
      if (this.isEditForm) {
        layouts.map(item => {
          if (item.id == this.form.id && item.widgetCode == this.form.key) {
            item.queryParams = JSON.stringify({ ...this.form })
          }
        })
      } else {
        layouts.push({
          x: (this.layout.length * 2) % 12,
          y: this.layout.length + 12, // puts it at the bottom
          w: 3,
          h: 3,
          i: this.layout.length + 1,
          queryParams: JSON.stringify({ ...this.formInfo, ...this.form }),
          widgetCode: this.formInfo.key
        })
      }
      this.$emit('update:layout', layouts)
      this.$emit('update:visible', false)
      this.$emit('success')
    }
  }
}
</script>
