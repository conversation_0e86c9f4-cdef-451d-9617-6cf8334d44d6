<template>
  <vone-tree-select
    v-model="groupId"
    search-nested
    :tree-data="groupTreeData"
    :disabled="disabled"
    :placeholder="placeholder"
    :limit="1"
    :limit-text="(count => `+${count}`)"
    :multiple="multiple"
    @input="change"
  />
</template>

<script>
import { teamList } from '@/api/vone/base/team'
import { gainTreeList, treeToList } from '@/utils'

export default {
  props: {
    value: {
      type: [String, Array],
      default: ''
    },
    placeholder: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: false
    },
    filterValue: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      groupId: '',
      groupTreeData: []
    }
  },
  watch: {
    value: {
      handler(val) {
        if (!val) {
          this.groupId = null
          return
        }
        this.groupId = this.value
      },
      immediate: true
      // deep: true
    }
  },
  created() {
    this.groupId = this.value
    teamList().then(res => {
      console.log(this.filterValue, 'this.filterValue')
      // if (this.filterValue && this.filterValue.length) {
      //   const list = res.data.filter(
      //     (i, j) => this.filterValue.indexOf(i.code) == -1
      //   )
      //   console.log(list, 'list')
      //   this.groupTreeData = gainTreeList(list)
      // } else {
      this.groupTreeData = gainTreeList(res.data)
      // }

      if (res.isSuccess) {
        const list = treeToList(res.data) || []
        if (list.findIndex(item => item.id == this.value) == -1) {
          this.groupId = null
        }
      } else {
        this.$message.warning(res.msg)
      }
    })
  },
  methods: {
    change() {
      this.$emit('input', this.groupId)
      this.$emit('change', this.groupId)
    }
  }
}
</script>
