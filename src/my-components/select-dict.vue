<template>
  <!-- 下拉框-字典 -->
  <el-select v-model="id" v-bind="$attrs" filterable clearable :placeholder="placeholder" style="width:100%" @change="change">
    <el-option v-for="(item, index) in list" :key="index" :label="item[labelKey]" :value="item[valueKey]" />
  </el-select>
</template>

<script>
import { apiBaseDictNoPage } from '@/api/vone/base/dict'

export default {
  props: {
    value: {
      type: [String, Array],
      default: ''
    },
    placeholder: {
      type: String,
      default: ''
    },
    /**
     * 字典key
     */
    type: {
      type: String,
      default: ''
    },
    labelKey: {
      type: String,
      default: 'name'
    },
    valueKey: {
      type: String,
      default: 'code'
    },
    filterValue: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // 选中的id
      id: '',
      list: []
    }
  },
  watch: {
    value(newVal) {
      this.id = newVal
    }
  },
  created() {
    this.id = this.value
    apiBaseDictNoPage({ type: this.type }).then(res => {
      if (this.type == 'ENGINE_INSTANCE') {
        res.data = res.data.filter(r => r.code === 'GIT_LAB')
      }
      if (this.filterValue && this.filterValue.length) {
        const hJson = res.data.filter(
          (i, j) => this.filterValue.indexOf(i.code) !== -1
        )
        this.list = hJson.filter(r => r.state)
      } else {
        this.list = res.data.filter(r => r.state)
      }
    })
  },
  methods: {
    change() {
      this.$emit('input', this.id)
    }
  }
}
</script>

<style>
</style>
