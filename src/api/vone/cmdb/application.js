// import request from '@/utils/request'
import request from '@/utils/axios-api'

// 获取应用组件列表数据
/**
 * @desc  分页查询应用组件列表
 * @param {string} appComponentsTypeKey 应用组件类型key
 * @param {string} applicationId 服务应用id
 * @param {string} businessSystemId 业务系统id
 * @param {string} hostId 服务器id
 * @param {string} name 应用组件名称
 * @param {string} orgId 机构id
 * @param {string} serverIp 服务器ip
 * @param {string} serverPort 服务器端口号
 */
export function apiBaseGetApplyList(data) {
  return request({
    url: '/api/cmdb/cmdb/appComponents/page',
    method: 'post',
    data
  })
}

/**
 * 应用组件---删除
 */
export function apiBaseDeleteAppCompById(data) {
  return request({
    url: `/api/cmdb/cmdb/appComponents`,
    method: 'DELETE',
    data
  })
}

/**
 * 应用组件-------更改参数文件
 */
export async function apiBasesendEnvConfig(data) {
  return request({
    url: `/api/cmdb/appComponentsEnvSend/send`,
    method: 'POST',
    data
  })
}

/**
 * 应用组件--权限分配--保存
 */
export async function apiBaseApplicationCompentGrant(data) {
  return request({
    url: `/api/cmdb/cmdb/appComponentsOrg`,
    method: 'put',
    data
  })
}

/**
 * 应用组件--权限分配--获取选中数据
 */
export async function apiBaseSelectAppCompOrgById(appComponentsId) {
  return request({
    url: `/api/cmdb/cmdb/appComponentsOrg/${appComponentsId}`,
    method: 'get'
  })
}

/**
 * 应用组件----新增---应用组件类型下拉框change事件获取高级属性配置
 * @desc 查询组件扩展属性
 */
export async function apiBaseGetAdvanced(params) {
  return request({
    url: `/api/cmdb/cmdb/appComponentsExtendProperties/` + params,
    method: 'get'
  })
}

/**
 * 查询应用组件详情
 */
export async function apiBaseDataFullInfoById(id) {
  return request({
    url: `/api/cmdb/cmdb/appComponents/${id}`,
    method: 'get'
  })
}

/**
 * 修改应用组件
 * @param {*} data
 */
export async function apiCmdbUpdateCmAppCompents(data) {
  return request({
    url: `/api/cmdb/cmdb/appComponents`,
    method: 'put',
    data
  })
}
/**
 * 应用组件----新增应用组件
 */
export async function apiBaseCmAppCompentsd(data) {
  return request({
    url: `/api/cmdb/cmdb/appComponents`,
    method: data.id ? 'put' : 'post',
    data
  })
}

/**
 * 根据机构ID查询业务应用的工程配置数据
 * @param {*} params
 */
export function apiCmdbSelectModuleBySystemId(applicationId) {
  return request({
    url: `/api/cmdb/cmdb/applicationModule/findByApplicationId/${applicationId}`,
    method: 'get'
  })
}

/**
 * 应用组件----新增---获取可用的服务器
 * @param {string} type 组件类型 appComponents/dbComponents
 */
export async function apiBaseGetselectByAppOrDb(type) {
  return request({
    url: `/api/cmdb/cmdb/host/findByComponentsType/` + type,
    method: 'get'
  })
}

/**
 * 根据服务应用ID查询应用组件
 * @param {*} params
 */
export function apiCmdbFindApplication(applicationId) {
  return request({
    url: `/api/cmdb/cmdb/appComponents/findAvailableForApplication/${applicationId}`,
    method: 'get'
  })
}

/**
 * 应用组件-参数文件
 */
export async function apiCmdbSendFiles(data) {
  return request({
    url: `/api/cmdb/appComponentsEnvSend/page`,
    method: 'POST',
    data
  })
}

