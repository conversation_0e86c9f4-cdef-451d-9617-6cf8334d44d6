import request from '@/utils/axios-api'
import requestUtils from '@/utils/requestUtils'
/**
 * @desc 查询用例
 * @param {string} libraryId 用例库id
 * @param {string} treeId 用例树层级id
 */
export function getCaseList(data) {
  return request({
    url: '/api/testm/testCase/findTestCaseOfCondition',
    method: 'post',
    data
  })
}
export function getAllConnectByLibraryId(id) {
  return request({
    url: `/api/testm/testProductCase/getAllConnectByLibraryId/${id}`,
    method: 'post'
  })
}
/**
 * @desc 新建用例
 * @param {string} expectedResult 期待结果
 * @param {string} intent 测试意图
 * @param {string} caseKey 用例标识
 * @param {string} leadingBy 负责人id
 * @param {string} libraryId 用例库id
 * @param {string} name 名称
 * @param {string} prerequisite 前提条件
 * @param {string} priority 用例等级
 * @param {string} productId 产品id
 * @param {string} requirementId 需求id
 * @param {string} stateId 评审状态
 * @param {string} stepType 测试步骤 文本/条目
 * @param {string} testStep 测试步骤
 * @param {strign} treeId 用例树id
 */
export function createCase(data) {
  return request({
    url: '/api/testm/testCase',
    method: 'post',
    data
  })
}
/**
 * @description 批量导入用例
 * @param {array} cases 多个用例
 * @param {string} treeId 左侧用例树分组id
 */
export function importCases2Tree(data, treeId) {
  return request({
    url: '/api/testm/testCase/updateCaseBath/' + treeId,
    method: 'put',
    data
  })
}
/**
 * @desc 删除用例
 * @param {string} ids 用例id列表
 */
export function deleteCase(data) {
  return request({
    url: '/api/testm/testCase',
    method: 'delete',
    data
  })
}
/**
 * @desc 修改用例信息
 * @param {string} expectedResult 期待结果
 * @param {string} intent 测试意图
 * @param {string} caseKey 用例标识
 * @param {string} leadingBy 负责人id
 * @param {string} libraryId 用例库id
 * @param {string} name 名称
 * @param {string} prerequisite 前提条件
 * @param {string} priority 用例等级
 * @param {string} productId 产品id
 * @param {string} requirementId 需求id
 * @param {string} stateId 评审状态
 * @param {string} stepType 测试步骤 文本/条目
 * @param {string} testStep 测试步骤
 * @param {strign} treeId 用例树id
 */
export function editCaseInfo(data) {
  return request({
    url: '/api/testm/testCase',
    method: 'put',
    data
  })
}
/**
 * @desc 查询当前项目下的测试计划
 * @param {string} projectId 项目id
 */
export function getProjectPlans(data) {
  return request({
    url: '/api/testm/testPlan/query',
    method: 'post',
    data
  })
}
/**
 * @desc 新增测试计划
 * @param {boolean} delay 是否延期
 * @param {string} description 描述
 * @param {string} planKey 计划英文标识
 * @param {string} leadingBy 计划负责人
 * @param {string} name 名称
 * @param {string} planEtime 计划结束时间
 * @param {string} planStime 计划开始时间
 * @param {string} projectId 项目id
 * @param {string} rateProgress 计划执行进度
 * @param {string} stateId 计划状态
 * @param {string} type 计划类型
 * @param {string} planId 迭代计划id
 * @param {string} productId 产品id
 * @param {string} envId 环境id
 */
export function createTestPlan(data) {
  return request({
    url: '/api/testm/testPlan',
    method: 'post',
    data
  })
}
/**
 * @desc 修改测试计划
 * @param {string} description 描述
 * @param {string} leadingBy 计划负责人
 * @param {string} name 名称
 * @param {string} stateId 计划状态
 * @param {string} planStime 计划开始时间
 * @param {string} planEtime 计划结束时间
 * @param {string} rateProgress 计划完成程度
 * @param {string} id 计划id（不可修改）
 * @param {string} delay 计划是否延期（不可修改）
 * @param {string} type 计划类型
 * @param {string} planKey 计划标识（不可修改）
 * @param {string} planId 迭代计划id
 * @param {string} productId 产品id
 * @param {string} envId 环境id
 */
export function editTestPlan(data) {
  return request({
    url: '/api/testm/testPlan',
    method: 'put',
    data
  })
}
/**
 * @desc 删除测试计划
 * @param {string}
 */
export function deleteTestPlan(data) {
  return request({
    url: '/api/testm/testPlan',
    method: 'delete',
    data
  })
}
/**
 * @desc 查询测试计划下的测试用例列表
 * @param {string} planId 计划id
 */
export function getPlanCases(data) {
  return request({
    url: '/api/testm/testPlanCase/queryTestPlanCase',
    method: 'post',
    data
  })
}
/**
 * @desc 执行测试计划下用例
 * @param {string} createdBy 创建人
 * @param {string} execBy 执行人
 * @param {string} execTime 执行时间
 * @param {string} planId 计划id
 * @param {string} result 详细结果
 * @param {string} status 执行结果状态
 * @param {string} testcaseId 用例id
 * @param {string} updatedBy 更新人
 */
export function excutePlanCase(data) {
  return request({
    url: '/api/testm/testPlanCaseResult',
    method: 'post',
    data
  })
}
export function saveTestPlanCaseExec(data) {
  return request({
    url: '/api/testm/testPlanCaseResult/saveTestPlanCaseExec',
    method: 'post',
    data
  })
}
/**
 * @desc 测试计划关联用例
 * @param {string} createdBy 创建人
 * @param {string} planId 计划id
 * @param {array} testcaseIds 测试用例id列表
 */
export function relatePlanCases(data) {
  return request({
    url: '/api/testm/testPlanCase',
    method: 'post',
    data
  })
}
/**
 * @desc 删除测试计划下用例
 * @param {array} testCaseIds 用例id列表
 * @param {string} planId 测试计划id
 */
export function deletePlanCases(data) {
  return request({
    url: '/api/testm/testPlanCase/deleteTestPlanCase',
    method: 'delete',
    data
  })
}
/**
 * @desc 查询测试报告列表
 * @param {}
 */
export function getCaseReports(data) {
  return request({
    url: '/api/testm/testReport/query',
    method: 'post',
    data
  })
}
/**
 * @desc 生成测试报告
 * @param {string} createdBy 创建人
 * @param {string} planId 计划id
 * @param {string} testReportName 名称
 */
export function createReport(data) {
  return request({
    url: '/api/testm/testReport',
    method: 'post',
    data
  })
}
/**
 * @desc 删除测试报告
 * @param {string} ids 测试报告id
 */
export function deleteReport(data) {
  return request({
    url: '/api/testm/testReport',
    method: 'delete',
    data
  })
}
/**
 * @desc 查询测试计划当前状态
 * @param {string} planId 计划id
 */
export function getTestPlanStatus(data) {
  return request({
    url: '/api/testm/testReport/queryTestPlanDetail',
    method: 'get',
    data
  })
}
/**
 * @desc 查询用例执行状态列表
 * @param {string} planId 计划id
 * @param {string} testCaseId 用例id
 */
export function getTestCaseStateList(data) {
  return request({
    url: '/api/testm/testPlanCaseResult/queryCaseResult',
    method: 'get',
    data
  })
}
/**
 * @desc 查询用例库用例数据
 * @param {array} ids 用例库id数组
 */
export function getLibraryAllProductCase(data) {
  return request({
    url: '/api/testm/productCaseLibrary/getLibraryAllProductCase',
    method: 'put',
    data
  })
}
/**
 * @desc 查询计划下所有用例执行结果
 * @param {string} planId 计划id
 */
export function getPlanAllCaseStateList(data) {
  return request({
    url: '/api/testm/testPlanCaseResult/query',
    method: 'post',
    data
  })
}
export function pageCase(data) {
  return request({
    url: '/api/testm/testPlanCase/pageCase',
    method: 'post',
    data
  })
}
/**
 * @description 下载excel导入模板
 * @param {string} id 项目id
 * @param {object} params
 */
export function downloadRequireImportTemplate(id, params) {
  return request({
    url: `/api/testm/testProductCase/excel/downloadImportTemplate/${id}`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json; charset=UTF-8'
    },
    responseType: 'blob', // 在请求中加上这一行，特别重要
    params
  })
}

/**
 * @description 导入excel文件
 * @param {string} id 项目id
 * @param {object} data 导入的文件
 */
export function apiRequireImport(id, data) {
  return request({
    url: `/api/testm/testCase/excel/import/${id}`,
    method: 'post',
    data: requestUtils.fileFormData(data)
  })
}
/**
 * @description 导入excel文件 新版
 * @param {string} libraryId 用例库id
 * @param {string} treeId 树id
 */
export function importExcelFile(libraryId, data) {
  return request({
    url: `/api/testm/testCase/excel/import/${libraryId}`,
    method: 'post',
    data: requestUtils.fileFormData(data)
  })
}
/**
 * @description 项目下测试首次导入xmind文件
 * @param {object} data 文件数据
 * @param {string} libraryId 用例库id
 * @param {string} treeId 树id
 */
export function importXmindFile(libraryId, treeId, data) {
  return request({
    url: `/api/testm/testCase/xmind/import/${libraryId}/${treeId}`,
    method: 'post',
    data: requestUtils.fileFormData(data)
  })
}
/**
 * @description 测试管理下导入xmind文件
 * @param {object} data 文件数据
 * @param {string} libraryId 用例库id
 * @param {string} treeId 树id
 */
export function importXmindFile2Test(libraryId, treeId, data) {
  return request({
    url: `/api/testm/testProductCase/xmind/import/${libraryId}/${treeId}`,
    method: 'post',
    data: requestUtils.fileFormData(data)
  })
}
/**
 * @description 测试管理用例库导入excel文件
 * @param {object} data 文件数据
 * @param {string} libraryId 用例库id
 * @param {string} treeId 树id
 */
export function importExcelFile2Test(libraryId, data) {
  return request({
    url: `/api/testm/testProductCase/excel/import/${libraryId}`,
    method: 'post',
    timeout: '0',
    data: requestUtils.fileFormData(data)
  })
}
// 批量执行测试计划下得用例
export function batchExecutionCase(planId, status, data) {
  return request({
    url: `/api/testm/testPlanCaseResult/batchExecutionCase/${planId}/${status}`,
    method: 'post',
    data
  })
}

// 测试管理,用例库,分页查询

export function apiTestUseCasePage(data) {
  return request({
    url: `/api/testm/productCaseLibrary/page`,
    method: `post`,
    data
  })
}

// 测试管理,用例库,修改，新增，删除
export function operationUseCase(data, method) {
  return request({
    url: `/api/testm/productCaseLibrary`,
    method: method,
    data
  })
}

// 查询用例详情
export function getUseCaseDetail(id) {
  return request({
    url: `/api/testm/productCaseLibrary/${id}`,
    method: `get`
  })
}

// 查询未归划测试用例 -分页

export function finddelCaseByIdPage(data) {
  return request({
    url: `/api/testm/testProductCase/page`,
    method: `post`,
    data
  })
}

// 查询未归划测试用例 -不分页
export function finddelCaseById(libraryId) {
  return request({
    url: `/api/testm/testProductCase/finddelCaseById/${libraryId}`,
    method: 'put'
  })
}

/**
 * @description 根据产品id查询产品用例树分组
 * @param {string} productId
 */
export function getProdCaseTreeByProdId(productId) {
  return request({
    url: `/api/testm/productCaseTree/findProductCaseTreeByProductId/${productId}`,
    method: 'get'
  })
}
// 产品用例树新增
export function productCaseTreeAdd(data) {
  return request({
    url: '/api/testm/productCaseTree',
    method: 'post',
    data
  })
}

// 产品用例树修改
export function productCaseTreeEdit(data) {
  return request({
    url: '/api/testm/productCaseTree',
    method: 'PUT',
    data
  })
}

// 产品用例树删除并删除树下用例
export function productCaseTreeDelAll(treeId) {
  return request({
    url: `/api/testm/productCaseTree/deleteAllById/${treeId}`,
    method: 'PUT',
    timeout: '0'
  })
}

// 产品用例树删除用例树
export function productCaseTreeDelTreeNode(treeId) {
  return request({
    url: `/api/testm/productCaseTree/deleteTreeById/${treeId}`,
    method: 'PUT'
  })
}

// 产品用例树下用例新增
export function testProductTreeCaseAdd(data) {
  return request({
    url: '/api/testm/testProductCase',
    method: 'post',
    data
  })
}

// 产品用例树下用例修改
export function testProductTreeCasePut(data) {
  return request({
    url: '/api/testm/testProductCase',
    method: 'PUT',
    data
  })
}

// 产品用例树下用例删除
export function testProductTreeCaseDel(data) {
  return request({
    url: '/api/testm/testProductCase',
    method: 'DELETE',
    data
  })
}

// 产品用例库，用例树拖拽
export function productTestCaseTreeSwap(data) {
  return request({
    url: `/api/testm/productCaseTree/updateById`,
    method: 'put',
    data
  })
}

// 产品用例库，用例树下用例复制
export function productTestCaseCopy(data) {
  return request({
    url: `/api/testm/testProductCase/copyTreeById`,
    method: 'put',
    data,
    timeout: '0'
  })
}

// 产品用例库权限
export function productCaseLibraryIdAuth(libraryId) {
  return request({
    url: `/api/testm/testProductCaseLibraryOrg/${libraryId}`,
    method: 'GET'
  })
}

//  产品用例库权限修改
export function productCaseLibraryOrgPut(data) {
  return request({
    url: `/api/testm/testProductCaseLibraryOrg`,
    method: 'put',
    data
  })
}
/**
 * @description 根据标签新建产品用例库
 * @param {string} tabId 标签id
 * @param {string} parentId 父用例库id
 * @param {*} 用例库数据
 */
export function createCaseLibraryByTag(data) {
  return request({
    url: '/api/testm/productCaseLibrary/saveCopyProductCaseLibrary',
    method: 'post',
    data
  })
}
//  产品用例详情
export function productCaseDetail(id) {
  return request({
    url: `/api/testm/testProductCase/${id}`,
    method: 'get'

  })
}
//  产品用例详情
export function findCaseById(id) {
  return request({
    url: `/api/testm/testProductCase/findCaseById/${id}`,
    method: 'get'
  })
}

/**
 * @description 拖拽用例添加到分组树
 * @param {string} treeId 树节点id
 * @param {array} ids 测试用例数组
 */
export function updateCases2Tree(treeId, data) {
  return request({
    url: `/api/testm/testProductCase/updateTreeIdTestProductCase/${treeId}`,
    method: 'post',
    data
  })
}

// 测试计划详情-用例修改优先级
export function updatePriority(caseId, priority, planId) {
  return request({
    url: `/api/testm/testProductCase/updatePriority?caseId=${caseId}&priority=${priority}&planId=${planId}`,
    method: `get`
    // params
  })
}
// 测试计划详情-用例修改优先级
export function updatePrioritys(data) {
  return request({
    url: `/api/testm/testPlanCase/updatePriority`,
    method: `post`,
    data
  })
}
// 测试用例批量修改
export function updateAllParam(data) {
  return request({
    url: `/api/testm/testProductCase/updateAllParam`,
    method: 'post',
    data
  })
}
// 查询回收站内用例
export function getTrashCase(data) {
  return request({
    url: `/api/testm/testProductCase/getTrashCase`,
    method: 'post',
    data
  })
}
// 用例移入回收站
export function trashCase(data) {
  return request({
    url: `/api/testm/testProductCase/trashCase`,
    method: 'post',
    data
  })
}
// 回收站还原
export function callBack(data) {
  return request({
    url: `/api/testm//testProductCase/callBack`,
    method: 'post',
    data
  })
}
// 批量修改当前用力库下所有用例的
export function updateAllByLibraryId(data) {
  return request({
    url: `/api/testm/testProductCase/updateAllByLibraryId`,
    method: 'post',
    data
  })
}
// 批量修改当前用力库下所有用例的
export function getFiveRequirement(data) {
  return request({
    url: `/api/alm/alm/requirement/getFiveRequirement`,
    method: 'post',
    data
  })
}
/**
 * @description 查询用例分组树下用例
 * @param {string} libraryId 用例库id
 * @param {string} treeId 树节点id
 */
export function getTreeByLibrary(libraryId, treeId) {
  return request({
    url: `/api/testm/productCaseTree/getTreeByLibrary/${libraryId}/${treeId}`,
    method: 'get'
  })
}
// 获取描述
export function getDecription(treeId) {
  return request({
    url: `/api/testm/productCaseTree/getDecription/${treeId}`,
    method: 'get'
  })
}
// 用例归档
export function caseArchive(data) {
  return request({
    url: '/api/testm/testProductCaseArchive/caseArchive',
    method: 'post',
    data
  })
}
// 用例归档
export function getArchiveByLibraryId(data) {
  return request({
    url: `/api/testm/testProductCaseArchive/getArchiveByTreeId`,
    method: 'post',
    data
  })
}
/**
 * @description 查询归档用例分组树
 * @param {string} id 用例库id
 * @param {string} treeId 树节点id
 */
export function getFileTreeByLibrary(id, treeId) {
  return request({
    url: `/api/testm/testProductCaseArchive/getTreeByLibrary/${id}/${treeId}`,
    method: 'get'
  })
}
// 用例归档还原
export function recallArchive(data) {
  return request({
    url: '/api/testm/testProductCaseArchive/recallArchive',
    method: 'post',
    data
  })
}
// 用例归档详情
export function getArchiveDetail(id) {
  return request({
    url: `/api/testm/testProductCaseArchive/getOne/${id}`,
    method: 'get'
  })
}
// 用例草稿箱
export function getdraft(data) {
  return request({
    url: `/api/testm/testProductCase/getDraftByLibraryId`,
    method: 'post',
    data
  })
}
// 项目用例库列表
export function getAllLibraryByProjectId(projectId) {
  return request({
    url: `/api/testm/productCaseLibrary/getAllLibraryByProjectId/${projectId}`,
    method: 'get'
  })
}
// 项目用例复制
export function copyCaseToProject(data) {
  return request({
    url: `/api/testm/testProductCase/copyCaseToProject`,
    method: 'post',
    data
  })
}
// 根据用例库id 查记录
export function getArchiveRecord(data) {
  return request({
    url: `/api/testm/testProductCaseArchive/getArchiveRecord`,
    method: 'post',
    data
  })
}
// 根据记录查用例
export function getArchiveCase(libraryId, updateTime, updateBy) {
  return request({
    url: `/api/testm/testProductCaseArchive/getArchiveCase/${libraryId}/${updateTime}/${updateBy}`,
    method: 'post'

  })
}
// 撤销归档
export function recallArchiveRecord(libraryId, updateTime, updateBy) {
  return request({
    url: `/api/testm/testProductCaseArchive/recallArchiveRecord/${libraryId}/${updateTime}/${updateBy}`,
    method: 'post',
    timeout: '0'
  })
}
/**
 * @desc 查询当前树节点下所有子节点及用例id
 * @param {string} id 树层级id
 */
export function getAllChild(id) {
  return request({
    url: `/api/testm/productCaseTree/getAllChild/${id}`,
    method: 'get'
  })
}
/**
 * @desc 查询用例树所有层级用例数量
 * @param {string} libraryId 用例库id
 */
export function queryAllCaseNumberOfTree(libraryId) {
  return request({
    url: `/api/testm/productCaseTree/queryAllCaseNumberOfTree/${libraryId}`,
    timeout: '0',
    method: 'get'
  })
}
export function queryAllCaseNumberOfDraftTree(libraryId) {
  return request({
    url: `/api/testm/productCaseTree/queryAllCaseNumberOfDraftTree/${libraryId}`,
    method: 'get'
  })
}

// 根据记录查用例
export function getTreeAndCase(data) {
  return request({
    url: `/api/testm/testProductCase/getTreeAndCase`,
    method: 'post',
    data
  })
}
// 用例树筛选
export function productCaseTree(data) {
  return request({
    url: `/api/testm/productCaseTree/queryCase`,
    method: 'post',
    data
  })
}

