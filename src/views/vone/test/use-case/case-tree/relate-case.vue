<template>
  <el-dialog
    v-if="visible"
    width="78%"
    top="10vh"
    title="批量迁移"
    :visible="visible"
    :before-close="onClose"
    height="70%"
    :close-on-click-modal="false"
    v-on="$listeners"
  >
    <el-row>
      <el-col v-loading="treeLoading" :span="8" class="elStyle l_body custom-tree">
        <vone-tree
          ref="tree"
          show-checkbox
          check-strictly
          :data="dataTree"
          height="500px"
          node-key="id"
          default-expand-all
          :expand-on-click-node="false"
          highlight-current
          :props="defaultProps"
          @node-click="treeCaseClick"
          @check="checkChange"
        >
          <span slot-scope="{ node, data }" class="custom-tree-node">
            <svg class="icon iconCls" aria-hidden="true">
              <use xlink:href="#el-application-filejia2" />
            </svg>

            <span class="treeNode">{{ data.name }}</span>
            <span v-if=" (node.checked || node.indeterminate) && data.num" class="num">{{ data.num }}</span>
          </span>
        </vone-tree>
      </el-col>

      <el-col :span="16" class="elStyle r_body">
        <vone-search-wrapper>
          <template slot="search">
            <vone-search-dynamic table-search-key="migrateCaseTable" :model.sync="formData" :table-ref="$refs['migrateCaseTable']" @getTableData="getTableList(currentNode)">
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="用例" prop="name">
                    <el-input v-model="formData.name" placeholder="请输入用例" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="编号" prop="caseKey">
                    <el-input v-model="formData.caseKey" placeholder="请输入编号" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="标签" prop="tabName">
                    <el-select
                      v-model="formData.tabName"
                      filterable
                      multiple
                      clearable
                      placeholder="请输入标签筛选用例"
                      remote
                      :remote-method="getTagsListByLibraryId"
                      :loading="requireLoading"
                      @focus="getTagsListByLibraryId"
                    >
                      <el-option v-for="(item) in tagsList" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="优先级" prop="priority ">
                    <vone-icon-select v-model="formData.priority" :data="prioritList" filterable clearable style="width:100%">
                      <el-option v-for="item in prioritList" :key="item.key" :label="item.name" :value="item.code">
                        <i
                          :class="`iconfont ${item.icon}`"
                          :style="{
                            color: item.color,
                            fontSize: '16px',
                            paddingRight: '6px',
                          }"
                        />
                        {{ item.name }}
                      </el-option>
                    </vone-icon-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </vone-search-dynamic>
          </template>

        </vone-search-wrapper>
        <main style="height:416px;">
          <vxe-table
            ref="migrateCaseTable"
            class="vone-vxe-table"
            height="auto"
            border
            resizable
            show-overflow="tooltip"
            :empty-render="{ name: 'empty' }"
            :data="tableData.records"
            :column-config="{ minWidth:'120px' }"
            :checkbox-config="{ reserve: true }"
            :loading="tableLoading"
            row-id="id"
            @checkbox-all="selectAllEvent"
            @checkbox-change="selectChangeEvent"
          >
            <vxe-column type="checkbox" width="36" fixed="left" align="center" />
            <vxe-column title="用例" field="name">
              <template #default="{ row }">
                <div style="text-align: left">
                  <i v-if="row.stepType === 'subclause'" class="iconfont el-icon-application-view-list" style="color: #37cdde;" />
                  <i v-else class="el-icon-document" style="color: #3e7bfa;" />
                  <span style="margin-left:4px;">{{ row.caseKey+ ' '+row.name }}</span>
                </div>
              </template>
            </vxe-column>
            <vxe-column title="优先级" field="priority">
              <template #default="{ row }">
                <span v-if="row.priority === 1">
                  <i class="iconfont el-icon-icon-dengji-zuidi2" style="color:#4ECF95;" />
                  最低
                </span>
                <span v-else-if="row.priority === 2">
                  <i class="iconfont el-icon-icon-dengji-jiaodi2" style="color:#5ACC5E;" />
                  较低
                </span>
                <span v-else-if="row.priority === 5">
                  <i class="iconfont el-icon-icon-dengji-zuigao2" style="color:#FA6A69;" />
                  最高
                </span>
                <span v-else-if="row.priority === 4">
                  <i class="iconfont el-icon-icon-dengji-jiaogao2" style="color:#FA8669;" />
                  较高
                </span>
                <span v-else>
                  <i class="iconfont el-icon-icon-dengji-putong2" style="color:var(--main-theme-color,#3e7bfa);" />
                  普通
                </span>
              </template>
            </vxe-column>
            <vxe-column title="维护人" field="leadingBy">
              <template #default="{ row }">
                <vone-user-avatar :avatar-path="getUserInfo(row)?getUserInfo(row).avatarPath :''" :name="getUserInfo(row)?getUserInfo(row).name :''" />
              </template>
            </vxe-column>
          </vxe-table>
        </main>

      </el-col>
    </el-row>
    <div slot="footer" class="dialog-footer">
      <div class="excuteUser">
        <el-form ref="taskForm" inline label-position="left" :rules="rules" :model="taskForm" style="width:100%;text-align:left">
          <el-form-item label="用例库" prop="libraryId">
            <el-select v-model="taskForm.libraryId" placeholder="请选择" filterable @change="getLibrary">
              <el-option v-for="item in productList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>

          <!-- <el-select v-model="taskForm.treeId" placeholder="请选择" filterable>
              <el-option v-for="item in treeList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select> -->
          <el-form-item label="所属分组" prop="treeId" class="poppeItem">
            <el-popover
              ref="groupPopover"

              class="groupPopper"

              placement="bottom"
              title="选择分组"
              trigger="click"
            >
              <div class="popoverMain">
                <el-tree
                  ref="tree"
                  class="filter-tree"
                  :load="loadNodes"
                  lazy
                  :data="tData"
                  node-key="id"
                  :check-strictly="true"
                  :highlight-current="true"
                  :expand-on-click-node="false"
                  @node-click="nodeClick"
                >
                  <div slot-scope="{ node, data }" class="custom-tree-node">
                    <span>
                      <!-- 文件图标 -->
                      <i
                        v-if="node.level === 1"
                        class="iconfont el-icon-cangku"
                        style="color: var(--main-theme-color,#3e7bfa)"
                      />
                      <svg v-else class="icon" aria-hidden="true">
                        <use xlink:href="#el-application-filejia2" />
                      </svg>
                      <el-button style="padding:0;min-width:0;color:#202124" type="text" class="treeNode">{{ data.name }}</el-button>
                    </span>
                  </div>
                </el-tree>
              </div>
              <div slot="reference" class="referenceEmit" style="cursor: pointer">
                <svg class="icon" aria-hidden="true">
                  <use xlink:href="#el-application-filejia2" />
                </svg>
                <span class="treeName">{{ newTreeName }}</span>

              </div>
            </el-popover>
          </el-form-item>

        </el-form>

      </div>
      <el-button @click="onClose">取消</el-button>

      <el-button :loading="saveLoading" type="primary" @click="saveInfo">确定</el-button>

    </div>
  </el-dialog>
</template>

<script>

import { finddelCaseByIdPage, getTreeByLibrary, getAllChild } from '@/api/vone/testmanage/case'
import { getAllLibraryInfo } from '@/api/vone/testmanage'
import { catchErr } from '@/utils'

import { debounce } from 'lodash'
import { getCaseLibraryTags } from '@/api/vone/testTab'
import { copyProductCaseToOtherLibrary, findProductCaseTreeOfRepository } from '@/api/vone/testplan'
import { list2Tree } from '@/utils/list2Tree'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    planData: {
      type: Object,
      default: () => {}
    },
    // 用例库id
    libraryId: {
      type: String,
      default: ''
    }

  },
  data() {
    return {
      rules: {
        libraryId: [
          { required: true, message: '请选择用例库', trigger: 'change' }
        ],
        treeId: [
          { required: true, message: '请选择分组', trigger: 'change' }
        ]
      },
      taskForm: {
        treeId: ''
      },
      treeLoading: false,
      // 优先级
      prioritList: [
        {
          name: '最高',
          code: 5,
          icon: 'el-icon-icon-dengji-zuigao2',
          color: '#FA6A69'
        }, {
          name: '较高',
          code: 4,
          icon: 'el-icon-icon-dengji-jiaogao2',
          color: '#FA8669'
        }, {
          name: '普通',
          code: 3,
          icon: 'el-icon-icon-dengji-putong2',
          color: 'var(--main-theme-color,#3e7bfa)'
        }, {
          name: '较低',
          code: 2,
          icon: 'el-icon-icon-dengji-jiaodi2',
          color: '#5ACC5E'
        }, {
          name: '最低',
          code: 1,
          icon: 'el-icon-icon-dengji-zuidi2',
          color: '#4ECF95'
        }
      ],
      formData: {
        name: '',
        caseKey: '',
        tabName: [],
        priority: ''
      },
      excuteUser: '', // 执行人
      tableOptions: {
        isOperation: false // 表格有操作列时设置
      },
      tableLoading: false,
      saveLoading: false,
      selectedId: [],
      dataTree: [],
      tableData: { records: [] }, // 表格数据
      currentNode: {},
      defaultProps: {
        label: 'name'
      },
      currentTree: {},
      newTreeName: '',
      productList: [],
      requireLoading: false,
      tagsList: [], // 用例库标签列表
      releaseCaseMap: {}, // 当前树节点已保存用例id
      allTreeCaseMap: {}, // 当前用例库下所有分组树节点和对应用例id数据
      treeList: [],
      tData: [],
      tableNotSelectData: [], // 未选中的数据
      allSelectId: [] // 所有选中的id
    }
  },
  computed: {
    getUserInfo() {
      return function(row) {
        return row?.leadingBy && row?.echoMap?.leadingBy
      }
    }
  },
  watch: {
    async visible(v) {
      if (!v) return
      this.selectedId = []
      this.gettreeData()
    }
  },
  mounted() {
    this.getAllProductList()
  },
  methods: {
    // 分组树点击
    nodeClick(val) {
      this.currentTree = val
      this.$set(this.taskForm, 'treeId', val.id)

      this.$refs.taskForm.fields[1].validateState = 'success'
      this.newTreeName = val.name
      // 关闭分组弹窗
      this.$refs['groupPopover'].doClose()
    },
    async getLibrary(val) {
      if (!val) return
      this.$set(this.taskForm, 'treeId', '')
      this.newTreeName = ''
      const res = await getTreeByLibrary(val, 0)

      this.tData = res.data
    },
    // 查询所有产品
    async getAllProductList() {
      const res = await getAllLibraryInfo()
      if (res.isSuccess) {
        this.productList = res.data
      }
    },
    getTagsListByLibraryId: debounce(function(query) {
      this.requireLoading = true
      getCaseLibraryTags({
        id: this.libraryId,
        name: query
      }).then(res => {
        this.tagsList = res.data
        this.requireLoading = false
      }).catch(() => {
        this.requireLoading = false
      })
    }, 1000),
    // 查询树节点数据
    async gettreeData() {
      this.treeLoading = true
      const [res, err] = await catchErr(findProductCaseTreeOfRepository(this.libraryId))
      if (err) return
      const tree = list2Tree(res.data, { parentKey: 'parentId' })
      this.dataTree = tree
      tree[0].disabled = true // 禁用根节点
      this.currentNode = tree[0] || {}
      this.getTableList(this.currentNode)
      this.$nextTick(() => {
        this.$refs.tree.setCurrentKey(this.currentNode.id)
      })

      this.getAllTreeCases(tree[0])
    },
    // 查询当前用例树所有层级用例数量
    async getAllTreeCases(node) {
      const [res, err] = await catchErr(getAllChild(node.id))
      this.treeLoading = false
      if (err) return
      if (res.isSuccess) {
        this.allTreeCaseMap = res.data.reduce((acc, cur) => (acc[cur.treeId] = cur) && acc, {})
      }
    },
    async loadNodes(node, resolve) {
      if (!this.taskForm.libraryId) return
      // 节点id
      const rootId = node.level == 0 ? 0 : node.data.id

      const res = await getTreeByLibrary(this.taskForm.libraryId, rootId)
      resolve(res.data)
    },
    treeCaseClick(data) {
      this.currentNode = data
      this.getTableList(data)
    },
    async checkChange(row, e) {
      this.currentNode = row
      const node = this.$refs.tree.getNode(row.id)
      const checked = node.checked
      // 当前节点关联用例数
      const caseIds = this.allTreeCaseMap[row.id]?.caseIds || []
      caseIds.map(id => this.updatedCaseIds(id, checked))
      // 设置当前节点选中用例数
      this.releaseCaseMap[row.id] = checked ? caseIds : null
      this.$set(row, 'num', checked ? caseIds.length : 0)
      this.$set(row, 'sum', caseIds.length)
      // 设置父节点选中
      this.setParentNode(node)
      // 设置子节点选中和用例数量
      this.setChildNodesCheck(node.childNodes, checked)
      // 查询当前节点下所有用例
      this.getTableList(node.data)

      this.$refs.tree.setCurrentKey(row.id)
    },
    // 递归设置子节点节点选中
    async setChildNodesCheck(childNodes, checked = false) {
      childNodes?.map(node => {
        node.checked = checked
        checked && (node.indeterminate = false)
        const caseIds = this.allTreeCaseMap[node.data.id]?.caseIds || []
        // 添加子节点选中用例
        caseIds.map(id => this.updatedCaseIds(id, checked))
        this.releaseCaseMap[node.data.id] = checked ? caseIds : null
        this.$set(node.data, 'num', checked ? caseIds.length : 0)
        this.$set(node.data, 'sum', caseIds.length)
        this.setChildNodesCheck(node.childNodes, checked)
      })
    },
    // 设置父节点选中状态
    async setParentNode(node) {
      let parent = node.parent
      while (parent && parent.level > 0) {
        const num = parent.data.num || 0
        if (node.checked) {
          const sum = parent.data.sum != undefined ? parent.data.sum : this.allTreeCaseMap[parent.data.id]?.caseIds?.length || 0
          // 当前节点所有用例是否已全部关联
          const allCaseSelected = num === sum
          // 子节点是否全选
          const allChildChecked = parent.childNodes.every(item => item.checked)
          parent.checked = allCaseSelected && allChildChecked
        } else {
          parent.checked = false
        }
        // 子节点存在选中状态
        const childHalfChecked = parent.childNodes.some(item => item.indeterminate || item.checked)
        parent.indeterminate = !parent.checked && (childHalfChecked || parent.data.num > 0)

        node = parent
        parent = parent.parent
      }
    },
    // 查询表格列表
    async getTableList(data, checked) {
      // 当前节点不存在时，不查询
      if (!this.currentNode.id) return

      const params = {
        current: 1,
        size: 9999,
        order: 'descending',
        sort: 'updateTime',
        extra: {},
        model: {
          state: false,
          draft: false, // 筛除草稿状态用例
          ...this.formData,
          libraryId: this.libraryId,
          treeId: data.id
        }
      }
      this.tableLoading = true
      const [res, err] = await catchErr(finddelCaseByIdPage(params))
      if (err) return
      this.tableLoading = false
      if (res.isSuccess) {
        this.tableData = res.data

        const tableList = res.data.records
        Array.isArray(tableList) && tableList.forEach((v) => {
          v.userData = v.echoMap.leadingBy
        })
        // 清除选中
        this.clearSelection()
        if (tableList.length === 0) return
        // 当前树节点已选择的用例，设置表格默认选中
        const caseIds = this.releaseCaseMap[data.id] || []
        const checkedDefaults = this.tableData.records.filter(v => caseIds.indexOf(v.id) > -1)

        this.$nextTick(() => {
          this.$refs.migrateCaseTable.setCheckboxRow(checkedDefaults, true)
        })
      }
    },
    onClose() {
      // 取消选择
      this.clearSelection()
      this.$emit('update:visible', false)
      this.tableData = { records: [] }
      this.currentNode = {}
      this.selectedId = []
      this.releaseCaseMap = {}
      this.currentTree = {}
      this.newTreeName = ''
      this.taskForm = {}
      // 清空选中节点
      this.$refs.tree.setCheckedKeys([])
    },
    // 保存选中用例
    async saveInfo() {
      if (this.selectedId.length === 0) {
        this.$message.warning('请选择用例添加')
        return
      }
      this.$refs.taskForm.validate((valid) => {
        if (!valid) return
        const treeIds = []
        for (const id in this.releaseCaseMap) {
          if (this.releaseCaseMap[id]) {
            treeIds.push(id)
          }
        }

        const treeMap = treeIds.map(id => this.allTreeCaseMap[id] || {})

        this.saveReleaseTreeAndCases({ caseIds: this.selectedId, treeMap })
      })
    },
    async saveReleaseTreeAndCases({ caseIds, treeMap }) {
      const params = {
        libraryId: this.taskForm.libraryId,
        treeId: this.currentTree.id,
        connectTreeId: [], // treeIds
        caseAndTreeReturnVo: treeMap,
        planId: this.planId,
        caseId: caseIds
      }
      this.saveLoading = true
      const [res, err] = await catchErr(copyProductCaseToOtherLibrary(params))
      this.saveLoading = false
      if (err) return
      if (res.isSuccess) {
        this.$message.success('迁移成功')
        this.onClose()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 更新选中的用例id
    updatedCaseIds(id, checked) {
      let index = this.selectedId.indexOf(id)
      if (checked) {
        index === -1 && this.selectedId.push(id)
      } else {
        while (index > -1) {
          this.selectedId.splice(index, 1)
          index = this.selectedId.indexOf(id)
        }
      }
    },
    // 取消选择
    clearSelection() {
      this.$refs.migrateCaseTable.clearCheckboxRow()
    },

    selectAllEvent({ checked }) {
      const selection = this.$refs.projectReleaseCaseTable.getCheckboxRecords()
      const isSelect = selection.length > 0
      this.tableData.records.map(row => this.updatedCaseIds(row.id, isSelect))

      this.updateNodeChecked(selection)
    },
    selectChangeEvent({ row }) {
      const selection = this.$refs.projectReleaseCaseTable.getCheckboxRecords()
      const isAdd = selection.indexOf(row) > -1
      this.updatedCaseIds(row.id, isAdd)

      this.updateNodeChecked(selection)
    },
    // 修改当前树节点选中状态
    async updateNodeChecked(selection) {
      const isSelect = selection.length > 0
      const selectAll = this.tableData.records.length === selection.length // 用例全选
      const node = this.$refs.tree.getNode(this.currentNode.id)
      const caseIds = selection.map(item => item.id)
      this.$set(node.data, 'num', selection.length)
      this.releaseCaseMap[this.currentNode.id] = caseIds

      // 根据当前节点下子节点选中状态
      let allChildChecked = true
      let someChildChecked = false
      if (node.childNodes.length > 0) {
        allChildChecked = node.childNodes.every(item => item.checked) // 子节点是否全选
        someChildChecked = node.childNodes.some(item => item.indeterminate || item.checked) // 子节点存在选中状态
      }

      node.checked = isSelect && selectAll && allChildChecked
      node.indeterminate = !node.checked && (someChildChecked || isSelect)

      // 设置父节点选中状态
      this.setParentNode(node)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep {
  .el-dialog__body {
    padding: 0;
  }
  .el-table::before {
    width: 0;
  }

  .pagination {
    margin: 0;
  }
}
.popoverMain {
  min-width: 300px;
  min-height: 100px;
  max-height: 300px;
  overflow-y: auto;
}

.elStyle {
  height: 516px;
  padding: 16px 0 0 16px;
  overflow: auto;
  ::v-deep.el-tree-node > .el-tree-node__children {
    min-width: min-content;
  }
}

.r_body {
  padding: 16px;
  border-left: 1px solid var(--disabled-bg-color, #ebeef5);
}
.custom-tree {
  overflow-y: overlay;
  padding-right: 10px;
  ::v-deep .el-tree > .el-tree-node {
    display: grid;
    min-width: 100%;
    overflow-x: auto;
  }
  ::v-deep.el-tree-node > .el-tree-node__children {
    display: inline;
    overflow: unset;
  }
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  & > span {
    flex: 1 1 auto;
    display: flex;
    align-items: center;
  }
  .iconCls {
    margin: 0 3px;
  }

  .treeNode {
    flex: 1 1 auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .num {
    padding: 0px 8px;
    flex: 0 0 auto;
    margin-left:4px;
    min-width: 24px;
    background-color: rgb(212, 216, 222);
    border-radius: 8px;
    text-align: center;
		color: rgb(255, 255, 255);
		height: 16px;
    line-height: 16px;
	}
}

.excuteUser {
	min-width: 800px;
  float: left;
  gap: 0 8px;
  display: flex;
  align-items: center;
  color: var(--sub-font-color);
  .label {
    width: 60px;
  }
}
.poppeItem{
	::v-deep .el-form-item__content{
		min-width: 100px;
	}
}
</style>
