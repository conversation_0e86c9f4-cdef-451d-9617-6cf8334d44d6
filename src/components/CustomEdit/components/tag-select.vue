<template>
  <el-select
    id="querySelect"
    ref="searchSelect"
    filterable
    remote
    allow-create
    collapse-tags
    :clearable="clearable"
    :multiple="multiple"
    :placeholder="placeholder"
    :remote-method="remoteMethod"
    loading-text="正在查询..."
    no-match-text="暂未查询到匹配数据,请重新输入"
    :loading="loading"
    v-bind="$attrs"
    popper-class="remote-user"
   
    @change="filterSelect"
  >
    <!-- @input.native="filterData" -->
    <el-option
      v-for="item in selectData"
      :key="isId ? item.id : item.name"
      :label="item.name"
      :value="isId ? item.id : item.name"
    />

  </el-select>
</template>

<script>
import { debounce } from 'lodash'
import { findTagsByNameWhileWriting } from '@/api/vone/alm/index'
export default {
  name: 'RemoteUser',
  props: {
    placeholder: {
      type: String,
      default: '输入标签名称查询'
    },
    multiple: {
      type: Boolean,
      default: false
    },
    isId: { // 判断传参是name还是id
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      selectData: [],
      loading: false,
      selectIconVal: null
    }
  },

  mounted() {
    this.getList()
  },
  methods: {
    filterSelect(val) {
      // this.$emit('filterSelect', val)
    },
    getList() {
      findTagsByNameWhileWriting({ name: '' }).then(res => {
        this.selectData = res.data
        this.loading = false
      })
    },
    // debounce函数去抖
    remoteMethod: debounce(function(query) {
      this.loading = true
      if (query != '') {
        findTagsByNameWhileWriting({ name: query }).then(res => {
          this.selectData = res.data
          this.loading = false
        })
      } else {
        this.getList()
      }
    }, 1000)

  }
}
</script>
<style lang="scss" scoped>
	.remote-user {
		padding-right: 10px;
	}

</style>
