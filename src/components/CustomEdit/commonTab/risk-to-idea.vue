<template>
  <div>
    <RiskCommon :placeholder="'请输入用户需求搜索'" :classify="'IDEA'" :tab-name="tabName" :issue-id="issueId" @toRouter="toRouter" />
  </div>
</template>

<script>
import RiskCommon from './risk-common.vue'
export default {
  name: 'RiskToIssue',
  components: {
    RiskCommon
  },
  props: {
    tabName: {
      type: String,
      default: null
    },
    issueId: {
      type: String,
      default: null
    }
  },
  data() {
    return {

    }
  },
  methods: {
    toRouter(val) {
      if (!this.$permission('reqm_center_idea_view')) {
        this.$message.warning('当前登录账户没有查看用户需求的权限,请联系管理员授权')
        return
      }

      const newpage = this.$router.resolve({
        path: `/reqmcenter/idea`, query: {
          showDialog: true,
          queryId: val?.echoMap?.bizId?.id,
          rowTypeCode: val?.echoMap?.bizId?.typeCode,
          stateCode: val?.echoMap?.bizId?.stateCode,
          projectId: val?.echoMap?.bizId?.projectId
        }
      })
      window.open(newpage.href, '_blank')
    }

  }

}
</script>

<style>

</style>
