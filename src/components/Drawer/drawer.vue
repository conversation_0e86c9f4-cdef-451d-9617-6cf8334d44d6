<template>
  <el-drawer
    :class="drawerClz"
    v-bind="{ ...$attrs, size: width }"
    :custom-class="modalClz"
    :style="style"
    :wrapper-closable="false"
    class="drawer"
   
  >
    <template v-if="$slots.title" slot="title">
      <slot name="title" />
    </template>
    <div v-loading="loading" class="vone-el-drawer__layout">
      <slot />
    </div>
    <div v-if="hasFooter" class="vone-drawer__footer">
      <slot name="footer" />
    </div>
  </el-drawer>
</template>

<script>
import classnames from 'classnames'

const DRAWER_WIDTH = {
  lg: '60%',
  md: '40%',
  sm: '30%'
}

export default {
  props: {
    size: {
      type: String,
      default: undefined
    },
    loading: Boolean
  },
  computed: {
    width() {
      return DRAWER_WIDTH[this.size] || DRAWER_WIDTH.md
    },
    hasFooter() {
      return !!this.$slots.footer
    },
    modalClz() {
      return classnames({
        'vone-drawer__has-footer': this.hasFooter
      })
    },
    drawerClz() {
      const { modal } = this.$attrs
      return classnames('vone-drawer', {
        'vone-drawer__no-modal': modal === false
      })
    },
    style() {
      const { withHeader } = this.$attrs
      return {
        width: withHeader == false ? this.width : undefined
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer {
  :deep() {
    .el-loading-parent--relative {
      position: inherit !important;
    }

    .vone-drawer__has-footer {
      padding-bottom: 57px;
      .vone-drawer__footer {
        position: absolute;
        width: 100%;
        left: 0;
        bottom: 0;
        border-top: 1px solid var(--disabled-bg-color, #ebeef5);
        padding: 12px 20px;
        text-align: left;
        z-index: 999;
      }
    }
    .vone-el-drawer__layout {
      height: 100%;
      overflow-x: hidden;
      overflow-y: auto;

    }
    .el-drawer__header{
      font-weight: 600;
      border-color: var(--disabled-bg-color);
    }
  }
  .vone-drawer__no-modal {
    top: 48px;
    pointer-events: none;
    :deep() {
      .el-drawer {
        pointer-events: all;
      }
    }
  }
}
</style>
<style lang="scss" scoped>

    .basicForm {
      .el-tabs__item {
				color: var(--main-font-color);
				font-size: 14px;
			}
			.el-tabs__item.is-active {
				color: var(--main-theme-color,#3e7bfa);
				background: transparent
			}
			.custom-botton {
				background: transparent;
			}
    }

</style>
