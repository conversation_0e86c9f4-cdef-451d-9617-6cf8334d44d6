<template>
  <div>
    <el-form
      ref="infoForm"
      :model="infoForm"
      :disabled="infoDisabled"
      :rules="rules"
    >
      <el-form-item label="用户需求分类" prop="typeCode">
        <el-select
          v-model="infoForm.typeCode"
          clearable
          style="width: 100%"
          :disabled="id ? true : false"
        >
          <el-option
            v-for="item in typeCodeList"
            :key="item.key"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="用户需求来源" prop="sourceCode">
        <el-select v-model="infoForm.sourceCode" clearable style="width: 100%">
          <el-option
            v-for="item in sourceList"
            :key="item.key"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="优先级" prop="priorityCode">
        <vone-icon-select
          v-model:value="infoForm.priorityCode"
          :data="prioritList"
          filterable
          clearable
          style="width: 100%"
        >
          <el-option
            v-for="item in prioritList"
            :key="item.key"
            :label="item.name"
            :value="item.code"
          >
            <i
              :class="`iconfont ${item.icon}`"
              :style="{
                color: item.color,
                fontSize: '16px',
                paddingRight: '6px',
              }"
            />
            {{ item.name }}
          </el-option>
        </vone-icon-select>
      </el-form-item>
      <el-form-item v-if="id" label="状态" prop="stateCode">
        <issueStatus
          v-if="id"
          :workitem="infoForm"
          :info-disabled="infoDisabled"
          @changeFlow="changeFlow"
        />
      </el-form-item>
      <el-form-item label="归属产品" prop="productId">
        <el-select
          v-model="infoForm.productId"
          clearable
          filterable
          style="width: 100%"
          popper-class="ideaSelect"
        >
          <el-option
            v-for="item in productIdList"
            :key="item.key"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="提出人">
        <vone-remote-user v-model:value="infoForm.putBy" />
      </el-form-item>
      <el-form-item label="处理人" prop="handleBy">
        <vone-remote-user v-model:value="infoForm.handleBy" />
      </el-form-item>
      <el-form-item label="负责人" prop="leadingBy">
        <vone-remote-user v-model:value="infoForm.leadingBy" />
      </el-form-item>
      <el-form-item label="期望完成时间" prop="expectedTime">
        <el-date-picker
          v-model="infoForm.expectedTime"
          type="datetime"
          format="yyyy-MM-dd HH:mm"
          value-format="yyyy-MM-dd HH:mm:ss"
          default-time="18:00:00"
          placeholder="选择计划开始时间"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import {
  $on,
  $off,
  $once,
  $emit,
} from '../../../../../../utils/gogocodeTransfer'
import { apiAlmProjectPlanNoPage } from '@/api/vone/project/iteration'
import { apiAlmSourceNoPage } from '@/api/vone/project/issue'
import {
  apiAlmProjectNoPage,
  productListByCondition,
} from '@/api/vone/project/index'

import { apiAlmPriorityNoPage } from '@/api/vone/alm/index'

import { apiAlmStateNoPage } from '@/api/vone/base/work-flow'
import issueStatus from '@/views/vone/project/common/change-status/index.vue'

import { getAlmGetTypeNoPage } from '@/api/vone/alm/index'

export default {
  components: {
    issueStatus,
  },
  props: {
    id: {
      type: String,
      default: undefined,
    },
    infoDisabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      rules: {
        typeCode: [
          { required: true, message: '请输入用户需求类型', trigger: 'change' },
        ],
        sourceCode: [
          { required: true, message: '请输入用户需求来源', trigger: 'change' },
        ],
        priorityCode: [
          { required: true, message: '请输入优先级', trigger: 'change' },
        ],
      },
      infoForm: {},
      sourceList: [], // 用户需求来源
      projectIdList: [], // 归属项目
      productIdList: [], // 归属产品

      stateList: [], // 状态
      prioritList: [], // 优先级
      typeCodeList: [], // 用户需求分类
      planIdList: [], // 迭代计划
      documentsList: [],
    }
  },
  mounted() {
    this.getProjectInfo()
  },
  methods: {
    getProjectInfo() {
      this.getIssueType() // 用户需求分类
      this.getProjectList() // 归属项目
      this.productList() // 归属产品

      this.getStateList() // 状态
      this.getPlanList() // 迭代计划
      this.getPrioritList() // 优先级
      this.getSourceList() // 来源
    },

    changeFlow() {
      $emit(this, 'changeFlow')
    },
    // 查询用户需求分类
    async getIssueType() {
      const res = await getAlmGetTypeNoPage('IDEA')
      if (!res.isSuccess) {
        return
      }
      this.typeCodeList = res.data
      if (!this.infoForm.typeCode) {
        this.infoForm['typeCode'] = res.data[0].code
      }
    },
    // 查询用户需求来源
    async getSourceList() {
      const res = await apiAlmSourceNoPage({
        typeClassify: 'IDEA',
      })
      if (!res.isSuccess) {
        return
      }
      this.sourceList = res.data
      if (!this.infoForm.sourceCode) {
        this.infoForm['sourceCode'] = res.data[0]?.code
      }
    },
    // 归属项目
    async getProjectList() {
      const res = await apiAlmProjectNoPage()

      if (!res.isSuccess) {
        return
      }
      this.projectIdList = res.data
    },
    // 归属产品
    async productList() {
      const res = await productListByCondition()

      if (!res.isSuccess) {
        return
      }
      this.productIdList = res.data
    },
    // 迭代计划
    async getPlanList() {
      const res = await apiAlmProjectPlanNoPage({
        projectId: this.$route.params.id,
      })
      if (!res.isSuccess) {
        return
      }
      this.planIdList = res.data
    },
    // 查状态
    async getStateList() {
      const res = await apiAlmStateNoPage()
      if (!res.isSuccess) {
        return
      }
      this.stateList = res.data
    },
    // 查优先级
    async getPrioritList(val) {
      const res = await apiAlmPriorityNoPage()
      this.tableLoading = false
      if (!res.isSuccess) {
        return
      }
      this.prioritList = res.data
      if (!this.infoForm.priorityCode) {
        this.infoForm['priorityCode'] = res.data[0].code
      }
    },
    async proving() {
      this.$refs.infoForm.validate((valid) => {
        if (valid) {
          $emit(this, 'save')
        } else {
          return
        }
      })
    },
  },
  emits: ['changeFlow', 'save'],
}
</script>

<style lang="scss" scoped>
:deep(.el-date-editor.el-input),
.el-date-editor.el-input__inner {
  width: 100%;
}
</style>

<style>
.ideaSelect {
  max-width: 100px;
}
</style>
