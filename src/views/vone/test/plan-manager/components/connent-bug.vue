<template>
  <el-dialog
    class="dialogContainer"
    title="关联缺陷"
    :model-value="visible"
    width="40%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="close"
  >
    <!-- 表单部分 -->
    <el-form ref="appForm" :model="appForm">
      <el-form-item label="缺陷">
        <el-select
          v-model="appForm.bugIds"
          remote
          :remote-method="getBug"
          :loading="requireLoading"
          multiple
          filterable
          placeholder="请输入缺陷名称"
        >
          <el-option
            v-for="item in bugList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <!-- 表单结束，按钮操作部分 -->
    <template v-slot:footer>
      <span class="dialog-footer">
        <el-button size="small" @click="$emit('update:visible', false)"
          >取消</el-button
        >
        <el-button type="primary" size="small" @click="save">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import { debounce } from 'lodash'

import { queryFiveProduct } from '@/api/vone/product'
import { queryBugList, connectBugPlan } from '@/api/vone/project/defect'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    planData: {
      type: Object,
      default: () => {},
    },
    defectData: {
      type: Object,
      default: () => {},
    },
    caseData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      requireLoading: false,
      appForm: {
        bugIds: [],
      },
      bugList: [],
    }
  },
  mounted() {},
  methods: {
    // 查需求列表
    getProductList: debounce(async function (query) {
      try {
        this.requireLoading = true
        const res = await queryFiveProduct({ name: query })
        this.requireLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
      } catch (e) {
        this.requireLoading = false
      }
    }, 1000),

    getBug: debounce(async function (query) {
      try {
        this.requireLoading = true
        if (!query) return
        const res = await queryBugList({ name: query })
        this.requireLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }

        if (this.defectData.records) {
          const newArray = res.data.filter(
            (item) =>
              !this.defectData.records.some((item2) => item2.id == item.id)
          )
          this.bugList = newArray
        } else {
          this.bugList = res.data
        }
      } catch (e) {
        this.requireLoading = false
      }
    }, 1000),

    // 保存
    save() {
      connectBugPlan(
        this.caseData.id,
        this.planData.id,
        this.appForm.bugIds
      ).then((res) => {
        if (res.isSuccess) {
          this.$message.success(res.data)
          this.close()
          $emit(this, 'success')
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    close() {
      this.$refs.appForm.resetFields()
      $emit(this, 'update:visible', false)
    },
  },
  emits: ['update:visible', 'success'],
}
</script>
