import request from '@/utils/axios-api'

/**
 * @desc 查询所有产品集
 * @param {object}
 */
export function getList(data) {
  return request({
    url: `/api/productset/productSetInfo/page`,
    method: `post`,
    data
  })
}
// 批量查询列表
export function getqueryList(data) {
  return request({
    url: `/api/productset/productSetInfo/query`,
    method: `post`,
    data
  })
}
// 新增
export function addProductfit(data) {
  return request({
    url: `/api/productset/productSetInfo`,
    method: `post`,
    data
  })
}
// 编辑
export function editProductfit(data) {
  return request({
    url: `/api/productset/productSetInfo`,
    method: `put`,
    data
  })
}
// 删除
export function delProductfit(data) {
  return request({
    url: `/api/productset/productSetInfo`,
    method: `delete`,
    data
  })
}
// 详情
export function detailProductfit(id) {
  return request({
    url: `/api/productset/productSetInfo/getListByProductSetId/${id}`,
    method: 'post'
  })
}
// 单体查询
export function onlyOneProductfit(id) {
  return request({
    url: `/api/productset/productSetInfo/${id}`,
    method: 'get'
  })
}
// 根据产品集id查询产品信息
export function queryListByProductSetId(id) {
  return request({
    url: `/api/productset/productSetInfo/queryListByProductSetId/${id}`,
    method: 'post'
  })
}

// 版本信息
export function getversionList(data) {
  return request({
    url: `/api/productset/version/query`,
    method: `post`,
    data
  })
}

// 新增
export function addversionProductfit(data) {
  return request({
    url: `/api/productset/version`,
    method: `post`,
    data
  })
}
// 编辑
export function editversionProductfit(data) {
  return request({
    url: `/api/productset/version`,
    method: `put`,
    data
  })
}
// 删除
export function delversionProductfit(data) {
  return request({
    url: `/api/productset/version`,
    method: `delete`,
    data
  })
}
// 详情
export function detailversionProductfit(id) {
  return request({
    url: `/api/productset/version/${id}`,
    method: 'get'
  })
}

// 关联产品版本列表
export function productVersionProductsetList(data) {
  return request({
    url: `/api/productset/productVersionProductset/query`,
    method: `post`,
    data
  })
}
/**
 * @description 查询产品集版本关联产品列表
 * @param {*} data
 */
export function queryListByProductSetVerisonId(data) {
  return request({
    url: `/api/product/product/productVersion/queryListByProductSetVerisonId`,
    method: `post`,
    data
  })
}
// 根据产品集版本id查询产品信息（下拉框选择）
export function queryListByProductSetVersionId(id) {
  return request({
    url: `/api/productset/version/queryListByProductSetVersionId/${id}`,
    method: `post`
  })
}
// 关联版本
export function productVersionProductset(data) {
  return request({
    url: `/api/productset/productVersionProductset`,
    method: `post`,
    data
  })
}
// 取消关联版本
export function delproductVersionProductset(data) {
  return request({
    url: `/api/productset/productVersionProductset/delete`,
    method: `post`,
    data
  })
}
// 产品集id查分页产品
export function queryListByProductSet(data) {
  return request({
    url: `/api/product/product/productInfo/queryListByProductSetId`,
    method: `post`,
    data
  })
}
// 根据产品集id查询产品下拉框信息
export function selectListByProductSet(id) {
  return request({
    url: `/api/product/product/productInfo/queryListByProductSetId/${id}`,
    method: `post`
  })
}
// 根据产品集概览
export function queryoverviewByProductSetId(id) {
  return request({
    url: `/api/productset/productSetInfo/queryoverviewByProductSetId/${id}`,
    method: `post`
  })
}
// 根据产品集版本id修改状态
export function updateStateById(id, status) {
  return request({
    url: `/api/productset/version/updateStateById/${id}/${status}`,
    method: `post`
  })
}

/**
 * @desc 查询产品资料库
 * @param {object}
 */
export function getDocumentPage(data) {
  return request({
    url: `/api/productset/productsetDocument/page`,
    method: `post`,
    data
  })
}
/**
 * @desc 新增产品资料库
 * @param {object}
 */
export function getDocumentAdd(data) {
  return request({
    url: `/api/productset/productsetDocument`,
    method: `post`,
    data
  })
}
/**
 * @desc 编辑产品资料库
 * @param {object}
 */
export function productsetDocument(data) {
  return request({
    url: `/api/productset/productsetDocument`,
    method: `PUT`,
    data
  })
}

// 根据id查询
export function getDocumentById(id) {
  return request({
    url: `/api/productset/productsetDocument/${id}`,
    method: `GET`
  })
}

// 删除
export function productsetDocumentDel(data) {
  return request({
    url: `/api/productset/productsetDocument`,
    method: `delete`,
    data
  })
}

// 查询项目集可关联的项目
export function getAssociableProjectInfo(projectsetId) {
  return request({
    url: `/api/alm/projectProductset/getAssociableProjectInfo/${projectsetId}`,
    method: `GET`
  })
}

/**
 * @desc 批量关联
 * @param {object}
 */
export function associationBatch(productsetId, data) {
  return request({
    url: `/api/alm/projectProductset/association/${productsetId}`,
    method: `post`,
    data
  })
}
/**
 * @desc 批量取消关联
 * @param {object}
 */
export function deleteByProjectIdBatch(productsetId, data) {
  return request({
    url: `/api/alm/projectProductset/deleteByProjectId/${productsetId}`,
    method: `delete`,
    data
  })
}

