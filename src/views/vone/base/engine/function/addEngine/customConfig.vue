<template>
  <el-table :data="customDatas">
    <el-table-column prop="key" label="属性标识">
      <template slot-scope="scope">
        <el-input v-model="scope.row.key" placeholder="请输入不超过100个字符的属性标识" />
      </template>
    </el-table-column>
    <!-- <el-table-column prop="name" label="属性名称">
      <template slot-scope="scope">
        <el-input v-model="scope.row.name" placeholder="请输入不超过100个字符的属性名称" />
      </template>
    </el-table-column> -->
    <el-table-column prop="value" label="属性值">
      <template slot-scope="scope">
        <el-input v-model="scope.row.value" placeholder="请输入不超过100个字符的属性值" />
      </template>
    </el-table-column>
    <!-- <el-table-column prop="description" label="属性描述">
      <template slot-scope="scope">
        <el-input v-model="scope.row.description" placeholder="请输入不超过100个字符的属性描述" />
      </template>
    </el-table-column> -->
    <el-table-column label="操作" width="100">
      <template slot-scope="scope">
        <el-button type="primary" size="small" circle icon="el-icon-plus" @click="addRow(scope.$index)" />
        <el-button type="danger" size="small" circle icon="el-icon-minus" :disabled="!canRemove" @click="deleteRow(scope.$index)" />
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  props: {
    engineData: {
      type: Object,
      default: undefined
    }
  },
  data() {
    return {
      customDatas: [{
        key: '',
        value: ''
        // engineId:
      }]
    }
  },
  computed: {
    canRemove() {
      return this.customDatas.length > 1
    }
  },
  watch: {
    engineData() {
      this.customDatas = [{}]
      if (!this.engineData) return
      if (this.engineData.customAttributeDatas && this.engineData.customAttributeDatas.length) {
        this.customDatas = this.engineData.customAttributeDatas
      }
    }
  },
  methods: {
    // 点击+号图标添加新的一栏
    addRow() {
      this.customDatas.push({})
    },
    // 点击减号删除一行
    deleteRow(index) {
      this.customDatas.splice(index, 1)
    },
    async getFormData() {
      return this.customDatas
    }
  }
}
</script>
