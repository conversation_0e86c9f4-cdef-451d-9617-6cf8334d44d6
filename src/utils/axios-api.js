import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getToken, getTenant, getUser } from '@/utils/auth'
import { resetRouter } from '@/router'
import store from '@/store'
import router from '@/router'
import { removeToken } from '@/utils/auth'

// 全局axios请求列表，用于取消请求
let axiosList = []

// import { Base64 } from 'js-base64'
const CANCEL_TOKEN = axios.CancelToken
// 请求添加条件，如token
axios.interceptors.request.use(config => {
  config['cancelToken'] = new CANCEL_TOKEN(c => {
    // 将当前页面请求存入axiosList中
    axiosList.push(c)
  })
  const isToken = config.headers['X-isToken'] === false ? config.headers['X-isToken'] : true
  const token = getToken()
  if (token && isToken) {
    config.headers.token = 'Bearer ' + token
  }
  const isTenant = config.headers['X-isTenant'] === false ? config.headers['X-isTenant'] : true
  if (isTenant && import.meta.env.VITE_IS_TENANT == 'true') {
    config.headers.tenant = getTenant()
  }
  return config
},
error => {
  return Promise.reject(error)
}
)

// 接口返回处理
axios.interceptors.response.use(
  response => {
    return response
  },
  error => {
    return Promise.reject(error)
  }
)

function handleError(error, reject, opts) {
  // debugger
  if (error.code === 'ECONNABORTED') {
    Message({
      showClose: true,
      message: '请求超时'
    })
  } else if (error.response && error.response.data) {
    // const resData = error.response.data
    if (error.response.status === 403 || error.response.status === 401) {
      if (error.response.config.url == '/api/oauth/oauth/menu/router') {
        ElMessageBox.alert(error.response.data.msg || '登录已失效，请重新登录', '提醒', {
          confirmButtonText: '确定',
          callback: async() => {
            // await store.dispatch('user/logout')
            removeToken()
            resetRouter()
            router.push(`/login`)
          }
        })
      } else {
        router.push({
          name: '401'
        })
      }
    } else if (error.response.status === 500 || error.response.status == 503) {
      if (error.response.config.url == '/api/oauth/oauth/menu/router') {
        return
      }
      router.push({
        name: '500'
      })
    } else {
      const resData = error.response.data
      if (resData.code === 40000 || resData.code === 40001 ||
        resData.code === 40002 || resData.code === 40003 ||
        resData.code === 40005 || resData.code === 40006 ||
        resData.code === 40008 || resData.code === 40009
      ) {
        ElMessageBox.alert(resData.msg, '提醒', {
          confirmButtonText: '确定',
          callback: async() => {
            const userInfo = getUser()
            await store.dispatch('user/logout', userInfo.id)
          }
        })
      } else if (resData.msg) {
        ElMessage.warning({
          showClose: true,
          message: resData.msg
        })
      }
    }
  }
  reject(error)
}

function handleSuccess(res, resolve, opts) {
  const resData = res.data
  if (resData && resData.isSuccess === false) {
    // 未登录
    if (resData.code === 40000 || resData.code === 40001 ||
      resData.code === 40002 || resData.code === 40003 ||
      resData.code === 40005 || resData.code === 40006 ||
      resData.code === 40008
    ) {
      ElMessageBox.alert(resData.msg, '提醒', {
        confirmButtonText: '确定',
        callback: async() => {
          const userInfo = getUser()
          await store.dispatch('user/logout', userInfo.id)
        }
      })
    }
  }
  resolve(resData)
}

// http请求
const httpServer = (opts) => {
  // 公共参数
  // const publicParams = {
  //   ts: Date.now()
  // }
  // http默认配置
  const method = opts.method.toUpperCase()
  const httpDefaultOpts = {
    method,
    // baseURL: '' + import.meta.env.VITE_BASE_API,
    url: opts.url,
    responseType: opts.responseType || '',
    // transformResponse: [function(data) { // 转换long
    //   return JSON.parse(data)
    // }],
    timeout: opts.timeout ? opts.timeout : 600000000
  }
  if (opts['meta']) {
    httpDefaultOpts.headers = opts['meta']
  }
  // 按需加上时间戳，已注释
  const dataRequest = ['PUT', 'POST', 'DELETE', 'PATCH']
  if (dataRequest.includes(method)) {
    httpDefaultOpts.data = opts.data || {}
    // httpDefaultOpts.params = publicParams
  } else {
    httpDefaultOpts.params = {
      // ...publicParams,
      ...(opts.data || {})
    }
  }

  // formData转换
  if (opts.formData) {
    httpDefaultOpts.transformRequest = [data => {
      const formData = new FormData()
      if (data) {
        Object.entries(data).forEach(item => {
          formData.append(item[0], item[1])
        })
      }
      return formData
    }]
  }
  // 原生下载进度条展示
  if (opts.onDownloadProgress) {
    httpDefaultOpts.onDownloadProgress = opts.onDownloadProgress
  }

  const promise = new Promise((resolve, reject) => {
    axios(httpDefaultOpts).then(response => {
      handleSuccess(response, resolve, opts)
    }).catch(error => {
      handleError(error, reject, opts)
    })
  })
  return promise
}

// 导出axios列表管理函数
export const getAxiosList = () => axiosList
export const clearAxiosList = () => { axiosList = [] }
export const cancelAllRequests = (reason = '页面切换，取消请求') => {
  axiosList.forEach(cancel => cancel(reason))
  axiosList = []
}

export default httpServer
