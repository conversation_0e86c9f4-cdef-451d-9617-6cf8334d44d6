<template>
  <div ref="echarts" :key="echartKey" class="chart" :style="{ width:width,height:height }" />
</template>
<script>
import { merge } from 'lodash'
import * as echarts from 'echarts'
import ResizeListener from 'element-resize-detector'
export default {
  name: 'VoneECharts',
  props: {
    options: {
      type: Object,
      default: () => ({})
    },
    width: {
      type: String,
      default: ''
    },
    height: {
      type: String,
      default: ''
    },
    echartKey: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      chart: null,
      instance: null
    }
  },
  watch: {
    'options': {
      deep: true,
      handler() {
        this.updateChartView()
      }
    }
  },
  mounted() {
    const that = this
    this.chart = echarts.init(this.$refs.echarts)
    this.updateChartView()
    window.addEventListener('resize', this.handleWindowResize)
    this.addChartResizeListener()
    if (!this.height) {
      this.$el.style.height = this.$parent.$el.offsetHeight - 58 + 'px'
    }
    // echart图的点击事件
    this.chart.on('click', function(params) {
      that.$emit('chartClick', params)
    })
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleWindowResize)
    this.instance.uninstall(this.$el)
    if (!this.chart) {
      return
    }
    /* 释放图表实例 */
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    assembleDataToOption() {
      return merge(
        {},
        this.options
      )
    },
    /**
     * 对chart元素尺寸进行监听，当发生变化时同步更新echart视图
     */
    addChartResizeListener() {
      this.instance = ResizeListener({
        strategy: 'scroll',
        callOnAdd: true
      })
      this.instance.listenTo(this.$el, () => {
        if (!this.chart) return
        this.chart.resize()
      })
    },
    /**
     * 更新echart视图
     */
    updateChartView() {
      if (!this.chart) return
      // this.chart.clear()
      const fullOption = this.assembleDataToOption()
      this.chart.setOption(fullOption, true)
    },
    /**
     * 重新生成实例
     *  */
    refreshChart() {
      if (!this.chart) return
      this.chart.clear()
      this.chart.setOption(this.options, true)
    },
    /**
     * 当窗口缩放时，echart动态调整自身大小
     */
    handleWindowResize() {
      if (!this.chart) return
      this.chart.resize()
    }
  }
}
</script>
<style lang="scss" scoped>
.chart {
  width: 100%;
  height: 100%;
}
</style>
