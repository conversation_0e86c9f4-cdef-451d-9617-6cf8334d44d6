<template>
  <div class="sectionPageContent">
    <div class="leftSection">
      <div class="header">
        <span>
          <el-select
            v-if="spaceList != undefined && spaceList.length > 0"
            v-model="currentSpace"
            @change="spaceChange"
          >
            <el-option
              v-for="item in spaceList"
              :key="item.value"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </span>
        <div class="search">
          <el-tooltip
            class="item"
            effect="dark"
            content="新增文档"
            placement="top"
          >
            <el-button
              type="text"
              class="add-doc"
              :icon="elIconTipsPlus"
              :disabled="!$permission('wiki_document_add')"
              title="新增文档"
              @click="() => addDoc()"
            />
          </el-tooltip>
        </div>
      </div>
      <div class="treebox">
        <left-tree
          v-if="docList.length > 0"
          ref="tree"
          :doc-list="docList"
          :current-doc="currentDoc"
          :parent-id="parentId"
          class="tree-main"
          @addDoc="addDoc"
          @deleteDoc="deleteDoc"
          @editDoc="editDoc"
          @clickNode="clickNode"
        />
        <vone-empty v-else />
      </div>
    </div>
    <div class="rightSection">
      <div class="header">
        <span class="title text-over" style="width: 70%">
          {{ title }}
        </span>
        <span v-show="editFLag" class="operation">
          <el-button
            plain
            class="add-doc"
            :disabled="!$permission('wiki_document_del')"
            @click="() => deleteDoc(currentDoc)"
            >删除</el-button
          >
          <el-button
            type="primary"
            class="add-doc"
            :disabled="!$permission('wiki_document_edit')"
            @click="saveDoc(true)"
            >保存</el-button
          >
          <el-button
            v-if="docList.length > 0 && projectInfo.typeId == 2"
            type="primary"
            :icon="elIconApplicationAi"
            @click="() => showAiDialog()"
          >
            需求分析
          </el-button>
        </span>
      </div>
      <div class="contentmain">
        <EditAomao
          v-if="editFLag"
          ref="editaomao"
          :doc-content="docContent"
          @saveDoc="saveDoc"
        />
      </div>
    </div>
    <el-dialog
      :title="
        $route.params.docId && $route.params.docId !== 'mulu'
          ? '编辑文档'
          : '新增文档'
      "
      :close-on-click-modal="false"
      :model-value="docVisible"
      width="456px"
    >
      <el-form :model="docForm" :rules="docRule">
        <el-form-item prop="title" label="文档标题">
          <el-input v-model="docForm.title" placeholder="文档标题" />
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <div>
          <el-button @click="close">取消</el-button>
          <el-button type="primary" @click="saveDoc">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- AI 弹窗 -->
    <!-- <ai-dialog
          v-if="aiVisible"
          ref="aiDialog"
          type="doc"
          v-model="aiVisible"
          :project-id="projectInfo.projectId"
          :type-code="'ISSUE'"
        /> -->
  </div>
</template>

<script>
// 注意：当没有文档或新增文档时，路由中docId传mulu这个参数
import LeftTree from "./left-tree.vue";
import EditAomao from "./edit/Editor.vue";
// import AddIntention from './add-intention.vue'
import {
  searchSpace,
  searchDoucment,
  operationDoc,
  getDoc,
} from "@/api/vone/wiki";
// import AiDialog from '@/components/AI/creat-dialog/dialogs/doc-dialog.vue'
export default {
  components: {
    LeftTree,
    EditAomao,
    // AiDialog
    // AddIntention
  },
  data() {
    return {
      spaceList: [],
      docList: [],
      spaceInfo: {},
      currentSpace: "", // 选中的空间
      currentDoc: "", // 选中的文档
      title: "", // 文档标题
      docValue: [], // 文档内容
      spaceVisible: false,
      readOnly: false, // 是否编辑模式
      docContent: "", // 文档回显内容
      parentId: "", // 父id
      editFLag: false, // 是否编辑
      intentionVisible: false, // 新增意向dialog
      addParam: { visible: false },
      docVisible: false,
      docForm: {
        title: "",
      },
      docRule: {
        title: [{ required: true, message: "请输入文档标题" }],
      },
      aiVisible: false,
      clickrow: {},
    };
  },
  computed: {
    projectInfo() {
      return {
        typeId: this.spaceInfo?.typeId || null,
        projectId: this.spaceInfo?.bizId || null,
      };
    },
  },
  watch: {
    $route: {
      handler: async function (router, oldVal) {
        if (
          router.params.spaceId != this.currentSpace &&
          router.params.spaceId != undefined
        ) {
          this.currentSpace = router.params.spaceId;
        }
        if (
          router.params.docId != this.currentDoc &&
          router.params.docId != undefined
        ) {
          this.currentDoc = router.params.docId;
          if (router.params.docId != "mulu") {
            this.getDoc(router.params.docId);
          }
        }
      },
      deep: true,
      immediate: true,
    },
    currentSpace: {
      handler: async function (val, oldVal) {
        if (val != "" && val != undefined) {
          this.searchDoucmentList(val);
        }
      },
      deep: true,
    },
  },
  mounted() {
    this.getSpaceList();
  },
  methods: {
    // 获取空间列表
    async getSpaceList() {
      const res = await searchSpace();
      if (res.isSuccess) {
        this.spaceList = res.data;
        // 当有选中的空间时,先判断空间列表中是否存在该空间
        const spaceArr = res.data.find(
          (item) => item.id == this.$route.params.spaceId
        );
        this.currentSpace = spaceArr.id;
        this.spaceInfo = spaceArr;
        if (spaceArr && this.$route.params.spaceId) {
          await this.searchDoucmentList(
            this.$route.params.spaceId,
            this.$route.params.docId
          );
        }
      } else {
        this.$message.warning(res.msg);
      }
    },
    // 切换空间
    async spaceChange(val) {
      this.currentSpace = val;
      this.spaceInfo = this.spaceList.find((item) => item.id == val);
    },
    // 获取文档列表并默认选中第一个，当传docId时，改更路由docId为此参数，否则为空间下第一个或没有文档
    async searchDoucmentList(id, docId) {
      await searchDoucment(id).then((res) => {
        if (res.isSuccess) {
          this.docList = res.data;
          // 当没有文档列表时
          if (res.data.length == 0) {
            this.editFLag = false;
            this.title = "";
            this.docContent = "";
          } else {
            this.clickNode(res.data[0]);
            this.jumpRoute(
              id,
              docId || (res.data.length > 0 ? res.data[0].id : "mulu"),
              res.data[0].name
            );
          }
        } else {
          this.$message.warning(res.msg);
        }
      });
    },

    showAiDialog() {
      this.aiVisible = true;
    },

    // 通过router控制页面内容
    jumpRoute(spaceId, docId, title) {
      this.$router.push({
        name: "wiki_document",
        params: {
          spaceId: spaceId,
          docId: docId,
        },
      });
    },
    // 根据文档id获取文档内容
    getDoc(id) {
      if (id == "mulu") {
        return;
      }
      getDoc(id).then((res) => {
        if (res.isSuccess) {
          if (res.data) {
            this.docForm.title = res.data.name;
            this.title = res.data.name;
            this.editFLag = true;
            const data = res.data.content;
            this.docContent = data;
          }
        } else {
          this.$message.warning(res.msg);
          return;
        }
      });
    },

    // 创建意向
    add() {
      // this.intentionVisible = true
      this.addParam = { visible: true };
    },
    // 新增文档或子文档
    addDoc(id) {
      this.docVisible = true;
      // 当添加子文档时 先质空parentId 重新赋值id
      this.parentId = "";
      if (id) {
        this.parentId = id;
      }
      // 新文档的内容，后续的优化会添加模版
      this.editFLag = true;
      this.docForm.title = "";
      // 只是为了改变docId
      this.jumpRoute(this.$route.params.spaceId, `mulu`);
    },
    editDoc(row) {
      // 编辑
      this.docVisible = true;
      this.docForm.title = row.name;
      this.jumpRoute(this.$route.params.spaceId, row.id, row.name);
    },
    close() {
      this.docVisible = false;
    },
    // 新增/编辑时保存
    async saveDoc(flag) {
      let params = {};
      const contont = await this.$refs.editaomao.save();
      if (this.$route.params.docId == "mulu" || this.parentId != "") {
        params = {
          content: "",
          name: this.docForm.title,
          spaceId: this.$route.params.spaceId,
        };
        if (this.parentId != "") {
          params.parentId = this.parentId;
        }
      } else {
        params = {
          content: JSON.stringify(contont),
          name: this.docForm.title,
          id: this.$route.params.docId,
        };
      }
      operationDoc(
        params,
        this.$route.params.docId != "mulu" ? "put" : "post"
      ).then(async (res) => {
        if (res.isSuccess) {
          if (this.$route.params.docId == "mulu" || this.parentId != "") {
            await this.searchDoucmentList(
              this.$route.params.spaceId,
              res.data.id
            );
          } else {
            searchDoucment(this.$route.params.spaceId).then((res) => {
              if (res.isSuccess) {
                this.docList = res.data;
              }
            });
          }
          this.title = this.docForm.title;
          this.docVisible = false;
          if (flag) this.$message.success("保存成功");
        } else {
          this.$message.warning(res.msg);
        }
      });
    },
    // 删除文档
    deleteDoc(id) {
      this.$confirm("是否删除当前文档?", "删除", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        operationDoc([id], "delete").then((res) => {
          if (res.isSuccess) {
            this.searchDoucmentList(this.$route.params.spaceId);
          } else {
            this.$message.warning(res.msg);
          }
        });
      });
    },
    clickNode(row) {
      this.clickrow = row;
    },
  },
};
</script>

<style lang="scss" scoped>
.sectionPageContent {
  display: flex;
  justify-content: center;
  height: calc(100vh - 20px);
}
.rightSection {
  height: 100%;
  padding: 0px;
  .header {
    height: 48px;
    line-height: 48px;
    border-bottom: 1px solid var(--solid-border-color);
    padding: 0px 16px;
    .operation {
      float: right;
    }
    .title {
      font-weight: 500;
      font-size: 16px;
    }
  }
  .contentmain {
    height: calc(100vh - 68px);
  }
  .doc-title {
    height: 48px;
    line-height: 48px;
  }
}
.leftSection {
  height: 100%;
  width: 240px;
  :deep(.el-input__inner) {
    border: none;
  }
  :deep(
      .vue-treeselect--open.vue-treeselect--open-below .vue-treeselect__control,
      .vue-treeselect--focused:not(.vue-treeselect--open)
        .vue-treeselect__control,
      .el-input--small .el-input__inner:focus,
      .el-textarea__inner:focus,
      .el-cascader .el-input .el-input__inner:focus,
      .el-cascader .el-input.is-focus .el-input__inner
    ) {
    border-color: unset;
  }
  .header {
    padding: 0px 16px;
    height: 48px;
    line-height: 48px;
    border-bottom: 1px solid var(--solid-border-color);
    display: flex;
    span {
      color: var(--font-main-color);
      font-size: 16px;
      font-weight: 500;
      flex: 1;
    }
    .search {
      display: flex;
      align-items: center;
      :deep(.el-divider--vertical) {
        margin: 0 10px 0 10px;
      }
      .el-button {
        min-width: unset;
        padding: 0px;
      }
      .iconfont {
        cursor: pointer;
        color: var(--font-second-color);
      }
      .iconfont:hover {
        color: var(--main-theme-color);
      }
      .iconfont.active {
        color: var(--main-theme-color);
      }
    }
  }
  .treebox {
    height: calc(100vh - 68px);
    overflow-y: auto;
  }
}
:deep(.el-select .el-input .el-select__caret) {
  color: var(--font-second-color);
}
</style>
