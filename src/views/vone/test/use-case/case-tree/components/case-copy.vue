<template>
  <el-dialog
    v-if="visible"
    :width="name == '用例复用' ? '78%' : '30%'"
    top="10vh"
    :model-value="visible"
    :before-close="onClose"
    :close-on-click-modal="false"
  >
    <template v-slot:title>
      <el-form ref="form" inline :model="form" :rules="rules">
        <el-form-item label="项目:" prop="projectId">
          <el-select
            v-model="form.projectId"
            placeholder="请选择用例库"
            @change="projectChange"
          >
            <el-option
              v-for="item in byProjectList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </template>

    <el-row>
      <el-col :span="name == '用例复用' ? 8 : 24" class="elStyle l_body">
        <el-input
          v-model="filterText"
          placeholder="搜索"
          :prefix-icon="ElIconSearch"
          style="margin-bottom: 8px"
        />
        <el-tree
          ref="tree"
          v-loading="treeLoading"
          node-key="id"
          :load="loadNode"
          lazy
          :data="tData"
          :show-checkbox="name !== '用例复用'"
          highlight-current
          :check-strictly="true"
          :expand-on-click-node="false"
          :props="defaultProps"
          :default-expanded-keys="expandedArray"
          :filter-node-method="filterNode"
          @node-click="treeCaseClick"
          @check="checkChange"
        >
          <template v-slot="{ data }">
            <span class="custom-tree-node">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#el-application-filejia2" />
              </svg>
              <span class="treeNode">{{ data.name }}</span>
            </span>
          </template>
        </el-tree>
      </el-col>
      <el-col v-if="name == '用例复用'" :span="16" class="elStyle r_body">
        <vone-search-wrapper>
          <template v-slot:search>
            <vone-search-dynamic
              table-search-key="caseCopyTable"
              :model="formData"
              :table-ref="$refs['caseCopyTable']"
              @getTableData="getTableList"
            >
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="用例" prop="name">
                    <el-input
                      v-model="formData.name"
                      placeholder="请输入用例"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="编号" prop="caseKey">
                    <el-input
                      v-model="formData.caseKey"
                      placeholder="请输入编号"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="标签" prop="tabName">
                    <tagSelect v-model:value="formData.tabName" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="优先级" prop="priority ">
                    <vone-icon-select
                      v-model:value="formData.priority"
                      :data="prioritList"
                      filterable
                      clearable
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in prioritList"
                        :key="item.key"
                        :label="item.name"
                        :value="item.code"
                      >
                        <i
                          :class="`iconfont ${item.icon}`"
                          :style="{
                            color: item.color,
                            fontSize: '16px',
                            paddingRight: '6px',
                          }"
                        />
                        {{ item.name }}
                      </el-option>
                    </vone-icon-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </vone-search-dynamic>
          </template>
        </vone-search-wrapper>
        <main style="height: calc(100vh - 398px)">
          <vxe-table
            ref="caseCopyTable"
            class="vone-vxe-table"
            height="auto"
            border
            resizable
            :loading="tableLoading"
            show-overflow="tooltip"
            :empty-render="{ name: 'empty' }"
            :data="tableData.records"
            :column-config="{ minWidth: '120px' }"
            :checkbox-config="{ reserve: true }"
            row-id="id"
            @checkbox-all="selectAllEvent"
            @checkbox-change="selectChangeEvent"
          >
            <vxe-column
              type="checkbox"
              width="36"
              fixed="left"
              align="center"
            />
            <vxe-column title="用例" field="name">
              <template #default="{ row }">
                <div style="text-align: left">
                  <el-icon class="iconfont" style="color: #37cdde"
                    ><el-icon-application-view-list
                  /></el-icon>
                  <el-icon style="color: #3e7bfa"><el-icon-document /></el-icon>
                  <span style="margin-left: 4px">{{
                    row.caseKey + " " + row.name
                  }}</span>
                </div>
              </template>
            </vxe-column>
            <vxe-column title="优先级" field="priority">
              <template #default="{ row }">
                <span v-if="row.priority === 1">
                  <el-icon class="iconfont" style="color: #4ecf95"
                    ><el-icon-icon-dengji-zuidi2
                  /></el-icon>
                  最低
                </span>
                <span v-else-if="row.priority === 2">
                  <el-icon class="iconfont" style="color: #5acc5e"
                    ><el-icon-icon-dengji-jiaodi2
                  /></el-icon>
                  较低
                </span>
                <span v-else-if="row.priority === 5">
                  <el-icon class="iconfont" style="color: #fa6a69"
                    ><el-icon-icon-dengji-zuigao2
                  /></el-icon>
                  最高
                </span>
                <span v-else-if="row.priority === 4">
                  <el-icon class="iconfont" style="color: #fa8669"
                    ><el-icon-icon-dengji-jiaogao2
                  /></el-icon>
                  较高
                </span>
                <span v-else>
                  <el-icon
                    class="iconfont"
                    style="color: var(--main-theme-color, #3e7bfa)"
                    ><el-icon-icon-dengji-putong2
                  /></el-icon>
                  普通
                </span>
              </template>
            </vxe-column>
            <vxe-column title="维护人" field="leadingBy">
              <template #default="{ row }">
                <span
                  v-if="row.leadingBy && row.echoMap && row.echoMap.leadingBy"
                >
                  <vone-user-avatar
                    :avatar-path="row.echoMap.leadingBy.avatarPath"
                    :name="row.echoMap.leadingBy.name"
                  />
                </span>
                <span v-else class="noSetting">
                  <el-icon class="iconfont" style="font-size: 26px"
                    ><el-icon-icon-light-avatar
                  /></el-icon>
                  <span> -- </span>
                </span>
              </template>
            </vxe-column>
          </vxe-table>
        </main>
        <vone-pagination
          ref="pagination"
          :total="tableData.total"
          @update="getTableList"
        />
      </el-col>
    </el-row>
    <template v-slot:footer>
      <div class="dialog-footer">
        <el-button :loading="btnLoading" @click="onClose">取消</el-button>
        <el-button type="primary" :loading="btnLoading" @click="saveInfo"
          >确定</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script>
import {
  ApplicationViewList as ElIconApplicationViewList,
  Document as ElIconDocument,
  IconDengjiZuidi2 as ElIconIconDengjiZuidi2,
  IconDengjiJiaodi2 as ElIconIconDengjiJiaodi2,
  IconDengjiZuigao2 as ElIconIconDengjiZuigao2,
  IconDengjiJiaogao2 as ElIconIconDengjiJiaogao2,
  IconDengjiPutong2 as ElIconIconDengjiPutong2,
  IconLightAvatar as ElIconIconLightAvatar,
  Search as ElIconSearch,
} from "@element-plus/icons-vue";
import {
  $on,
  $off,
  $once,
  $emit,
} from "../../../../../../utils/gogocodeTransfer";

import tagSelect from "@/views/vone/test/components/tag-select.vue";
import {
  getTreeByLibrary,
  finddelCaseByIdPage,
} from "@/api/vone/testmanage/case";
import { getLibraryIdByProjectId } from "@/api/vone/testmanage";

import { apiAlmProjectNoPage } from "@/api/vone/project/index";
import { catchErr } from "@/utils";

export default {
  data() {
    return {
      treeLoading: false,
      tData: [],
      width: "",
      // 用例库标签列表
      tagsList: [],
      // 用例库列表
      byProjectList: [],
      form: {
        projectId: "",
      },
      rules: {
        projectId: [{ required: true, message: "请选择", trigger: "change" }],
      },
      filterText: "",
      // 用户信息
      userMap: {},
      // 优先级
      prioritList: [
        {
          name: "最高",
          code: 5,
          icon: "el-icon-icon-dengji-zuigao2",
          color: "#FA6A69",
        },
        {
          name: "较高",
          code: 4,
          icon: "el-icon-icon-dengji-jiaogao2",
          color: "#FA8669",
        },
        {
          name: "普通",
          code: 3,
          icon: "el-icon-icon-dengji-putong2",
          color: "var(--main-theme-color,#3e7bfa)",
        },
        {
          name: "较低",
          code: 2,
          icon: "el-icon-icon-dengji-jiaodi2",
          color: "#5ACC5E",
        },
        {
          name: "最低",
          code: 1,
          icon: "el-icon-icon-dengji-zuidi2",
          color: "#4ECF95",
        },
      ],
      formData: {
        name: "",
        caseKey: "",
        tabName: "",
        priority: "",
      },
      resolve: {},
      tableOptions: {
        isOperation: false, // 表格有操作列时设置
        isIndex: false, // 列表序号
        pagerCount: 5,
      },
      treeData: [],
      btnLoading: false,
      tableLoading: false,
      tableSelected: [],
      // 表格数据
      tableData: { records: [] },
      currentNode: {},
      defaultProps: {
        label: "name",
      },
      expandedArray: [],
      checkArray: [],
      extraData: {},
      libraryId: "",
      ElIconSearch,
    };
  },
  components: {
    tagSelect,
    ElIconApplicationViewList,
    ElIconDocument,
    ElIconIconDengjiZuidi2,
    ElIconIconDengjiJiaodi2,
    ElIconIconDengjiZuigao2,
    ElIconIconDengjiJiaogao2,
    ElIconIconDengjiPutong2,
    ElIconIconLightAvatar,
  },
  props: {
    visible: {
      type: Boolean,
      default: true,
    },
    name: {
      type: String,
      default: "",
    },
  },
  computed: {
    // 默认选中用例id
    selectedId() {
      return this.tableSelected.map((v) => v.id);
    },
  },
  watch: {
    visible: {
      deep: true,
      immediate: true,

      handler: function handler(val) {
        this.getprojectList();
      },
    },
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  methods: {
    refreshNode(id) {
      const node = this.$refs.tree.getNode(id);
      node.loaded = false;

      node.expand(); // 主动调用展开节点方法,重新查询该节点下的所有子节点
    },
    async projectChange() {
      const res = await getLibraryIdByProjectId({
        projectId: this.form.projectId,
      });
      if (res.isSuccess) {
        this.libraryId = res.data;
        this.librarayCh(res.data);
        // getCaseTree(res.data).then(res1 => {
        //   if (res1.isSuccess) {
        //     const data = list2Tree(res1.data, { parentKey: 'parentId' })
        //     this.GraphData = data[0]
        //   }
        // })
      }
    },
    async librarayCh(e) {
      const res = await getTreeByLibrary(e, 0);
      this.tData = res.data;
      this.name !== "用例复用" &&
        res.data.forEach((item) => {
          if (item.parentId == "0") {
            item.disabled = true;
          }
          return;
        });

      this.getTableList(res.data[0].id);
    },
    // 默认显示节点树懒加载方法
    async loadNode(node, resolve) {
      // this.treeLoading = true
      // 节点id
      this.resolve = resolve;
      const rootId = node.level == 0 ? 0 : node.data.id;
      var liId = "";
      if (!this.libraryId) {
        const res = await getLibraryIdByProjectId({
          projectId: this.form.projectId,
        });
        if (res.isSuccess) {
          liId = res.data;
        }
      } else {
        liId = this.libraryId;
      }
      const res = await getTreeByLibrary(liId, rootId);
      // this.treeLoading = false
      if (node.level === 0) {
        // 设置默认节点
        this.menuNode = res.data?.[0];
        this.currentCasekey = res.data?.[0]?.id;
        this.currentNode = res.data[0];
        // this.getTableData()
        this.name == "用例复用" && this.getTableList(this.menuNode.id);
        this.$nextTick(() => {
          this.$refs.tree?.setCurrentKey(this.currentCasekey); // 树当前选中
          this.$refs.tree?.getNode(this.currentCasekey)?.expand(); // 手动展开
        });
      }
      this.name !== "用例复用" &&
        res.data.forEach((item) => {
          if (item.parentId == "0") {
            item.disabled = true;
          }
        });
      resolve(res.data);
    },
    async getprojectList() {
      const res = await apiAlmProjectNoPage();
      if (res.isSuccess) {
        this.byProjectList = res.data;
        if (!res.data || res.data.length == 0) return;
        this.form["projectId"] = res.data[0].id;
      }
    },

    filterNode(value, data) {
      if (!value) return true;
      return data.name.toLowerCase().indexOf(value) !== -1;
    },
    treeCaseClick(data) {
      // 点击节点
      this.currentNode = data;
      this.name == "用例复用" && this.getTableList(data.id);
      this.refreshNode(data.id);
    },
    checkChange(row, e) {
      this.currentNode = row;
      this.$refs.tree.setCheckedKeys([row.id]);
    },

    onClose() {
      // 取消选择
      this.name == "用例复用" && this.clearSelection();
      $emit(this, "update:visible", false);
      this.tableData = { records: [] };
      this.currentNode = {};
      // 清空选中节点
      this.$refs.tree?.setCurrentKey();
    },
    // 查询表格列表
    async getTableList(nodeId) {
      if (!this.libraryId) return;
      const tableAttr = this.$refs?.pagination?.pageObj || {
        current: 1,
        size: 20,
      };
      const params = {
        ...tableAttr,
        extra: {},
        model: {
          draft: false,
          state: false,
          libraryId: this.libraryId,
          treeId: nodeId,
          ...this.formData,
          tabName: this.formData.tabName ? this.formData.tabName : [],
        },
      };
      this.tableLoading = true;
      const [res, err] = await catchErr(finddelCaseByIdPage(params));
      this.tableLoading = false;
      if (err) return;
      if (res.isSuccess) {
        this.tableData = res.data;
        const tableList = res.data.records;

        this.clearSelection();
        if (tableList.length === 0) return;
        // 点击左侧树设置表格全选
        if (this.checkArray.indexOf(nodeId) > -1) {
          this.$nextTick(() => {
            this.$refs.caseCopyTable.setCheckboxRow(
              this.tableData.records,
              true
            );
          });

          this.selectionAll(this.tableData.records);
        } else {
          const checkId = []; // 当前表格选中
          tableList.map((item) => {
            if (this.selectedId.indexOf(item.id) > -1) {
              checkId.push(item);
            }
          });
          this.$nextTick(() => {
            this.$refs.caseCopyTable.setCheckboxRow(checkId, true);
          });
          if (checkId.length > 0) {
            // 是否半勾选
            const isHalf = checkId.length < tableList.length;
            this.$refs.tree.getNode(nodeId).indeterminate = isHalf; // 设置半选中状态
            if (checkId.length === tableList.length) {
              this.$refs.tree.setChecked(this.currentNode.id, true);
            }
          }
        }
      }
    },
    // 保存选中用例
    async saveInfo() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.name == "用例复用" && this.tableSelected.length === 0) {
            this.$message.warning("请选择用例添加");
            return;
          }
          const ids = [];
          this.tableSelected.map((item) => {
            ids.push(item.id);
          });

          $emit(this, "success", ids, this.libraryId, this.currentNode.id);
          this.onClose();
        } else {
          return false;
        }
      });
    },
    selectAllEvent({ checked }) {
      const selection = this.$refs.caseCopyTable.getCheckboxRecords();

      this.selectionAll(selection);
    },
    selectChangeEvent({ row }) {
      const selection = this.$refs.caseCopyTable.getCheckboxRecords();

      this.selectionChange(selection, row);
    },
    // 切换全选
    selectionAll(selection) {
      // 是否全选
      const checkAll = selection.length > 0;
      const node = this.$refs.tree.getNode(this.currentNode.id);
      let parent = node.parent;

      this.$refs.tree.setChecked(this.currentNode.id, checkAll);
      node.indeterminate = false;
      // 手动设置父级半选中
      while (parent && parent.level >= 0) {
        parent.indeterminate =
          checkAll ||
          parent.childNodes.some((node) => node.checked || node.indeterminate); // 全选或者有子节点选中
        parent = parent.parent;
      }
      // 全选
      if (checkAll) {
        selection.map((item) => {
          this.selectedId.indexOf(item.id) === -1 &&
            this.tableSelected.push(item);
        });
      } else {
        // 取消选择
        this.tableData.records.forEach((ele) => {
          let index = this.selectedId.indexOf(ele.id);
          while (index > -1) {
            this.tableSelected.splice(index, 1);
            index = this.selectedId.indexOf(ele.id);
          }
        });
      }
    },
    // 切换选择
    selectionChange(selection, row) {
      // 当前项选中还是未选中
      const isSelect = selection.findIndex((ele) => ele.id === row.id) > -1;

      if (isSelect) {
        this.tableSelected.push(row);
      } else {
        let index = this.selectedId.indexOf(row.id);
        while (index > -1) {
          this.tableSelected.splice(index, 1);
          index = this.selectedId.indexOf(row.id);
        }
      }

      const isChecked = selection.length > 0;
      const isIndeterminate =
        selection.length > 0
          ? selection.length < this.tableData.records.length
          : false;
      const node = this.$refs.tree.getNode(this.currentNode.id);
      let parent = node.parent;
      // 手动设置当前节点选中及父节点选中状态
      this.$refs.tree.setChecked(this.currentNode.id, isChecked);
      node.indeterminate = isIndeterminate;
      while (parent && parent.level >= 0) {
        parent.indeterminate =
          isIndeterminate ||
          parent.childNodes.some((node) => node.checked || node.indeterminate); // 全选或者有子节点选中
        parent = parent.parent;
      }
    },
    // 取消选择
    clearSelection() {
      this.$refs.caseCopyTable.clearCheckboxRow();
    },
  },
  emits: ["update:visible", "success"],
};
</script>

<style lang="scss" scoped>
:deep(.el-form-item) {
  margin-bottom: 0px;
}
:deep() {
  .el-dialog__body {
    padding: 0;
  }
}
.elStyle {
  height: calc(100vh - 273px);
  padding: 16px;
  overflow: auto;
  :deep(.el-tree-node > .el-tree-node__children) {
    min-width: min-content;
  }
}
.r_body {
  border-left: 1px solid var(--disabled-bg-color, #ebeef5);
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  & > span {
    flex: 1 1 auto;
    display: flex;
    align-items: center;
  }

  .treeNode {
    flex: 1 1 auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
