import request from '@/utils/axios-api'

// 查询用户组列表
export function getUserGroupList(data) {
  return request({
    url: `/api/base/base/userGroup/page`,
    method: 'post',
    data
  })
}

// 新增/修改用户组u
export function userGroupAddEdit(data, method) {
  return request({
    url: `/api/base/base/userGroup`,
    method: method,
    data
  })
}

// 查询详情
export function getUserGroupDetail(id) {
  return request({
    url: `/api/base/base/userGroup/${id}`,
    method: 'get'
  })
}

// 删除用户
export function deleteUserGroup(data) {
  return request({
    url: `/api/base/base/userGroup`,
    method: 'delete',
    data
  })
}

// 批量查询用户组
export function getUserGroupQuery(data) {
  return request({
    url: `/api/base/base/userGroup/query`,
    method: 'post',
    data
  })
}

