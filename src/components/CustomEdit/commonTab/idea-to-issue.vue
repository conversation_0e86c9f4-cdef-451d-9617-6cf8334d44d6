<template>

  <!-- <vone-div-wrapper :title="tabName"> -->
  <div v-loading="pageLoading" class="wrapper">
    <!-- 主办产品 -->
    <div>
      <section v-if="!hostTempleteList.length" class="section-host">
        <el-button type="text" icon="el-icon-plus" @click="showHostProduct = !showHostProduct">
          主办产品
        </el-button>
        <!-- <el-form v-if="addRelation" ref="issueTab" :model="issueTab" :rules="relateRules"> -->
        <!-- <el-row :gutter="6" type="flex" class="mt-10" justify="space-between"> -->
        <el-form v-if="showHostProduct" ref="formTab" :model="form" :rules="formRules" class="shadow_bottom">
          <el-row type="flex" class="row_host">
            <el-form-item prop="hostProduct" style="width:100%">
              <el-select v-model.trim="form.hostProduct" placeholder="请输入产品名称" clearable filterable remote :remote-method="getProducttList" :loading="productLoading" @focus="setOptionWidth">
                <el-option v-for="ele in productList" :key="ele.id" :value="ele.id" :label="ele.name" :disabled="ele.disabled" :style="{ width: selectOptionWidth }" :title="ele.name">
                  <svg class="icon svg-icon" aria-hidden="true">
                    <use xlink:href="#el-icon-icon-wendangkuchanpin" />
                  </svg>

                  {{ `${ele.name}` }}
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button size="mini" style="margin-left:8px" class="miniBtn" @click="showHostProduct =! showHostProduct">
                取消
              </el-button>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" style="margin-left:8px" size="mini" class="miniBtn" @click="productAdd('1')">
                保存
              </el-button>
            </el-form-item>

          </el-row>
        </el-form>

      </section>
      <template v-else>

        <section v-for="item in hostTempleteList" :key="item.id" class="section-has">
          <div v-if="item.isEdit" class="shadow_bottom">
            <el-row type="flex" class="row_host" style="align-items:center">
              <el-select v-model.trim="item.productId" placeholder="请输入产品名称" clearable filterable remote :remote-method="getProducttList" :loading="productLoading" @focus="setOptionWidth">
                <el-option v-for="ele in productList" :key="ele.id" :value="ele.id" :label="ele.name" :disabled="ele.disabled" :style="{ width: selectOptionWidth }" :title="ele.name">
                  <svg class="icon svg-icon" aria-hidden="true">
                    <use xlink:href="#el-icon-icon-wendangkuchanpin" />
                  </svg>

                  {{ `${ele.name}` }}
                </el-option>
              </el-select>
              <el-button size="mini" style="margin-left:16px" class="miniBtn" @click="item.isEdit =! item.isEdit">
                取消
              </el-button>
              <el-button type="primary" size="mini" class="miniBtn" @click="editProduct(item)">
                保存
              </el-button>

            </el-row>
          </div>
          <el-row v-else type="flex" justify="space-between">
            <span class="host_title">
              {{ item.productName }}
              <a @click="editHost(item)">
                <i class="iconfont el-icon-application-edit" />
              </a>
              <a @click="deleteProduct(item)">
                <i class="iconfont el-icon-application-delete" />
              </a>

              <span class="host_text">
                主
              </span>

            </span>

            <span>

              <el-button class="con-issue-idea" size="mini" type="text" icon="el-icon-link" @click="showTask(item,'1')">
                关联
              </el-button>
              <el-button class="con-issue-idea" size="mini" type="text" icon="el-icon-plus" @click="showTask(item,'0')">
                新增
              </el-button>
            </span>

          </el-row>

          <!-- 关联需求/新增需求 -->
          <div v-if="item.coTaskType !=null" class="shadow_bottom">
            <el-row v-if="item.coTaskType == 1" type="flex" class="row_host" style="align-items:center">
              <el-select v-model.trim="item.coIssue" placeholder="请输入需求名称" clearable filterable remote :remote-method="getRequirementList" :loading="requireLoading" class="requireSelect" multiple @focus="setOptionWidth">
                <el-option v-for="ele in issueList" :key="ele.id" :value="ele.id" :label="ele.name" :disabled="ele.disabled" :style="{ width: selectOptionWidth }" :title="ele.name">
                  {{ `${ele.code}   ${ele.name}` }}
                </el-option>
              </el-select>
              <el-button size="mini" style="margin-left:16px" class="miniBtn" @click="item.coTaskType = null">
                取消
              </el-button>
              <el-button type="primary" style="margin-left:8px" size="mini" class="miniBtn" @click="batchIssue(item,'1')">
                保存
              </el-button>

            </el-row>

            <el-row v-if="item.coTaskType == 0" class="row_host">

              <simpleAddIssue v-if="item.coTaskType == 0" no-file :type-code="'IDEA'" :issue-id="issueId" no-epic class="mt-10" style="margin-bottom:20px" :product-id="simpleProductId" @success="initAdd" @cancel="item.coTaskType = null" />
            </el-row>
          </div>

          <el-table v-if="item.requirementList&&item.requirementList.length" ref="tableData" class="vone-table voneNobg-table" :data="item.requirementList">
            <el-table-column prop="code" label="需求" show-overflow-tooltip min-width="250" class-name="name_col">
              <template slot-scope="scope">
                <a class="table_title" @click="toRouter(scope.row)">
                  <span v-if="scope.row.typeCode && scope.row.echoMap && scope.row.echoMap.typeCode">
                    <i :class="`iconfont ${scope.row.echoMap.typeCode.icon}`" :style="{ color:`${scope.row.echoMap.typeCode ? scope.row.echoMap.typeCode.color : '#ccc'}`}" />
                    {{ scope.row.code + " " + scope.row.name }}
                  </span>
                  <!-- {{ scope.row.code }}
                  {{ scope.row.name }} -->
                </a>
              </template>
            </el-table-column>
            <el-table-column prop="projectId" label="项目" show-overflow-tooltip min-width="160">
              <template slot-scope="scope">
                <span v-if="scope.row.projectId && scope.row.echoMap.projectId">
                  {{ scope.row.echoMap.projectId.name }}
                </span>
                <span v-else class="noSetting">
                  未关联项目
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="typeCode" label="类型" show-overflow-tooltip min-width="120">
              <template slot-scope="scope">
                <span v-if="scope.row.typeCode && scope.row.echoMap.typeCode">
                  {{ scope.row.echoMap.typeCode.name || '-' }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="handleBy" label="处理人" show-overflow-tooltip min-width="125">
              <template slot-scope="scope">

                <span>
                  <vone-user-avatar :avatar-path="getUserInfo(scope.row) ? getUserInfo(scope.row).avatarPath : ''" :name="getUserInfo(scope.row) ? getUserInfo(scope.row).name : ''" />
                </span>

              </template>
            </el-table-column>
            <el-table-column prop="planEtime" label="计划完成时间" show-overflow-tooltip min-width="165">
              <template slot-scope="scope">

                {{ scope.row.planEtime||'未设置' }}

              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template slot-scope="scope">
                <el-button type="text" size="small" icon="iconfont el-icon-icon-line-jiechuguanlian" class="table_handler" @click="deleteRelation(scope.row)" />
              </template>
            </el-table-column>
          </el-table>

          <div v-else class="empty-container">
            <svg-icon icon-class="zanwu" class-name="empty-svg" />
            暂无关联内容
          </div>

        </section>

      </template>
    </div>
    <div class="rowLine" />
    <!-- 辅办产品 ----------------------------------------------------------------->
    <div>
      <section>
        <el-button type="text" icon="el-icon-plus" @click="addCoProduct">
          辅办产品
        </el-button>
        <div>
          <el-form v-if="showCoProduct" ref="coRules" :model="form" :rules="coRules" class="shadow_bottom">
            <el-row type="flex" class="row_host">
              <el-form-item prop="coProduct" style="width:100%">
                <el-select v-model.trim="form.coProduct" placeholder="请输入产品名称" clearable filterable remote :remote-method="getProducttList" :loading="productLoading" multiple @focus="setOptionWidth">
                  <el-option v-for="ele in productList" :key="ele.id" :value="ele.id" :label="ele.name" :disabled="ele.disabled" :style="{ width: selectOptionWidth }" :title="ele.name">
                    <svg class="icon svg-icon" aria-hidden="true">
                      <use xlink:href="#el-icon-icon-wendangkuchanpin" />
                    </svg>

                    {{ `${ele.name}` }}
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button size="mini" style="margin-left:16px" class="miniBtn" @click="showCoProduct =! showCoProduct">
                  取消
                </el-button>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" style="margin-left:8px" size="mini" class="miniBtn" @click="productAdd('2')">
                  保存
                </el-button>
              </el-form-item>

            </el-row>
          </el-form>

        </div>
      </section>

      <template v-if="coTempleteList.length">
        <section v-for="item in coTempleteList" :key="item.id" class="section-has">
          <el-row type="flex" justify="space-between">
            <span class="host_title">
              {{ item.productName }}
              <a @click="deleteProduct(item)">
                <i class="iconfont el-icon-application-delete" />
              </a>
              <span class="host_text_co">
                辅
              </span>
            </span>
            <span>
              <el-button size="mini" class="con-issue-idea" type="text" icon="el-icon-link" @click="showTask(item,'1')">
                关联
              </el-button>
              <el-button size="mini" class="con-issue-idea" type="text" icon="el-icon-plus" @click="showTask(item,'0')">
                新增
              </el-button>
            </span>

          </el-row>

          <!-- 关联需求/新增需求 -->
          <div v-if="item.coTaskType !=null" class="shadow_bottom">
            <el-row v-if="item.coTaskType == 1" type="flex" class="row_host" style="align-items:center">
              <el-select v-model.trim="item.coIssue" placeholder="请输入需求名称" clearable filterable remote :remote-method="getRequirementList" :loading="requireLoading" class="requireSelect" multiple @focus="setOptionWidth">
                <el-option v-for="ele in issueList" :key="ele.id" :value="ele.id" :label="ele.name" :disabled="ele.disabled" :style="{ width: selectOptionWidth }" :title="ele.name">
                  {{ `${ele.code}   ${ele.name}` }}
                </el-option>
              </el-select>
              <el-button size="mini" style="margin-left:16px" class="miniBtn" @click="item.coTaskType = null">
                取消
              </el-button>
              <el-button type="primary" style="margin-left:8px" size="mini" class="miniBtn" @click="batchIssue(item,'2')">
                保存
              </el-button>
            </el-row>

            <el-row v-if="item.coTaskType == 0" class="row_host">
              <simpleAddIssue v-if="item.coTaskType == 0" no-file :type-code="'IDEA'" :issue-id="issueId" no-epic class="mt-10" style="margin-bottom:20px" :product-id="simpleProductId" @success="initAdd" @cancel="item.coTaskType = null" />
            </el-row>
          </div>

          <el-table v-if="item.requirementList&&item.requirementList.length" ref="tableData" class="vone-table voneNobg-table" :data="item.requirementList">
            <el-table-column prop="code" label="需求" show-overflow-tooltip min-width="250" class-name="name_col">
              <template slot-scope="scope">
                <a class="table_title" @click="toRouter(scope.row)">
                  <!-- <svg class="icon svg-icon" aria-hidden="true">
                    <use xlink:href="#el-icon-icon-yonghugushi" />
                  </svg>
                  {{ scope.row.code }}
                  {{ scope.row.name }} -->
                  <span v-if="scope.row.typeCode && scope.row.echoMap && scope.row.echoMap.typeCode">
                    <i :class="`iconfont ${scope.row.echoMap.typeCode.icon}`" :style="{ color:`${scope.row.echoMap.typeCode ? scope.row.echoMap.typeCode.color : '#ccc'}`}" />
                    {{ scope.row.code + " " + scope.row.name }}
                  </span>
                </a>
              </template>
            </el-table-column>
            <el-table-column prop="projectId" label="项目" show-overflow-tooltip min-width="160">
              <template slot-scope="scope">
                <span v-if="scope.row.projectId && scope.row.echoMap.projectId">
                  {{ scope.row.echoMap.projectId.name }}
                </span>
                <span v-else class="noSetting">
                  未关联项目
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="typeCode" label="类型" show-overflow-tooltip min-width="120">
              <template slot-scope="scope">
                <span v-if="scope.row.typeCode && scope.row.echoMap.typeCode">
                  {{ scope.row.echoMap.typeCode.name || '-' }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="handleBy" label="处理人" show-overflow-tooltip min-width="125">
              <template slot-scope="scope">
                <span>
                  <vone-user-avatar :avatar-path="getUserInfo(scope.row) ? getUserInfo(scope.row).avatarPath : ''" :name="getUserInfo(scope.row) ? getUserInfo(scope.row).name : ''" />
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="planEtime" label="计划完成时间" show-overflow-tooltip min-width="165">
              <template slot-scope="scope">

                {{ scope.row.planEtime||'未设置' }}

              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template slot-scope="scope">
                <el-button type="text" size="small" icon="iconfont el-icon-icon-line-jiechuguanlian" class="table_handler" @click="deleteRelation(scope.row)" />
              </template>
            </el-table-column>
          </el-table>

          <div v-else class="empty-container">
            <svg-icon icon-class="zanwu" class-name="empty-svg" />
            暂无关联内容
          </div>
        </section>
      </template>

    </div>
    <div class="rowLine" />
    <div>
      <section>
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <el-button type="text" style="color:#202124">
            未分组
          </el-button>
          <span>
            <el-button size="mini" class="con-issue-idea" type="text" icon="el-icon-link" @click="showTaskUnassociated('1')">
              关联
            </el-button>
            <el-button size="mini" class="con-issue-idea" type="text" icon="el-icon-plus" @click="showTaskUnassociated('0')">
              新增
            </el-button>
          </span>

        </div>
        <!-- 关联需求/新增需求 -->
        <div v-if="associationItem.association" class="shadow_bottom">
          <el-row v-if="associationItem.association == '1'" type="flex" class="row_host" style="align-items:center">
            <el-select v-model.trim="associationItem.coIssue" placeholder="请输入需求名称" clearable filterable remote :remote-method="getRequirementList" :loading="requireLoading" class="requireSelect" multiple @focus="setOptionWidth">
              <el-option v-for="ele in issueList" :key="ele.id" :value="ele.id" :label="ele.name" :disabled="ele.disabled" :style="{ width: selectOptionWidth }" :title="ele.name">
                {{ `${ele.code}   ${ele.name}` }}
              </el-option>
            </el-select>
            <el-button size="mini" style="margin-left:16px" class="miniBtn" @click="associationItem.association = ''">
              取消
            </el-button>
            <el-button type="primary" style="margin-left:8px" size="mini" class="miniBtn" @click="batchIssue(associationItem,'3')">
              保存
            </el-button>
          </el-row>

          <el-row v-if="associationItem.association == '0'" class="row_host">
            <simpleAddIssue v-if="associationItem.association == '0'" no-file :type-code="'IDEA'" :issue-id="issueId" no-epic class="mt-10" style="margin-bottom:20px" @success="initAdd" @cancel="associationItem.association = ''" />
          </el-row>
        </div>
      </section>

      <template v-if="ungroupedTempleteList.length">
        <section v-for="item in ungroupedTempleteList" :key="item.id" class="section-has">

          <el-table v-if="item.requirementList&&item.requirementList.length" ref="tableData" class="vone-table voneNobg-table" :data="item.requirementList">
            <el-table-column prop="code" label="需求" show-overflow-tooltip min-width="250" class-name="name_col">
              <template slot-scope="scope">
                <a class="table_title" @click="toRouter(scope.row)">
                  <!-- <svg class="icon svg-icon" aria-hidden="true">
                    <use xlink:href="#el-icon-icon-yonghugushi" />
                  </svg>
                  {{ scope.row.code }}
                  {{ scope.row.name }} -->
                  <span v-if="scope.row.typeCode && scope.row.echoMap && scope.row.echoMap.typeCode">
                    <i :class="`iconfont ${scope.row.echoMap.typeCode.icon}`" :style="{ color:`${scope.row.echoMap.typeCode ? scope.row.echoMap.typeCode.color : '#ccc'}`}" />
                    {{ scope.row.code + " " + scope.row.name }}
                  </span>
                </a>
              </template>
            </el-table-column>
            <el-table-column prop="projectId" label="项目" show-overflow-tooltip min-width="160">
              <template slot-scope="scope">
                <span v-if="scope.row.projectId && scope.row.echoMap.projectId">
                  {{ scope.row.echoMap.projectId.name }}
                </span>
                <span v-else class="noSetting">
                  未关联项目
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="typeCode" label="类型" show-overflow-tooltip min-width="120">
              <template slot-scope="scope">
                <span v-if="scope.row.typeCode && scope.row.echoMap.typeCode">
                  {{ scope.row.echoMap.typeCode.name || '-' }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="handleBy" label="处理人" show-overflow-tooltip min-width="125">
              <template slot-scope="scope">
                <span>
                  <vone-user-avatar :avatar-path="getUserInfo(scope.row) ? getUserInfo(scope.row).avatarPath : ''" :name="getUserInfo(scope.row) ? getUserInfo(scope.row).name : ''" />
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="planEtime" label="计划完成时间" show-overflow-tooltip min-width="165">
              <template slot-scope="scope">

                {{ scope.row.planEtime||'未设置' }}

              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template slot-scope="scope">
                <el-button type="text" size="small" icon="iconfont el-icon-icon-line-jiechuguanlian" class="table_handler" @click="deleteRelation(scope.row)" />
              </template>
            </el-table-column>
          </el-table>

          <div v-else class="empty-container">
            <svg-icon icon-class="zanwu" class-name="empty-svg" />
            暂无关联内容
          </div>
        </section>
      </template>

    </div>
    <div class="rowLine" />
  </div>

</template>

<script>

import {
  apiAlmRequirementNoPage
} from '@/api/vone/project/issue'

import { debounce } from 'lodash'

import simpleAddIssue from '../components/fast-add.vue'
import { apiAlmSaveIssue } from '@/api/vone/project/issue'

import { productListByCondition } from '@/api/vone/project/index'
import {
  issueIdeaProduct,
  issueIdeaProductDel,
  issueIdeaProductAdd,
  issueIdeaProductRequirement,
  issueIdeaProductRequirementDel,
  issueIdeaProductPut,
  issueIdeaProductRelation
} from '@/api/vone/reqmcenter/idea'

export default {
  name: 'IdeaToIssue',
  components: {
    simpleAddIssue
  },
  props: {
    issueId: {
      type: String,
      default: undefined
    },
    tabName: {
      type: String,
      default: null
    },
    typeCode: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      hostTempleteList: [], // 主办
      coTempleteList: [], // 辅办
      ungroupedTempleteList: [], // 未分组
      issueForm: {
        hostIssue: [],
        coIssue: []
      },
      formRules: {
        hostProduct: [{ required: true, message: '请选择产品', trigger: 'change' }]
      },
      coRules: {
        coProduct: [{ required: true, message: '请选择产品', trigger: 'change' }]
      },
      productLoading: false,
      showHostProduct: false,
      showCoProduct: false,
      tableData: [
      ],
      productList: [],
      pageLoading: false,
      loading: false,
      issueList: [],
      selectOptionWidth: '',
      requireLoading: false,
      taskForm: {
        taskId: []
      },
      form: {
        coProduct: []
      },
      params: [],
      simpleProductId: null,
      associationItem: {
        association: ''
      }
    }
  },
  computed: {
    getUserInfo() {
      return function(row) {
        return row?.handleBy && row?.echoMap?.handleBy
      }
    }
  },
  watch: {
    issueId: {
      handler: function() {
        this.getAllProductTemplete()
      },
      immediate: true
    }
  },

  mounted() {
    // this.getAllProductTemplete()
  },
  methods: {
    showTaskUnassociated(e) {
      this.associationItem.association = e
    },
    editHost(item) {
      const listHost = this.hostTempleteList.map(r => r.requirementList || [])
      const arr = listHost.reduce(function(a, b) {
        return a.concat(b)
      })
      if (arr.length) {
        this.$message.warning('当前产品下有关联的需求,请先取消关联关系')
        return
      }
      item.isEdit = !item.isEdit
    },
    addCoProduct() {
      this.showCoProduct = !this.showCoProduct
      this.productList = []
    },
    showTask(item, val) {
      this.productList = []
      this.issueList = []
      this.simpleProductId = item?.productInfo?.id || ''
      this.$set(this.form, 'productId', item?.productInfo?.id || '')
      this.$set(item, 'coTaskType', val)
      this.showCoProduct = false
      this.showHostProduct = false
    },

    toRouter(val) {
      // console.log(this.$permission(), '121212')
      // if (!this.$permission('reqm_center_list')) {
      //   this.$message.warning('当前登录账户没有查看需求的权限,请联系管理员授权')
      //   return
      // }

      const newpage = this.$router.resolve({
        path: `/reqmcenter/require/requireList`, query: {
          showDialog: true,
          queryId: val.id,
          rowTypeCode: val.typeCode,
          stateCode: val.stateCode,
          projectId: val.projectId
        }
      })
      window.open(newpage.href, '_blank')
    },
    async initAdd(requirementId) {
      const res = await issueIdeaProductRelation({
        ideaId: this.issueId,
        requirementId: requirementId
      })
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.getAllProductTemplete()
    },

    // 查询所有主办产品和辅办产品
    async getAllProductTemplete() {
      if (!this.issueId) {
        return
      }
      this.pageLoading = true
      const res = await issueIdeaProduct(
        this.issueId
      )
      this.pageLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      res.data.forEach(element => {
        element.productName = element.ideaProduct && element.productInfo ? element.productInfo.name : ''
        element.isEdit = false
        element.isHost = element.ideaProduct ? element.ideaProduct.isHost : null
        if (element.ideaProduct && element.ideaProduct.isHost) {
          element.type = '1'
        } else {
          if (element.ideaProduct && element.productInfo) {
            element.type = '2'
          } else {
            element.type = '3'
          }
        }
        // element.productId = element.productInfo ? element.ideaProduct.id : ''
      })
      // 主办
      this.hostTempleteList = res.data.filter(r => r.type == '1')

      // 协办
      this.coTempleteList = res.data.filter(r => r.type == '2')
      // 未来分组
      this.ungroupedTempleteList = res.data.filter(r => r.type == '3')
    },
    // 删除主办和辅办产品
    async deleteProduct(row) {
      const mapList = row.isHost ? this.hostTempleteList : this.coTempleteList
      const listHost = mapList.map(r => r.requirementList || [])
      const arr = listHost.reduce(function(a, b) {
        return a.concat(b)
      })
      if (arr.length) {
        this.$message.warning('当前产品下有关联的需求,请先取消关联关系')
        return
      }

      this.$confirm(`你确定要取消关联【${row.productName}】的关联关系吗?`, '取消关联', {
        confirmButtonText: '确认',
        type: 'warning',
        closeOnClickModal: false
      })
        .then(async() => {
          const { isSuccess, msg } = await issueIdeaProductDel(
            [row.ideaProduct.id]
          )
          if (!isSuccess) {
            this.$message.error(msg)
            return
          }
          this.$message.success(msg)
          this.getAllProductTemplete()
        })
        .catch(() => { })
    },
    // 添加主办/辅办产品
    async productAdd(val) {
      if (val == 1) {
        try {
          await this.$refs.formTab.validate()
        } catch (error) {
          return
        }
        this.params = [{
          ideaId: this.issueId,
          isHost: true,
          productId: this.form.hostProduct
        }]
      } else {
        try {
          await this.$refs.coRules.validate()
        } catch (error) {
          return
        }
        this.params = this.form.coProduct.map(r => ({
          ideaId: this.issueId,
          isHost: false,
          productId: r
        }))
      }

      const res = await issueIdeaProductAdd(this.params)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success(res.msg)
      this.$set(this.form, 'coProduct', [])
      this.showCoProduct = false
      this.getAllProductTemplete()
    },
    // 修改主办/辅办产品
    async editProduct(item) {
      const res = await issueIdeaProductPut({
        id: item.ideaProduct.id,
        ideaId: this.issueId,
        isHost: item.isHost,
        productId: item.productId
      })
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$set(item, 'isEdit', false)
      this.$message.success(res.msg)
      this.getAllProductTemplete()
    },
    async batchIssue(item, type) {
      const params = {
        ideaId: this.issueId,
        requirementIds: item.coIssue
      }
      const res = await issueIdeaProductRequirement(params)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success(res.msg)
      this.getAllProductTemplete()
    },
    // 下拉框弹出时，设置弹框的宽度
    setOptionWidth(event) {
      this.$nextTick(() => {
        this.selectOptionWidth = event.target.parentNode.parentNode.clientWidth + 'px'
      })
    },
    // 查产品列表
    getProducttList: debounce(async function(query) {
      try {
        this.productLoading = true
        const res = await productListByCondition({ name: query })
        this.productLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }

        const allId = this.hostTempleteList.concat(this.coTempleteList).map(r => r.productName)

        res.data.forEach(element => {
          element.disabled = allId.includes(element.name)
        })

        this.productList = res.data
      } catch (e) {
        this.productLoading = false
      }
    }, 1000),

    // 查需求列表
    getRequirementList: debounce(async function(query, productId) {
      try {
        this.requireLoading = true
        const res = await apiAlmRequirementNoPage({ name: query, productId: this.form.productId })
        this.requireLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }

        res.data.forEach(element => {
          element.disabled = element.id == this.issueId
        })
        this.issueList = res.data
      } catch (e) {
        this.requireLoading = false
      }
    }, 1000),

    async saveTask() {
      this.saveLoading = true
      const params = this.issueList.find(r => r.id == this.taskForm.relationIssue)
      params.projectId = this.taskForm.projectId
      params.ideaId = this.issueId
      const res = await apiAlmSaveIssue(params)
      this.saveLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('创建成功')
      this.$emit('success')

      this.form = {
        name: '',
        typeCode: this.typeCodeList[0].code,
        userId: this.userList[0].id
      }
      this.fileList = []
    },
    // 删除需求关系
    async deleteRelation(row) {
      this.$confirm(`你确定要删除 【${row.name}】的关联关系吗?`, '取消关联', {
        confirmButtonText: '确认',
        type: 'warning',
        closeOnClickModal: false
      })
        .then(async() => {
          const { isSuccess, msg } = await issueIdeaProductRequirementDel(
            {
              ideaId: this.issueId,
              requirementId: row.id
            }
          )
          if (!isSuccess) {
            this.$message.error(msg)
            return
          }
          this.$message.success(msg)
          this.getAllProductTemplete()
        })
        .catch(() => { })
    }

  }
}
</script>
<style lang="scss" scoped>
:deep(.el-dialog .el-dialog__body .el-form-item) {
  margin-bottom: 0px;
}
.con-issue-idea {
  font-weight: 400;
  font-size: 14px;
  padding: 0 12x;
  min-width: auto;
}
:deep(.el-button + .el-button) {
  margin: 12px 0px 12px 12px;
}
.wrapper {
  // 列表操作列的图标显示隐藏/
  .table_handler {
    display: none;
  }
  .el-table--enable-row-hover .el-table__body tr:hover > td {
    .table_handler {
      display: inline-block;
    }
  }
  // --------------------
  .shadow_bottom {
    margin: 0 -38px 16px;
    padding-bottom: 16px;
    // padding: 0 16px 16px;
    box-shadow: 0px 15px 10px -15px #ccc;
  }
  .row_host {
    padding: 10px 48px 0px 32px;
  }
  .section-host {
    margin: 1px 0;
  }
  .section-has {
    padding: 10px 0;
    .host_text {
      padding: 1px 6px;
      font-size: 12px;
      font-weight: 500;
      border-radius: 2px;
      color: #f53ba1;
      border: 1px solid #f53ba1;
      margin-left: 8px;
    }
    .host_text_co {
      padding: 1px 6px;
      font-size: 12px;
      font-weight: 500;
      border-radius: 2px;
      color: var(--sub-font-color);
      border: 1px solid var(--sub-font-color);
      margin-left: 8px;
    }

    .host_title {
      line-height: 32px;
      padding: 0 10px;
      border-radius: 5px;
      color: #202124;
      font-weight: 600;
      & a {
        color: var(--main-theme-color);
        margin: 0 5px;
        display: none;
      }
    }
    .host_title:hover {
      background: var(--tab-bg-color);
      .host_text,
      .host_text_co {
        display: none;
      }
      a {
        display: inline-block;
      }
    }
  }
  .rowLine {
    border-bottom: 1px solid var(--el-divider);
    margin: 0 -20px 5px;
  }
  .noSetting {
    color: var(--col-no-setting);
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
  }
}
.vone-table {
  :deep(.el-table__row) {
    // height: 34px;
  }
}
:deep(.name_col) {
  .cell {
    display: flex;
    div.name_icon {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: normal;
      word-break: break-all;
      flex: 1;
      display: flex;
      align-items: center;
    }
  }
}
.table_title {
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 0 8px;
  height: calc(15vh);
  .empty-svg {
    width: 50px;
    height: 40px;
  }
}
</style>
