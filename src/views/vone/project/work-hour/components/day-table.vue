<template>
  <div v-loading="tableLoading">
    <el-form ref="dayForm" :model="dayForm" :inline-message="false">
      <main style="height: calc(100vh - 270rem)">
        <vxe-table
          ref="dayTable"
          class="vone-vxe-table"
          border
          resizable
          height="auto"
          show-overflow="tooltip"
          :empty-render="{ name: 'empty' }"
          :data="dayForm.tableData"
          :column-config="{ minWidth: '120px' }"
          :checkbox-config="{ reserve: true }"
          row-id="id"
        >
          <vxe-column title="用户" field="filledBy" min-width="150">
            <template #default="{ row, rowIndex }">
              <span v-if="row.verified || (entry == 'fast' && row.echoMap)">
                <vone-user-avatar
                  :avatar-path="row.echoMap.filledBy.avatarPath"
                  :avatar-type="row.echoMap.filledBy.avatarType"
                  :name="row.echoMap.filledBy.name"
                />
              </span>
              <el-form-item
                v-else
                label-width="0"
                :prop="'tableData.' + rowIndex + '.filledBy'"
                :rules="{
                  required: true,
                  message: '请选择用户',
                  trigger: 'change',
                }"
              >
                <vone-remote-user
                  v-model:value="row.filledBy"
                  placeholder="请选择"
                />
              </el-form-item>
            </template>
          </vxe-column>

          <vxe-column v-if="entry == 'fast'" title="项目" width="120">
            <template #default="{ row, rowIndex }">
              <span v-if="row.verified || (entry == 'fast' && row.echoMap)">
                <span v-if="row.echoMap && row.echoMap.projectId">
                  {{ row.echoMap.projectId.name }}
                </span>
              </span>
              <el-form-item
                v-else
                label-width="0"
                :prop="'tableData.' + rowIndex + '.projectId'"
                :rules="{
                  required: true,
                  message: '请选择项目',
                  trigger: 'change',
                }"
              >
                <el-select
                  v-model="row.projectId"
                  filterable
                  style="width: 100%"
                  @change="(e) => projectChange(e, row)"
                >
                  <el-option
                    v-for="i in allProjectList"
                    :key="i.id"
                    :label="i.name"
                    :value="i.id"
                  />
                </el-select>
              </el-form-item>
            </template>
          </vxe-column>

          <vxe-column title="工作项类型" field="type" min-width="120">
            <template #default="{ row, rowIndex }">
              <span v-if="row.verified || (entry == 'fast' && row.echoMap)">
                <span v-if="row.type">
                  <span>
                    <i
                      :class="typeCodeMap[row.type].icon"
                      :style="{ color: typeCodeMap[row.type].color }"
                    />
                    {{ typeCodeMap[row.type].label }}
                  </span>
                </span>
              </span>

              <el-row v-else class="typeselect">
                <el-form-item
                  :key="row.type"
                  label-width="0"
                  :prop="'tableData.' + rowIndex + '.type'"
                  :rules="{
                    required: true,
                    message: '请选择类型',
                    trigger: 'change',
                  }"
                >
                  <el-select
                    v-model="row.type"
                    class="select-tyle"
                    placeholder="请选择"
                    @change="(e) => getworkItemSearch(e, row, null)"
                  >
                    <el-option
                      v-for="op in typeCodeList"
                      :key="op.value"
                      :label="op.label"
                      :value="op.value"
                    >
                      <i :class="op.icon" :style="{ color: op.color }" />
                      {{ op.label }}
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-row>
            </template>
          </vxe-column>
          <vxe-column title="工作项" field="bizId" min-width="150">
            <template #default="{ row, rowIndex }">
              <span v-if="row.verified || (entry == 'fast' && row.echoMap)">
                <span v-if="row.echoMap && row.echoMap.biz">
                  {{ row.echoMap.biz.name }}
                </span>
              </span>
              <el-row v-else class="typeselect">
                <el-form-item
                  :key="row.bizId"
                  label-width="0"
                  :prop="'tableData.' + rowIndex + '.bizId'"
                  :rules="{
                    required: true,
                    message: '请选择工作项',
                    trigger: 'change',
                  }"
                >
                  <el-select
                    v-model="row.bizId"
                    filterable
                    class="select-option"
                    placeholder="请输入编号或标题搜索"
                    :remote-method="(query) => remoteMethod(query, row)"
                    :loading="loading"
                    no-match-text="暂未查询到匹配数据,请重新输入"
                    remote
                    clearable
                  >
                    <virtual-list
                      :style="{ 'max-height': '258px', 'overflow-y': 'auto' }"
                      data-key="id"
                      :extra-props="{
                        label: 'name',
                        value: 'id',
                      }"
                      :data-sources="row.workItem || []"
                      :data-component="itemConponent"
                    />
                  </el-select>
                </el-form-item>
              </el-row>
            </template>
          </vxe-column>
          <vxe-column title="工时h" field="duration" min-width="150">
            <template v-slot:header="{ column }">
              <div>
                工时h (<span
                  :style="{
                    color: getColor(
                      Number(getSum(column.property) + otherDuration)
                    ),
                  }"
                  ><span>{{
                    Number(getSum(column.property) + otherDuration)
                  }}</span>
                  /
                  <span>{{
                    configInfo["NORM_HOURS_OF_EVERY_DAY"].value
                  }}</span> </span
                >)
              </div>
            </template>
            <template #default="{ row, rowIndex }">
              <span v-if="row.verified">
                {{ row.duration }}
              </span>
              <el-form-item
                v-else
                label-width="0"
                :prop="'tableData.' + rowIndex + '.duration'"
                :rules="[
                  { required: true, message: '请输入工时', trigger: 'blur' },
                  { validator: checkTime, trigger: ['blur', 'change'] },
                ]"
              >
                <el-input
                  v-model.number="row.duration"
                  type="number"
                  class="number-input"
                  placeholder="请输入"
                  @change="numberChange($event, row)"
                />
              </el-form-item>
            </template>
          </vxe-column>
          <vxe-column title="描述" field="description" min-width="150">
            <template #default="{ row, rowIndex }">
              <span v-if="row.verified">
                {{ row.description }}
              </span>
              <el-form-item
                v-else
                label-width="0"
                :prop="'tableData.' + rowIndex + '.description'"
                :rules="[
                  { required: false, message: '请输入描述', trigger: 'blur' },
                ]"
              >
                <el-input v-model="row.description" placeholder="请输入" />
              </el-form-item>
            </template>
          </vxe-column>

          <vxe-column title="操作" fixed="right" align="left" width="70">
            <template #default="{ row, rowIndex }">
              <el-button
                :disabled="
                  row.verified || !$permission('project_man_hour_del')
                    ? true
                    : false
                "
                :icon="elIconApplicationDelete"
                type="text"
                size="small"
                @click.prevent="deleteRow(row, rowIndex)"
              />
            </template>
          </vxe-column>
        </vxe-table>
      </main>
    </el-form>
  </div>
</template>

<script>
import dayjs from "dayjs";
const calendar = require("dayjs/plugin/calendar");
dayjs.extend(calendar);
import {
  workItemSearch,
  deleteWorkingHoursInfo,
  getWorkingHoursDay,
  getWorkingHoursDayAll,
} from "@/api/vone/manhour/index";
import storage from "store";
import projectMixin from "@/mixin/project-list";
import VirtualList from "vue-virtual-scroll-list";
import itemConponent from "./item-conponent.vue";
import _ from "lodash";

export default {
  components: {
    VirtualList,
    itemConponent,
  },
  mixins: [projectMixin],
  props: {
    configInfo: {
      type: Object,
      default: () => {},
    },
    typeCodeList: {
      type: Array,
      default: () => [],
    },
    dayDate: {
      type: String,
      default: "",
    },
    entry: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      itemConponent: itemConponent,
      loading: false,
      dayForm: {
        tableData: [],
      },
      tableLoading: false,
      typeCodeMap: {},
      otherDuration: 0,
    };
  },

  computed: {
    getColor() {
      return function (count) {
        if (count < this.configInfo["NORM_HOURS_OF_EVERY_DAY"].value) {
          return "#DB2C3A"; // √
        } else if (count == this.configInfo["NORM_HOURS_OF_EVERY_DAY"].value) {
          return "#F7A500"; // 当数量等于8时显示黄色
        } else {
          return "#00BF80"; // 当数量大于8时显示绿色
        }
      };
    },
    // filter() {
    //   return function(input, option) {
    //     input = input.toLowerCase()
    //     const key = option.code || ''
    //     let name = option.name || ''
    //     name = name.toLowerCase()
    //     return key.includes(input) || name.includes(input)
    //   }
    // }
  },
  watch: {
    dayDate: {
      deep: true,

      handler(val) {
        this.getTableData();
      },
    },
  },
  mounted() {
    if (this.entry == "fast") {
      this.getAllInfo();
    } else {
      this.getTableData();
    }
    this.typeCodeMap = this.typeCodeList.reduce(
      (r, v) => (r[v.value] = v) && r,
      {}
    );
  },
  methods: {
    remoteMethod: _.debounce(function (query, row, date) {
      this.loading = true;
      if (query != "") {
        this.params.search = query;
        this.getworkItemSearch(row.type, row, "noSet");
      }
    }, 1000),

    checkTime(rule, value, callback) {
      if (typeof value !== "number") {
        callback(new Error("工时为数字"));
      } else if (value <= 0) {
        callback(new Error("工时应大于0"));
      } else if (
        Number(value + this.otherDuration) >
        Number(this.configInfo["MAX_HOURS_OF_EVERY_DAY"]?.value)
      ) {
        callback(
          new Error(
            `工时不大于${this.configInfo["MAX_HOURS_OF_EVERY_DAY"]?.value}`
          )
        );
      } else {
        callback();
      }
    },
    getSum(field) {
      return (
        (this.$refs.dayTable &&
          this.$refs.dayTable.tableData.reduce(
            (total, row) => total + (row[field] ? row[field] : 0),
            0
          )) ||
        0
      );
    },
    async getTableData() {
      this.tableLoading = true;
      const userInfo = storage.get("user");
      const res = await getWorkingHoursDay(
        this.dayDate,
        this.dayDate,
        [userInfo.id],
        this.$route.params.id
      );

      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }

      this.otherDuration =
        res.data.hoursDays?.find((r) => r.day == this.dayDate)?.otherDuration ||
        0;
      if (res.data && res.data.userHistory.length) {
        this.handleData(res.data.userHistory);
      }

      this.tableLoading = false;
    },
    async getAllInfo() {
      const userInfo = storage.get("user");
      const res = await getWorkingHoursDayAll(this.dayDate, this.dayDate, [
        userInfo.id,
      ]);
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      if (res.data && res.data.userHistory.length) {
        this.handleData(res.data.userHistory);
      }
    },
    handleData(list) {
      list.forEach((element) => {
        element["fillingTime"] = element.filledInfos[0]?.fillingTime;
        element["id"] = element.filledInfos[0]?.id;
        element["verified"] = element.filledInfos[0]?.verified;
        element["type"] = element.type?.code;

        if (!element.verified) {
          this.getworkItemSearch(element.type, element, "noSet");
        }
      });

      this.dayForm.tableData = list;
    },
    async projectChange(e, row) {
      if (!row.type) {
        return;
      }
      row["bizId"] = "";
      this.loading = true;
      const res = await workItemSearch({
        sourceTypes: [row.type],
        rateProgress: this.configInfo?.BIZ_STATUS?.value,
        projectId: e,
      });
      if (!res.isSuccess) {
        return this.$message.warning(res.msg);
      }
      const day = this.$refs.dayTable.tableData
        .filter((r) => r.bizId)
        .map((j) => j.bizId);
      res.data.forEach((element) => {
        element.disabled = !!day.includes(element.id);
      });

      row["workItem"] = res.data;
      this.loading = false;
    },
    async getworkItemSearch(e, row, noSet) {
      this.loading = true;
      if (noSet != "noSet") {
        row["bizId"] = "";
      }

      const res = await workItemSearch({
        sourceTypes: [e],
        rateProgress: this.configInfo?.BIZ_STATUS?.value,
        projectId: this.entry == "fast" ? row.projectId : this.$route.params.id,
        limit: "-1",
      });

      if (!res.isSuccess) {
        return this.$message.warning(res.msg);
      }
      const day = this.$refs.dayTable.tableData
        .filter((r) => r.bizId)
        .map((j) => j.bizId);

      res.data.forEach((element) => {
        element.disabled = !!day.includes(element.id);
      });

      row["workItem"] = res.data;
      this.loading = false;
    },
    addChild() {
      const userInfo = storage.get("user");
      const parameter = {
        filledBy: userInfo.id,
        type: "",
        name: "",
        total: 0,
        description: "",
        duration: null,
        verified: false,
      };

      this.params = {};
      this.dayForm.tableData.push(parameter);
    },
    async deleteRow(row, e) {
      if (!row.filledInfos || !row.filledInfos.length) {
        this.dayForm.tableData.splice(e, 1);
        return;
      }

      try {
        await this.$confirm(`确定删除此条工时记录吗?`, "删除", {
          type: "warning",
          closeOnClickModal: false,
        });
        const res = await deleteWorkingHoursInfo([row.id]);
        if (!res.isSuccess) {
          this.$message.warning(res.msg);
          return;
        } else {
          this.$message.success("删除成功");
          if (this.entry == "fast") {
            this.getAllInfo();
          } else {
            this.getTableData();
          }
        }
      } catch (e) {
        return;
      }
    },
    numberChange(e, row, item) {
      if (e == "") {
        row[item] = null;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
:deep(.vxe-table .el-form-item) {
  margin: 0 !important;
}
.number-input {
  :deep(input::-webkit-inner-spin-button) {
    appearance: none !important;
  }

  :deep(input::-webkit-outer-spin-button) {
    appearance: none !important;
  }

  input[type="number"] {
    appearance: textfield;
  }
}
</style>
