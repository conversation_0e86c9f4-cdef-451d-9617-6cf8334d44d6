import request from '@/utils/axios-api'
// 菜单查询
export function apiBaseMenuList(data) {
  return request({
    url: '/api/base/base/menu/page',
    method: 'post',
    data
  })
}

// 菜单新增
export function apiBaseMenuAddApp(data) {
  return request({
    url: '/api/base/base/menu',
    method: 'POST',
    data
  })
}
// 根据ID查询菜单
export function apiBaseMenuGetById(id) {
  return request({
    url: `/api/base/base/menu/${id}`,
    method: 'GET'
  })
}

// 查询系统下所有菜单
export function apiBaseMenuTree(data) {
  return request({
    url: '/api/base/base/menu/tree',
    method: 'POST',
    data
  })
}

// 菜单修改
export function apiBaseMenuEditApp(data) {
  return request({
    url: '/api/base/base/menu',
    method: 'PUT',
    data
  })
}
// 菜单删除
export function apiBaseMenuDelApp(data) {
  return request({
    url: '/api/base/base/menu',
    method: 'DELETE',
    data
  })
}

// 菜单功能查询
export function apiBaseMenuFunctionPage(data) {
  return request({
    url: '/api/base/base/function/page',
    method: 'POST',
    data
  })
}

// 菜单功能新增
export function apiBaseMenuAddFunction(data) {
  return request({
    url: '/api/base/base/function',
    method: 'POST',
    data
  })
}

// 菜单功能修改
export function apiBaseMenuEditFunction(data) {
  return request({
    url: '/api/base/base/function',
    method: 'PUT',
    data
  })
}

// 菜单功能权限删除
export function apiBaseMenuFunctionDel(data) {
  return request({
    url: '/api/base/base/function',
    method: 'DELETE',
    data
  })
}

// 根据ID查询菜单功能权限
export function apiBaseMenuFunctionGetById(id) {
  return request({
    url: `/api/base/base/function/${id}`,
    method: 'GET'
  })
}

// 查询所有菜单
export function apiBaseMenuNoPage(data) {
  return request({
    url: '/api/base/base/menu/query',
    method: 'POST',
    data
  })
}

// 查询所有菜单，不分页
export function apiVaBaseMenuList(data) {
  return request({
    url: '/api/base/base/function/query',
    method: 'POST',
    data
  })
}

