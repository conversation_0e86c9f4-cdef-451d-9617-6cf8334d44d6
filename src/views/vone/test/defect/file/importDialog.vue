<template>
  <div>
    <el-dialog
      title="导入缺陷"
      width="40%"
      v-model:value="visible"
      :before-close="onClose"
      :close-on-click-modal="false"
    >
      <el-form ref="form" :model="form" :rules="rules">
        <el-form-item prop="file">
          <el-upload
            class="upload-demo"
            action
            :limit="1"
            :auto-upload="true"
            :multiple="false"
            :http-request="httpRequest"
            :before-upload="beforeUpload"
          >
            <template v-slot:trigger>
              <el-button
                size="small"
                type="primary"
                :icon="elIconApplicationFile"
                >选取文件</el-button
              >
            </template>
            <el-button type="text" class="ml-3" @click="getDownload"
              >下载批量上传模板</el-button
            >
            <template v-slot:tip>
              <div class="el-upload__tip">只能上传xls类型文件</div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>

      <template v-slot:footer>
        <div class="dialog-footer">
          <el-button @click="onClose">取消</el-button>
          <el-button
            type="primary"
            :icon="elIconEditImport"
            :loading="upLoading"
            @click="submit"
            >上传</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from "../../../../../utils/gogocodeTransfer";
import { downloadBugTemplate, apiAlmBugLoad } from "@/api/vone/project/defect";
import { download } from "@/utils";

export default {
  components: {
    // errorImport
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: undefined,
    },
    importUrl: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      form: {},
      upLoading: false,
      hasError: false,
      dialogFormVisible: false,
      errorImportParam: { visible: false },
      rules: {},
    };
  },
  methods: {
    onClose() {
      $emit(this, "update:visible", false);
      this.$refs.form.resetFields();
    },
    httpRequest(file) {
      this.form["file"] = file.file;
    },
    // 下载批量导入模板
    async getDownload() {
      try {
        download(
          `缺陷批量导入模版.xls`,
          await downloadBugTemplate(this.$route.params.id)
        );
      } catch (e) {
        this.$message.error("模板下载失败");
        return;
      }
    },
    async submit() {
      try {
        this.upLoading = true;
        const res = await apiAlmBugLoad({
          file: this.form.file,
        });
        this.upLoading = false;
        if (!res.isSuccess) {
          this.$message.warning(res.msg);
          return;
        }

        $emit(this, "success");
        this.onClose();

        if (res.extra) {
          $emit(this, "hasError", res);
          this.$message.warning(res.msg);
        } else {
          this.$message.success(res.msg);
        }
      } catch (e) {
        this.upLoading = false;
      }
    },
    // 文件验证规则
    beforeUpload(file) {
      const isXls = file.name.substr(file.name.lastIndexOf(".") + 1) === "xls";
      // const size = file.size / 1024 <= 5
      if (!isXls) {
        this.$message.error("只支持xls类型文件!");
      }
      // if (!size) {
      //   this.$message.error('最大支持5M的文件!')
      // }

      return isXls;
    },
  },
  emits: ["update:visible", "hasError", "success"],
};
</script>
