<template>
  <!-- 根据列属性排序 -->
  <el-dropdown :hide-on-click="false" trigger="click" placement="bottom-start" @command="(e) => e && e()">
    <span class="label">
      <span class="weak-title">排序</span>
      {{ columnSortName }}
      <span class="labe-icon">
        <i v-if="sortObj.order == 'descending'" class="iconfont el-icon-direction-arrowFall active" />
        <i v-if="sortObj.order == 'ascending'" class="iconfont el-icon-direction-arrow-rise active" />
      </span>
    </span>
    <el-dropdown-menu slot="dropdown" class="column-el-dropdown column-sort">
      <div class="sort-wrap">
        <el-dropdown-item v-for="(col) in columns.filter(r=>r.field !='tag')" v-show="(col.type != 'checkbox' && col.title != '操作')" :key="col.field || col.id">
          <div class="sort-item">
            <span :class="['sort-title', sortObj.sort == col.field ? 'active' : '']">{{ col.title }}</span>
            <span class="sort-icon">
              <i class="iconfont el-icon-direction-arrowFall" :class="[(sortObj.sort == col.field && sortObj.order == 'descending') ? 'active' : '']" @click="changeSort(col, 'descending')" />
              <i class="iconfont el-icon-direction-arrow-rise" :class="[(sortObj.sort == col.field && sortObj.order == 'ascending') ? 'active' : '']" @click="changeSort(col, 'ascending')" />
            </span>
          </div>
        </el-dropdown-item>
      </div>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
export default {
  props: {
    columns: {
      type: Array,
      default: () => []
    },
    sortObj: {
      type: Object,
      default: () => {}
    }

  },
  data() {
    return {
      columnSortName: '未排序'
      // sortObj: { } // 排序对象

    }
  },
  watch: {
    sortObj: {
      handler(val) {
        if (!val) return
        this.columnSortName = this.columns.find(r => r.field == val.sort)?.title || '未排序'
      },
      immediate: true
    }
  },
  mounted() {

  },

  methods: {
    // 根据列sort进行数据排序
    changeSort(col, type) {
      const flag = type == this.sortObj.order && col.field == this.sortObj.sort
      this.columnSortName = flag ? '未排序' : col.title
      const sortObj = {
        order: flag ? '' : type,
        sort: flag ? '' : col.field
      }
      this.$emit('changeSort', sortObj)
    }
  }

}
</script>

<style lang="scss" scoped>
@use '../index.scss' as *;
.weak-title {
  font-size: 12px;
  color: var(--font-second-color);
  padding-right: 4px;
}

</style>
<style lang="scss">
@use '../index-global.scss' as *;
</style>
