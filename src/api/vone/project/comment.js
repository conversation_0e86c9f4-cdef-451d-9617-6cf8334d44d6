import request from '@/utils/axios-api'

/**
 * @desc 模糊搜索全部工作项
 * @param {string} search
 */
export function getworkitem(data) {
  return request({
    url: '/api/alm/alm/detection/work_item/search',
    method: 'post',
    data
  })
}
// 获取当前资源下评论列表
export function getcomment(data) {
  return request({
    url: '/api/alm/alm/detection',
    method: 'get',
    data
  })
}
// 发送评论
export function sendcomment(data) {
  return request({
    url: '/api/alm/alm/detection',
    method: 'post',
    data
  })
}
// 编辑评论
export function editcomment(data) {
  return request({
    url: '/api/alm/alm/detection',
    method: 'put',
    data
  })
}
// 删除评论
export function deletecomment(id) {
  return request({
    url: `/api/alm/alm/detection/${id}`,
    method: 'DELETE'
  })
}
// 根据项目id验证是否在该项目中
export function getProjecCheck(data) {
  return request({
    url: '/api/alm/alm/project/auth/check',
    method: 'get',
    data
  })
}
// 获取关注新增
export function followPost(data) {
  return request({
    url: '/api/alm/issueFocus/focus',
    method: 'post',
    data
  })
}
// 取消关注
export function followClear(data) {
  return request({
    url: '/api/alm/issueFocus/clear',
    method: 'post',
    data
  })
}
// 关注列表
export function followList(data) {
  return request({
    url: '/api/alm/issueFocus/getFocus',
    method: 'post',
    data
  })
}
