<template>
  <div class="tree">
    <el-tree
      ref="tree"
      class="filter-tree"
      :data="docList"
      node-key="id"
      highlight-current
      default-expand-all
      :current-node-key="currentDoc"
      :expand-on-click-node="false"
      @node-click="nodeClick"
    >
      <template v-slot="{ node, data }">
        <span class="custom-tree-node">
          <span class="text-over">{{ data.name }}</span>
          <span class="tree-operation">
            <el-icon class="iconfont"><el-icon-tips-plus-circle /></el-icon>
            <el-icon class="iconfont"><el-icon-application-edit /></el-icon>
            <el-icon class="iconfont" style="padding-right: 8px"
              ><el-icon-application-delete
            /></el-icon>
          </span>
        </span>
      </template>
    </el-tree>
  </div>
</template>

<script>
import {
  TipsPlusCircle as ElIconTipsPlusCircle,
  ApplicationEdit as ElIconApplicationEdit,
  ApplicationDelete as ElIconApplicationDelete,
} from "@element-plus/icons-vue";
import { $on, $off, $once, $emit } from "../../../utils/gogocodeTransfer";
export default {
  components: {
    ElIconTipsPlusCircle,
    ElIconApplicationEdit,
    ElIconApplicationDelete,
  },
  props: {
    docList: {
      type: Array,
      default: () => [],
    },
    currentDoc: {
      type: String,
      default: null,
    },
  },
  data() {
    return {};
  },
  computed: {},
  watch: {
    currentDoc: {
      handler: async function (val, oldVal) {
        if (val != "" && val != undefined) {
          this.$nextTick(() => {
            this.$refs.tree.setCurrentKey(val);
          });
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    operationTreeNode(row) {
      if (row.type == "add") {
        $emit(this, "addDoc", row.data.id);
      } else if (row.type == "edit") {
        $emit(this, "editDoc", row.data);
      } else {
        $emit(this, "deleteDoc", row.data.id);
      }
    },
    nodeClick(data) {
      if (data.id == this.currentDoc) return;
      $emit(this, "clickNode", data);
      this.$router.push({
        name: "wiki_document",
        params: {
          spaceId: this.$route.params.spaceId,
          docId: data.id,
        },
      });
    },
  },
  emits: ["addDoc", "editDoc", "deleteDoc", "clickNode"],
};
</script>

<style lang="scss" scoped>
.tree {
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
    width: 90%;
    .tree-operation {
      opacity: 0;
    }
  }
  .el-button {
    padding: 0 4px;
    min-width: 40px;
    line-height: 26px;
  }
  .tree-operation {
    i {
      color: var(--font-second-color);
    }
  }
  .custom-tree-node:hover {
    .tree-operation {
      opacity: 1;
    }
  }
  .custom-tree-node {
    .tree-operation {
      i:hover {
        color: var(--main-theme-color);
      }
    }
  }
  :deep(
      .el-tree--highlight-current
        .el-tree-node.is-current
        > .el-tree-node__content
    ) {
    background-color: #f5f6fa;
  }
}
</style>

<style lang="scss" scoped>
.wiki-dropdown {
  top: -3px;
  .el-dropdown-menu__item--divided {
    padding: 0 16px;
  }
}
</style>
