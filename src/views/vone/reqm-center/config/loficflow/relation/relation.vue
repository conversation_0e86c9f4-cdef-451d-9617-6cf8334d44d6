<template>
  <div>
    <div v-for="item in list" :key="item.key" class="assign-node">
      <header>
        {{ item.name }}
        <span class="count">
          {{ item.value.length }}
        </span>
        <!-- <a v-if="item.value.length > 2" class="moreRow">
              更多
            </a> -->
      </header>
      <section>
        <div
          v-for="i in item.value.slice((currentPage - 1) * 2, currentPage * 2)"
          :key="i.id"
        >
          <span class="rowName">
            <i
              :style="{ color: i.echoMap.typeCode && i.echoMap.typeCode.color }"
              :class="[
                'iconfont',
                `${i.echoMap.typeCode && i.echoMap.typeCode.icon}`,
              ]"
            />

            <!-- <svg class="icon" aria-hidden="true">
                  <use xlink:href="#el-icon-icon-yixiang" />
                </svg> -->
            <a @click="showInfo(i)">
              {{ i.code }}

              {{ i.name }}
            </a>
          </span>

          <el-tag>{{ getStateInfo(i) }}</el-tag>

          <span>
            <vone-user-avatar
              :avatar-path="getUserInfo(i) ? getUserInfo(i).avatarPath : ''"
              :name="getUserInfo(i) ? getUserInfo(i).name : ''"
            />
          </span>
        </div>
      </section>

      <el-pagination
        layout="prev, pager, next"
        small
        :page-size="2"
        class="pagination"
        :current-page="currentPage"
        :total="item.value.length"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
// import Vue from 'vue'
// import VueRouter from 'vue-router'

// Vue.use(VueRouter)
export default {
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      currentPage: 1,
    }
  },
  computed: {
    getUserInfo() {
      return function (row) {
        return row?.leadingBy && row?.echoMap?.leadingBy
      }
    },
    getStateInfo() {
      return function (row) {
        return row?.stateCode && row?.echoMap?.stateCode?.name
      }
    },
  },
  mounted() {},
  methods: {
    async showInfo(row) {
      const projectKey = row.echoMap?.projectId?.code
      const projectTypeCode = row.echoMap?.projectId?.typeCode
      const projectId = row.echoMap?.projectId?.id
      const pathStr = '/project/issue'

      const newpage = await this.$router.resolve({
        path: `${pathStr}/${projectKey}/${projectTypeCode}/${projectId}`,
        query: {
          showDialog: true,
          queryId: row?.id,
          rowTypeCode: row?.typeCode,
          stateCode: row?.stateCode,
          projectId: projectId,
        },
      })

      const winOpen = window.open(newpage.href, '_blank')
      winOpen.opener = null
    },
    handleCurrentChange(val) {
      this.currentPage = val
    },
  },
}
</script>

<style lang="scss" scoped>
.assign-node {
  box-sizing: border-box;
  width: 378px;
  height: 125px;
  background-color: #fff;
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.08);
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  header {
    background-color: #eaedf0;
    padding: 0 12px;
    height: 36px;
    line-height: 36px;
    .moreRow {
      float: right;
      font-size: 12px;
      color: var(--main-theme-color);
    }
    .count {
      font-size: 12px;
      font-weight: 600;
    }
  }
  section {
    div {
      // background-color: #F5F7FA;
      padding: 0 12px;
      height: 26px;
      line-height: 26px;
      display: flex;
      gap: 10px;
      justify-content: space-between;
      align-items: center;
      .rowName {
        width: 250px;
        overflow: hidden; // 溢出隐藏
        white-space: nowrap; // 强制一行
        text-overflow: ellipsis; // 文字溢出显示省略号
        a {
          color: var(--main-theme-color);
        }
      }
    }
  }
}
.pagination {
  position: absolute;
  right: 6px;
  bottom: 6px;
}
:deep() {
  .el-pagination .el-pager li:not(.disabled).active {
    background-color: #fff;
    color: #2c2e36;
    border: 1px solid var(--input-border-color);
  }
  .el-pagination.is-background .btn-prev,
  .el-pagination.is-background .btn-next {
    background: none;
  }
}
</style>
