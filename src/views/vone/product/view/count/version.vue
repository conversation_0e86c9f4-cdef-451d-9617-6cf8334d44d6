<template>
  <vone-echarts-card :title="'版本计划'" :height="'430px'">
    <div v-if="stepData.length" class="vone-version-step">
      <el-steps>
        <el-step
          v-for="(item, index) in stepData"
          :key="index"
          :description="item.planRtime"
        >
          <template v-slot:title>
            <div>
              <el-tooltip :content="item.name" placement="top">
                <span>
                  {{
                    stepData.length > 5
                      ? item.name.substring(0, 5) + "..."
                      : item.name
                  }}
                </span>
              </el-tooltip>
            </div>
          </template>
          <template v-slot:icon>
            <div style="width: 100%; height: 100%">
              <div v-if="item.stateId == '1'" class="icons unstart">
                <span />
              </div>
              <div v-if="item.stateId == '2'" class="icons planning">
                <span />
              </div>
              <div v-if="item.stateId == '3'" class="icons success">
                <el-icon class="iconfont"
                  ><el-icon-diedai-yiwancheng-light
                /></el-icon>
              </div>
              <div v-if="item.stateId == '4'" class="icons error">
                <el-icon class="iconfont"
                  ><el-icon-tips-exclamation-circle-fill
                /></el-icon>
              </div>
            </div>
          </template>
        </el-step>
      </el-steps>
    </div>
    <vone-empty v-else />
  </vone-echarts-card>
</template>

<script>
import {
  DiedaiYiwanchengLight as ElIconDiedaiYiwanchengLight,
  TipsExclamationCircleFill as ElIconTipsExclamationCircleFill,
} from "@element-plus/icons-vue";
import dayjs from "dayjs";
import { getProductVersionList } from "@/api/vone/product/index";
export default {
  components: {
    ElIconDiedaiYiwanchengLight,
    ElIconTipsExclamationCircleFill,
  },
  data() {
    return {
      stepData: [],
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      getProductVersionList({
        productId: this.$route.params.productId,
      }).then((res) => {
        if (res.isSuccess) {
          var arr = [];
          res.data.forEach((item) => {
            item.planRtime
              ? (item.planRtime = dayjs(item.planRtime).format("YYYY-MM-DD"))
              : "";
            arr.push(item);
          });
          this.stepData = arr;
        } else {
          this.$message.warning(res.msg);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.vone-version-step {
  margin-top: 13%;
  padding: 0 30px;
  :deep(.el-tooltip) {
    color: #202123;
  }
  :deep(.el-step__description) {
    padding-right: 0 !important;
    position: absolute;
    top: -20px;
    width: 100%;
  }
  .el-step {
    position: relative;
  }
  .today {
    position: absolute;
    text-align: center;
    top: 0px;
    left: 50%;
    a {
      display: inline-block;
      width: 15px;
      height: 15px;
      border: 2px solid;
      border-radius: 50%;
      margin-top: 10px;
      background: var(--main-bg-color, #fff);
    }
  }
}
.iconImg {
  width: 20px;
  height: 20px;
}
:deep(.el-step__icon) {
  border: 0;
}
:deep(.el-step__description) {
  width: 150px !important;
}
.icons {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.unstart {
  background: #adb0b8;
  text-align: center;
  line-height: 22px;
  span {
    display: inline-block;
    width: 9px;
    height: 9px;
    border-radius: 50%;
    background: var(--main-bg-color, #fff);
  }
}
.planning {
  background: #ffbf47;
  text-align: center;
  line-height: 22px;
  span {
    display: inline-block;
    width: 9px;
    height: 9px;
    border-radius: 50%;
    background: var(--main-bg-color, #fff);
  }
}
.success .iconfont {
  color: #3cb540;
  font-size: 24px;
}
.error .iconfont {
  color: #fa6b57;
  font-size: 24px;
}
.custom-theme-dark {
  .vone-version-step:deep(.el-tooltip) {
    color: #e6e9f0;
  }
}
</style>
