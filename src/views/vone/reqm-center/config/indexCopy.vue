<template>
  <vone-graph-group ref="graph" :graph-data="graphData" />
  <!-- <svg-icon icon-class="idea-graph" style="width: calc(100vw - 100px);height: calc(100vh - 94px);" /> -->
</template>

<script>
import {
  getRelationDevData,
  getRelationTasks,
  getRelationTestData,
  getRequiredRelation,
} from '@/api/vone/reqmcenter/require'
export default {
  data() {
    return {
      graphData: [
        {
          id: 'requirement',
          label: '需求',
          attrs: [],
        },
        {
          id: 'task',
          label: '规划',
          attrs: [],
        },
        {
          id: 'development',
          label: '开发',
          attrs: [],
        },
        {
          id: 'testing',
          label: '测试',
          attrs: [],
        },
        {
          id: 'production',
          label: '投产',
          attrs: [],
        },
      ],
      issueData: {}, // 需求数据
      taskData: {}, // 任务数据
      testData: {}, // 测试数据
      devData: {}, // 开发数据
    }
  },
  created() {
    this.getIssueData()
  },
  methods: {
    // 查询需求相关数据
    async getIssueData() {
      const { requireId } = this.$route.params
      const res = await getRequiredRelation(requireId)
      if (res.isSuccess) {
        const chidren =
          res.data?.sons?.length > 0
            ? [res.data.requirement, ...res.data.sons]
            : [res.data.requirement]
        // 设置连线
        this.graphData[0].attrs = chidren.map((ele, i) => {
          ele.key = 'requirement_' + i
          ele.types = 'requirement'
          return ele
        })

        // 查询相关任务
        await this.getTasksData(res.data?.issueCodeList)
        // 查询开发数据
        await this.getDevData(res.data?.issueCodeList)
        // 查询测试数据
        await this.getTestData(res.data?.issueCodeList)
      }
    },
    // 查询相关任务数据
    async getTasksData(issueCodes = []) {
      const { requireId } = this.$route.params
      const params = {
        requireId,
        issueCodes,
      }
      const res = await getRelationTasks(params)
      if (res.isSuccess) {
        const projectPlan = res.data.projectPlan
        if (projectPlan) {
          projectPlan.typeMap = projectPlan.type
          delete projectPlan.type
        }
        const taskList = res.data.taskList
        const chidren =
          taskList?.length > 0
            ? [projectPlan, ...taskList]
            : projectPlan
            ? [projectPlan]
            : []
        this.graphData[1].attrs =
          chidren.length > 0
            ? chidren.map((ele, i) => {
                ele.key = 'task_' + i
                ele.types = i === 0 ? 'iteration' : 'requirement'
                return ele
              })
            : [
                {
                  key: 'task_0',
                  types: 'placeholder',
                },
              ]
        // 设置连接迭代模块的连线数据
        this.graphData[0].attrs[0].relation =
          taskList?.length > 0
            ? taskList?.map((ele, i) => {
                return {
                  key: 'task_' + (i + 1),
                  nodeId: 'task',
                }
              })
            : [
                {
                  key: 'task_0',
                  nodeId: 'task',
                  lineType: 'placeholder',
                },
              ]
        // 设置连接开发模块的连线数据
        this.graphData[1].attrs[0].relation = [
          {
            key: 'development_0',
            nodeId: 'development',
            lineType: 'placeholder',
          },
        ]
      }
    },
    // 查询相关开发数据
    async getDevData(issueCodes = []) {
      const { requireId } = this.$route.params
      const params = {
        requireId,
        issueCodes,
      }
      const res = await getRelationDevData(params)
      if (res.isSuccess) {
        // 分支数据
        const branchList =
          res.data.branchList?.map((ele, i) => (ele.types = 'branch') && ele) ??
          []
        // 代码库数据
        const repository =
          res.data.codeRepository?.map(
            (ele, i) => (ele.types = 'repositery') && ele
          ) ?? []
        const chidren = [...repository, ...branchList].map(
          (ele, i) => (ele.key = 'development_' + i) && ele
        )
        // 设置占位数据
        this.graphData[2].attrs =
          chidren.length > 0
            ? chidren
            : [
                {
                  key: 'development_0',
                  types: 'placeholder',
                },
              ]
        // 设置连接测试模块的连线数据
        this.graphData[2].attrs[0].relation = [
          {
            key: 'testing_0',
            nodeId: 'testing',
            lineType: 'placeholder',
          },
        ]
      }
    },
    // 查询相关测试数据
    async getTestData(issueCodes = []) {
      const { requireId } = this.$route.params
      const params = {
        requireId,
        issueCodes,
      }
      const res = await getRelationTestData(params)
      if (res.isSuccess) {
        // 缺陷数据
        const bugList =
          res.data.bugList?.map((e) => (e.types = 'bug') && e) || []
        // 流水线数据
        const pipelineList =
          res.data.pipelineList?.map((e) => (e.types = 'pipeline') && e) || []
        // 制品数据
        const pack = res.data.pack?.map((e) => (e.types = 'package') && e) || []
        // 测试计划数据
        const testPlanList =
          res.data.testPlanList?.map((e) => {
            e.types = 'testPlan'
            e.typeCpde = e.type
            delete e.type
            return e
          }) || []
        // 设置key值
        const chidren = [
          ...bugList,
          ...pipelineList,
          ...testPlanList,
          ...pack,
        ].map((ele, i) => (ele.key = 'testing_' + i) && ele)
        // 数据为空，设置占位数据
        this.graphData[3].attrs =
          chidren.length > 0
            ? chidren
            : [
                {
                  key: 'testing_0',
                  types: 'placeholder',
                },
              ]
        // 设置连接发布模块的连线
        this.graphData[3].attrs[0].relation = [
          {
            key: 'production_0',
            nodeId: 'production',
            lineType: 'placeholder',
          },
        ]
        const prods = pipelineList
          .filter((ele) => ele?.pipeline?.envId.indexOf('PRD') > -1)
          .map((ele, i) => (ele.key = 'production_' + i) && ele)
        // 设置发布的占位数据
        this.graphData[4].attrs =
          prods.length > 0
            ? prods
            : [
                {
                  key: 'production_0',
                  types: 'placeholder',
                },
              ]
        // 刷新图片
        this.$refs.graph.refreshGrap()
      }
    },
  },
}
</script>
