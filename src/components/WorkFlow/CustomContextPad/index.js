import _ from 'lodash'
export default hideTextAnnotation => {
  class CustomContextPad {
    constructor(contextPad) {
      contextPad.registerProvider(this)
    }

    getContextPadEntries() {
      return entries => {
        const data = _.pick(entries, ['connect', 'append.text-annotation', 'delete'])
        if (hideTextAnnotation) {
          delete data['append.text-annotation']
        }
        Object.keys(data).forEach(key => (data[key].group = key.replace(/\./g, '')))
        return data
      }
    }
  }

  CustomContextPad.$inject = ['contextPad']

  return {
    __init__: ['customContextPad'],
    customContextPad: ['type', CustomContextPad]
  }
}
