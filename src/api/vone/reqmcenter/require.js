import request from '@/utils/axios-api'
import requestUtils from '@/utils/requestUtils'

// 查询需求列表
export function requirementPage(data) {
  return request({
    url: '/api/alm/alm/requirement/page',
    method: 'post',
    data
  })
}

// 意向新增/编辑
export function requirementAddOrEdit(data) {
  return request({
    url: '/api/alm/alm/requirement',
    method: data.id ? 'put' : 'post',
    data
  })
}

/**
 * 根据ID删除意向
 * @param id
 */
export async function requirementDel(data) {
  return request({
    url: `/api/alm/alm/requirement`,
    method: 'delete',
    data
  })
}

/**
 * 根据ID查询意向详情
 * @param id
 */
export async function requirementInfo(id) {
  return request({
    url: `/api/alm/alm/requirement/${id}`,
    method: 'get'
  })
}
// 需求分类
export function requirementType(data) {
  return request({
    url: '/api/alm/alm/type/query',
    method: 'post',
    data
  })
}
// 根据分类查询事项类型
export function findByClassify(id) {
  return request({
    url: `/api/alm/alm/type/findByClassify/${id}`,
    method: 'get'
  })
}
// 需求中心看板信息
export function findKanbanByRequirementQuery(data) {
  return request({
    url: '/api/alm/alm/projectKanban/findKanbanByRequirementQuery',
    method: 'post',
    data
  })
}
// 事件类型信息
export function findKanbanByKanbanType(id, data) {
  return request({
    url: `/api/alm/alm/projectKanban/findKanbanByKanbanType/${id}`,
    method: 'post',
    data
  })
}
// 需求树形看板数据
export function requirementTree(data) {
  return request({
    url: '/api/alm/alm/requirement/requirementTree',
    method: 'post',
    data
  })
}
// 查询意向或者需求关联的父子需求
export function selectRequirementByIdeaIdOrRequirementId(data) {
  return request({
    url: '/api/alm/alm/requirement/selectRequirementByIdeaIdOrRequirementId',
    method: 'post',
    data
  })
}

/**
 * @desc 根据需求id查询需求及父需求数据
 * @param {string} requireId 需求id
 */
export function getRequiredRelation(requireId) {
  return request({
    url: '/api/alm/alm/requirement/showRequirementTopology/' + requireId,
    method: 'post'
  })
}
/**
 * @desc 根据需求id查询关联的项目和计划
 * @param {string} requireId 需求id
 * @param {array} issueCodes 需求id列表
 */
export function getRelationTasks({ requireId, issueCodes }) {
  return request({
    url: '/api/alm/alm/requirement/showPlanTopology/' + requireId,
    method: 'post',
    data: issueCodes
  })
}
/**
 * @desc 根据需求id查询关联的测试数据
 * @param {string} requireId 需求id
 * @param {array} issueCodes 需求id列表
 */
export function getRelationTestData({ requireId, issueCodes }) {
  return request({
    url: '/api/alm/alm/requirement/showTestmTopology/' + requireId,
    method: 'post',
    data: issueCodes
  })
}
/**
 * @desc 根据需求id查询关联的开发数据
 * @param {string} requireId 需求id
 * @param {array} issueCodes 需求id列表
 */
export function getRelationDevData({ requireId, issueCodes }) {
  return request({
    url: '/api/alm/alm/requirement/showDevelopmentTopology/' + requireId,
    method: 'post',
    data: issueCodes
  })
}

// 下载导入模板
export function downloadRequireImportTemplate(params) {
  return request({
    url: `/api/alm/alm/requirement/excel/downloadImportTemplate`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json; charset=UTF-8'
    },
    responseType: 'blob', // 在请求中加上这一行，特别重要
    params
  })
}

// 导入Excel
export function apiRequireImport(data) {
  return request({
    url: `/api/alm/alm/requirement/excel/import`,
    method: 'post',
    data: requestUtils.fileFormData(data)
  })
}

/**
 * 需求和用户需求关联关系
 * @param
 */
export async function issueToIdeaQuery(data) {
  return request({
    url: `/api/alm/alm/issueIdeaProductRequirement/query`,
    method: 'POST',
    data
  })
}
