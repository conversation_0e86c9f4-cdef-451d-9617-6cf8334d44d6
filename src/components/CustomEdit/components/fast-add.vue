<template>
  <vone-simple-add
    ref="simpleAdd"
    v-model.trim="form.name"
    :rules="rules"
    :model="form"
    label="标题"
    :file-list="fileList"
    @update:file-list="fileList = $event"
    :loading="saveLoading"
    :input-width="180"
    @submit="saveSubmit"
    @open="openCreateDetail"
    @cancel="$emit('cancel')"
  >
    <!-- 项目 -->
    <el-dropdown
      v-model="form.projectId"
      class="dropList minWidth"
      trigger="click"
      @command="changeProject"
    >
      <el-row type="flex" align="middle" justify="space-between">
        <span class="el-dropdown-link textTitle">
          <span v-if="projectMap && projectMap[form.projectId]">
            <el-row
              type="flex"
              style="
                display: flex;
                align-items: center;
                cursor: pointer;
                justify-content: space-between;
              "
            >
              <!-- <i :class="`iconfont ${projectMap[form.typeCode].icon}`" :style="{ color:`${projectMap[form.typeCode].color ? projectMap[form.typeCode].color : '#ccc'}`}" class="iconStyle" /> -->
              <span>
                {{
                  projectMap[form.projectId].name.length > 5
                    ? projectMap[form.projectId].name.substr(0, 5) + "..."
                    : projectMap[form.projectId].name
                }}
              </span>

              <el-icon class="iconfont el-icon-circle-close iconStyle"
                ><el-icon-direction-down
              /></el-icon>
            </el-row>
          </span>
          <span v-else>
            <span> 未设置项目 </span>
            <el-icon class="iconfont el-icon--right"
              ><el-icon-direction-down
            /></el-icon>
          </span>
        </span>
      </el-row>
      <template v-slot:dropdown>
        <el-dropdown-menu>
          <template v-if="projectList && projectList.length > 0">
            <el-dropdown-item
              v-for="item in projectList"
              :key="item.id"
              :command="item.id"
            >
              <span v-if="item.icon">
                <i
                  :class="`iconfont ${item.echoMap.typeCode.icon}`"
                  :style="{
                    color: `${
                      item.echoMap.typeCode.color
                        ? item.echoMap.typeCode.color
                        : '#ccc'
                    }`,
                  }"
                />
              </span>
              {{ item.name }}
            </el-dropdown-item>
          </template>
          <vone-empty v-else desc="无可选项目" />
        </el-dropdown-menu>
      </template>
    </el-dropdown>

    <!-- 需求类型下拉列表 -->
    <el-dropdown
      v-model="form.typeCode"
      class="dropList minWidth"
      trigger="click"
      @command="changeClass"
    >
      <el-row type="flex" align="middle" justify="space-between">
        <span class="el-dropdown-link textTitle">
          <span v-if="defectMap && defectMap[form.typeCode]">
            <el-row
              type="flex"
              style="display: flex; align-items: center; cursor: pointer"
            >
              <i
                :class="`iconfont ${defectMap[form.typeCode].icon}`"
                :style="{
                  color: `${
                    defectMap[form.typeCode].color
                      ? defectMap[form.typeCode].color
                      : '#ccc'
                  }`,
                }"
                class="iconStyle"
              />
              <span> {{ defectMap[form.typeCode].name }} </span>
              <el-icon class="iconfont el-icon--right"
                ><el-icon-direction-down
              /></el-icon>
            </el-row>
          </span>
          <span v-else>
            <span> 未设置分类 </span>
            <el-icon class="iconfont el-icon--right"
              ><el-icon-direction-down
            /></el-icon>
          </span>
        </span>
      </el-row>
      <template v-slot:dropdown>
        <el-dropdown-menu>
          <template v-if="typeCodeList && typeCodeList.length > 0">
            <el-dropdown-item
              v-for="item in typeCodeList"
              :key="item.code"
              :command="item.code"
            >
              <span v-if="item.icon">
                <i
                  :class="`iconfont ${item.icon}`"
                  :style="{ color: `${item.color ? item.color : '#ccc'}` }"
                />
              </span>
              {{ item.name }}
            </el-dropdown-item>
          </template>
          <vone-empty v-else desc="无可选分类" />
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    <!-- 人员下拉列表 -->

    <vone-remote-user
      v-model:value="form.handleBy"
      :project-id="projectId"
      class="minWidth"
    />

    <el-popover v-if="!noFile" width="517" trigger="hover">
      <vone-upload
        ref="uloadFile"
        biz-type="IDEA_FILE_UPLOAD"
        @change="onChange"
      />
      <template v-slot:upload>
        <div />
      </template>
      <vone-empty v-if="fileList.length === 0" desc="暂无附件" />
      <template v-slot:reference>
        <el-icon
          class="iconfont"
          style="
            cursor: pointer;
            color: var(--main-theme-color);
            margin: 0 12px;
          "
          ><el-icon-icon-fujian
        /></el-icon>
      </template>
    </el-popover>
  </vone-simple-add>
</template>

<script>
import {
  DirectionDown as ElIconDirectionDown,
  IconFujian as ElIconIconFujian,
} from "@element-plus/icons-vue";
import { $on, $off, $once, $emit } from "../../../utils/gogocodeTransfer";

import { apiAlmTypeNoPage } from "@/api/vone/alm/index";

import { apiAlmSaveIssue } from "@/api/vone/project/issue";

import { apiAlmProjectNoPage } from "@/api/vone/project/index";
import { apiAlmGetTypeNoPage } from "@/api/vone/alm/index";

// import dayjs from 'dayjs'
import storage from "store";

export default {
  components: {
    ElIconDirectionDown,
    ElIconIconFujian,
  },
  name: "SimpleAdd",
  props: {
    testCaseId: {
      type: Number,
      default: null,
    },
    planId: {
      type: String,
      default: "",
    },
    issueId: {
      // 需求id,用于需求关联任务时,保存时传需求id
      type: String,
      default: undefined,
    },
    noFile: Boolean,

    typeCode: {
      // 分类枚举值,只作为查询分类的入参,ISSUE,BUG,TASK,RISK,及区分保存时调哪个接口
      type: String,
      default: undefined,
    },
    noEpic: Boolean, // 史诗拆分用户故事,需求新增时,过滤掉史诗
    productId: {
      // 用户需求关联需求时，需要查询产品关联的项目
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      projectMap: {},
      projectList: [],

      form: {
        name: "",
        typeCode: "",
        handleBy: "",
        envCode: "",
      },
      userList: [],
      typeCodeList: [], // 用户需求分类
      typeList: [], // 分类下拉框
      envList: [], // 环境
      fileList: [],
      defectMap: {}, // 缺陷分类

      envMap: {},
      saveLoading: false,
      rules: {
        name: [
          { required: true, message: "请输入标题", trigger: "blur" },
          {
            pattern: "^.{1,250}$",
            message: "请输入不超过250个字符组成的标题",
            trigger: "change",
          },
        ],
      },
      projectId: this.$route.params.id,
    };
  },
  computed: {
    projectKey() {
      return this.$route.params.projectKey;
    },
  },
  mounted() {
    this.getIssueType();

    this.getProjectList();

    // 新增时默认赋值当前登录人
    const userInfo = storage.get("user");
    this.form["handleBy"] = userInfo.id;
  },
  methods: {
    onChange(val) {
      this.fileList = val;
    },
    resetProject() {
      this.form["projectId"] = "";
    },
    async getIssueType(val) {
      const res = await apiAlmTypeNoPage({
        classify: "ISSUE",
      });
      if (!res.isSuccess) {
        return;
      }
      this.typeCodeList = res.data;
      this.defectMap = this.typeCodeList.reduce(
        (r, v) => (r[v.code] = v) && r,
        {}
      );
      this.form["typeCode"] = res.data[0].code;
    },
    // 打开新建详细数据弹窗
    openCreateDetail() {
      $emit(this, "createDetail");
      $emit(this, "cancel");
    },
    // 切换类型
    changeClass(value) {
      this.form["typeCode"] = value;
    },
    // 切换项目
    changeProject(value) {
      this.form["projectId"] = value;

      this.changeProjectType(value);
    },
    async changeProjectType(val) {
      const res = await apiAlmGetTypeNoPage(val, "ISSUE");
      if (!res.isSuccess) {
        return;
      }
      this.typeCodeList = res.data;
      this.defectMap = this.typeCodeList.reduce(
        (r, v) => (r[v.code] = v) && r,
        {}
      );
      this.form["typeCode"] = res.data[0].code;
    },
    changeUser(v) {
      this.form["handleBy"] = v;
    },

    async saveSubmit() {
      const params = {
        name: this.form.name,
        typeCode: this.form.typeCode,
        estimateHour: 1,
        priorityCode: "HIGH",
        putBy: this.form.handleBy,
        handleBy: this.form.handleBy,
        leadingBy: this.form.handleBy,
        projectId: this.form.projectId,
        productId: this.productId ? this.productId : null,
        parentId: this.typeCode == "ISSUE" ? this.issueId : null,
        ideaId: this.typeCode == "IDEA" ? this.issueId : null,
        files: this.fileList.length ? this.fileList : [],
        sourceCode: "DEMAND_FEEDBACK",
      };
      this.saveIssue(params);
    },
    async saveIssue(params) {
      try {
        this.saveLoading = true;
        const res = await apiAlmSaveIssue(params);
        this.saveLoading = false;
        if (!res.isSuccess) {
          this.$message.warning(res.msg);
          return;
        }
        this.$message.success("创建成功");
        $emit(this, "success", res.data.id);
        this.form = {
          name: "",
          typeCode: this.typeCodeList[0].code,
          userId: this.userList[0].id,
        };
        this.fileList = [];
      } catch (e) {
        this.saveLoading = false;
      }
    },
    async getProjectList() {
      const res = await apiAlmProjectNoPage({
        productId: this.productId,
      });
      if (!res.isSuccess) {
        return;
      }
      this.projectList = res.data;
      this.projectMap = res.data.reduce((r, v) => (r[v.id] = v) && r, {});
    },
  },
  emits: ["cancel", "success", "createDetail"],
};
</script>

<style lang="scss" scoped>
.dropList {
  min-width: 60px;
  margin: 0 6px;
}
.textTitle {
  white-space: nowrap;
}
.iconStyle {
  margin-left: 5px;
  margin-right: 5px;
  cursor: pointer;
}
:deep(.el-dropdown-menu) {
  height: 200px;
  overflow-y: auto;
  overflow-x: hidden;
}
.minWidth {
  min-width: 80px;
  margin-left: 10px;
}
</style>
