<template>
  <div v-loading="pageLoading">
    <vone-edit-wrapper show-footer>
      <vone-card-wrapper title="基本信息">
        <el-form
          ref="basicHostForm"
          label-position="top"
          :rules="basicHostRules"
          :model="basicHostForm"
        >
          <el-row :gutter="24">
            <el-col :span="6">
              <el-form-item prop="name" label="主机名">
                <el-input
                  v-model="basicHostForm.name"
                  :disabled="isEdit"
                  placeholder="请输入主机名"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="ip" label="IP">
                <el-input
                  v-model="basicHostForm.ip"
                  :disabled="isEdit && basicHostForm.createType != 'OMP'"
                  placeholder="请输入ip"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="operatingSystem" label="操作系统">
                <el-input
                  v-model="basicHostForm.operatingSystem"
                  placeholder="请输入操作系统"
                  :disabled="isEdit"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="operatingSystemVersion" label="操作系统版本">
                <el-input
                  v-model="basicHostForm.operatingSystemVersion"
                  placeholder="请输入操作系统版本"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="6">
              <el-form-item prop="operatingSystemKernel" label="操作系统内核">
                <el-select
                  v-model="basicHostForm.operatingSystemKernel"
                  :disabled="isEdit"
                  clearable
                  placeholder="请选择操作系统内核"
                  style="width: 100%"
                >
                  <el-option
                    v-for="(item, index) in versionList"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="hdSize" label="磁盘大小(Mb)">
                <el-input
                  v-model="basicHostForm.hdSize"
                  placeholder="请输入磁盘大小"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="CPU核数" prop="cpuNum">
                <el-input-number
                  v-model="basicHostForm.cpuNum"
                  :disabled="isEdit"
                  :min="1"
                  :max="128"
                  :step="2"
                  placeholder="请输入CPU核数"
                  :precision="0"
                  controls-position="right"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="places" label="系统位数">
                <el-radio-group
                  v-model="basicHostForm.places"
                  :disabled="isEdit"
                >
                  <el-radio-button label="32" />
                  <el-radio-button label="64" />
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="6">
              <el-form-item prop="cpuModel" label="CPU型号">
                <el-input
                  v-model="basicHostForm.cpuModel"
                  placeholder="请输入CPU型号"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="memSize" label="内存容量(Mb)">
                <el-input
                  v-model="basicHostForm.memSize"
                  placeholder="请输入内存容量(Mb)"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="type" label="服务器类型">
                <el-select
                  v-model="basicHostForm.type"
                  clearable
                  placeholder="请选择服务器类型"
                  style="width: 100%"
                >
                  <el-option label="未知" value="UNKNOWN" />
                  <el-option label="物理机" value="PHYSICAL_MACHINE" />
                  <el-option label="虚拟机" value="VIRTUAL_MACHINE" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="description" label="描述">
                <el-input
                  v-model="basicHostForm.description"
                  placeholder="请输入描述"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </vone-card-wrapper>

      <!-- 高级属性配置 -->
      <vone-card-wrapper
        v-if="advanceTemplete.length"
        title="高级属性配置"
        class="contain"
      >
        <el-form
          ref="hostAdvancedDatas"
          :model="hostAdvancedDatas"
          :rules="advanceHostRules"
          label-position="top"
        >
          <el-row :gutter="24">
            <template v-for="(item, index) in advanceTemplete">
              <template>
                <el-col :key="index" :span="6">
                  <el-form-item :prop="item.key" :label="item.name">
                    <el-input
                      v-model="hostAdvancedDatas[item.key]"
                      :type="item.key === 'SSH-PASSWORD' ? 'password' : 'text'"
                      :placeholder="item.name"
                    />
                  </el-form-item>
                </el-col>
              </template>
            </template>
          </el-row>
        </el-form>
      </vone-card-wrapper>

      <template v-slot:footer>
        <el-row>
          <el-button @click="cancle">取消</el-button>
          <el-button type="primary" :loading="saveLoading" @click="saveHost"
            >确定</el-button
          >
        </el-row>
      </template>
    </vone-edit-wrapper>
  </div>
</template>

<script>
import {
  apiCmdbHostSelectAdvanceTemplate,
  apiCmdbHostAdd,
  apiCmdbGetHostById,
} from '@/api/vone/cmdb/host'
import { apiBaseDictEnumList } from '@/api/vone/ouath/ouath'

// import _ from 'lodash'
export default {
  components: {},
  data() {
    return {
      saveLoading: false,
      versionList: [],
      pageLoading: false,
      loading: false,

      basicHostForm: {
        createType: '',
        name: '',
        memSize: '',
        ip: '',
        operatingSystem: '',
        operatingSystemVersion: '',
        type: '',
        places: '32',
        patrolStrategy: '30',
        operatingSystemKernel: '',
        cpuModel: '',
        cpuNum: '2',
      },
      hostAdvancedDatas: {}, // 高级属性配置
      advanceTemplete: [],
      basicHostRules: {
        name: [
          {
            required: true,
            message: '请输入长度不超过64个字符的主机名',
            trigger: 'change',
            max: 64,
          },
        ],
        ip: [
          { required: true, message: '请输入ip', trigger: 'change' },
          {
            pattern:
              '^((?:(?:25[0-5]|2[0-4]\\d|[01]?\\d?\\d)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d?\\d))$',
            message: '请输入合法的ip',
          },
        ],
        operatingSystem: [
          { required: true, message: '请选择操作系统', trigger: 'change' },
        ],
        operatingSystemKernel: [
          { required: true, message: '请选择操作系统', trigger: 'change' },
        ],
        operatingSystemVersion: [
          {
            max: 50,
            message: '请输入长度不超过50个字符的系统版本',
            trigger: 'change',
          },
        ],
        // cpuNum: [
        //   {
        //     message: '请输入CPU核数',
        //     trigger: 'blur'
        //   }
        // ],
        cpuModel: [
          {
            max: 64,
            message: '请输入长度不超过64个字符的CPU主频',
            trigger: 'blur',
          },
        ],
        hdSize: [
          {
            pattern: '^[0-9]*$',
            message: '请输入数字',
          },
        ],
        memSize: [
          { min: 1, max: 10000000, trigger: 'change' },
          {
            pattern: '^[0-9]*$',
            message: '请输入数字',
          },
        ],
        description: [
          {
            max: 100,
            message: '请输入长度不超过100个字符的描述',
            trigger: 'blur',
          },
        ],
        type: [
          { required: false, message: '请选择服务器类型', trigger: 'change' },
        ],
        places: [
          { required: true, message: '请选择系统位数', trigger: 'change' },
        ],
      },
      advanceHostRules: {},
      hostAccendantDatas: [],
      hostAdvancedAttributes: [],
      hostCustomAttributeDatas: [],
      isEdit: false,
    }
  },
  async mounted() {
    this.getVersion()
    this.getAdvance()
    if (this.$route.params.id) {
      await this.getHostInfoById()
      this.isEdit = true
      // if (!this.$route.params.id) {
      //   this.isEdit = true
      // } else {
      //   this.isEdit = false
      // }
    } else {
      this.isEdit = false
    }
  },
  methods: {
    async getVersion() {
      const res = await apiBaseDictEnumList(['OperatingSystemType'])

      if (!res.isSuccess) {
        return
      }
      this.versionList = res.data.OperatingSystemType
    },
    selectChanged(value) {
      this.allname = value.toUpperCase()
    },
    // 信息回显
    async getHostInfoById() {
      this.pageLoading = true
      const res = await apiCmdbGetHostById(this.$route.params.id)
      this.pageLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      res.data.operatingSystemKernel = res.data.operatingSystemKernel
        ? res.data.operatingSystemKernel.code
        : ''
      res.data.status = res.data.status ? res.data.status.code : ''
      res.data.type = res.data.type ? res.data.type.code : ''
      res.data.createType = res.data.createType ? res.data.createType.code : ''

      this.basicHostForm = res.data
      // this.$set(this.basicHostForm,"operatingSystemKernel","")

      // const paramsMap = {}
      const formList = res.data.hostExtends.map((item) => ({
        key: item.key,
        value: item.value,
      }))

      if (formList.length > 0) {
        const map = {}
        formList.forEach((item) => (map[item.key] = item.value))
        this.hostAdvancedDatas = map
      }
    },

    // 获取高级属性设置模板
    async getAdvance() {
      const res = await apiCmdbHostSelectAdvanceTemplate()
      if (!res.isSuccess) {
        return this.$message.warning(res.msg)
      }

      this.advanceTemplete = res.data
      const advanceRoule = res.data.map((r) => ({
        required: r.notEmpty,
        message: r.message,
        max: r.maxlength,
        trigger: 'change',
        pattern: r.regexp,
        key: r.key,
      }))

      advanceRoule.forEach((item) => {
        if (!this.advanceHostRules[item.key]) {
          this.advanceHostRules[item.key] = [item]
        } else {
          this.advanceHostRules[item.key].push(item)
        }
      })
    },

    // 新增服务器
    async saveHost() {
      try {
        await Promise.all([
          this.$refs.basicHostForm.validate(),
          this.$refs.hostAdvancedDatas.validate(),
        ])
      } catch (error) {
        return
      }

      try {
        // 拼接高级属性
        this.advanceTemplete.map((r, p) => {
          this.hostAdvancedAttributes.push({
            key: r.key,
            type: r.inputType.code,
            value: this.hostAdvancedDatas[r.key],
          })
        })

        this.basicHostForm.hostExtends = this.hostAdvancedAttributes
        this.saveLoading = true
        const res = await apiCmdbHostAdd(this.basicHostForm)
        this.saveLoading = false

        if (!res.isSuccess) {
          return this.$message.error(res.msg)
        }
        this.$message.success('保存成功')
        this.$router.go(-1)
      } catch (error) {
        this.saveLoading = false
        return
      }
    },
    cancle() {
      this.$router.go(-1)
    },
  },
}
</script>

<style lang="scss" scoped>
.contain {
  min-height: calc(100vh - 510px);
  overflow-y: auto;
}
:deep(.el-col-6) {
  height: 90px;
}
</style>
