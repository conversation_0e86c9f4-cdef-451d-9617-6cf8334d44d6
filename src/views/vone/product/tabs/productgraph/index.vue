<template>
  <!-- <div> -->
  <!-- <div>
          <el-form ref="form" :inline="true" :model="form" label-width="80px">
            <el-form-item label="布局类型">
              <el-select v-model="form.type" placeholder="请选择类型">
                <el-option v-for="e in layoutoptions" :key="e.value" :label="e.label" :value="e.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="节点类型">
              <el-select v-model="form.nodeType" placeholder="请选择">
                <el-option label="圆形" value="circle" />
                <el-option label="矩形" value="rect" />
              </el-select>
            </el-form-item>
          </el-form>
        </div> -->
  <product-graph
    v-if="Object.keys(graphData).length > 0"
    :graph-data="graphData"
    :icon="icon"
    :layout-type="form"
  />
  <!-- </div> -->
</template>

<script>
import productGraph from './components/Graph/index.vue'
import { getProductMessage } from '@/api/vone/product'

import voneImg from '@/assets/logo.png'

export default {
  components: {
    productGraph,
  },
  data() {
    return {
      icon: '',
      graphData: {},
      layoutoptions: [
        {
          value: 'circular',
          label: '环形',
        },
        {
          value: 'force',
          label: '力导向图',
        },
        {
          value: 'dagre',
          label: '层次',
        },
      ],
      form: {
        type: 'circular',
        nodeType: 'circle',
      },
    }
  },
  mounted() {
    this.getTopoGraphInfo()
  },
  methods: {
    // 查询拓扑图数据
    async getTopoGraphInfo() {
      const res = await getProductMessage({
        productId: this.$route.params.productId,
      })
      if (!res.isSuccess) {
        this.$message.error(res.msg)
      }
      const data = res.data
      data.nodes.filter((i, index) => {
        if (i.id == this.$route.params.productId) {
          i.iscore = true
          i.type = 'dom-node'
          i.size = 128
          i.label = i.name.length > 8 ? i.name.slice(0, 8) + '...' : i.name
          this.icon = i.icon || voneImg
        } else {
          i.iscore = false
          i.type = 'dom-node'
          i.size = [80, 30]
          i.img = i.icon
          i.label = i.name
        }
      })
      const edges = []
      data.edges.filter((i) => {
        edges.push({ source: i.target, target: i.source })
      })
      data.edges = edges

      this.graphData = data
    },
  },
}
</script>
