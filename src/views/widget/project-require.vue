<template>
  <vone-echarts-card :title="formInfo.title" :name="formInfo.name">
    <vone-echarts v-if="data.length > 0" :options="options" />
    <vone-empty v-else />
  </vone-echarts-card>
</template>

<script>
import { requirementComponent } from '@/api/vone/project/overview.js'

export default {
  props: {
    formInfo: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      data: [],
      backgroundColor: '#2c343c',
      options: {
        tooltip: {
          trigger: 'item',
          axisPointer: {
            type: 'shadow',
          },
          backgroundColor: '#fff',
          borderColor: 'none',
          extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);', // 附加阴影样式
          textStyle: {
            color: '#53565C',
          },
        },
        legend: {
          orient: 'horizontal',
          x: 'center', // 可设定图例在左、右、居中
          y: 'bottom',
          textStyle: {
            // 图例文字的样式
            color: '#777F8E',
          },
          itemHeight: 8,
          itemWidth: 8,
          icon: 'circle',
        },
        // title: {
        //   text: '总数',
        //   top: '50%',
        //   left: '25%',
        //   textStyle: {
        //     fontWeight: 'normal',
        //     fontSize: 14,
        //     color: '#777F8E'
        //   }
        // },
        // graphic: {
        //   type: 'text',
        //   left: '29%',
        //   top: '38%',
        //   style: {
        //     text: '',
        //     textAlign: 'center',
        //     fill: '#000',
        //     width: 30,
        //     fontSize: 18
        //     // height:60
        //   }
        // },
        series: [
          {
            name: '需求概览',
            type: 'pie',
            center: ['50%', '40%'],
            radius: ['55%', '80%'],
            avoidLabelOverlap: false,
            itemStyle: {
              // borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 0,
            },
            label: {
              show: true,
              position: 'center',
              formatter: '{num|' + 0 + '}' + '\n\r' + '{name|总数}',
              rich: {
                num: {
                  fontSize: 18,
                  color: '#777F8E',
                },
                name: {
                  fontFamily: '微软雅黑',
                  fontSize: 14,
                  color: '#777F8E',
                  lineHeight: 30,
                },
              },
            },
          },
        ],
      },
    }
  },
  mounted() {
    if (this.formInfo.projectId) {
      this.getRequire()
    }
  },
  methods: {
    async getRequire() {
      await requirementComponent(this.formInfo.projectId).then((res) => {
        if (res.isSuccess) {
          this.data = res.data.data
          if (res.data.data) {
            res.data.data.forEach((item) => {
              item.itemStyle = {
                color: item.color,
              }
            })
          }
          this.options.series[0].label.formatter =
            '{num|' + res.data.count + '}' + '\n\r' + '{name|总数}'
          this.options.series[0].data = res.data.data
        }
      })
    },
  },
}
</script>
