<template>
  <!-- db配置 -->
  <div v-loading="pageLoading">
    <vone-search-wrapper v-if="this.$route.params.type != 1">
      <template v-slot:actions>
        <el-button type="primary" @click="saveInfo">保存</el-button>
      </template>
    </vone-search-wrapper>
    <el-form
      ref="form"
      :auto-layout="false"
      :rules="rules"
      label-width="0"
      :model="baseForm"
    >
      <main
        style="margin-top: 16px"
        :style="{
          height:
            $route.params.type != 1
              ? 'calc(100vh - 418px)'
              : 'calc(100vh - 370px)',
        }"
      >
        <vxe-table
          class="vone-vxe-table deployTable"
          border
          resizable
          height="auto"
          show-overflow="tooltip"
          :empty-render="{ name: 'empty' }"
          :data="baseForm.tableData"
          :column-config="{ minWidth: '120px' }"
          :checkbox-config="{ reserve: true }"
          row-id="id"
        >
          <vxe-column title="环境名称" field="envKey" width="180">
            <template #default="{ row, rowIndex }">
              <el-form-item
                :prop="`tableData.${rowIndex}.envKey`"
                :rules="rules.envKey"
              >
                <el-select
                  v-model="row.envKey"
                  style="width: 100%"
                  placeholder="请选择环境名称"
                  :disabled="$route.params.type == 1"
                >
                  <el-option
                    v-for="(item, index) in moduleNameList"
                    :key="index"
                    :label="item.name"
                    :value="item.code"
                  />
                </el-select>
              </el-form-item>
            </template>
          </vxe-column>
          <vxe-column field="dbComponentsId" title="数据库" width="220">
            <template #default="{ row, rowIndex }">
              <el-form-item
                :prop="`tableData.${rowIndex}.dbComponentsId`"
                :rules="rules.dbComponentsId"
              >
                <el-select
                  v-model="row.dbComponentsId"
                  style="width: 100%"
                  placeholder="请选择环数据库"
                  :disabled="$route.params.type == 1"
                  @change="changeDb(row)"
                >
                  <el-option
                    v-for="(item, index) in dbList"
                    :key="index"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </template>
          </vxe-column>
          <vxe-column field="dbComponentsTypeKey" title="类型" />
          <vxe-column field="serverIp" title="主机IP">
            <template #default="{ row }">
              <span>{{ row.serverIp }}</span>
            </template>
          </vxe-column>
          <vxe-column field="serverPort" title="端口号">
            <template #default="{ row }">
              <span>{{ row.serverPort }}</span>
            </template>
          </vxe-column>
          <vxe-column field="updateTime" title="更新时间" width="170" />
          <vxe-column v-if="$route.params.type != 1" title="操作" width="120">
            <template #default="{ rowIndex }">
              <el-button
                type="text"
                :icon="elIconTipsPlusCircle"
                @click.prevent="addRow(rowIndex, tableData)"
              />
              <el-button
                type="text"
                :icon="elIconApplicationDelete"
                @click.prevent="deleteRow(rowIndex, tableData)"
              />
            </template>
          </vxe-column>
        </vxe-table>
      </main>
    </el-form>

    <el-row
      v-if="this.$route.params.type != 1"
      class="mt-3"
      type="flex"
      justify="center"
    >
      <!-- <el-button v-if="TYPE == 0" v-show="active > 0" @click="stepFoaward">上一步</el-button>
          <span :style="{ flex: 1 }" />
          <el-button v-if="TYPE == 0 && !end" type="primary" @click="saveNext">保存并下一步</el-button> -->
      <!-- <el-button type="primary" @click="saveInfo">保存</el-button> -->

      <!-- <el-button v-if="TYPE == 0 && end" type="primary" @click="saveNext">完成</el-button> -->
    </el-row>
  </div>
</template>

<script>
import {
  $on,
  $off,
  $once,
  $emit,
} from "../../../../../../utils/gogocodeTransfer";
import {
  apiBuniessUpdateDbCompentsBySystemId,
  apiBuniessSelectDbCompentsBySystemId,
} from "@/api/vone/cmdb/server";
import { apiCmdbDataBaseNoPage } from "@/api/vone/cmdb/database";

import assign from "lodash/assign";

export default {
  props: {
    active: {
      type: Number,
      default: 0,
    },
    configListLength: {
      type: Number,
      default: 0,
    },
    applicationInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      tableData: [
        {
          envKey: "",
          dbComponentsId: "",
          dictdbComponentsId: "",
          serverIp: "",
          serverPort: "",
        },
      ],
      moduleNameList: [],
      compentsIdList: [],
      dbList: [],
      dbListMap: {},
      pageLoading: false,
      TYPE: undefined,
      end: false,
      baseForm: {
        tableData: [],
      },
      rules: {
        envKey: [{ required: true, message: "请选择环境" }],
        dbComponentsId: [{ required: true, message: "请选择数据库" }],
      },
    };
  },
  computed: {
    // _tableData() {
    //   return this.baseForm.tableData.map(item => {
    //     return assign(item, this.dbListMap[item.dbComponentsId])
    //   })
    // }
  },
  mounted() {
    this.TYPE = this.$route.params.type;

    this.getDb();
    this.getTableData();

    if (this.configListLength == this.active + 1) {
      this.end = true;
    } else {
      this.end = false;
    }

    // 环境下拉框数据,取自当前服务应用配置的环境

    if (this.applicationInfo.applicationEnvs.length) {
      this.moduleNameList = this.applicationInfo.applicationEnvs.map((r) => ({
        code: r.envKey,
        name: r.envKey,
      }));
    }
  },
  methods: {
    // 点击+号图标添加新的一栏
    addRow(index, rows) {
      const obj = {};
      obj.envKey = "";
      obj.dbComponentsId = "";
      obj.dictdbComponentsId = "";
      obj.serverIp = "";
      obj.serverPort = "";
      this.baseForm.tableData.push(obj);
    },
    // 点击减号删除一行
    deleteRow(index, rows) {
      if (this.baseForm.tableData.length > 1) {
        this.baseForm.tableData.splice(index, 1);
      } else {
        this.baseForm.tableData = [{}];
        return;
      }
    },
    addDb() {
      this.$router.push({
        name: "base_addDataBase",
      });
    },
    changeDb(row) {
      return assign(row, this.dbListMap[row.dbComponentsId]);
    },

    // 查询数据库
    async getDb() {
      this.pageLoading = true;
      const res = await apiCmdbDataBaseNoPage();
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      this.pageLoading = false;
      const map = {};
      res.data.map((d) => (map[d.id] = d));
      this.dbList = res.data;
      this.dbListMap = map;
    },

    // 查询表格数据
    async getTableData() {
      this.pageLoading = true;
      const res = await apiBuniessSelectDbCompentsBySystemId(
        this.$route.params.systemId
      );

      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      this.pageLoading = false;
      if (res.data.length > 0) {
        res.data.map((item) => {
          return assign(item, this.dbListMap[item.dbComponentsId]);
        });
        this.baseForm.tableData = res.data;
      } else {
        this.baseForm.tableData = [{}];
      }
    },

    // 保存信息
    async saveInfo() {
      await this.$refs.form.validate();

      const applicationDbs = this.baseForm.tableData.map((r) => ({
        applicationId: this.$route.params.systemId,
        envKey: r.envKey,
        dbComponentsId: r.dbComponentsId,
      }));

      const res = await apiBuniessUpdateDbCompentsBySystemId({
        applicationId: this.$route.params.systemId,
        applicationDbs,
      });
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      this.$message.success("保存成功");
      this.getTableData();
    },
    stepFoaward() {
      $emit(this, "stepFoaward");
    },
    // async saveNext() {
    //   await this.saveInfo()
    //   // this.activeIndex++;
    //   this.$emit('success')
    // }
  },
  emits: ["stepFoaward"],
};
</script>

<style lang="scss" scoped>
.deployTable {
  .el-form-item--small.el-form-item {
    margin-top: 16px;
  }
}
</style>
