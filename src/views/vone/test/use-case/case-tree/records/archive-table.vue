<template>
  <div @click.stop>
    <!-- 树形表格 -->
    <el-table
      v-if="treeArchive"
      ref="product-case-records"
      v-loading="tableLoading"
      style="width: 100%"
      class="vone-table"
      table-key="product-case-records"
      row-key="id"
      :data="tableData"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
      <el-table-column
        label="用例"
        prop="name"
        min-width="320px"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          <span class="text-over">
            <svg v-if="row.parentId" class="icon" aria-hidden="true">
              <use xlink:href="#el-application-filejia2" />
            </svg>
            <el-icon
              class="iconfont"
              style="color: var(--main-theme-color, #3e7bfa); margin-right: 4px"
              ><el-icon-testcase
            /></el-icon>
            <span>{{ row.name }}</span>
          </span>
        </template>
      </el-table-column>
    </el-table>

    <el-table
      v-else
      ref="product-case-records"
      v-loading="tableLoading"
      class="vone-table"
      table-key="product-case-records"
      style="width: 100%"
      row-key="id"
      :data="tableData"
    >
      <el-table-column
        label="用例"
        prop="name"
        min-width="320px"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          <div class="text-over">
            <el-icon
              class="iconfont"
              style="color: var(--main-theme-color, #3e7bfa); margin-right: 4px"
              ><el-icon-testcase
            /></el-icon>
            <span>{{ row.caseKey + "  " + row.name }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="draft" width="100px">
        <template v-slot="{ row }">
          <el-button disabled>{{ row.draft ? "草稿" : "终稿" }}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="版本" prop="version" />
      <el-table-column prop="priority" label="优先级" min-width="120">
        <template v-slot="{ row }">
          <span v-if="row.priority === 1">
            <el-icon class="iconfont" style="color: #4ecf95"
              ><el-icon-icon-dengji-zuidi2
            /></el-icon>
            最低
          </span>
          <span v-else-if="row.priority === 2">
            <el-icon class="iconfont" style="color: #5acc5e"
              ><el-icon-icon-dengji-jiaodi2
            /></el-icon>
            较低
          </span>
          <span v-else-if="row.priority === 5">
            <el-icon class="iconfont" style="color: #fa6a69"
              ><el-icon-icon-dengji-zuigao2
            /></el-icon>
            最高
          </span>
          <span v-else-if="row.priority === 4">
            <el-icon class="iconfont" style="color: #fa8669"
              ><el-icon-icon-dengji-jiaogao2
            /></el-icon>
            较高
          </span>
          <span v-else>
            <el-icon
              class="iconfont"
              style="color: var(--main-theme-color, #3e7bfa)"
              ><el-icon-icon-dengji-putong2
            /></el-icon>
            普通
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="维护人"
        prop="leadingBy"
        width="150"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          <span v-if="row.leadingBy && row.echoMap && row.echoMap.leadingBy">
            <vone-user-avatar
              :avatar-path="row.echoMap.leadingBy.avatarPath"
              :name="row.echoMap.leadingBy.name"
            />
          </span>
          <span v-else> -- </span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import {
  Testcase as ElIconTestcase,
  IconDengjiZuidi2 as ElIconIconDengjiZuidi2,
  IconDengjiJiaodi2 as ElIconIconDengjiJiaodi2,
  IconDengjiZuigao2 as ElIconIconDengjiZuigao2,
  IconDengjiJiaogao2 as ElIconIconDengjiJiaogao2,
  IconDengjiPutong2 as ElIconIconDengjiPutong2,
} from "@element-plus/icons-vue";
import { list2Tree } from "@/utils/list2Tree";
import { getArchiveCase } from "@/api/vone/testmanage/case";

export default {
  components: {
    ElIconTestcase,
    ElIconIconDengjiZuidi2,
    ElIconIconDengjiJiaodi2,
    ElIconIconDengjiZuigao2,
    ElIconIconDengjiJiaogao2,
    ElIconIconDengjiPutong2,
  },
  props: {
    libraryId: {
      type: String,
      default: "",
    },
    data: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      treeArchive: false,
      tableLoading: false,
      tableData: [],
    };
  },
  methods: {
    // 递归
    getchildrenNode(val) {
      if (val) {
        val.map((v) => {
          if (v.echoMap.case && v.echoMap.case.length > 0) {
            v.echoMap.case.map((i) => {
              v.children ? v.children.push(i) : (v.children = [i]);
            });
          }
          this.getchildrenNode(v.children);
        });
      }
      return val;
    },
    async getTableData() {
      this.tableLoading = true;
      const res = await getArchiveCase(
        this.$route.params.caseId || this.libraryId,
        this.data.updateTime,
        this.data.updatedBy
      );
      this.tableLoading = false;
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      if (res.data.tree) {
        this.treeArchive = true;

        const data = list2Tree(res.data.tree, { parentKey: "parentId" });
        this.tableData = this.getchildrenNode(data);
      } else {
        this.treeArchive = false;
        this.tableData = res.data.case;
      }
    },
  },
};
</script>
