<template>
  <div>
    <vone-search-wrapper>
      <template slot="actions">
        <el-button icon="iconfont el-icon-application-delete" size="small" :disabled="!$permission('base_file_del')" @click="deleteAll">批量删除</el-button>
        <el-button type="primary" icon="iconfont el-icon-edit-export" :loading="allLoading" :disabled="!$permission('base_file_load')" @click="allLoad">批量下载</el-button>
      </template>
    </vone-search-wrapper>

    <div style="height:calc(100vh - 288px)">
      <vxe-table
        ref="file-table"
        class="vone-vxe-table"
        border
        height="auto"
        show-overflow="tooltip"
        :loading="loading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth:'120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >

        <vxe-column type="selection" width="55" />

        <vxe-column field="originalFileName" title="原始文件名" min-width="180" show-overflow-tooltip>
          <template v-slot="{ row }">
            {{ row.originalFileName }}
          </template>
        </vxe-column>
        <vxe-column field="bucket" show-overflow-tooltip title="桶" />

        <vxe-column field="bizType" show-overflow-tooltip title="业务类型">
          <template v-slot="{ row }">
            <span v-if="row.bizType">{{ row.bizType.desc }}</span>
            <span v-else>{{ row.bizType }}</span>
          </template>
        </vxe-column>
        <vxe-column field="fileType" title="文件类型">
          <template v-slot="{ row }">
            <span v-if="row.fileType">{{ row.fileType.desc }}</span>
            <span v-else>{{ row.fileType }}</span>

          </template>
        </vxe-column>
        <vxe-column field="size" show-overflow-tooltip title="大小" sortable />

        <vxe-column field="createTime" show-overflow-tooltip title="上传时间" sortable min-width="80" />

        <vxe-column title="操作" fixed="right" align="left" width="55">
          <template #default="{ row }">
            <template>
              <el-tooltip class="item" content="下载" placement="top">
                <el-button type="text" :disabled="!$permission('base_file_load')" icon="iconfont el-icon-edit-download" @click="downLoad(row)" />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="删除" placement="top">
                <el-button :disabled="!$permission('base_file_del')" type="text" icon="iconfont el-icon-application-delete" @click="del(row)" />
              </el-tooltip>

            </template>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
  </div>
</template>

<script>
import { apiBaseFileManage, apiBaseFileDel, apiBaseFileLoadById } from '@/api/vone/base/file'

import { download, fileFormatSize, downloadZipFile } from '@/utils'
export default {
  data() {
    return {

      pageLoading: false,
      allLoading: false,
      selecteTableData: [],
      data: {},
      tableOptions: {
        isSelection: true, // 表格有多选时设置
        isOperation: true, // 表格有操作列时设置
        isIndex: false, // 列表序号
        operation: {
          isFixed: true, // 是否固定在右侧
          // 表格有操作列时设置
          label: '操作', // 列名
          width: '100', // 根据实际情况给宽度
          data: [
            // 功能数组
            {
              type: 'icon', // 为icon则是图标
              label: '下载', // 功能名称
              icon: 'el-icon-edit-download', // icon class
              handler: this.downLoad, // 操作事件
              disabled: !this.$permission('base_file_load')
            },
            {
              type: 'icon', // 为icon则是图标
              label: '删除', // 功能名称
              icon: 'iconfont el-icon-application-delete', // icon class
              handler: this.del, // 操作事件
              disabled: !this.$permission('base_file_del')
            }
          ],
          // 更多操作按钮,如果按钮超过3个，从第三个开始需要添加在moreData中
          moreData: [
          ]
        }
      }
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    async getTableData() {
      this.pageLoading = true
      let params = {}
      const tableAttr = this.$refs['file-table'].exportTableQueryData()
      this.$set(tableAttr, 'sort', 'updateTime')
      this.$set(tableAttr, 'order', 'descending')
      params = {
        ...tableAttr,
        extra: {},
        model: { ...this.formData }
      }
      const res = await apiBaseFileManage(params)
      this.pageLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      res.data.records.forEach(element => {
        element.size = fileFormatSize(element.size)
      })
      this.data = res.data
    },
    async del(row) {
      await this.$confirm('确定删除该信息吗?', '删除', {
        type: 'warning',
        closeOnClickModal: false
      })
        .then(async(actions) => {
          const res = await apiBaseFileDel([row.id])
          this.pageLoading = false
          if (!res.isSuccess) {
            this.$message.warning(res.msg)
            return
          }
          this.$message.success('删除成功')
          this.getTableData()
        })
        .catch(() => { })
    },
    // 批量删除
    async deleteAll() {
      this.selecteTableData = this.getVxeTableSelectData('file-table')

      if (!this.selecteTableData.length) {
        this.$message.warning('请选择要删除的数据')
        return
      }

      try {
        await this.$confirm(`是否删除 ${this.selecteTableData.length} 条数据?`, '删除', {
          type: 'warning'
        })
        this.pageLoading = true
        const selectId = this.selecteTableData.map(r => r.id)
        const res = await apiBaseFileDel(selectId)
        this.pageLoading = false
        if (!res.isSuccess) {
          this.$message.error(res.msg)
          return
        }
        this.$message.success('删除成功')
        this.getTableData()
      } catch (e) {
        this.pageLoading = false
      }
    },
    // 单个下载
    async downLoad(row) {
      download(row.originalFileName, await apiBaseFileLoadById(
        [row.id]
      ))
    },
    // 批量下载
    async allLoad() {
      this.selecteTableData = this.getVxeTableSelectData('file-table')
      if (!this.selecteTableData.length) {
        this.$message.warning('请选择要下载的文件')
        return
      }
      this.allLoading = true
      const fileId = this.selecteTableData.map(r => r.id)
      downloadZipFile(`${Date.now()}.zip`, await apiBaseFileLoadById(
        fileId
      ))
      this.allLoading = false
    }

  }

}
</script>

<style>
</style>
