<template>
  <div>
    <el-row class="menu-main">
      <el-col :span="6">
        <aside>
          <header class="header">
            <el-dropdown trigger="click" @command="(e) => changeMenu(e)">
              <span class="el-dropdown-link">
                {{ menuName
                }}<el-icon class="iconfont el-icon--right"
                  ><el-icon-direction-down
                /></el-icon>
              </span>
              <template v-slot:dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-for="item in allAppList"
                    :key="item.id"
                    :command="item.id"
                    >{{ item.name }}</el-dropdown-item
                  >
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </header>

          <div class="treeContent">
            <edit
              :check-row="checkRow"
              :is-add="isAdd"
              @refreshTable="refreshTable"
              @handleNodeClick="handleNodeClick"
            />
          </div>
        </aside>
      </el-col>
      <el-col :span="18">
        <header class="header">接口权限</header>
        <main>
          <vone-search-wrapper>
            <template v-slot:moreSearch>
              <el-input
                v-model="name"
                style="width: 280px"
                placeholder="请输入内容"
                @change="searchTableClick"
              />
            </template>
            <template v-slot:actions>
              <el-button
                :disabled="!$permission('base_function_add')"
                type="primary"
                @click="addMenu"
                >新增</el-button
              >
            </template>
          </vone-search-wrapper>
          <div style="height: calc(100vh - 228px)">
            <vxe-table
              ref="function-table"
              class="vone-vxe-table"
              height="auto"
              border
              resizable
              show-overflow="tooltip"
              :empty-render="{ name: 'empty' }"
              :loading="tableLoading"
              :data="tableData.records"
              :column-config="{
                minWidth: '120px',
              }"
            >
              <vxe-column title="名称" field="name" />
              <vxe-column title="编码" field="code" />
              <vxe-column title="描述" field="description" />
              <vxe-column title="操作" fixed="right" align="left" width="120">
                <template #default="{ row }">
                  <template>
                    <el-tooltip class="item" content="编辑" placement="top">
                      <el-button
                        type="text"
                        :disabled="!$permission('base_function_edit')"
                        :icon="elIconApplicationEdit"
                        @click="editRow(row)"
                      />
                    </el-tooltip>
                    <el-divider direction="vertical" />
                    <el-tooltip class="item" content="删除" placement="top">
                      <el-button
                        type="text"
                        :disabled="!$permission('base_function_delete')"
                        :icon="elIconApplicationDelete"
                        @click="delRow(row)"
                      />
                    </el-tooltip>
                  </template>
                </template>
              </vxe-column>
            </vxe-table>
          </div>
          <vone-pagination
            ref="pagination"
            style="position: static"
            :total="tableData.total"
            @update="getFunctionList"
          />
        </main>
      </el-col>
    </el-row>
    <addFunctionDialog
      v-bind="addFunctionParams"
      v-if="addFunctionParams.visible"
      ref="addFunction"
      v-model:value="addFunctionParams.visible"
      @success="getFunctionList"
    />
  </div>
</template>

<script>
import Edit from "./edit.vue";
import addFunctionDialog from "./addFunctionDialog.vue";
import {
  apiBaseMenuFunctionDel,
  apiBaseMenuFunctionPage,
} from "@/api/vone/base/meun";
export default {
  components: { Edit, addFunctionDialog },
  data() {
    return {
      formData: {
        menuId: "",
      },
      code: "",
      name: "",
      nodeDetail: {},
      tableData: {},
      tableLoading: false,
      addFunctionParams: {
        visible: false,
        parentMenu: {},
        id: "",
      },
      paramsData: {
        menuId: this.$route.params.id,
        name: undefined,
      },
      parentMenu: {},
      checkRow: {},
      input: "",
      isAdd: false,
      checkNodes: [], // 选中的节点,
      allAppList: [],
      menuName: "",
    };
  },
  watch: {
    $route() {
      this.getMenu();
    },
  },
  mounted() {
    this.getMenu();
  },
  methods: {
    getMenu() {
      const appList = this.$store.state.user.appRouterMenu;
      const personAppList = [];
      appList.map((item) => {
        if (item.name != "404") {
          personAppList.push({
            id: item.meta.menuId,
            menuCode: item.meta.code,
            icon: item.meta.icon,
            path: item.path,
            name: item.meta.title,
            sort: item.sort,
          });
        }
      });
      personAppList.map((item) => {
        if (item.id == this.$route.params.id) {
          this.menuName = item.name;
        }
      });
      this.allAppList = personAppList;
    },
    changeMenu(e) {
      this.$router.push("/base/menu/config/" + e);
    },
    async getFunctionList() {
      this.tableLoading = true;
      const pageObj = this.$refs.pagination?.pageObj || {
        current: 1,
        size: 20,
      };
      const params = {
        ...pageObj,
        extra: {},
        model: { ...this.formData },
      };
      const res = await apiBaseMenuFunctionPage(params);
      this.tableLoading = false;
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      this.tableData = res.data;
    },
    nodeChange(e) {
      this.isAdd = false;
      this.checkRow = e;
    },
    nodeCheck() {
      this.checkNodes = this.$refs.tree
        .getCheckedKeys()
        .concat(this.$refs.tree.getHalfCheckedKeys());
    },
    handleCommand(e) {
      if (e == "add") {
        this.isAdd = true;
        this.checkRow = {};
        this.addMenu();
      } else if (e == "del") {
        this.delTree();
      }
    },
    addMenu() {
      if (!this.parentMenu.id) {
        this.$message.warning("请选择父级菜单");
        return;
      }
      this.addFunctionParams = {
        visible: true,
        parentMenu: this.parentMenu,
        id: "",
      };
    },
    editRow(row) {
      this.addFunctionParams = {
        visible: true,
        parentMenu: this.parentMenu,
        id: row.id,
      };
    },
    // 删除
    async delRow(item) {
      await this.$confirm(`确定删除【${item.name}】吗?`, "删除", {
        type: "warning",
        customClass: "delConfirm",
        showClose: false,
      });
      const res = await apiBaseMenuFunctionDel([item.id]);

      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      this.$message.success("删除成功");
      this.getFunctionList();
    },
    // 刷新表格
    refreshTable(isReset) {
      this.$refs["function-table"].getTableData(isReset);
    },
    searchTableClick() {
      this.paramsData["name"] = this.name;
      this.getFunctionList();
      // this.$refs['function-table'].searchTable()
      this.name = undefined;
      this.paramsData["name"] = undefined;
      // this.$refs['function-table'].getTableData(isReset)
    },
    // 获取表格数据
    selecteTableData(val) {
      this.tableSeletData = val.selection;
    },
    // 左侧树点击事件
    handleNodeClick(data) {
      this.paramsData["menuId"] = data.id;
      this.parentMenu = data || {};
      this.formData["menuId"] = data.id;
      this.getFunctionList();

      // this.$refs['function-table'].searchTable()
    },
    handleClose() {
      this.dialogVisible = false;
    },
    hold() {
      this.$refs["addform"].validate((valid) => {
        if (valid) {
          alert("submit!");
          this.dialogVisible = false;
        } else {
          return false;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@use "@/styles/variables.scss";
.menu-main {
  background-color: var(--main-bg-color);
  border: 4px;
  .header {
    line-height: 48px;
    font-weight: 600;
    height: 48px;
    border-bottom: 1px solid var(--el-divider);
    padding: 0 16px;
  }
  aside {
    border-right: 1px solid var(--el-divider);
    height: calc(
      100vh - #{$nav-top-height} - #{$main-margin} - #{$main-margin}
    );

    .treeContent {
      height: calc(100vh - #{$hasHeader} - 65px);
      overflow-y: auto;
    }
  }
  main {
    padding: 16px 16px 10px 16px;
  }
}
.el-dropdown {
  color: #202124;
  font-weight: 600;
  cursor: pointer;
}
</style>
