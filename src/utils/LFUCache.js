const LFUCache = function(capacity) {
  this.size = capacity
  this.valueMap = new Map() // 记录值
  this.useMap = new Map() // 记录使用次数
}

LFUCache.prototype.get = function(key) {
  const { useMap, valueMap } = this
  if (!valueMap.has(key)) return null // 当存在时 删掉原来的重新添加  使用值加1

  const use = useMap.get(key)
  const value = valueMap.get(key)
  valueMap.delete(key)
  useMap.set(key, use + 1)
  valueMap.set(key, value)
  return JSON.parse(JSON.stringify(value))
}

LFUCache.prototype.put = function(key, value) {
  const { size, valueMap, useMap } = this
  // 保证保存的数据独立
  const saved = JSON.parse(JSON.stringify(value))
  if (size === 0) return
  const min = Math.min(...useMap.values()) // 缓存下 最小使用值
  if (valueMap.has(key)) { // 如果存在  use加一
    const use = useMap.get(key)
    useMap.set(key, use + 1)
  } else {
    // 不存在就直接加
    useMap.set(key, 1)
  }
  // 更新数据
  valueMap.set(key, saved)
  // 当超出, 删掉不常用的 当碰到用的次数相同的删掉 较前未使用的
  if (valueMap.size > size) {
    const it = valueMap.keys() // 缓存 key 遍历器
    let delKey = it.next().value
    while (useMap.get(delKey) !== min) { // 获取使用值为 min 的key
      delKey = it.next().value
    }
    useMap.delete(delKey) // 删掉该项
    valueMap.delete(delKey)
  }
}

export default LFUCache
