<template>
  <div>
    <el-dialog :title="`${title}导入`" width="40%" :model-value="visible" @update:model-value="$emit('update:visible', $event)" :before-close="onClose" :close-on-click-modal="false">
      <!-- Vue3: v-on="$listeners" 已被移除，事件监听器现在包含在 $attrs 中 -->
      <div v-if="showError">
        <el-alert :title="info.msg" type="warning" :closable="false" />
        <p>
          <el-button size="small" type="text" :loading="loading" @click.stop="downloadFun">下载文件</el-button>

        </p>
      </div>
      <el-form v-else ref="form" :model="form" :rules="rules">
        <el-form-item prop="file">
          <el-upload class="upload-demo" action :limit="1" :auto-upload="true" :multiple="false" :http-request="httpRequest" :before-upload="beforeUpload">
            <el-button slot="trigger" size="small" type="primary" icon="iconfont el-icon-icon-wenjian">选取文件</el-button>
            <el-button type="text" class="ml-3" @click="getDownload">下载批量上传模板</el-button>
            <div slot="tip" class="el-upload__tip">只能上传xls类型文件</div>
          </el-upload>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="onClose">取消</el-button>
        <el-button v-if="!showError" type="primary" icon="iconfont el-icon-edit-upload" :loading="upLoading" @click="submit">上传</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import { downloadTemplate, apiImport } from '@/api/common/index'

import { download } from '@/utils'

export default {
  name: 'ImportFile',
  emits: ['update:visible', 'success'],
  components: {
    // errorImport
  },
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    id: {
      type: String,
      default: undefined
    },
    title: {
      type: String,
      default: undefined
    },
    url: {
      type: String,
      default: undefined
    },
    importUrl: {
      type: String,
      default: undefined
    },
    type: {
      type: String,
      default: undefined
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      showError: false,
      form: {},
      upLoading: false,
      hasError: false,
      dialogFormVisible: false,
      errorImportParam: { visible: false },
      rules: {
        file: [
          { required: true, message: '请选择文件' }
          // {
          //   validator: (r, file, cb) => {
          //     const isExcel =
          //       file.name.substr(file.name.lastIndexOf('.') + 1) === 'xls'
          //     const size = file.size / 1024 <= 500
          //     if (!isExcel) {
          //       cb('只能上传xls类型文件!')
          //     } else if (!size) {
          //       cb('上传文件大小不能超过500M!')
          //     } else {
          //       cb()
          //     }
          //   }
          // }
        ]
      },
      info: {},
      loading: false
    }
  },
  methods: {
    onClose() {
      this.$emit('update:visible', false)
      if (this.showError) {
        return
      }
      this.$refs.form.resetFields()
    },
    httpRequest(file) {
      this.$set(this.form, 'file', file.file)
    },
    // 下载下载批量用户导入模板
    async getDownload() {
      // 项目下需要传项目id,其它地方不需要(缺陷，任务模板下载的url，projectId传参特殊)
      try {
        download(`${this.title}批量导入模版.xls`, await downloadTemplate(this.url, {
          projectId: !this.type ? this.$route.params.id : null
        }))
      } catch (e) {
        this.$message.error('模板下载失败')
        return
      }
    },
    async submit() {
      try {
        await this.$refs.form.validate()
        this.upLoading = true
        const param = {
          file: this.form.file,
          ...this.data
        }
        const res = await apiImport(this.importUrl, param)
        this.upLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }

        if (res.extra) {
          this.info = res
          this.$message.warning(res.msg)
          this.showError = true
        } else {
          this.showError = false
          this.$emit('success')
          this.onClose()
          this.$message.success(res.msg)
        }
      } catch (e) {
        this.upLoading = false
      }
    },
    // 文件验证规则
    beforeUpload(file) {
      const isXls = file.name.substr(file.name.lastIndexOf('.') + 1) === 'xls'
      // const size = file.size / 1024 <= 5
      if (!isXls) {
        this.$message.error('只支持xls类型文件!')
      }
      // if (!size) {
      //   this.$message.error('最大支持5M的文件!')
      // }

      return isXls
    },
    downloadFun() {
      this.loading = true

      const blob = this.dataURLtoBlob(this.info.extra.fileStream)
      const link = document.createElement('a')
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      link.setAttribute('download', this.info.extra.fileName)
      document.body.appendChild(link)
      link.click()

      this.loading = false
    },
    dataURLtoBlob(base64Str) {
      var bstr = atob(base64Str); var n = bstr.length; var u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      // 下载的是excel格式的文件
      return new Blob([u8arr], { type: 'application/vnd.ms-excel' })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
