<template>
  <el-table
    v-loading="grantLoading"
    :data="tableData"
    :span-method="objectSpanMethod"
  >
    <el-table-column prop="orgName" label="所属机构" />
    <el-table-column prop="name" label="所属用户">
      <template v-slot="scope">
        {{ scope.row.id }}
        <!-- <JUser :value="scope.row.id" /> -->
      </template>
    </el-table-column>

    <el-table-column prop="roleGroupName" label="所属角色" />
  </el-table>
</template>

<script>
import { apiBaseSelectOrgAndUserBySystemId } from '@/api/vone/cmdb/server'

export default {
  props: {
    type: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      tableData: [],
      grantLoading: false,
      userData: [],
    }
  },
  mounted() {
    this.getTable()
  },
  methods: {
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (row.idx === 0) {
          return {
            rowspan: row.org.userDatas.length,
            colspan: 1,
          }
        } else {
          return {
            rowspan: 0,
          }
        }
      }
    },
    async getTable() {
      this.grantLoading = true
      const res = await apiBaseSelectOrgAndUserBySystemId(
        this.$route.params.systemId
      )
      this.grantLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      // const table = data.map(r => ({
      //   name: r.name,
      //   userDataName: r.userDatas.map(i => i.name),
      //   userGroupsDatas: r.userDatas
      //     .map(i => i.userGroupsDatas)
      //     .map(j => j.roleName)
      // }));

      // res.data.forEach((org) => {
      //   org.echoMap.forEach((user, idx) => {
      //     user.org = org
      //     user.orgName = org.name
      //     user.idx = idx
      //   })
      // })

      // const table = _.concat.apply(
      //   _,
      //   res.data.map((r) => r.userDatas)
      // )

      // table.forEach((element) => {
      //   element.roleGroupName = element.userGroupsDatas
      //     .map((r) => r.roleGroupName)
      //     .join(',')
      // })

      // this.tableData = table
    },
  },
}
</script>
