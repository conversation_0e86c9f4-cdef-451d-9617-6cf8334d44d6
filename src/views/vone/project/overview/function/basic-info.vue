<template>
  <vone-echarts-card title="项目基本信息">
    <div class="vone-form-detail">
      <el-row :gutter="24">
        <el-col
          ><span class="form-title">项目标识:</span>
          {{ basicData.code }}
        </el-col>
        <el-col
          ><span class="form-title">项目名称:</span>
          <span
            class="text-over"
            style="width: 70%; color: var(--main-font-color)"
          >
            {{ basicData.name }}</span
          >
        </el-col>
        <el-col
          ><span class="form-title">组织机构:</span>
          {{ basicData.org && basicData.org.name }}
        </el-col>
        <el-col
          ><span class="form-title" style="float: left">负责人:</span>
          <span v-if="basicData.leadingUser">
            <vone-user-avatar
              :avatar-path="basicData.leadingUser.avatarPath"
              :name="basicData.leadingUser.name"
              :avatar-type="true"
            />
          </span>

          <!-- <a style="display:flex;aliagn-items:center;">

                <el-tooltip v-for="(item, index) in baseUsers" :key="index" style="margin-left:-5px" :content="item.name" placement="top">
                  <vone-user-avatar
                    :avatar-path="item.avatarPath"
                    :avatar-type="true"
                    :show-name="false"
                  />
                </el-tooltip>

                <template v-if="moreUsers&&moreUsers.length>0">
                  <el-popover placement="bottom" trigger="hover">
                    <ul class="more_users">
                      <li v-for="item in moreUsers" :key="item.id">
                        <vone-user-avatar
                          :avatar-path="item.avatarPath"
                          :name="item.name"
                          :avatar-type="true"
                        />
                      </li>
                    </ul>
                    <span slot="reference" class="persionL">+{{ moreUsers.length }}</span>
                  </el-popover>

                </template>
              </a> -->
        </el-col>
      </el-row>
    </div>
  </vone-echarts-card>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import { searchProjectBasic } from '@/api/vone/dashboard/index'
export default {
  data() {
    return {
      basicData: {
        name: '',
      },
      persionList: [],
    }
  },
  computed: {
    baseUsers() {
      return this.basicData?.users?.slice(0, 5) || []
    },
    moreUsers() {
      return this.basicData.users?.slice(5, -1) || []
    },
  },
  mounted() {
    this.getBasic()
  },
  methods: {
    getBasic() {
      searchProjectBasic(this.$route.params.id).then((res) => {
        if (res.isSuccess) {
          this.basicData = res.data
          console.log(this.basicData, 'this.basicData')
          $emit(this, 'success', res.data)
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
  },
  emits: ['success'],
}
</script>

<style lang="scss" scoped>
.vone-form-detail :deep(.el-col) {
  line-height: 46px;
}
:deep(.el-card__header) {
  font-weight: bold;
}
.persionL {
  display: inline-block;
  background: #eaecf0;
  height: 26px;
  width: 26px;
  text-align: center;
  border-radius: 50%;
  line-height: 26px;
  color: #1d2129;
}
.more_users {
  max-height: 320px;
  margin-right: -12px;
  overflow: auto;
}
</style>
