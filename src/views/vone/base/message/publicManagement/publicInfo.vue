<template>
  <vone-drawer size="md" title="公告详情" v-model="visible" :before-close="onClose" :wrapper-closable="false" width="60%" :close-on-click-modal="false">

    <vone-desc v-loading="loading" :column="1" class="page">

      <vone-desc-item label="标题">
        {{ form.title }}
      </vone-desc-item>
      <vone-desc-item label="索引号">
        {{ form.code }}
      </vone-desc-item>

      <vone-desc-item label="主题分类">
        {{ form.noticeType }}
      </vone-desc-item>
      <vone-desc-item label="发布日期">
        {{ form.updateTime }}
      </vone-desc-item>
      <vone-desc-item label="发布状态">
        <template>
          <el-tag v-if="form.state == 2" style="color:var(--Red-10); border-color:var(--Red--50); background: var(--Red--50)">已撤销</el-tag>
          <el-tag v-if="form.state == 1" style="color:var(--Green-10); border-color:var(--Green--50); background: var(--Green--50)">已发布</el-tag>
          <el-tag v-if="form.state == 0" style="color:#8C8C8C; border-color:#F5F5F5; background: #F5F5F5"> 暂存</el-tag>
        </template>
      </vone-desc-item>

      <vone-desc-item label="创建时间">
        {{ form.createTime }}
      </vone-desc-item>
      <vone-desc-item label="发布人">
        <span v-if="form.updatedBy && form.echoMap && form.echoMap.updatedBy">
          <vone-user-avatar
            :avatar-path="form.echoMap.updatedBy.avatarPath"
            :name="form.echoMap.updatedBy.name"
          />
        </span>

      </vone-desc-item>
      <vone-desc-item label="联系人">
        <span v-if="form.liaisoner && form.echoMap && form.echoMap.liaisoner">
          <vone-user-avatar
            :avatar-path="form.echoMap.liaisoner.avatarPath"
            :name="form.echoMap.liaisoner.name"
          />
        </span>

      </vone-desc-item>
      <vone-desc-item label="联系电话">
        {{ form.phone }}
      </vone-desc-item>
      <vone-desc-item label="公告内容">
        {{ form.body }}
      </vone-desc-item>
      <vone-desc-item label="描述">
        {{ form.description }}
      </vone-desc-item>
      <vone-desc-item>
        <vone-upload
          ref="noticeUploadFile"
          file-title="上传文件"
          multiple
          drag
          biz-type="NOTICE_FILE_UPLOAD"
          :is-upload="false"
          :files-data="fileList"
          :class-name="'hidebtn'"
        >
          <div class="el-upload__text"><i class="el-icon-upload" />将文件拖到此处，或<em>点击上传</em>文件</div>
        </vone-upload>
      </vone-desc-item>

    </vone-desc>

    <el-row slot="footer">
      <el-button @click="onClose">取消</el-button>
    </el-row>
  </vone-drawer>
</template>

<script>

import {
  getNotice
} from '@/api/vone/base/notice'

export default {

  props: {
    id: {
      type: String,
      default: null
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      form: {},
      fileList: []
    }
  },
  mounted() {
    this.getInfo()
  },
  methods: {

    onClose() {
      this.$emit('update:visible', false)
    },
    getInfo() {
      getNotice(this.id).then(res => {
        if (res.isSuccess) {
          this.form = res.data
          this.fileList = res.data.files
        } else {
          this.$message.warning(res.msg)
        }
      })
    }

  }
}
</script>

<style  lang="scss" scoped >

.page{
	padding: 20px;
}
:deep(.vone-el-drawer__layout) {
	padding: 20px;
}

</style>

