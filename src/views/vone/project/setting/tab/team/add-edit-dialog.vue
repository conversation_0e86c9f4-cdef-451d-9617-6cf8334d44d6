<template>
  <el-dialog
    :title="editTitle"
    :model-value="dialogVisible"
    width="600px"
    top="5vh"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="close"
  >
    <!-- 表单部分 -->
    <el-form
      ref="form"
      v-loading="formLoading"
      :rules="rules"
      :model="teamForm"
      label-position="top"
      label-width="100px"
    >
      <el-row :gutter="24">
        <el-col v-if="teamForm.parentId">
          <el-form-item label="上级团队" prop="parentName">
            <el-input
              v-model="teamForm.parentName"
              disabled
              placeholder="请选择上级团队"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="团队名称" prop="name">
            <el-input
              v-model="teamForm.name"
              :disabled="teamForm.type == 'OrgTeam'"
              placeholder="请输入名称"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="类型" prop="type">
            <el-select v-model="teamForm.type" disabled>
              <el-option
                v-for="item in typeList"
                :key="item.id"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="负责人" prop="leaderBy">
            <vone-remote-user v-model:value="teamForm.leaderBy" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="描述" prop="description">
            <el-input
              v-model="teamForm.description"
              :disabled="teamForm.type == 'OrgTeam'"
              type="textarea"
              placeholder="描述"
              maxlength="100"
              show-word-limit
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template v-slot:footer>
      <div>
        <el-button @click="close">取消</el-button>
        <el-button type="primary" :loading="loading" @click="onSave"
          >确定</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script>
import {
  $on,
  $off,
  $once,
  $emit,
} from '../../../../../../utils/gogocodeTransfer'
import { addTeam, editProjectTeam } from '@/api/vone/project/team'
import { editTeam } from '@/api/vone/base/team'
import { apiBaseDictNoPage } from '@/api/vone/base/dict'
export default {
  props: {
    clickNode: {
      type: Object,
      default: () => {},
    },
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    editTitle: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      srcList: [],
      rules: {
        parentId: [
          {
            required: true,
            message: '请选择上级团队',
            trigger: 'change',
          },
        ],
        type: [
          {
            required: true,
            message: '请选择类型',
            trigger: 'change',
          },
        ],
        leaderBy: [
          {
            required: true,
            message: '请选择负责人',
            trigger: 'blur',
          },
        ],
        name: [
          {
            required: true,
            message: '请输入长度不超过50个字符的名称',
            trigger: 'change',
          },
          {
            max: 50,
            message: '请输入长度不超过50个字符的名称',
            trigger: 'change',
          },
        ],
      },
      teamForm: {
        id: '',
        name: '',
        parentId: null,
        parentName: '',
        leaderBy: '',
        type: 'ProjectTeam',
        description: '',
        projectId: this.$route.params.id,
      },
      formLoading: false,
      typeList: [],
      parentData: [],
      loading: false,
    }
  },
  computed: {},
  mounted() {
    this.getType()
    if (this.clickNode.id) {
      if (this.editTitle == '新增团队') {
        // this.teamForm.id = this.clickNode.id
        this.teamForm.parentId = this.clickNode.id
        this.teamForm.parentName = this.clickNode.name
      } else {
        // 编辑
        this.teamForm = this.clickNode
        this.teamForm.projectId = this.$route.params.id
        this.teamForm.parentId = this.clickNode.parentId
        this.teamForm.parentName = this.clickNode?.echoMap?.parentId?.name
      }
    }
  },
  methods: {
    getType() {
      apiBaseDictNoPage({ type: 'TEAM_TYPE' }).then((res) => {
        if (res.isSuccess) {
          this.typeList = res.data
        }
      })
    },
    async onSave() {
      try {
        await this.$refs.form.validate()
      } catch (e) {
        return
      }
      this.loading = true
      if (this.editTitle == '新增团队') {
        addTeam(this.teamForm).then(async (res) => {
          this.loading = false
          if (res.isSuccess) {
            this.$message.success('保存成功')
            $emit(this, 'success')
            $emit(this, 'update:dialogVisible', false)
          } else {
            this.$message.warning(res.msg)
          }
        })
      } else {
        // editTeam()
        if (this.clickNode?.type == 'OrgTeam') {
          editProjectTeam(
            this.teamForm.projectId,
            this.teamForm.id,
            this.teamForm.leaderBy
          ).then(async (res) => {
            this.loading = false
            if (res.isSuccess) {
              this.$message.success('修改成功')
              $emit(this, 'success')
              $emit(this, 'update:dialogVisible', false)
            } else {
              this.$message.warning(res.msg)
            }
          })
        } else {
          editTeam(this.teamForm).then(async (res) => {
            this.loading = false
            if (res.isSuccess) {
              this.$message.success('修改成功')
              $emit(this, 'success')
              $emit(this, 'update:dialogVisible', false)
            } else {
              this.$message.warning(res.msg)
            }
          })
        }
      }
    },
    close() {
      $emit(this, 'update:dialogVisible', false)
      this.$refs.form.resetFields()
    },
  },
  emits: ['update:dialogVisible', 'success'],
}
</script>

<style lang="scss" scoped>
.el-image {
  height: 100px;
  width: 100px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
</style>
