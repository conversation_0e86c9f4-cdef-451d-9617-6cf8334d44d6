<template>
  <div>
    <el-dialog
      v-loading="loading"
      title="选择导出字段"
      v-model:value="visible"
      :before-close="onClose"
      :close-on-click-modal="false"
    >
      <el-checkbox
        v-model="checkAll"
        :indeterminate="isIndeterminate"
        @change="checkAllChange"
        >全选</el-checkbox
      >
      <div style="margin: 15px 0" />

      <el-checkbox-group v-model="checkedHost" @change="groupChange">
        <el-row>
          <template v-for="(item, index) in groupData" :key="index">
            <el-col :span="8">
              <el-checkbox :label="item.key">{{ item.name }}</el-checkbox>
            </el-col>
          </template>
        </el-row>
      </el-checkbox-group>

      <template v-slot:footer>
        <div class="dialog-footer">
          <el-button @click="onClose">取消</el-button>
          <el-button type="primary" @click="exportHost">导出</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import { apiCmdbHostColumns, apiCmdbHostExport } from '@/api/vone/cmdb/host'

export default {
  props: {
    visible: {
      type: Boolean,
      default: true,
    },
    params: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      loading: false,
      groupData: [],
      checkAll: false,
      checkedHost: [],
      groupOption: [],
      isIndeterminate: true,
      groupName: [],
    }
  },
  mounted() {
    this.getHostColumnsData()
  },
  methods: {
    onClose() {
      this.checkedHost = []
      $emit(this, 'update:visible', false)
    },
    checkAllChange(val) {
      this.checkedHost = val ? this.groupOption : []

      this.isIndeterminate = false
    },
    groupChange(value) {
      const checkedHost = value.length

      this.checkAll = checkedHost === this.groupOption.length
      this.isIndeterminate =
        checkedHost > 0 && checkedHost < this.groupOption.length
    },

    async getHostColumnsData() {
      this.loading = false
      const { data, success, message } = await apiCmdbHostColumns()
      //   this.loading = true;
      if (!success) {
        return this.$message.error(message)
      }
      this.groupData = data
      this.groupOption = data.map((r) => r.key)

      this.groupName = data.map((r) => r.name)
    },
    // 导出服务器数据
    async exportHost() {
      if (!this.checkedHost.length) {
        this.$message.warning('请至少选择一项')
        return
      }
      const hJson = this.groupData.filter(
        (i, j) => this.checkedHost.indexOf(i.key) !== -1
      )
      const obj = {}
      hJson.forEach((i) => {
        obj[i.key] = i.name
      })

      const hostJson = {
        params: this.params,
        header: obj,
      }
      this.loading = true
      await apiCmdbHostExport(hostJson)
      this.loading = false
      $emit(this, 'update:visible', false)
      $emit(this, 'success')
      this.checkedHost = []
    },
  },
  emits: ['update:visible', 'success'],
}
</script>
