<template>
  <div>
    <header class="records">归档记录</header>

    <vone-empty v-if="recordList && recordList.length === 0" style="height:500px;" />

    <el-collapse v-else v-model="active" accordion class="collapse" @change="handleChange">
      <el-collapse-item v-for="(item,index) in recordList" :key="item.updateTime" :name="index">
        <div slot="title" class="collapse-title">
          <el-row type="flex" justify="space-between" align="middle">
            <span>{{ item.updateTime }}</span>

            <vone-user-avatar v-if="item.updatedBy" :avatar-path="item.echoMap.updatedBy.avatarPath" :name="item.echoMap.updatedBy.name" />

            <el-tooltip class="item" effect="dark" content="撤销归档" placement="top">
              <el-button size="mini" type="text" :loading="item.revertLoading" icon="iconfont el-icon-application-undo" @click.stop="callback(item)" />
            </el-tooltip>
          </el-row>
        </div>
        <recordTable ref="recordTable" :data="item" style="margin-top:16px;" />
      </el-collapse-item>
    </el-collapse>

    <el-pagination v-if="recordList && recordList.length > 0 " layout="->,total,sizes, prev, pager, next, jumper" :page-size.sync="pageData.size" :current-page.sync="pageData.current" :total="pageData.total" @size-change="handlePageChange" @current-change="handlePageChange" />
  </div>
</template>
<script>
import { getArchiveRecord, recallArchiveRecord } from '@/api/vone/testplan'
import recordTable from './record-table.vue'
export default {
  components: {
    recordTable
  },
  data() {
    return {
      collapseLoading: false,
      tableShow: false,
      active: '',
      recordList: [],
      pageData: { // 分页配置
        current: 1,
        size: 10,
        total: 0,
        sort: 'id',
        model: {
          libraryId: this.$route.params.caseId
        }
      },
      pageSizes: [10, 20, 50, 100]
    }
  },
  created() {
    this.getrecordList()
  },
  methods: {
    handleChange(val) {
      if (val >= 0) {
        this.$refs.recordTable[val].getTableData()
      }
    },
    async callback(item) {
      try {
        await this.$confirm('确定撤销归档吗？', '删除', {
          type: 'warning',
          customClass: 'delConfirm'
        })
        this.$set(item, 'revertLoading', true)
        const res = await recallArchiveRecord(item.updateTime, item.updatedBy)
        this.$set(item, 'revertLoading', false)
        if (res.isSuccess) {
          this.$message.success('撤销成功')

          this.getrecordList()
          this.active = ''
        }
      } catch (e) {
        this.$set(item, 'revertLoading', false)
      }
    },
    handlePageChange() {
      this.getrecordList()
    },
    // 查记录
    async getrecordList() {
      const res = await getArchiveRecord(this.pageData)
      if (res.isSuccess) {
        this.recordList = res.data.records
        this.pageData.total = Number(res.data.total)
      }
    }

  }

}
</script>
<style lang="scss" scoped>

.records {
  height: 68px;
  line-height: 68px;
  padding: 0 16px;
  font-weight: 600;
  font-size: 16px;
  border-bottom: 1px solid #ebeef5;
}
.collapse{
	margin:16px;
	height:calc(100vh - 218px);
}
::v-deep .el-collapse-item__header {
  height: 48px !important;
  line-height: 48px !important;
}
::v-deep .collapse-title {
  flex: 1 0 90%;
  order: 1;
  .el-collapse-item__header {
    flex: 1 0 auto;
    order: -1;
  }
}
</style>

