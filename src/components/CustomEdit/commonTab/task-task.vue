<template>
  <div class="wrapper">
    <!-- 父任务 -->
    <section v-if="parentTableData.length" class="section-has parent-content">
      <el-row type="flex" justify="space-between" style="margin-top: 16px">
        <span class="host_title_parent"> 父任务 </span>
      </el-row>
      <section class="parent-table">
        <vxe-table
          v-if="parentTableData.length != 0"
          ref="tableData"
          class="vone-vxe-table nobg-table"
          show-overflow="tooltip"
          :loading="tableLoading"
          :empty-render="{ name: 'empty' }"
          :data="parentTableData"
          :column-config="{ resizable: true, minWidth: 120 }"
          row-id="id"
        >
          <vxe-column title="名称" field="code" fixed="left">
            <template v-slot="scope">
              <a class="table_title" @click="toRouter(scope.row)">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#el-icon-icon-renwu" />
                </svg>
                {{ scope.row.code }}
                {{ scope.row.name }}
              </a>
            </template>
          </vxe-column>
          <vxe-column title="状态" field="stateCode">
            <template v-slot="scope">
              <span
                v-if="
                  scope.row.stateCode &&
                  scope.row.echoMap &&
                  scope.row.echoMap.stateCode
                "
              >
                <span
                  :style="{
                    border: `1px solid ${scope.row.echoMap.stateCode.color}`,
                    color: `${scope.row.echoMap.stateCode.color}`,
                  }"
                  class="tagCustom"
                  >{{ scope.row.echoMap.stateCode.name }}</span
                >
              </span>
            </template>
          </vxe-column>
          <vxe-column title="处理人" field="handleBy">
            <template v-slot="scope">
              <span>
                <vone-user-avatar
                  :avatar-path="
                    getUserInfo(scope.row)
                      ? getUserInfo(scope.row).avatarPath
                      : ''
                  "
                  :name="
                    getUserInfo(scope.row) ? getUserInfo(scope.row).name : ''
                  "
                />
              </span>
            </template>
          </vxe-column>
          <vxe-column title="计划完成时间" field="planEtime">
            <template v-slot="scope">
              {{
                scope.row.planEtime
                  ? dayjs(scope.row.planEtime).format("YYYY-MM-DD")
                  : null
              }}
            </template>
          </vxe-column>
          <vxe-column title="操作" fixed="right" align="left" width="60">
            <template v-slot="scope">
              <el-button
                v-if="!isParent"
                type="text"
                size="small"
                :icon="elIconIconLineJiechuguanlian"
                class="table_handler"
                @click="deleteTask(scope.row, 'parent')"
              />
            </template>
          </vxe-column>
        </vxe-table>
        <vone-empty v-else />
      </section>
    </section>
    <!-- 拆解任务 -->
    <section class="section-has">
      <el-row type="flex" justify="space-between">
        <span class="host_title"> 拆解任务 </span>
        <span>
          <el-button
            size="mini"
            type="text"
            :icon="ElIconPlus"
            @click="handleClickAdd"
          >
            新增
          </el-button>
        </span>
      </el-row>

      <!-- 拆解任务 -->
      <section v-loading="childrenLoading">
        <div v-if="childTask">
          <el-row style="margin-top: 16px">
            <simpleAddIssue
              no-file
              :type-code="'TASK'"
              :parent-id="issueId"
              @success="addSuccess"
              @cancel="childTask = false"
            />
          </el-row>
        </div>
        <vxe-table
          v-if="childrenTableData.length != 0"
          ref="tableData"
          class="vone-vxe-table nobg-table"
          show-overflow="tooltip"
          :loading="tableLoading"
          :empty-render="{ name: 'empty' }"
          :data="childrenTableData"
          :column-config="{ resizable: true, minWidth: 120 }"
          row-id="id"
        >
          <vxe-column title="名称" field="code" fixed="left">
            <template v-slot="scope">
              <a class="table_title" @click="toRouter(scope.row)">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#el-icon-icon-renwu" />
                </svg>
                {{ scope.row.code }}
                {{ scope.row.name }}
              </a>
            </template>
          </vxe-column>
          <vxe-column title="类型" field="typeCode">
            <template v-slot="scope">
              <span
                v-if="
                  scope.row.typeCode &&
                  scope.row.echoMap &&
                  scope.row.echoMap.typeCode
                "
              >
                <el-tag>
                  {{ scope.row.echoMap.typeCode.name }}
                </el-tag>
              </span>
            </template>
          </vxe-column>
          <vxe-column title="处理人" field="handleBy">
            <template v-slot="scope">
              <span>
                <vone-user-avatar
                  :avatar-path="
                    getUserInfo(scope.row)
                      ? getUserInfo(scope.row).avatarPath
                      : ''
                  "
                  :name="
                    getUserInfo(scope.row) ? getUserInfo(scope.row).name : ''
                  "
                />
              </span>
            </template>
          </vxe-column>
          <vxe-column title="计划完成时间" field="planEtime">
            <template v-slot="scope">
              {{
                scope.row.planEtime
                  ? dayjs(scope.row.planEtime).format("YYYY-MM-DD")
                  : null
              }}
            </template>
          </vxe-column>
          <vxe-column title="操作" fixed="right" align="left" width="100">
            <template v-slot="scope">
              <el-tooltip class="item" content="编辑" placement="top">
                <el-button
                  style="padding: 0 10px; min-width: 20px"
                  type="text"
                  :disabled="
                    scope.row.stateCode == 'DONE' ||
                    !$permission('project_issue_edit')
                  "
                  :icon="elIconApplicationEdit"
                  @click="toRouter(scope.row, 'edit')"
                />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="取消关联" placement="top">
                <el-button
                  style="padding: 0 10px; min-width: 20px"
                  type="text"
                  size="small"
                  :icon="elIconIconLineJiechuguanlian"
                  class="table_handler"
                  @click="deleteTask(scope.row, 'task')"
                />
              </el-tooltip>
            </template>
          </vxe-column>
        </vxe-table>
        <vone-empty v-else />
      </section>
    </section>
    <!-- 关联任务 -->
    <section class="section-has" style="margin-top: 16px">
      <el-row type="flex" justify="space-between">
        <span class="host_title"> 关联任务 </span>
        <span>
          <el-button
            size="mini"
            type="text"
            :icon="elIconEditRelate"
            @click="addTask = !addTask"
          >
            关联
          </el-button>
        </span>
      </el-row>
      <div v-if="addTask" style="margin-top: 16px">
        <el-row type="flex" class="row_host" style="align-items: center">
          <el-select
            v-model="issueForm.projectId"
            filterable
            style="margin-right: 6px"
            @change="changeProject"
            @focus="setOptionWidth"
          >
            <el-option
              v-for="item in projectIdList"
              :key="item.key"
              :title="item.name"
              :label="item.name"
              :value="item.id"
              :style="{ width: selectOptionWidth }"
            />
          </el-select>
          <el-select
            v-model.trim="issueForm.issue"
            placeholder="请输入任务名称"
            clearable
            filterable
            remote
            :remote-method="getTaskList"
            :loading="requireLoading"
            class="requireSelect"
            @focus="setOptionWidth"
          >
            <el-option
              v-for="ele in taskList"
              :key="ele.id"
              :value="ele.id"
              :label="ele.name"
              :disabled="ele.disabled"
              :style="{ width: selectOptionWidth }"
              :title="ele.name"
            >
              {{ `${ele.code}   ${ele.name}` }}
            </el-option>
          </el-select>
        </el-row>
        <div class="minbtns">
          <el-button
            size="mini"
            style="margin-left: 8px"
            class="miniBtn"
            @click="addTask = !addTask"
          >
            取消
          </el-button>
          <el-button
            type="primary"
            size="mini"
            class="miniBtn"
            style="margin-left: 8px"
            :loading="relationTaskLoading"
            @click="addRelationTask"
          >
            保存
          </el-button>
        </div>
      </div>
      <!-- 关联 -->
      <section v-loading="relationLoading">
        <vxe-table
          v-if="relationTableData.length != 0"
          ref="tableData"
          class="vone-vxe-table nobg-table"
          show-overflow="tooltip"
          :loading="tableLoading"
          :empty-render="{ name: 'empty' }"
          :data="relationTableData"
          :column-config="{ resizable: true, minWidth: 120 }"
          row-id="id"
        >
          <vxe-column title="需求" field="code" fixed="left">
            <template v-slot="scope">
              <a class="table_title" @click="toRouter(scope.row)">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#el-icon-icon-renwu" />
                </svg>
                {{ scope.row.code }}
                {{ scope.row.name }}
              </a>
            </template>
          </vxe-column>
          <vxe-column title="状态" field="stateCode">
            <template v-slot="scope">
              <span
                v-if="
                  scope.row.stateCode &&
                  scope.row.echoMap &&
                  scope.row.echoMap.stateCode
                "
              >
                <span
                  :style="{
                    border: `1px solid ${scope.row.echoMap.stateCode.color}`,
                    color: `${scope.row.echoMap.stateCode.color}`,
                  }"
                  class="tagCustom"
                  >{{ scope.row.echoMap.stateCode.name }}</span
                >
              </span>
            </template>
          </vxe-column>
          <vxe-column title="处理人" field="handleBy">
            <template v-slot="scope">
              <span>
                <vone-user-avatar
                  :avatar-path="
                    getUserInfo(scope.row)
                      ? getUserInfo(scope.row).avatarPath
                      : ''
                  "
                  :name="
                    getUserInfo(scope.row) ? getUserInfo(scope.row).name : ''
                  "
                />
              </span>
            </template>
          </vxe-column>
          <vxe-column title="计划完成时间" field="planEtime">
            <template v-slot="scope">
              {{
                scope.row.planEtime
                  ? dayjs(scope.row.planEtime).format("YYYY-MM-DD")
                  : null
              }}
            </template>
          </vxe-column>
          <vxe-column title="操作" fixed="right" align="left" width="100">
            <template v-slot="scope">
              <el-tooltip class="item" content="编辑" placement="top">
                <el-button
                  style="padding: 0 10px; min-width: 20px"
                  type="text"
                  :disabled="
                    scope.row.stateCode == 'DONE' ||
                    !$permission('project_issue_edit')
                  "
                  :icon="elIconApplicationEdit"
                  @click="toRouter(scope.row, 'edit')"
                />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="取消关联" placement="top">
                <el-button
                  style="padding: 0 10px; min-width: 20px"
                  type="text"
                  :icon="elIconIconLineJiechuguanlian"
                  @click="deleteRelation(scope.row)"
                />
              </el-tooltip>
            </template>
          </vxe-column>
        </vxe-table>
        <vone-empty v-else />
      </section>
    </section>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from "../../../utils/gogocodeTransfer";
import { apiAlmProjectNoPage } from "@/api/vone/project/index";

import { debounce } from "lodash";

import simpleAddIssue from "../components/fast-add-task.vue";

import {
  apiAlmProjectRelation,
  apiAlmAddDepend,
  apiAlmDeleteDepend,
} from "@/api/vone/project/issue";
import {
  apiAlmTaskFindNextNoPage,
  apiAlmCorrelatedTaskDel,
} from "@/api/vone/project/task";

export default {
  name: "TaskTask",
  components: {
    simpleAddIssue,
  },
  props: {
    issueId: {
      type: String,
      default: undefined,
    },
    projectId: {
      type: String,
      default: null,
    },
    typeCode: {
      type: String,
      default: null,
    },
    isParent: {
      // 判断是否为父需求，父需求没有解除关联关系的按钮
      type: Boolean,
      default: false,
    },
    // 拆分子项是否弹框
    showPopupForSplit: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      issueForm: {},
      childTask: false,
      childrenTableData: [], // 拆解任务列表
      relationTableData: [], // 关联任务列表
      parentTableData: [], // 父任务
      addCoTask: null,
      noCoProduct: false,
      relationTaskLoading: false,
      showAddIssue: null,
      childrenLoading: false,
      relationLoading: false,
      parentLoading: false,
      noProduct: false,
      productLoading: false,

      productList: [],
      pageLoading: false,

      projectIdList: [], // 项目
      issueTab: {
        relation: "DEPEND",
      },
      addRelation: false,
      tableLoading: false,
      createSimple: false,
      typeId: "",
      formData: {},

      taskList: [],

      loading: false,
      affectTable: [], // 影响
      connectTable: [], // 关联
      dependTable: [], // 依赖
      relationTable: [],
      issueTable: [], // 父需求

      selectOptionWidth: "",
      requireLoading: false,
      addTask: false,
      form: {},
    };
  },
  computed: {
    getUserInfo() {
      return function (row) {
        return row?.handleBy && row?.echoMap?.handleBy;
      };
    },
  },
  watch: {
    issueId() {
      this.getProjectList();
      this.getChildrenTable(); // 需求拆分
      this.getRelationTable(); // 关联任务/父任务
    },
    immediate: true,
  },
  mounted() {
    this.getRelationTable();
    this.getProjectList();
    this.getChildrenTable(); // 需求拆分
  },
  methods: {
    handleClickAdd() {
      if (this.showPopupForSplit) {
        $emit(this, "add-child", "task");
      } else {
        this.childTask = !this.childTask;
      }
    },
    addSuccess() {
      this.getChildrenTable();
      $emit(this, "initList");
    },

    toRouter(val, type) {
      // if (!this.$permission('project_task_view')) {
      //   this.$message.warning('当前登录账户没有查看需求的权限,请联系管理员授权')
      //   return
      // }
      const newpage = this.$router.resolve({
        path: `/project/task/${this.$route.params.projectKey}/${this.$route.params.projectTypeCode}/${this.$route.params.id}`,
        query: {
          showDialog: true,
          queryId: val.id,
          rowTypeCode: val.typeCode,
          stateCode: val.stateCode,
          type: type,
        },
      });
      window.open(newpage.href, "_blank");
    },
    // 下拉框弹出时，设置弹框的宽度
    setOptionWidth(event) {
      this.$nextTick(() => {
        this.selectOptionWidth =
          event.target.parentNode.parentNode.clientWidth + "px";
      });
    },

    // 拆解列表
    async getChildrenTable() {
      if (!this.issueId) {
        return;
      }
      this.childTask = false;
      this.childrenLoading = true;
      const res = await apiAlmTaskFindNextNoPage({
        parentId: this.issueId,
      });
      this.childrenLoading = false;
      if (!res.isSuccess) {
        return;
      }
      this.childrenTableData = res.data;
    },
    // 关联任务
    async getRelationTable() {
      if (!this.issueId) {
        return;
      }
      this.addTask = false;
      this.relationLoading = true;
      const res = await apiAlmProjectRelation("TASK", this.issueId);
      this.relationLoading = false;
      if (!res.isSuccess) {
        return;
      }
      this.relationTableData = res.data.RELATION;
      this.parentTableData = res.data.PARENT;
    },
    // 归属项目
    async getProjectList() {
      const res = await apiAlmProjectNoPage();
      if (!res.isSuccess) {
        return;
      }
      this.projectIdList = res.data;
      this.issueForm["projectId"] = this.$route.params.id;
    },
    // 添加关联关系
    async addRelationTask() {
      try {
        this.relationTaskLoading = true;
        const res = await apiAlmAddDepend({
          fromId: this.issueId,
          fromType: "TASK",
          relationType: "RELATION",
          toId: this.issueForm.issue,
          toType: "TASK",
        });
        this.relationTaskLoading = false;
        if (!res.isSuccess) {
          return;
        }
        this.$message.success(res.msg);
        this.getRelationTable();
        this.issueForm["issue"] = "";
      } catch (e) {
        this.relationTaskLoading = false;
      }
    },

    // 查任务列表
    getTaskList: debounce(async function (query, projectId) {
      try {
        this.requireLoading = true;
        const res = await apiAlmTaskFindNextNoPage({
          name: query,
          projectId: this.issueForm.projectId,
        });
        this.requireLoading = false;
        if (!res.isSuccess) {
          this.$message.warning(res.msg);
          return;
        }
        res.data.forEach((element) => {
          element.disabled = element.id == this.issueId;
        });

        this.taskList = res.data;
      } catch (e) {
        this.requireLoading = false;
      }
    }, 1000),

    // 删除关联任务
    async deleteRelation(row) {
      this.$confirm(
        `你确定要取消关联 【${row.name}】的关联关系吗?`,
        "取消关联",
        {
          confirmButtonText: "确认",
          type: "warning",
          closeOnClickModal: false,
        }
      )
        .then(async () => {
          row["ideaId"] = null;
          const { isSuccess, msg } = await apiAlmDeleteDepend(row.relationId);
          if (!isSuccess) {
            this.$message.error(msg);
            return;
          }
          this.$message.success(msg);
          this.getRelationTable();
          // this.$emit('success')
        })
        .catch(() => {});
    },
    // 删除任务
    async deleteTask(row, type) {
      await this.$confirm(`确定解除与【${row.name}】的关联关系吗?`, "提示", {
        type: "warning",
        closeOnClickModal: false,
      });
      const { isSuccess, msg } = await apiAlmCorrelatedTaskDel({
        correlatedType: "DELETE",
        taskId: type == "parent" ? row.id : this.issueId,
        taskIds: type == "parent" ? [this.issueId] : [row.id],
      });
      if (!isSuccess) {
        this.loading = false;
        this.$message.error(msg);
        return;
      }
      this.$message.success(msg);
      if (type == "parent") {
        this.getRelationTable();
      } else {
        this.getChildrenTable();
      }
    },
    changeProject(val) {
      this.getTaskList(val);
    },
  },
  emits: ["add-child", "initList"],
};
</script>

<style lang="scss" scoped>
:deep(.vone-vxe-table .vxe-table--body-wrapper) {
  height: unset;
}
.i-text {
  color: var(--main-theme-color);
  cursor: pointer;
}
.wrapper {
  .parent-table {
    :deep(.el-table tr.el-table__row) {
      background-color: var(--node-cildren-bg-color);
    }
  }
  .parent-content {
    .host_title_parent {
      color: #202124;
      font-weight: 600;
      line-height: 22px;
      border-radius: 5px;
    }
    margin: -16px -20px 16px -20px;
    padding: 0px 18px !important;
  }
  // --------------------
  .shadow_bottom {
    margin: 0 -38px 16px;
    padding: 0 16px 16px;
    box-shadow: 0px 15px 10px -15px #ccc;
  }
  .section-host {
    margin: 1px 0;
  }
  .section-has {
    // padding: 10px 0;

    .host_title {
      font-weight: 600;
      line-height: 22px;
      border-radius: 5px;
      color: #202124;
    }
  }
}
:deep(.name_col) {
  .cell {
    display: flex;
    div.name_icon {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: normal;
      word-break: break-all;
      flex: 1;
      display: flex;
      align-items: center;
    }
  }
}
.table_title {
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
:deep(.el-button) {
  line-height: 24px;
  height: 24px;
}
:deep(.el-row--flex) {
  line-height: 22px; /*// margin: 24px 0px 9px 0px;*/
}
.minbtns {
  text-align: right;
  :deep(.el-button + .el-button) {
    margin: 12px 0px 12px 12px;
  }
}
</style>
