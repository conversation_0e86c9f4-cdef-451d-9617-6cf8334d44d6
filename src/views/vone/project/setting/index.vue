<template>
  <div>
    <div v-if="types.length" class="pageContentNoH topTab">
      <el-tabs v-model="tabActive" class="vone-tab-line">
        <el-tab-pane
          v-for="item in types"
          :key="item.type"
          :label="item.name"
          :name="item.type"
        />
      </el-tabs>
    </div>
    <template v-for="item in types">
      <config-table
        v-if="tabActive === item.type"
        :type="item.type"
        :name="item.name"
      />
    </template>
    <div v-if="!types.length" class="empty">
      <vone-empty desc="请配置相关角色权限" />
    </div>
  </div>
</template>

<script>
import configTable from './tab/config'

export default {
  components: {
    configTable,
  },
  data() {
    return {
      tabActive: 'ISSUE',
      types: [
        {
          type: 'ISSUE',
          name: '需求',
          show: this.$permission('project_setting_issue'),
        },
        {
          type: 'TASK',
          name: '任务',
          show: this.$permission('project_setting_task'),
        },
        {
          type: 'BUG',
          name: '缺陷',
          show: this.$permission('project_setting_defect'),
        },
        {
          type: 'RISK',
          name: '风险',
          show: this.$permission('project_setting_risk'),
        },
        {
          type: 'team',
          name: '团队',
          show: this.$permission('project_setting_team'),
        },
        {
          type: 'user',
          name: '成员',
          show: this.$permission('project_setting_user'),
        },
        {
          type: 'group',
          name: '角色',
          show: this.$permission('project_setting_role'),
        },
        {
          type: 'type_switch',
          name: '事项开关',
          show: this.$permission('project_setting_work'),
        },
      ],
    }
  },
  created() {
    const { projectTypeCode } = this.$route.params
    this.types = this.types.filter((item) => item.show)
    if (
      projectTypeCode !== 'WALL' &&
      this.$permission('project_setting_kanban')
    ) {
      this.types.push({
        type: 'kanban_config',
        name: '看板配置',
      })
    }
    this.tabActive = this.types[0]?.type
  },
  mounted() {
    const { active } = this.$route.query
    active && (this.tabActive = active)
  },
}
</script>

<style lang="scss" scoped>
.topTab {
  margin-bottom: 10px;
  height: 48px;
}
.empty {
  height: calc(100vh - 100px);
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
