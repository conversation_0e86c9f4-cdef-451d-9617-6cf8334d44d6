import request from '@/utils/axios-api'

// 表单查询
export function apiVaBaseCustomPage(data) {
  return request({
    url: '/api/tm-base/customForm/page',
    method: 'post',
    data
  })
}

// 根据ID查询表单详情
export function apiVaBaseCustomInfo(id) {
  return request({
    url: `/api/tm-base/customForm/${id}`,
    method: 'GET'
  })
}

// 复制自定义表单
export function apiVaBaseCustomFormCopy(data) {
  return request({
    url: '/api/tm-base/customForm/copy',
    method: 'post',
    data
  })
}

// 表单修改
export function apiVaBaseCustomFormPut(data) {
  return request({
    url: '/api/tm-base/customForm',
    method: 'PUT',
    data
  })
}

// 表单删除
export function apiVaBaseCustomFormDel(data) {
  return request({
    url: '/api/tm-base/customForm',
    method: 'DELETE',
    data
  })
}

// 查询工作项分类信息
export function apiVaBaseClassify() {
  return request({
    url: `/api/alm/issue/classify/query`,
    method: 'GET'
  })
}

// 表单批量查询
export function apiVaBaseCustomFormQuery(data) {
  return request({
    url: '/api/tm-base/customForm/query',
    method: 'post',
    data
  })
}

// 工作项分类修改
export function apiVaBaseClassifyPut(data) {
  return request({
    url: '/api/alm/issue/classify',
    method: 'PUT',
    data
  })
}

// 查询自定义表单工作项[用于需求中心]
export function apiVaBaseCustomFormField(typeClassify, data) {
  return request({
    url: `/api/alm/alm/projectCustomForm/queryCustomFormField/${typeClassify}`,
    method: 'GET',
    data
  })
}

// 查询自定义表单工作项---------[用于项目下]
export function apiVaBaseCustomFormFieldProject(projectId, typeClassify, data) {
  return request({
    url: `/api/alm/alm/projectCustomForm/queryProjectCustomFormField/${projectId}/${typeClassify}`,
    method: 'GET',
    data
  })
}

// 查询自定义表单工作项---------[用于项目集下]
export function apiVaBaseCustomFormFieldProgram(programId, typeClassify, data) {
  return request({
    url: `/api/alm/alm/projectCustomForm/queryProgramCustomFormField/${programId}/${typeClassify}`,
    method: 'GET',
    data
  })
}

// 查询项目下表单字段---------[用于项目下设置-工作流]
export function apiVaBaseCustomProjectSetting(projectId, typeClassify, typeCode) {
  return request({
    url: `/api/alm/alm/projectCustomForm/queryByProjectIdAndTypeClassifyAndTypeCode/${projectId}/${typeClassify}/${typeCode}`,
    method: 'GET'

  })
}

// 查询项目下表单字段---------[用于项目下设置-工作流字段回显]
export function apiVaBaseCustomProjectFormCheck(projectId, typeClassify, data) {
  return request({
    url: `/api/alm/alm/projectCustomForm/queryProjectCustomFormField/${projectId}/${typeClassify}`,
    method: 'GET',
    data

  })
}

// 修改自定义表单排序
export function apiVaBaseFormSortPut(data) {
  return request({
    url: '/api/tm-base/customForm/sort',
    method: 'PUT',
    data
  })
}

