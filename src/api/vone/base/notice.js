import request from '@/utils/axios-api'
// 菜单查询
export function getNoticeList(data) {
  return request({
    url: '/api/base/base/notice/page',
    method: 'post',
    data
  })
}

// 新增公告
export function addNotice(data) {
  return request({
    url: '/api/base/base/notice',
    method: 'post',
    data
  })
}

// 修改公告
export function editNotice(data) {
  return request({
    url: '/api/base/base/notice',
    method: 'put',
    data
  })
}

// 删除公告
export function delNotice(data) {
  return request({
    url: '/api/base/base/notice',
    method: 'delete',
    data
  })
}

// 修改状态
export function editState(data) {
  return request({
    url: '/api/base/base/notice/updateStateBath',
    method: 'put',
    data
  })
}
// 查询信息
export function getNotice(id) {
  return request({
    url: '/api/base/base/notice/'+id,
    method: 'get',
  })
}