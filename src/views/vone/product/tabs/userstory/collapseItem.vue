<template>
  <div @click.stop>
    <div v-if="collapseItem">
      <el-collapse v-model="activeitems" class="collapses" accordion>
        <el-collapse-item
          v-for="item in storyList"
          :key="item.id"
          v-loading="cardLoading"
          :name="item.groupName"
          style="margin: 0"
        >
          <template v-slot:title>
            <div class="collapse-title">
              <el-icon class="iconfont" style="margin-right: 19px"
                ><el-icon-icon-wendangkushuxiala
              /></el-icon>
              <span class="name">{{ item.groupName }}</span>
              <span v-if="item.groupName !== '全部'" class="groupCount">{{
                item.groupCount
              }}</span>
            </div>
          </template>
          <cardItem
            v-loading="cardLoading"
            :table-data="item.requirementMapVoList"
          />
        </el-collapse-item>
      </el-collapse>
      <vone-empty v-if="storyList.length == 0" />
    </div>

    <cardItem
      v-else
      v-loading="cardLoading"
      :table-data="requirementMapVoList"
    />
  </div>
</template>

<script>
import { IconWendangkushuxiala as ElIconIconWendangkushuxiala } from "@element-plus/icons-vue";
import cardItem from "./card-item.vue";
import { getMapList } from "@/api/vone/product";
export default {
  components: {
    cardItem,
    ElIconIconWendangkushuxiala,
  },
  data() {
    return {
      loading: false,
      activeitems: "",
      activeFilter: "",
      storyList: [],
      requirementMapVoList: [],
      collapseItem: "",
      cardLoading: false,
    };
  },
  methods: {
    async getProductList(item) {
      this.cardLoading = true;
      this.collapseItem = item.code;

      const params = {
        groupType: item.code || "",
        isgroup: !!item.code,
      };
      const res = await getMapList(
        this.$route.params.productId,
        item.id !== "0" ? item.id : "",
        params
      );

      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      if (item.code) {
        this.storyList = res.data || [];
      } else {
        this.requirementMapVoList = res.data[0]?.requirementMapVoList;
      }
      this.cardLoading = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.collapses {
  :deep(.el-collapse-item) {
    margin-bottom: 0px;
  }
  :deep(.el-collapse-item__header.is-active) {
    border-bottom: 1px solid #f2f3f5;
    border-bottom-color: #f2f3f5;
  }
}
.collapse-status {
  background: #ffeecf;
  color: #fa962b;
  height: 28px;
  margin: 0 24px 0 16px;
  &.success {
    color: #2cc750;
    background-color: #d7fada;
  }
}
.collapse-time {
  height: 28px;
  color: #838a99;
  background: #f2f3f5;
}
:deep(.collapse-title) {
  flex: 1 0 90%;
  order: 1;
  .el-collapse-item__header {
    flex: 1 0 auto;
    order: -1;
  }
  .groupCount {
    margin-left: 8px;
    padding: 0 6px;
    background: #f2f3f5;
    border-radius: 100px;
    font-weight: 500;
    color: #838a99;
  }
}
:deep(.el-collapse-item__arrow) {
  display: none;
}
:deep(.el-collapse-item__wrap) {
  overflow-y: auto;
}
.active {
  transform: rotate(90deg);
}
.filter-group {
  height: 32px;
  padding: 4px 12px;
  margin-left: 16px;
  border-radius: 2px;
  &:hover {
    background-color: #f2f3f5;
  }
  &.active {
    border: 1px solid #3e7bfa;
  }
}
</style>

<style lang="scss">
.custom-theme-dark {
  :deep(.el-collapse-item__header:hover) {
    background-color: #333947;
  }
  :deep(.el-collapse-item__header .is-active) {
    background-color: #333947;
  }
  .filter-group:hover {
    background-color: #333947;
  }
}
</style>
