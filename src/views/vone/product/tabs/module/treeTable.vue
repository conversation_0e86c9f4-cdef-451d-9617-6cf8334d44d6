<template>
  <div :style="{ height: tableHeight }">
    <vxe-table
      ref="productModuleTable"
      class="vone-vxe-table"
      border
      height="auto"
      show-overflow="tooltip"
      :empty-render="{ name: 'empty' }"
      :data="data"
      :column-config="{ minWidth: '120px' }"
      :checkbox-config="{ reserve: true }"
      row-id="id"
      :tree-config="{ transform: false, rowField: 'id', children: 'children' }"
    >
      <vxe-column
        :key="extraData.cardView + 'developmentManager'"
        title="研发经理"
        field="developmentManager"
        min-width="150"
      >
        <template v-slot="scope">
          <vone-user-avatar
            :avatar-path="
              getDevelopUserInfo(scope.row)
                ? getDevelopUserInfo(scope.row).avatarPath
                : ''
            "
            :name="
              getDevelopUserInfo(scope.row)
                ? getDevelopUserInfo(scope.row).name
                : ''
            "
          />
        </template>
      </vxe-column>
      <vxe-column
        :key="extraData.cardView + 'description'"
        title="描述"
        field="description"
        min-width="150"
      />
    </vxe-table>
  </div>
</template>

<script>
export default {
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    extraData: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    getUserInfo() {
      return function (row) {
        return row?.productManager && row?.echoMap?.productManager
      }
    },
    getDevelopUserInfo() {
      return function (row) {
        return row?.developmentManager && row?.echoMap?.developmentManager
      }
    },
    tableHeight() {
      const height = this.extraData?.height - 30 + 'px' || '0px'
      return `calc(${this.$reduceTableHeight} - ${height})`
    },
  },
}
</script>
