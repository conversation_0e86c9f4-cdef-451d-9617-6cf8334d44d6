<template>
  <div>
    <div className="doc-comment-edit-wrapper">
      <div className="doc-comment-edit-input">
        <el-input v-model="valueData" @change="change" />
      </div>
      <div className="doc-comment-edit-button">
        <el-button size="small" @click="onCancel"> 取消 </el-button>
        <el-button
          size="small"
          type="primary"
          loading="{loading}"
          @click="onOk"
        >
          确定
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
export default {
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      valueData: '',
    }
  },
  methods: {
    change(e) {
      const value = e.target.value
      this.valueData = value
      if ($emit(this, 'onChange')) $emit(this, 'onChange', value)
    },
    onCancel() {
      $emit(this, 'onCancel')
    },
    onOk(event) {
      $emit(this, 'onOk', event)
    },
  },
  emits: ['onChange', 'onOk', 'onCancel'],
}
</script>
