import Markdown from 'markdown-it'
import highlight from 'highlight.js'

function highlightBlock(str, lang) {
  return `<pre class="code-block-wrapper"><code class="hljs ">${str}</code></pre>`
}

export const mdi = new Markdown({
  html: false,
  linkify: true,
  highlight(code, language) {
    const validLang = !!(language && highlight.getLanguage(language))
    if (validLang) {
      const lang = language ?? ''
      return highlightBlock(highlight.highlight(code, { language: lang }).value, lang)
    }
    return highlightBlock(highlight.highlightAuto(code).value, '')
  }
})
