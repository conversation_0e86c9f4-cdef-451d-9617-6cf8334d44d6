<template>
  <page-wrapper>
    <ssh v-bind="ssh" v-if="ssh.show" />
    <el-card v-else class="login-form">
      <h3>连接终端 {{ id }}</h3>
      <el-form :auto-layout="false" label-width="60px" :model="loginForm">
        <el-form-item label="账号" prop="username">
          <el-select v-model="loginForm.username">
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="user.name"
              :value="user.id"
            >
              <el-row type="flex" align="center">
                <el-col> {{ user.name }} [ {{ user.userName }} ] </el-col>
                <span :style="{ flex: 1 }" />
                <span v-if="user">
                  <el-tag
                    v-if="user.loginType == 0"
                    type="success"
                    size="mini"
                    effect="plain"
                    >自动登录</el-tag
                  >
                  <el-tag v-else size="mini" effect="plain">手动登录</el-tag>
                </span>
              </el-row>
            </el-option>
          </el-select>
        </el-form-item>
        <transition name="el-fade-in">
          <el-form-item
            v-if="user && user.loginType == 1"
            label="密码"
            prop="password"
          >
            <el-input v-model="loginForm.password" show-password />
          </el-form-item>
        </transition>
        <el-form-item>
          <el-button native-type="submit" type="primary" @click="doLink"
            >连接</el-button
          >
        </el-form-item>
      </el-form></el-card
    >
  </page-wrapper>
</template>

<script>
import { apiCmdbGetHostUserJudgeAuth } from '@/api/vone/cmdb/host'
import ssh from './ssh.vue'
export default {
  components: {
    ssh,
  },
  data() {
    return {
      loginForm: {},
      id: this.$route.params.id,
      users: [],
      userMap: {},
      ssh: {
        show: false,
      },
    }
  },
  computed: {
    user() {
      return this.userMap[this.loginForm.username]
    },
  },
  mounted() {
    this.loadUsers()
  },
  methods: {
    async loadUsers() {
      const { data, success, message } = await apiCmdbGetHostUserJudgeAuth(
        this.id
      )
      if (!success) {
        this.$message.warning(message)
        return
      }
      this.users = data
      this.userMap = data.reduce((r, v) => (r[v.id] = v) && r, {})
    },
    doLink() {
      this.ssh = {
        show: true,
        id: this.id,
        username: this.loginForm.username,
        password: this.loginForm.password,
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.login-form {
  width: 400px;
  margin-left: auto;
  margin-right: auto;
  margin-top: 20vh;
  h3 {
    text-align: center;
    margin-bottom: 40px;
  }
}
</style>
