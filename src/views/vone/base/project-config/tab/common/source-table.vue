<template>
  <div>
    <vone-search-wrapper style="margin-bottom: 16px;">
      <template slot="custom">
        <span class="strong">来源</span>
      </template>
      <template slot="actions">
        <el-button type="primary" icon="iconfont el-icon-tips-plus-circle" :disabled="!$permission('work_source_add')" @click="addPriority">新增</el-button>
        <el-dropdown trigger="click" @command="(e) => e && e()">
          <el-button class="btnMore"><i class="iconfont el-icon-application-more " /></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="(item, index) in actions" :key="index" :icon="item.icon" :command="item.fn" :disabled="item.disabled">
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
    </vone-search-wrapper>
    <div :style="{height: tableHeight}">
      <vxe-table
        ref="source-table"
        class="vone-vxe-table draggTable"
        border
        height="auto"
        show-overflow="tooltip"
        :loading="tableLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ resizable: true, minWidth: 120 }"
        :checkbox-config="{checkMethod:selectable}"
        row-id="id"
      >
        <vxe-column type="checkbox" width="36" align="center" fixed="left" />
        <vxe-column title="名称" field="name" fixed="left" />
        <vxe-column title="编码" field="code" width="120" />
        <vxe-column title="描述" field="description" />
        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <template>
              <el-tooltip class="item" content="编辑" placement="top">
                <el-button type="text" :disabled="row.readonly ||!$permission('work_source_edit')" icon="iconfont el-icon-application-edit icon_click" @click="editClickRow(row)" />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="删除" placement="top">
                <el-button type="text" :disabled="row.readonly || !$permission('work_source_delete')" icon="iconfont el-icon-application-delete" @click="deleteRow(row)" />
              </el-tooltip>
            </template>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <vone-pagination ref="pagination" :total="tableData.total" @update="getTableData" />

    <!-- 新增来源 -->
    <el-dialog :title="title" width="30%" v-model="dialogFormVisible" :close-on-click-modal="false">
      <el-form ref="sourceForm" :rules="rules" :model="sourceForm">

        <el-form-item label="名称" prop="name">
          <el-input v-model="sourceForm.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="编号" prop="code">
          <el-input v-model="sourceForm.code" placeholder="请输入编号" :disabled="title == '编辑来源'" />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input v-model="sourceForm.description" placeholder="请输入描述" />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="sureAdd">确定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { apiAlmSourceAdd, apiAlmSourcePage, getAlmGetSourceId } from '@/api/vone/alm/index'
import { apiAlmSourceDel } from '@/api/vone/base/work-flow'

export default {
  props: {
    id: {
      type: String,
      default: undefined
    },
    typeClassify: {
      type: String,
      default: undefined
    }
  },
  data() {
    return {
      rules: {
        name: [
          {
            required: true,
            message: '请输入长度不超过20个字符的名称',
            trigger: 'change'
          },
          {
            pattern: '^[a-zA-Z0-9_\\u4e00-\\u9fa5]{1,20}$',
            message: '请输入不超过20位由中文、字母、数字或者下划线组成的名称'
          },
          {
            max: 20,
            message: '请输入长度不超过20个字符的名称',
            trigger: 'change'
          }
        ],
        code: [
          {
            required: true,
            message: '请输入长度不超过20个字符的标识',
            trigger: 'change'
          },
          {
            pattern: '^\\w{1,20}$',
            message: '请输入不超过20位由字母、数字或者下划线组成的标识'
          }
        ],
        description: [
          {
            max: 100,
            message: '请输入长度不超过100个字符的描述',
            trigger: 'change'
          }
        ]
      },
      tableLoading: false,
      tableOptions: {
        isColSort: true, // 列是否排序
        isSelection: true, // 表格有多选时设置
        isOperation: true, // 表格有操作列时设置
        isIndex: false, // 列表序号
        operation: {
          isFixed: true,
          // 表格有操作列时设置
          label: '操作', // 列名
          width: '120', // 根据实际情况给宽度
          data: [
            // 功能数组
            {
              type: 'icon', // 为icon则是图标
              label: '编辑', // 功能名称
              icon: 'iconfont el-icon-application-edit', // icon class
              handler: this.editClickRow, // 操作事件
              disabled: this.getStatus
            },
            {
              type: 'icon', // 为icon则是图标
              label: '删除', // 功能名称
              icon: 'iconfont el-icon-application-delete', // icon class
              handler: this.deleteRow, // 操作事件
              disabled: this.getDel
            }
          ],
          // 更多操作按钮
          moreData: []
        }
      },
      tableData: {},
      selecteTableData: [],
      dialogFormVisible: false,
      sourceForm: {
        code: '',
        color: '#ccc',
        description: '',
        icon: 'el-icon-eleme',
        typeClassify: this.typeClassify,
        readonly: false
      },
      saveLoading: false,
      title: undefined,
      formData: {
        typeClassify: this.typeClassify
      },
      actions: [
        {
          name: '批量删除',
          disabled: !this.$permission('work_source_delete'),
          fn: this.deleteAll
        }
      ]
    }
  },
  computed: {
    tableHeight() {
      const height = (this.extraData?.height || 0) + 'px'
      return `calc(${this.$reduceTableHeight} - ${height})`
    }
  },
  watch: {
    typeClassify: {
      handler(v) {
        if (v) {
          this.$nextTick(() => {
            this.getTableData()
          })
        }
      },
      immediate: true
    }
  },
  mounted() {
    if (this.typeClassify) {
      this.getTableData()
    }
  },
  methods: {
    selectable({ row }) {
      if (row.readonly) {
        return false
      } else {
        return true
      }
    },
    onClose() {
      this.dialogFormVisible = false
      this.$refs.sourceForm.resetFields()

      this.$nextTick(() => {
        this.$refs['sourceForm'].clearValidate()
      })
    },
    getStatus(index, row) {
      return this.tableData.records[index].readonly || !this.$permission('work_source_edit')
    },
    getDel(index, row) {
      return this.tableData.records[index].readonly || !this.$permission('work_source_delete')
    },
    async getTableData() {
      const pageObj = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
      this.$set(this.formData, 'typeClassify', this.typeClassify)
      this.tableLoading = true
      const params = {
        ...pageObj,
        extra: {},
        model: { ...this.formData }
      }
      const res = await apiAlmSourcePage(params)
      this.tableLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.tableData = res.data
    },
    addPriority() {
      this.title = '新增来源'
      this.dialogFormVisible = true
    },
    editClickRow(row) {
      this.title = '编辑来源'
      this.getInfo(row)
      this.dialogFormVisible = true
    },
    async getInfo(row) {
      this.formLoading = true
      const res = await getAlmGetSourceId(
        row.id
      )
      this.formLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.sourceForm = res.data
    },
    async sureAdd() {
      try {
        await this.$refs.sourceForm.validate()
      } catch (e) {
        return
      }
      try {
        this.$set(this.sourceForm, 'typeClassify', this.typeClassify)
        this.saveLoading = true
        const res = await apiAlmSourceAdd(
          this.sourceForm
        )
        this.saveLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.$message.success('保存成功')
        this.onClose()
        this.getTableData()
      } catch (e) {
        this.saveLoading = false
      }
    },
    deleteRow(item) {
      this.$confirm(`确定删除【${item.name}】吗?`, '删除', {
        type: 'warning',
        closeOnClickModal: false,
        customClass: 'delConfirm',
        showClose: false
      }).then(async() => {
        const res = await apiAlmSourceDel(
          [item.id]
        )
        if (!res.isSuccess) {
          return this.$message.error(res.msg)
        }

        this.$message.success('删除成功')
        this.getTableData()
      })
    },
    // 批量删除
    async deleteAll() {
      const selectData = this.getVxeTableSelectData('source-table')
      if (!selectData.length) {
        this.$message.warning('请选择要删除的数据')
        return
      }
      try {
        await this.$confirm(`是否删除 ${selectData.length} 条数据?`, '提示', {
          type: 'warning',
          customClass: 'delConfirm',
          showClose: false
        })
        this.pageLoading = true
        const selectId = this.getVxeTableSelectData('source-table').map(r => r.id)
        const res = await apiAlmSourceDel(selectId)
        this.pageLoading = false
        if (!res.isSuccess) {
          this.$message.error(res.msg)
          return
        }
        this.$message.success('删除成功')
        this.getTableData()
      } catch (e) {
        this.pageLoading = false
      }
    }
  }

}
</script>

<style lang="scss" scoped>
.strong{
  font-size: 16px;
  font-weight: 500;
  color: #2C2E36;
}
</style>
