<template>
  <div class="drawerBox">
    <el-form
      ref="infoForm"
      :model="infoForm"
      :rules="rules"
      :disabled="infoDisabled"
      label-position="right"
      label-width="100px"
      style="margin-bottom: 75px"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="意向分类" prop="typeCode">
            <el-select
              v-model="infoForm.typeCode"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="item in typeCodeList"
                :key="item.key"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="意向来源" prop="sourceCode">
            <el-select
              v-model="infoForm.sourceCode"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="item in sourceList"
                :key="item.key"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="优先级" prop="priorityCode">
            <vone-icon-select
              v-model:value="infoForm.priorityCode"
              :data="prioritList"
              filterable
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="item in prioritList"
                :key="item.key"
                :label="item.name"
                :value="item.code"
              >
                <i
                  :class="`iconfont ${item.icon}`"
                  :style="{
                    color: item.color,
                    fontSize: '16px',
                    paddingRight: '6px',
                  }"
                />
                {{ item.name }}
              </el-option>
            </vone-icon-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="归属产品" prop="productId">
            <el-select
              v-model="infoForm.productId"
              clearable
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="item in productIdList"
                :key="item.key"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="提出人" prop="putBy">
            <vone-remote-user v-model:value="infoForm.putBy" />
            <!-- <el-select v-model="infoForm.putBy" clearable filterable style="width:100%">
                  <el-option v-for="item in userList" :key="item.key" :label="item.name" :value="item.id" />
                </el-select> -->
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="期望完成" prop="expectedTime">
            <el-date-picker
              v-model="infoForm.expectedTime"
              :prefix-icon="elIconApplicationRunHistory"
              type="datetime"
              format="yyyy-MM-dd HH:mm"
              value-format="yyyy-MM-dd HH:mm:ss"
              default-time="18:00:00"
              placeholder="选择期望完成时间"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row>
            <el-col :span="12">
              <el-form-item label="实际完成" prop="actualTime">
                <el-date-picker v-model="infoForm.actualTime" format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="选择实际完成时间" />
              </el-form-item>
            </el-col>
          </el-row> -->
      <el-form-item label="意向描述：" prop="description">
        <el-input
          v-model="infoForm.description"
          type="textarea"
          style="95%"
          :rows="3"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
      <!-- <el-form-item label="上传附件：" prop="description">
            <el-upload
              class="upload-demo"
              :on-remove="handleRemove"
              :before-remove="beforeRemove"
              multiple
              :limit="3"
              :on-exceed="handleExceed"
              :file-list="fileList"
            >
              <el-button size="small" type="primary">点击上传</el-button>
            </el-upload>
          </el-form-item> -->
    </el-form>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from "../../../utils/gogocodeTransfer";
import { apiAlmSourceNoPage } from "@/api/vone/project/issue";
import { productListByCondition } from "@/api/vone/project/index";

import { apiAlmPriorityNoPage } from "@/api/vone/alm/index";
import { apiAlmStateNoPage } from "@/api/vone/base/work-flow";
import { getAlmGetTypeNoPage } from "@/api/vone/alm/index";
import { getUser } from "@/utils/auth";
export default {
  props: {
    infoDisabled: {
      type: Boolean,
      default: false,
    },
    spaceInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      infoForm: {},
      sourceList: [], // 意向来源
      productIdList: [], // 归属产品
      stateList: [], // 状态
      prioritList: [], // 优先级
      typeCodeList: [], // 意向分类
      rules: {
        typeCode: [{ required: true, message: "请选择意向分类" }],
        sourceCode: [{ required: true, message: "请选择意向来源" }],
        priorityCode: [{ required: true, message: "请选择优先级" }],
      },
    };
  },
  mounted() {
    this.getIssueType(); // 意向分类
    this.productList(); // 归属产品
    this.getStateList(); // 状态
    this.getPrioritList(); // 优先级
    this.getSourceList(); // 来源
  },
  methods: {
    // 查询意向分类
    async getIssueType() {
      const res = await getAlmGetTypeNoPage("IDEA");
      if (!res.isSuccess) {
        return;
      }
      this.typeCodeList = res.data;
      this.infoForm["typeCode"] = res.data.length ? res.data[0].code : "";
      this.infoForm["putBy"] = getUser().id;
      // this.infoForm.typeCode = res.data[0].code
    },
    // 查询意向来源
    async getSourceList() {
      const res = await apiAlmSourceNoPage({
        typeClassify: "IDEA",
      });
      if (!res.isSuccess) {
        return;
      }
      this.sourceList = res.data;
    },
    // 归属产品
    async productList() {
      const res = await productListByCondition();

      if (!res.isSuccess) {
        return;
      }
      this.productIdList = res.data;
      if (this.spaceInfo.typeId == "3") {
        this.infoForm["productId"] = this.spaceInfo.bizId;
        // this.infoForm.productId = this.spaceInfo.bizId
      }
    },
    // 查状态
    async getStateList() {
      const res = await apiAlmStateNoPage();
      if (!res.isSuccess) {
        return;
      }
      this.stateList = res.data;
    },
    // 查优先级
    async getPrioritList() {
      const res = await apiAlmPriorityNoPage();
      this.tableLoading = false;
      if (!res.isSuccess) {
        return;
      }
      this.prioritList = res.data;
      this.infoForm["priorityCode"] = res.data[0].code;
    },
    async proving() {
      this.$refs.infoForm.validate((valid) => {
        if (valid) {
          $emit(this, "save");
        } else {
          return;
        }
      });
    },
  },
  emits: ["save"],
};
</script>

<style lang="scss" scoped>
:deep(.el-date-editor.el-input),
.el-date-editor.el-input__inner {
  width: 100%;
}
:deep(.el-form-item) {
  height: 40px;
}
:deep(.el-form-item__label) {
  text-align: right;
} /*// .drawerBox*/
 {
  /*//   //  overflow-y: auto;*/ /*//   // overflow-x: hidden;*/ /*//   min-height: calc(100vh - 250px);*/ /*//*/
}
</style>
