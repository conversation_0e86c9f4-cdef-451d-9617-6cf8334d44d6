import request from '@/utils/axios-api'

/**
 * @desc 查询项目下的测试报告
 * @param {string} type
 */
export function projectGetPageTestReport(data) {
  return request({
    url: '/api/testm/testReport/getPageTestReport',
    method: 'put',
    data
  })
}

/**
 * @desc 项目下测试用例复制
 * @param {string} type
 */
export function projectTreeCasePut(data) {
  return request({
    url: '/api/testm/testCase/copyTreeById',
    method: 'put',
    data
  })
}

// 项目下测试用例--分页
export function projectCasePage(data) {
  return request({
    url: '/api/testm/testCase/page',
    method: 'post',
    data
  })
}
// 项目下测试用例--分页
export function caseDetail(id) {
  return request({
    url: `/api/testm/testCase/${id}`,
    method: 'get'
  })
}

// 获取历史执行记录列表
export function getTestHistoryList(params) {
  return request({
    url: `/api/testm/testPlanCase/getHistoryxecutePlanCaseResults`,
    method: 'get',
    data: params
  })
}
