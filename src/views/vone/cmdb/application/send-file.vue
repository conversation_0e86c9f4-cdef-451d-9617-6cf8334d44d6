<template>
  <el-dialog
    title="更新参数结果"
    v-model:value="visible"
    width="50%"
    :close-on-click-modal="false"
    :before-close="onClose"
  >
    <el-table :data="tableData">
      <el-table-column prop="sendUserName" label="下发人" />
      <el-table-column prop="isSuccess" label="下发结果">
        <template v-slot="scope">
          <el-tag v-if="scope.row.isSuccess" type="success"> 成功 </el-tag>
          <el-tag v-else-if="!scope.row.isSuccess" type="danger"> 失败 </el-tag>
          <span v-else>--</span>
        </template>
      </el-table-column>

      <el-table-column prop="sendTime" label="下发时间" show-overflow-tooltip />
      <el-table-column prop="message" label="原因" show-overflow-tooltip />
    </el-table>
    <template v-slot:footer>
      <span class="dialog-footer">
        <el-button @click="onClose">取消</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../utils/gogocodeTransfer'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    tableData: {
      type: Array,
      default: () => [],
    },
  },
  mounted() {},
  methods: {
    onClose() {
      $emit(this, 'update:visible', false)
    },
  },
  emits: ['update:visible'],
}
</script>
