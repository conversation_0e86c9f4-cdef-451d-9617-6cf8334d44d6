<template>
  <div class="custom-form">
    <el-row v-if="conditions.length" :gutter="20">
      <template>
        <el-col
          v-for="(condition, index) in conditions"
          :key="`${condition.id}` + `${index}`"
          :span="24"
          class="form-col"
        >
          <el-select
            v-model="condition.authType"
            placeholder="请选择"
            class="form-field"
            filterable
            @change="changeAuthType(condition)"
          >
            <el-option
              v-for="item in formFieldsData"
              :key="item.key"
              :label="item.name"
              :value="item.key"
              :disabled="disabledSelectItem(item)"
            />
            <!-- :disabled="disabledSelectItem(item)" -->
          </el-select>

          <el-select
            v-if="condition.authType == 'ROLE'"
            v-model="condition.authIds"
            placeholder="请选择角色"
            collapse-tags
            multiple
            class="form-item"
          >
            <el-option
              v-for="item in roleList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
          <vone-tree-select
            v-else-if="condition.authType == 'TEAM'"
            v-model:value="condition.authIds"
            multiple
            search-nested
            :tree-data="teamList"
            placeholder="请选择团队"
            class="form-item"
          />
          <vone-remote-user
            v-else-if="condition.authType == 'USER'"
            v-model:value="condition.authIds"
            :no-name="false"
            multiple
            class="form-item"
          />

          <span class="del-btn" @click="removeCondition(index)">
            <el-icon class="iconfont"><el-icon-application-delete /></el-icon>
          </span>
        </el-col>
      </template>
    </el-row>
    <vone-empty v-else />

    <div v-if="conditions.length != 3" class="add-btn">
      <span @click="addCondition">
        <el-icon><el-icon-plus /></el-icon>
        添加
      </span>
    </div>
  </div>
</template>

<script>
import {
  ApplicationDelete as ElIconApplicationDelete,
  Plus as ElIconPlus,
} from "@element-plus/icons-vue";
import { batchQuery } from "@/api/vone/base/role";
import { teamList } from "@/api/vone/base/team";
import { gainTreeList } from "@/utils";
export default {
  components: {
    ElIconApplicationDelete,
    ElIconPlus,
  },
  props: {
    filterData: {
      type: Array,
      default: () => [],
    },
    form: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      roleList: [],
      teamList: [],
      formFieldsData: [
        {
          key: "ROLE",
          name: "角色",
        },
        {
          key: "TEAM",
          name: "团队",
        },
        {
          key: "USER",
          name: "人员",
        },
      ],
      conditions: [],
    };
  },
  computed: {
    disabledSelectItem() {
      return function (row) {
        const selectItems = this.conditions.filter(
          (item) => item.authType == row.key
        );
        return selectItems.length > 0;
      };
    },
  },
  watch: {
    filterData: {
      deep: true,

      handler: function (val) {
        // 数据回显逻辑处理
        if (val.length) {
          this.conditions = val;
        }
      },

      immediate: true,
    },
  },
  mounted() {
    this.getRoleList();
    this.getTeamList();
  },
  methods: {
    addCondition() {
      const filterList = this.formFieldsData.filter(
        (r) => this.conditions.map((j) => j.authType).indexOf(r.key) == -1
      );

      this.conditions.push({
        authType: filterList[0].key,
        dashboardId: this.form.id,
      });
    },
    async getRoleList() {
      const res = await batchQuery();
      if (!res.isSuccess) {
        console.warn(res.msg);
        return;
      }
      this.roleList = res.data;
    },
    async getTeamList() {
      const res = await teamList();
      if (!res.isSuccess) {
        console.warn(res.msg);
        return;
      }
      this.teamList = gainTreeList(res.data);
    },
    removeCondition(index) {
      this.conditions.splice(index, 1);
    },
    changeAuthType(row) {
      row["authIds"] = [];
    },
  },
};
</script>

<style lang="scss" scoped>
.custom-form {
  .form-col {
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    .form-field {
      width: 130px;
      margin-right: 4px;
    }
    .form-operator {
      width: 90px;
      margin-right: 4px;
    }
    .form-item {
      width: calc(100% - 130px);
      margin-right: 15px;
      :deep(.el-select__tags + .el-input) {
        .el-input__inner {
          height: 32px !important;
        }
      }
    }
    .del-btn {
      cursor: pointer;
      color: var(--font-second-color);
      &:hover {
        color: #db2c3a;
      }
    }
  }
  .add-btn {
    span {
      color: var(--main-theme-color);
      font-size: 14px;
      cursor: pointer;
    }
  }
}
</style>
