<template>
  <vone-auth-org v-bind="$attrs" :visible="visible" :tree-data="treeData" :loading="loading" :value="value" :disabled-value="disabledValue" :success="success" v-on="$listeners" />
</template>
<script>

import { orgList } from '@/api/vone/base/org'
import {
  productCaseLibraryIdAuth, productCaseLibraryOrgPut } from '@/api/vone/testmanage/case'
import { catchErr } from '@/utils'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      treeData: [],
      loading: true,
      value: [],
      disabledValue: []
    }
  },
  watch: {
    visible: {
      handler: async function(v) {
        this.loading = true
        if (!v) return
        await this.getOrgChildTree()
        await this.getSelectOrgById()
        this.$nextTick(() => (this.loading = false))
      },
      immediate: true
    }
  },
  methods: {
    // 获取权限分配树结构数据信息
    async getOrgChildTree() {
      const [res, err] = await catchErr(orgList())
      if (err) return
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.treeData = res.data
    },
    // 获取选中的节点
    async getSelectOrgById() {
      const [res, err] = await catchErr(productCaseLibraryIdAuth(this.id))
      if (err) return
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.value = res.data.map(r => r.orgId)
      this.disabledValue = res.data.filter(r => r.isCreate).map(r => r.orgId)
    },
    // 保存权限分配数据
    async success(checked) {
      const orgDatas = checked.map(orgId => (orgId))
      const [res, err] = await catchErr(productCaseLibraryOrgPut({
        testProductCaseLibraryId: [this.id],
        orgIds: orgDatas
      }))
      if (err) return
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('保存成功')
      this.$emit('update:visible', false)
      // this.$emit('success')
    }
  }
}
</script>
