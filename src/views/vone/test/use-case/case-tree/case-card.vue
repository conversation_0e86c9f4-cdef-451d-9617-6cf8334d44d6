<template>
  <el-drawer
    v-loading="caseCardLoading"
    :title="dialogType == 'add' ? '用例新增' : '用例编辑'"
    v-model="visible"
    size="600px"
    :append-to-body="false"
    :modal-append-to-body="false"
    :before-close="onClose"
  >
    <!-- <template slot="title">
          <header class="header">
            <span class="title" :class="{gap:dialogType !== 'add'}">
              {{ dialogType=='add'?'用例新增':'用例编辑' }}
            </span>

          </header>
         -->

    <div class="caseForm">
      <el-form
        ref="caseFormRef"
        :key="id"
        :model="caseForm"
        :rules="addRules"
        label-position="top"
      >
        <div class="fixhe">
          <el-row class="form_title">
            <el-form-item prop="name">
              <el-input
                v-model="caseForm.name"
                placeholder="请输入用例标题"
                @input="changeInput"
              />
            </el-form-item>
          </el-row>
          <el-row>
            <el-col :span="10" :offset="1">
              <div class="leading">
                <el-form-item label="维护人" prop="leadingBy">
                  <vone-remote-user
                    v-model:value="caseForm.leadingBy"
                    :default-data="defaultUser"
                    @change="changeInput"
                  />
                </el-form-item>
              </div>
            </el-col>
            <el-col :span="10" :offset="1">
              <div class="priority">
                <el-form-item label="优先级" prop="priority">
                  <vone-icon-select
                    v-model:value="caseForm.priority"
                    :data="prioritList"
                    placeholder="请选择优先级"
                    filterable
                    clearable
                    class="select level-select"
                    :no-permission="noPermission"
                    @change="changeInput"
                  >
                    <el-option
                      v-for="item in prioritList"
                      :key="item.code"
                      :label="item.name"
                      :value="item.code"
                    >
                      <div style="display: flex; align-items: center">
                        <i
                          :class="`iconfont ${item.icon}`"
                          :style="{
                            color: item.color,
                            fontSize: '16px',
                            paddingRight: '6px',
                          }"
                        />
                        <span>{{ item.name }}</span>
                      </div>
                    </el-option>
                  </vone-icon-select>
                </el-form-item>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="attribute">
          <h4>属性</h4>
          <el-row>
            <el-col :span="12">
              <el-form-item label="所属分组" style="width: 95%">
                <el-popover
                  ref="groupPopover"
                  class="groupPopper"
                  placement="bottom"
                  title="选择分组"
                  trigger="click"
                >
                  <div class="popoverMain">
                    <el-tree
                      v-if="treeData.length > 0"
                      ref="tree"
                      class="filter-tree"
                      :data="treeData"
                      node-key="id"
                      highlight-current
                      :expand-on-click-node="false"
                      @node-click="nodeClick"
                    >
                      <template v-slot="{ node, data }">
                        <div class="custom-tree-node">
                          <span :title="data.name">
                            <!-- 文件图标 -->
                            <el-icon
                              class="iconfont"
                              style="color: var(--main-theme-color, #3e7bfa)"
                              ><el-icon-cangku
                            /></el-icon>
                            <svg v-else class="icon" aria-hidden="true">
                              <use xlink:href="#el-application-filejia2" />
                            </svg>
                            <el-button
                              style="padding: 0; min-width: 0; color: #202124"
                              :disabled="data.parentId == '0'"
                              type="text"
                              class="treeNode"
                              >{{ data.name }}</el-button
                            >
                          </span>
                        </div>
                      </template>
                    </el-tree>
                    <el-tree
                      v-else
                      ref="tree"
                      class="filter-tree"
                      :load="loadNode"
                      lazy
                      node-key="id"
                      :highlight-current="true"
                      :expand-on-click-node="false"
                      @node-click="nodeClick"
                    >
                      <template v-slot="{ node, data }">
                        <div class="custom-tree-node">
                          <span :title="data.name">
                            <!-- 文件图标 -->
                            <el-icon
                              class="iconfont"
                              style="color: var(--main-theme-color, #3e7bfa)"
                              ><el-icon-cangku
                            /></el-icon>
                            <svg v-else class="icon" aria-hidden="true">
                              <use xlink:href="#el-application-filejia2" />
                            </svg>
                            <el-button
                              style="padding: 0; min-width: 0; color: #202124"
                              :disabled="data.parentId == '0'"
                              type="text"
                              class="treeNode"
                              >{{ data.name }}</el-button
                            >
                          </span>
                        </div>
                      </template>
                    </el-tree>
                  </div>
                  <template v-slot:reference>
                    <div class="referenceEmit" style="cursor: pointer">
                      <svg class="icon" aria-hidden="true">
                        <use xlink:href="#el-application-filejia2" />
                      </svg>
                      <span class="treeName" :title="newTreeName">{{
                        newTreeName
                      }}</span>
                    </div>
                  </template>
                </el-popover>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="关联需求">
                <el-select
                  v-model="caseForm.requirementId"
                  placeholder="请输入需求名称"
                  clearable
                  filterable
                  multiple
                  remote
                  :remote-method="getRequirementList"
                  :loading="requireLoading"
                  class="requireSelect"
                  @focus="setOptionWidth"
                  @change="changeInput"
                >
                  <el-option
                    v-for="ele in requirementList"
                    :key="ele.id"
                    :value="ele.id"
                    :label="ele.code + ' ' + ele.name"
                    :style="{ width: selectOptionWidth }"
                    :title="ele.name"
                  >
                    {{ `${ele.code}   ${ele.name}` }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="dialogType !== 'add'">
            <el-col :span="12">
              <el-form-item label="编号">
                <el-input
                  v-model="caseForm.caseKey"
                  disabled
                  style="width: 95%"
                  placeholder="请输入英文标识"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="版本">
                <div class="versionBackground">
                  {{ caseForm.version || "--" }}
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12" class="execTime">
              <el-form-item
                label="用例预估执行时长"
                prop="execTime"
                style="width: 95%"
              >
                <el-input
                  v-model.trim="caseForm.execTime"
                  placeholder="请输入数字"
                  @input="changeInput"
                >
                  <template v-slot:append>分钟</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="附件" prop="files" class="fileLoad">
                <div key="id">
                  <vone-upload
                    ref="caseUploadFile"
                    storage-type="LOCAL"
                    biz-type="TEST_PRODUCT_CASE_FILE_UPLOAD"
                    :files-data="fileList"
                    @change="changeInput"
                  />
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="测试意图" prop="intent">
                <vone-upload
                  ref="intentFilesUploadFile"
                  style="margin-bottom: 10px"
                  storage-type="LOCAL"
                  biz-type="TEST_PRODUCT_CASE_INTENT_FILE_UPLOAD"
                  :files-data="intentFiles"
                  @change="changeInput"
                />
                <el-input
                  v-model="caseForm.intent"
                  placeholder="请输入测试意图"
                  type="textarea"
                  :autosize="{ minRows: 2 }"
                  maxlength="300"
                  show-word-limit
                  @change="changeInput"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="标签" prop="tabInfo">
                <template v-slot:label>
                  <span style="font-weight: 500">标签</span>
                </template>
                <tagSelect
                  v-model:value="caseForm.tabInfo"
                  @getVailtab="getVailtab"
                  @resetTab="resetTab"
                  @change="changeInput"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="前置条件" prop="prerequisite">
                <template v-slot:label>
                  <span style="font-weight: 500">前置条件</span>
                </template>
                <vone-upload
                  ref="prerequisiteFilesUploadFile"
                  style="margin-bottom: 10px"
                  storage-type="LOCAL"
                  biz-type="TEST_PRODUCT_CASE_PREREQUISITE_FILE_UPLOAD"
                  :files-data="prerequisiteFiles"
                  @change="changeInput"
                />
                <el-input
                  v-model="caseForm.prerequisite"
                  placeholder="请输入前置条件"
                  type="textarea"
                  :autosize="{ minRows: 2 }"
                  maxlength="300"
                  show-word-limit
                  @input="changeInput"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item prop="stepType" style="position: relative">
                <template v-slot:label>
                  <span style="font-weight: 500">测试步骤</span>
                  <div class="basicBox">
                    <svg
                      v-if="caseForm.stepType === 'subclause'"
                      class="icon"
                      aria-hidden="true"
                    >
                      <use xlink:href="#el-icon-test-step" />
                    </svg>
                    <svg v-else class="icon" aria-hidden="true">
                      <use xlink:href="#el-icon-test-text" />
                    </svg>
                    <!-- <i v-if="caseForm.stepType === 'text'" class="el-icon-document basicIcon" style="color: var(--main-theme-color,#3e7bfa)" />
                      <i v-else class="iconfont el-icon-application-view-list" style="color: #37cdde" /> -->

                    <div class="basicText">
                      {{
                        caseForm.stepType === "text" ? "文本描述" : "步骤描述"
                      }}
                    </div>
                    <el-icon class="basicIcon"
                      ><el-icon-caret-bottom
                    /></el-icon>
                    <el-select
                      v-model="caseForm.stepType"
                      placeholder="请选择更改类型"
                      class="select"
                      popper-class="typeSelect"
                    >
                      <el-option
                        v-for="item in stepList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      >
                        <div class="selectItem">
                          <div class="selectLabel">
                            <i
                              :class="item.icon"
                              :style="{ color: item.color }"
                            />
                            <span>{{ item.label }}</span>
                          </div>
                          <div class="selectDesc" v-html="item.desc" />
                        </div>
                      </el-option>
                    </el-select>
                  </div>
                </template>
                <vone-upload
                  ref="testStepFilesUploadFile"
                  style="margin-bottom: 10px"
                  storage-type="LOCAL"
                  :disabled="dialogType === 'detail'"
                  biz-type="TEST_PRODUCT_CASE_TESTSTEP_FILE_UPLOAD"
                  :files-data="testStepFiles"
                  @change="changeInput"
                />
                <el-input
                  v-if="caseForm.stepType === 'text'"
                  v-model="caseForm.testStep"
                  placeholder="请输入描述"
                  type="textarea"
                  :autosize="{ minRows: 2 }"
                  maxlength="800"
                  show-word-limit
                  @input="changeInput"
                />

                <template v-else>
                  <el-table
                    ref="stepTable"
                    class="vone-table stepTable"
                    :data="stepData"
                  >
                    <el-table-column label="步骤" class-name="my-cell">
                      <template v-slot="{ row }">
                        <el-input
                          v-model="row.caseStepDes"
                          placeholder="请输入步骤"
                          class="stepList"
                          type="textarea"
                          maxlength="800"
                          show-word-limit
                          :autosize="{ minRows: 2 }"
                          @input="changeInput"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column label="预期" class-name="my-cell">
                      <template v-slot="{ row }">
                        <el-input
                          v-model="row.expectResult"
                          placeholder="请输入预期"
                          class="stepList"
                          type="textarea"
                          maxlength="800"
                          show-word-limit
                          :autosize="{ minRows: 2 }"
                          @input="changeInput"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="56">
                      <template v-slot="{ $index }">
                        <div style="display: flex; justify-content: center">
                          <el-button
                            type="text"
                            style="padding: 0"
                            @click="handleDel($index)"
                          >
                            <el-icon class="iconfont"
                              ><el-icon-application-delete
                            /></el-icon>
                          </el-button>
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                  <div class="stepBtn">
                    <el-button
                      type="text"
                      :icon="ElIconCirclePlus"
                      @click="handleAdd"
                    >
                      新增</el-button
                    >
                  </div>
                </template>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="caseForm.stepType == 'text'">
            <el-col :span="24">
              <el-form-item label="预期结果" prop="expectedResult">
                <el-input
                  v-model="caseForm.expectedResult"
                  placeholder="请输入预期结果"
                  type="textarea"
                  :autosize="{ minRows: 2 }"
                  maxlength="800"
                  show-word-limit
                  @input="changeInput"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </div>
    <footer class="footer">
      <div class="btnBox">
        <el-button v-if="dialogType !== 'detail'" @click="onClose"
          >取消</el-button
        >
        <el-button
          v-if="dialogType !== 'detail'"
          type="primary"
          :loading="saveLoading"
          :disabled="changeValue"
          @click="submit"
          >确定</el-button
        >
        <el-button v-if="dialogType === 'add'" @click="onReseat"
          >重置</el-button
        >
        <el-button
          v-if="dialogType === 'edit'"
          type="warning"
          :loading="updateLoading"
          :disabled="draft || !$permission('testm_case_update')"
          @click="upLevel"
          >升级</el-button
        >
        <el-switch
          v-if="dialogType !== 'detail'"
          v-model="draft"
          style="margin-left: 12px"
          active-text="存为草稿"
          @change="changeInput"
        />
        <el-checkbox
          v-if="dialogType === 'add'"
          v-model="checked"
          style="margin-left: 12px"
          @change="changeInput"
          >创建下一条</el-checkbox
        >
      </div>
    </footer>
  </el-drawer>
</template>

<script>
import {
  Cangku as ElIconCangku,
  CaretBottom as ElIconCaretBottom,
  ApplicationDelete as ElIconApplicationDelete,
  CirclePlus as ElIconCirclePlus,
} from "@element-plus/icons-vue";
import { $on, $off, $once, $emit } from "../../../../../utils/gogocodeTransfer";
import storage from "store";

import {
  testProductTreeCaseAdd,
  getTreeByLibrary,
  testProductTreeCasePut,
} from "@/api/vone/testmanage/case";

import { iderTorequire } from "@/api/vone/project/issue";
import { testProductCaseUpgrade } from "@/api/vone/testmanage/index";

import _, { debounce } from "lodash";
import Sortable from "sortablejs";
import tagSelect from "@/views/vone/test/components/tag-select.vue";
// 重置表单属性
const defaultForm = () => {
  return {
    version: "v1.0",
    treeId: "",
    name: "", // 用例名称
    caseKey: "", // 用例标识
    leadingBy: "", // 负责人
    requirementId: [],
    intent: "", // 测试意图
    prerequisite: "", // 前置条件
    stepType: "text", // 步骤类型 text/subclause
    testStep: "", // 测试步骤
    expectedResult: "", // 预期结果
    negative: "true", // 用例性质
    priority: null, // 用例级别
    tabInfo: [],
    files: [],
    intentFiles: [], // 意图附件
    prerequisiteFiles: [], // 条件
    testStepFiles: [], // 描述
  };
};
export default {
  data() {
    const validateDepth = (rule, value, callBack) => {
      if (value && !/^[1-9]\d*$/.test(value)) {
        callBack(new Error("请输入不以0开头的数字"));
      } else {
        callBack();
      }
    };
    return {
      caseCardLoading: false,
      changeValue: true,
      // 是否为草稿
      draft: false,
      noPermission: false,
      fileList: [],
      // 意图附件
      intentFiles: [],
      prerequisiteFiles: [],
      testStepFiles: [],
      selectOptionWidth: "",
      saveLoading: false,
      // 升级
      updateLoading: false,
      requireLoading: false,
      // 是否继续创建
      checked: false,
      // 新增右侧分组名称
      newTreeName: "",
      // 选中节点数据
      currentTree: {
        name: "",
        id: "",
      },
      caseForm: defaultForm(),
      addRules: {
        name: [
          { required: true, message: "请输入用例标题", trigger: "blur" },
          {
            pattern: "^([^ ]){1,128}$",
            message: "请输入不超过128个除空格外的字符",
            trigger: "blur",
          },
        ],
        execTime: [{ validator: validateDepth, trigger: "blur" }],
        leadingBy: [{ required: true, message: "请选择", trigger: "change" }],
        negative: [
          { required: true, message: "请选择用例性质", trigger: "change" },
        ],
        priority: [
          { required: true, message: "请选择用例级别", trigger: "change" },
        ],
      },
      validateTab: true,
      // 人员列表
      defaultUser: [],
      // 需求列表
      requirementList: [],
      // 优先级
      prioritList: [
        {
          name: "最高",
          code: 5,
          icon: "el-icon-icon-dengji-zuigao2",
          color: "#FA6A69",
        },
        {
          name: "较高",
          code: 4,
          icon: "el-icon-icon-dengji-jiaogao2",
          color: "#FA8669",
        },
        {
          name: "普通",
          code: 3,
          icon: "el-icon-icon-dengji-putong2",
          color: "var(--main-theme-color,#3e7bfa)",
        },
        {
          name: "较低",
          code: 2,
          icon: "el-icon-icon-dengji-jiaodi2",
          color: "#5ACC5E",
        },
        {
          name: "最低",
          code: 1,
          icon: "el-icon-icon-dengji-zuidi2",
          color: "#4ECF95",
        },
      ],
      // 步骤列表
      stepList: [
        {
          label: "文本描述",
          value: "text",
          icon: "iconfont el-icon-test-text",
          color: "var(--main-theme-color,#3e7bfa)",
          desc: `适用于简单的测试场景，没有明确测<br>试步骤。`,
        },
        {
          label: "步骤描述",
          value: "subclause",
          icon: "iconfont el-icon-test-step",
          color: "var(--main-theme-color,#3e7bfa)",
          desc: `适用于需要每一个步骤进行测试的场<br>景，有明确的测试步骤、预期结果。`,
        },
      ],
      // 测试步骤
      stepData: [
        {
          caseStepNum: 1,
          caseStepDes: "",
          expectResult: "",
        },
      ],
      ElIconCirclePlus,
    };
  },
  components: {
    tagSelect,
    ElIconCangku,
    ElIconCaretBottom,
    ElIconApplicationDelete,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: "",
    },
    libraryId: {
      type: String,
      default: "",
    },
    // 弹窗类型
    dialogType: {
      type: String,
      default: "add",
    },
    // 选中的用例数据
    infoData: {
      type: Object,
      default: () => ({}),
    },
    treeData: {
      type: Array,
      default: () => [],
    },
    // 选中节点数据
    nodeData: {
      type: Object,
      default: () => ({}),
    },
    id: {
      type: String,
      default: "",
    },
  },
  watch: {
    visible(val) {
      if (val) {
        const userInfo = storage.get("user");
        this.caseForm["leadingBy"] = userInfo.id;
        this.setCaseForm();
      }
    },
    infoData(val, old) {
      if (this.visible && val?.id != old?.id) {
        this.setCaseForm();
      }
    },
  },
  mounted() {
    this.draft = this.type == "graftTree";

    this.setCaseForm();
  },
  methods: {
    changeInput(val) {
      if (val) {
        this.changeValue = false;
      }
    },
    getVailtab() {
      this.validateTab = false;
      this.$refs.caseFormRef.fields[6].validateMessage = "标签格式错误";
      this.$refs.caseFormRef.fields[6].validateState = "error";
    },
    resetTab() {
      this.validateTab = true;
      this.$refs.caseFormRef.fields[6].validateState = "success";
    },
    async loadNode(node, resolve) {
      // 节点id
      const rootId = node.level == 0 ? 0 : node.data.id;

      const res = await getTreeByLibrary(
        this.$route.params.caseId || this.libraryId,
        rootId
      );
      resolve(res.data);
    },
    changeStepType() {
      this.changeValue = false;

      if (this.caseForm.stepType != "text") {
        this.$nextTick(() => {
          this.rowDrop();
        });
      }
    },
    // 行拖拽
    rowDrop() {
      var _this = this;
      const tbody = document.querySelector(".left_side .stepTable  tbody");

      if (tbody) {
        Sortable.create(tbody, {
          group: "name",
          animation: 150,
          onEnd({ newIndex, oldIndex }) {
            // 未拖拽和拖拽数据
            const dragged = _this.stepData[oldIndex];
            const unDraggedList = _this.stepData.filter(
              (ele, i) => i !== oldIndex
            );
            // 排序后数据
            unDraggedList.splice(newIndex, 0, dragged);
            // 清空表格数据，重新赋值，防止拖拽后渲染错误
            _this.stepData = [];
            _this.$nextTick(() => {
              _this.stepData = unDraggedList;
            });
          },
        });
      }
    },
    // 下拉框弹出时，设置弹框的宽度
    setOptionWidth(event) {
      this.$nextTick(() => {
        this.selectOptionWidth =
          event.target.parentNode.parentNode.clientWidth + "px";
      });
    },
    // 编辑时数据回显
    async setCaseForm() {
      this.getRequirementList();
      // 是否保存为草稿
      this.draft = this.type == "graftTree" || this.caseForm.draft;
      this.changeValue = true;
      this.checked = false;

      if (this.dialogType && this.dialogType !== "add") {
        // 查询需求列表
        this.caseForm = { ...this.caseForm, ...this.infoData };
        this.newTreeName = "未分组";
        this.caseForm.requirementId = this.caseForm.requirementId
          ? this.caseForm.requirementId.split(",")
          : "";
        this.currentTree = { id: this.caseForm.treeId };
        if (this.caseForm.echoMap && this.caseForm.echoMap.treeId) {
          this.newTreeName = this.caseForm.echoMap.treeId.name;
          this.currentTree = this.caseForm.echoMap.treeId;
        }
        if (this.caseForm.requirementId?.length > 0) {
          this.requirementList = [...this.caseForm.echoMap.requirement];
        }
        if (this.caseForm?.echoMap?.leadingBy) {
          this.defaultUser = [this.caseForm.echoMap.leadingBy];
        }

        // 测试步骤显示
        if (this.caseForm.stepType === "subclause") {
          this.caseForm.testStep = "";
          this.stepData =
            this.infoData.testStep && JSON.parse(this.infoData.testStep);

          this.$nextTick(() => {
            this.rowDrop();
          });
        }
      } else {
        this.caseForm = defaultForm();
        const userInfo = storage.get("user");
        this.caseForm["leadingBy"] = userInfo.id;
        this.caseForm["priority"] = 3;
        this.newTreeName = this.nodeData.name;
        this.currentTree = this.nodeData;
      }

      this.$nextTick(() => {
        this.fileList = this.infoData.files || [];
        this.intentFiles = this.infoData.intentFiles || [];
        this.prerequisiteFiles = this.infoData.prerequisiteFiles || [];
        this.testStepFiles = this.infoData.testStepFiles || [];
        this.$refs.tree?.setCurrentKey([this.nodeData.id]);
      });
    },

    // 查需求列表
    getRequirementList: debounce(async function (query, requireId) {
      try {
        this.requireLoading = true;
        const params = {};
        if (this.$route.params.id) {
          params.projectId = [this.$route.params.id];
        } else {
          params.productId =
            this.$route.params.productInfoIds &&
            this.$route.params.productInfoIds.split(",");
        }
        const res = await iderTorequire({
          name: query,
          projectId: this.$route.params.id,
        });
        this.requireLoading = false;
        if (!res.isSuccess) {
          this.$message.warning(res.msg);
          return;
        }
        this.requirementList = res.data;
      } catch (e) {
        this.requireLoading = false;
      }
    }, 1000),

    // 分组树点击
    nodeClick(val) {
      if (val.parentId == "0") return;
      this.currentTree = val;
      this.newTreeName = val.name;
      this.changeValue = false;
      // 关闭分组弹窗
      this.$refs["groupPopover"].doClose();
    },
    // 重置
    onReseat() {
      this.$refs.intentFilesUploadFile.$refs.file.clearFiles();
      this.$refs.prerequisiteFilesUploadFile.$refs.file.clearFiles();
      this.$refs.testStepFilesUploadFile.$refs.file.clearFiles();
      this.$refs.caseUploadFile.$refs.file.clearFiles();
      this.caseForm = defaultForm();
      this.$nextTick(() => {
        this.onClear();
      });
      this.$refs["caseFormRef"].resetFields();
    },
    // 恢复初始状态
    onClear() {
      this.stepData = [
        {
          caseStepNum: 1,
          caseStepDes: "",
          expectResult: "",
        },
      ];
    },
    // 新增-按钮
    handleAdd() {
      const obj = {
        caseStepNum: this.stepData.length + 1,
        caseStepDes: "",
        expectResult: "",
      };
      this.stepData.push(obj);
    },
    // 删除-按钮
    handleDel(index) {
      if (this.stepData.length > 1) {
        this.stepData.splice(index, 1);
        this.stepData = this.stepData.map((item, index) => {
          item.caseStepNum = index + 1;
          return item;
        });
        this.changeValue = false;
      }
    },
    async submit() {
      this.dialogType === "add" ? this.createNewCase() : this.editCase();
    },
    // 新建用例
    async createNewCase() {
      try {
        if (!this.validateTab) {
          return;
        }
        await this.$refs.caseFormRef.validate();
        this.saveLoading = true;
        this.caseForm["files"] = this.$refs["caseUploadFile"].uploadFiles;
        this.caseForm["intentFiles"] =
          this.$refs["intentFilesUploadFile"].uploadFiles;
        this.caseForm["prerequisiteFiles"] =
          this.$refs["prerequisiteFilesUploadFile"].uploadFiles;
        this.caseForm.stepType !== "subclause"
          ? (this.caseForm["testStepFiles"] =
              this.$refs["testStepFilesUploadFile"].uploadFiles)
          : (this.caseForm["testStepFiles"] = []);
        const obj = _.pick(this.caseForm, [
          "name",
          "tabInfo",
          "expectedResult",
          "intent",
          "leadingBy",
          "prerequisite",
          "priority",
          "stepType",
          "version",
          "files",
          "intentFiles",
          "prerequisiteFiles",
          "testStepFiles",
          "execTime",
        ]);
        const params = {
          ...obj,
          tabInfo: this.caseForm.tabInfo ? this.caseForm.tabInfo : [],
          requirementId: Array.isArray(this.caseForm.requirementId)
            ? this.caseForm.requirementId.join()
            : this.caseForm.requirementId,
          caseKey: "",
          draft: this.draft || false,
          libraryId: this.$route.params.caseId || this.libraryId, // 用例库
          testStep:
            this.caseForm.stepType === "subclause"
              ? JSON.stringify(this.stepData)
              : this.caseForm.testStep, // 测试步骤
          stateId: "", // 评审状态
          state: false,
          treeId: this.currentTree.id, // 用例树id
        };

        const res = await testProductTreeCaseAdd(params);
        this.saveLoading = false;

        if (res.isSuccess) {
          this.$message.success("新建成功");
          // 未选中保存创建下一条
          $emit(this, "success");
          $emit(this, "getTagsListByLibraryId"); // 刷新标签

          !this.checked && $emit(this, "update:visible", false);
          this.onClear();
          this.fileList = [];
          this.intentFiles = [];
          this.prerequisiteFiles = [];
          this.testStepFiles = [];
          if (this.checked) {
            this.caseForm = defaultForm();
            const userInfo = storage.get("user");
            this.caseForm["leadingBy"] = userInfo.id;
            this.caseForm["priority"] = 3;
            this.newTreeName = this.nodeData.name;
            this.currentTree = this.nodeData;
            this.onClear();
            this.fileList = [];
            this.intentFiles = [];
            this.prerequisiteFiles = [];
            this.testStepFiles = [];
          }
        } else {
          this.$message.error(res.msg);
        }
      } catch (e) {
        this.saveLoading = false;
      }
    },
    // 编辑用例
    async editCase() {
      try {
        if (!this.validateTab) {
          return;
        }
        await this.$refs.caseFormRef.validate();
        this.saveLoading = true;
        this.caseForm["files"] = this.$refs["caseUploadFile"].uploadFiles;
        this.caseForm["intentFiles"] =
          this.$refs["intentFilesUploadFile"].uploadFiles;
        this.caseForm["prerequisiteFiles"] =
          this.$refs["prerequisiteFilesUploadFile"].uploadFiles;
        this.caseForm.stepType !== "subclause"
          ? (this.caseForm["testStepFiles"] =
              this.$refs["testStepFilesUploadFile"].uploadFiles)
          : (this.caseForm["testStepFiles"] = []);
        const obj = _.pick(this.caseForm, [
          "id",
          "name",
          "tabInfo",
          "expectedResult",
          "intent",
          "caseKey",
          "leadingBy",
          "prerequisite",
          "priority",
          "stepType",
          "version",
          "files",
          "intentFiles",
          "prerequisiteFiles",
          "testStepFiles",
          "state",
          "execTime",
        ]);
        const params = {
          ...obj,
          requirementId: Array.isArray(this.caseForm.requirementId)
            ? this.caseForm.requirementId.join()
            : this.caseForm.requirementId,
          draft: this.draft,
          libraryId: this.$route.params.caseId || this.libraryId, // 用例库
          testStep:
            this.caseForm.stepType === "subclause"
              ? JSON.stringify(this.stepData)
              : this.caseForm.testStep, // 测试步骤
          productId: this.infoData.productId, // 产品id
          stateId: this.infoData.stateId, // 评审状态
          treeId: this.currentTree.id || 0, // 用例树id
        };

        const res = await testProductTreeCasePut(params);
        this.saveLoading = false;
        if (res.isSuccess) {
          this.$message.success("修改成功");
          $emit(this, "success");
          $emit(this, "getTagsListByLibraryId"); // 刷新标签
          this.onClose();
        }
      } catch (e) {
        this.saveLoading = false;
      }
    },
    onClose() {
      this.caseForm = defaultForm();

      $emit(this, "update:visible", false);
      this.$nextTick(() => {
        this.onClear();
      });
    },
    // 升级async
    async upLevel() {
      try {
        if (!this.validateTab) {
          return;
        }
        await this.$refs.caseFormRef.validate();
        this.updateLoading = true;

        const newVersion = (
          Number(this.caseForm.version.substring(1, 10)) + 1
        ).toString();

        this.caseForm["version"] = `v${newVersion}.0`;

        const obj = _.pick(this.caseForm, [
          "id",
          "name",
          "tabInfo",
          "expectedResult",
          "intent",
          "caseKey",
          "leadingBy",
          "prerequisite",
          "priority",
          "stepType",
          "version",
          "execTime",
        ]);
        const params = {
          ...obj,
          requirementId: Array.isArray(this.caseForm.requirementId)
            ? this.caseForm.requirementId.join()
            : this.caseForm.requirementId,
          state: false,
          draft: false,
          libraryId: this.$route.params.caseId || this.libraryId, // 用例库
          testStep:
            this.caseForm.stepType === "subclause"
              ? JSON.stringify(this.stepData)
              : this.caseForm.testStep, // 测试步骤
          productId: this.infoData.productId, // 产品id
          stateId: this.infoData.stateId, // 评审状态
          treeId: this.currentTree.id || 0, // 用例树id
        };

        const res = await testProductCaseUpgrade(params);
        this.updateLoading = false;
        if (!res.isSuccess) {
          this.$message.warning(res.msg);
          return;
        }
        this.$message.success("用例升级成功");
        $emit(this, "success");
        this.onClose();
      } catch (e) {
        this.updateLoading = false;
      }
    },
  },
  emits: ["update:visible", "success", "getTagsListByLibraryId"],
};
</script>

<style lang="scss" scoped>
@mixin ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
:deep(.el-drawer__header) {
  padding: 12px 16px;
}
.caseForm {
  padding-bottom: 50px;
  overflow-y: auto;
  overflow-x: hidden;
  .fixhe {
    padding: 8px 12px;
    border-bottom: 1px solid var(--solid-border-color);
    .el-col-offset-1 {
      margin-left: 12px;
    }
    .el-form-item__label {
      color: var(--font-second-color);
    }
    .el-col-10 {
      width: unset;
      min-width: 124px;
      padding: 4px 16px !important;

      :deep(div .el-input--small .el-input__inner) {
        border: none;
        background: none;
      }
      :deep(.el-input__suffix) {
        display: none;
      }
      :deep(.el-input--small) {
        font-size: 14px;
      }
      // :deep(.is-disabled .el-input--small) {
      //   font-size: 14px;
      //   color: #000;
      // }
      :deep(.el-input.is-disabled .el-input__inner) {
        color: #000 !important;
        font-size: 14px;
      }
    }
    .el-col:hover {
      background: var(--col-hover-bg);
    }
    .form_title {
      padding-left: 28px;
      :deep(.el-input--small .el-input__inner) {
        width: 100%;
        border: none;
        background: none;
        font-weight: bold;
        font-size: 16px;
        padding-left: 0;
      }
      .el-col {
        padding: 0px !important;
      }
      :deep(.el-form-item) {
        margin-bottom: 8px;
      }

      :deep(.el-form-item__label:before) {
        display: none;
      }

      :deep(.el-input__inner:focus) {
        background: var(--col-hover-bg);
      }
      :deep(.el-form-item__content) {
        display: inline-block;
        width: 95%;
      }
      & :hover {
        background: var(--col-hover-bg);
      }
    }
    .el-col-10 {
      .el-form-item {
        margin-bottom: 0px;
      }
      :deep(.avatar) {
        img {
          width: 30px;
          height: 30px;
        }
      }
      .priority {
        :deep(.iconfont) {
          font-size: 29px !important;
          height: 30px;
          padding-left: 0 !important;
        }
      }
      :deep(.el-input__inner) {
        margin-left: 10px;
      }
      :deep(.el-form-item__label:before) {
        display: none;
      }
    }
  }
  .attribute {
    padding: 16px;
    h4 {
      margin: 0px 0px 12px 0px;
    }
    .execTime {
      :deep(.el-form-item__content) {
        border: 1px solid #c1c8d6;
        border-radius: 2px;
        .el-input__inner {
          border: 0;
        }
        .el-input-group {
          border: 0;
          border-radius: 0;
        }
        .el-input-group__append {
          border: 0;
          background-color: #fff;
        }
      }
    }
    :deep(.el-popover__reference-wrapper) {
      line-height: 30px;
    }
    :deep(.referenceEmit) {
      border: 1px solid #c1c8d6;
      border-radius: 2px;
    }
  }
}
header.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .title {
    display: flex;
    align-items: center;
    gap: 0 18px;
    font-size: 14px;
  }
  .gap {
    gap: 0 18px;
  }
}
.btnBox {
  position: relative;
  display: flex;
  align-items: center;
  height: 45px;
  text-align: right;
} /*// 节点树样式*/
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  & > span {
    // flex: 1 1 auto;
    display: flex;
    align-items: center;
  }

  .treeNode {
    margin-left: 6px;
    flex: 1 1 auto;
    @include ellipsis;
  }
}
.stepTable {
  :deep() {
    .el-table__cell {
      border-right: 1px solid var(--disabled-bg-color, #ebeef5);
    }
    th.el-table__cell {
      background-color: var(--bottom-bg-color);
    }
    td div.cell {
      padding: 0; // 清除单元格内容的padding
    }
    tr:hover {
      .el-textarea__inner {
        background-color: var(--hover-bg-color);
      }
    }
  }
} /*// 步骤测试样式*/
.stepList {
  :deep() {
    .el-textarea__inner {
      padding-bottom: 20px;
      border: none;
      resize: vertical;
    }
  }
}
.stepBtn {
  text-align: center;
  border-left: 1px solid var(--disabled-bg-color, #ebeef5);
  border-right: 1px solid var(--disabled-bg-color, #ebeef5);
  border-bottom: 1px solid var(--disabled-bg-color, #ebeef5);
}
.basicBox {
  display: flex;
  align-items: center;
  position: absolute;
  top: 0;
  left: 75px;
  cursor: pointer;
  .basicText {
    margin: 0 6px;
    white-space: nowrap;
  }
  .basicIcon {
    color: var(--main-font-color);
  }
  .select {
    opacity: 0;
    position: absolute;
    top: 6px;
    left: 0;
    height: 30px;
    z-index: 99;
    width: 90px;
    cursor: pointer;
  }
}
.popoverMain {
  min-width: 300px;
  min-height: 100px;
  max-height: 300px;
  overflow-y: auto;
}
.referenceEmit {
  display: flex;
  align-items: center;
  .groupIcon {
    margin-left: 6px;
  }
  .treeName {
    height: 30px;
    max-width: 200px;
    @include ellipsis;
  }
} /*// 测试步骤选择样式*/
.typeSelect {
  :deep(.el-select-dropdown__list) {
    padding: 0;
  }
  .el-select-dropdown__item {
    height: auto;
    padding: 0 16px 12px;

    &:first-child {
      padding-top: 16px;
    }
    &:last-child {
      padding-bottom: 16px;
    }
    &:not(.selected) {
      color: var(--main-font-color);
    }
  }
  .selectLabel {
    display: flex;
    gap: 14px 0;
    align-items: center;
  }
  .selectDesc {
    font-weight: normal;
    line-height: 20px;
    color: var(--auxiliary-font-color);
  }
}
:deep(.level-select) {
  .el-input__prefix {
    display: flex;
    align-items: center;
  }
}
.requireSelect {
  :deep(.el-input__inner) {
    padding-right: 30px;
    overflow: hidden;
  }
  :deep(.el-select__tags) {
    max-width: unset;
  }
}
.footer {
  margin-top: 12px;
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 0;
  padding: 5px 20px;
  text-align: right;
  background-color: var(--main-bg-color, #fff);
  border-top: 1px solid var(--disabled-bg-color, #ebeef5);
}
:deep(.el-input__prefix) {
  left: 0px;
}
.leading {
  :deep(.el-input__inner) {
    padding-left: 32px;
  }
}
</style>

<style lang="scss">
.typeSelect {
  .el-select-dropdown__list {
    padding: 0;
  }
}
.btnBox {
  .el-button.is-disabled {
    border-color: #e6e9f0;
  }
}
</style>
