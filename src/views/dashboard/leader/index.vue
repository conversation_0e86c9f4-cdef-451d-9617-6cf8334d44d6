<template>
  <div id="dashboard-main" class="dashboard-main">
    <div class="dashboard-header">
      <el-form :model="form" label-width="80px">
        <el-form-item label="纬度" prop="latitude">
          <el-select v-model="form.latitude" value-key="id" style="width: 150px" @change="changeLatitude">
            <el-option label="机构" value="org">机构</el-option>
            <el-option label="项目" value="project">项目</el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="form.latitude == 'project'" label="项目" prop="projectId">
          <el-select
            v-model="form.projectId"
            multiple
            collapse-tags
            filterable
            value-key="id"
            style="width: 200px"
            @change="projectChange"
          >
            <div style="padding-left: 12px; line-height: 34px">
              <el-checkbox
                v-if="projectIdList && projectIdList.length > 1"
                v-model="isCheckAll"
                :indeterminate="isIndeterminate"
                @change="handleCheckAllChange"
                >全选</el-checkbox
              >
            </div>
            <el-option v-for="item in projectIdList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="form.latitude == 'org'" label="机构" prop="orgId">
          <vone-tree-select
            v-model="form.orgId"
            search-nested
            multiple
            :tree-data="orgData"
            style="width: 240px"
            placeholder="请选择机构"
            :limit="1"
            :limit-text="count => `+${count}`"
            @select="orgChange"
          />
        </el-form-item>
        <el-form-item label="开始时间" prop="startDate">
          <el-date-picker
            v-model="form.startDate"
            type="date"
            placeholder="选择开始时间"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            :picker-options="startPickerOptions"
            @change="changeDate"
          />
        </el-form-item>
        <el-form-item label="结束时间" prop="endDate">
          <el-date-picker
            v-model="form.endDate"
            type="date"
            placeholder="选择结束时间"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            :picker-options="endPickerOptions"
            @change="changeDate"
          />
        </el-form-item>
      </el-form>
    </div>
    <div v-for="(item, index) in dataList" :key="index" class="bg">
      <el-row :gutter="24">
        <el-col :span="5">
          <Bdetail :data="item" :layout="form" />
        </el-col>
        <el-col :span="19" style="display: flex">
          <pieEchartB
            style="margin: 0px 20px"
            title="需求概览"
            :layout="form"
            :data="item.chartData && item.chartData.requirementResult"
            :project-id="item.chartData && item.chartData.bizId"
            :org-id="item.chartData && item.chartData.bizId"
          />
          <pieEchartB
            title="任务概览"
            :layout="form"
            :data="item.chartData && item.chartData.taskResult"
            :project-id="item.chartData && item.chartData.bizId"
            :org-id="item.chartData && item.chartData.bizId"
          />
          <pieEchartB
            style="margin: 0px 20px"
            title="缺陷概览"
            :layout="form"
            :data="item.chartData && item.chartData.bugResult"
            :project-id="item.chartData && item.chartData.bizId"
            :org-id="item.chartData && item.chartData.bizId"
          />
          <pieEchartB
            title="工时概览"
            :layout="form"
            :data="item.chartData && item.chartData.issueHourResult"
            :project-id="item.chartData && item.chartData.bizId"
            :org-id="item.chartData && item.chartData.bizId"
          />
          <div class="btnbox" @click="showChild(index)">
            <i v-if="item.showUser" class="iconfont el-icon-direction-up" style="cursor: pointer" />
            <i v-else class="iconfont el-icon-direction-down" style="cursor: pointer" />
          </div>
        </el-col>
      </el-row>
      <div v-if="item.showUser" class="childBox">
        <el-row
          v-for="(userEchart, userIndex) in item.userTable.records"
          :key="userIndex"
          :gutter="24"
          class="small-bg"
        >
          <el-col :span="5">
            <Sdetail :data="userEchart" :parent-data="item" :form="form" />
          </el-col>
          <el-col :span="19" style="display: flex">
            <pieEchartS
              style="margin: 0px 20px"
              title="需求概览"
              :layout="form"
              :data="userEchart.chartData && userEchart.chartData.requirementResult"
              :project-id="item.chartData && item.chartData.bizId"
              :org-id="item.chartData && item.chartData.bizId"
              :user-id="userEchart.chartData && userEchart.chartData.bizId"
            />
            <pieEchartS
              title="任务概览"
              :layout="form"
              :data="userEchart.chartData && userEchart.chartData.taskResult"
              :project-id="item.chartData && item.chartData.bizId"
              :org-id="item.chartData && item.chartData.bizId"
              :user-id="userEchart.chartData && userEchart.chartData.bizId"
            />
            <pieEchartS
              style="margin: 0px 20px"
              title="缺陷概览"
              :layout="form"
              :data="userEchart.chartData && userEchart.chartData.bugResult"
              :project-id="item.chartData && item.chartData.bizId"
              :org-id="item.chartData && item.chartData.bizId"
              :user-id="userEchart.chartData && userEchart.chartData.bizId"
            />
            <pieEchartS
              title="工时概览"
              :layout="form"
              :data="userEchart.chartData && userEchart.chartData.issueHourResult"
              :project-id="item.chartData && item.chartData.bizId"
              :org-id="item.chartData && item.chartData.bizId"
              :user-id="userEchart.chartData && userEchart.chartData.bizId"
            />
          </el-col>
        </el-row>

        <vone-pagination :ref="`pagination${index}`" :total="item.userTable.total" @update="getUserList(item, index)" />
      </div>
    </div>
  </div>
</template>
<script>
import Bdetail from './function/b-detail.vue'
import Sdetail from './function/s-detail.vue'
import pieEchartB from './function/pie-echart-b.vue'
import pieEchartS from './function/pie-echart-s.vue'
import { apiAlmProjectNoPage } from '@/api/vone/project/index'
import { getProjectList, getOrgList, getOrgUser, getPie } from '@/api/vone/dashboard/leader'
import { getProjectUserList } from '@/api/vone/project/team'
import { orgList } from '@/api/vone/base/org'
import { gainTreeList } from '@/utils'
import dayjs from 'dayjs'
export default {
  components: {
    Bdetail,
    Sdetail,
    pieEchartB,
    pieEchartS
  },
  data() {
    return {
      data: {},
      form: {
        latitude: 'project',
        projectId: [],
        time: [],
        productId: [],
        orgId: [],
        startDate: '',
        endDate: ''
      },
      projectIdList: [],
      orgData: [],
      defaultTime: [],
      showDetail: false,
      dataList: [],
      isIndeterminate: true,
      isCheckAll: false,
      type: this.$route.query?.type || '',
      startPickerOptions: {
        disabledDate: time => {
          if (this.form?.startDate) {
            return time.getTime() > new Date(this.form.endDate).getTime()
          }
        }
      },
      endPickerOptions: {
        disabledDate: time => {
          if (this.form?.startDate) {
            return time.getTime() < new Date(this.form.startDate).getTime()
          }
        }
      }
    }
  },

  watch: {
    form: {
      handler(val) {
        if (val) {
          this.data = val
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.getProjectList()
    this.getOrgList()
    this.setDefaultTimeRange()
    if (this.type == 1) {
      const formData = JSON.parse(localStorage.getItem('leaderwork'))
      this.form = formData
    }
  },
  methods: {
    handleCheckAllChange(val) {
      const data = this.projectIdList.map(item => item.id)
      this.form.projectId = val ? data : []
      this.isIndeterminate = false
      if (!val) return
      this.getList()
    },
    getList() {
      if (this.form.latitude == 'project') {
        if (!this.form.projectId?.length) {
          return this.$message.warning('请选择项目')
        }
        getProjectList({
          projectIds: this.form.projectId.join(',')
        }).then(res => {
          if (res.isSuccess) {
            this.dataList = res.data
            this.dataList.map((item, index) => {
              this.$set(item, 'showUser', false)
              this.getProjectUser(item, index)
              this.geChartData(item, index, 'project')
            })
          } else {
            this.$message.warning(res.msg)
          }
        })
      } else {
        if (!this.form.orgId?.length) {
          return this.$message.warning('请选择机构')
        }
        getOrgList({
          orgIds: this.form.orgId.join(',')
        }).then(res => {
          if (res.isSuccess) {
            this.dataList = res.data
            this.dataList.map((item, index) => {
              this.$set(item, 'showUser', false)
              this.getOrgUser(item, index)
              this.geChartData(item, index, 'org')
            })
          } else {
            this.$message.warning(res.msg)
          }
        })
      }
    },
    projectChange(val) {
      const checkedCount = val.length
      this.isCheckAll = checkedCount === this.projectIdList.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.projectIdList.length
      if (!val) return
      if (!this.form.time) {
        this.$message.warning('请选择时间')
        return
      }
      this.getList()
    },
    orgChange(val) {
      if (!this.form.time) {
        this.$message.warning('请选择时间')
        return
      }
      this.$nextTick(() => {
        this.getList()
      })
    },
    getProjectUser(row, index) {
      const pageObj = (this?.$refs?.['pagination' + index]?.[0] &&
        this?.$refs?.['pagination' + index]?.[0]?.pageObj) || { current: 1, size: 20 }
      const params = {
        ...pageObj,
        extra: {
          ...this.extraData
        },
        model: { projectId: row.id }
      }
      getProjectUserList(params).then(res => {
        if (res.isSuccess) {
          this.dataList[index].userTable = res.data
          res.data?.records.map(async (item, idx) => {
            await this.geChartData(item, index, 'user', idx, 'project', row.id)
          })
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    getOrgUser(row, index) {
      const pageObj = (this?.$refs?.['pagination' + index]?.[0] &&
        this?.$refs?.['pagination' + index]?.[0]?.pageObj) || { current: 1, size: 20 }
      const params = {
        ...pageObj,
        extra: {
          ...this.extraData
        },
        model: { orgId: row.id }
      }
      getOrgUser(params).then(res => {
        if (res.isSuccess) {
          // this.dataList[index].userTable = res.data
          this.$set(this.dataList[index], 'userTable', res.data)
          res.data?.records.map((item, idx) => {
            this.geChartData(item, index, 'user', idx, 'org', row.id)
          })
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    getUserList(row, index) {
      if (this.form.latitude == 'project') {
        this.getProjectUser(row, index)
      } else {
        this.getOrgUser(row, index)
      }
    },
    // 饼图
    geChartData(row, index, type, idx, pType, pId) {
      getPie({
        projectIds: pType == 'project' ? [pId] : type == 'project' ? [row.id] : [],
        orgIds: pType == 'org' ? [pId] : type == 'org' ? [row.id] : [],
        userIds: type == 'user' ? [row.id] : [],
        startDate: dayjs(this.form.time && this.form.time[0]).format('YYYY-MM-DD'),
        endDate: dayjs(this.form.time && this.form.time[1]).format('YYYY-MM-DD')
      }).then(res => {
        if (res.isSuccess) {
          if (type == 'project' || type == 'org') {
            this.$set(this.dataList[index], 'chartData', res.data[0])
          } else {
            this.$set(this.dataList[index].userTable.records[idx], 'chartData', res.data[0])
          }
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    setDefaultTimeRange() {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 1) // 设置为当前月份的上一个月
      // this.defaultTime = [start, end]
      // if (this.type != 1) {
      //   this.form.time = [...this.defaultTime]
      // }
      this.form.startDate = start
      this.form.endDate = end
    },
    async getProjectList() {
      const res = await apiAlmProjectNoPage()

      if (!res.isSuccess) {
        return
      }
      this.projectIdList = res.data.filter(item => item.classify != 'DELIVERY')
      if (this.type != 1) {
        this.form.projectId = [res.data[0].id]
      }
      this.getList()
    },
    // 查询所有机构
    async getOrgList() {
      const res = await orgList()
      if (!res.isSuccess) {
        return
      }

      const orgTree = gainTreeList(res.data)
      this.orgData = orgTree
    },
    changeDate(val) {
      // if (!val) return
      if (this.form.startDate && this.form.endDate) {
        this.form.time = [this.form.startDate, this.form.endDate]
        this.getList()
      } else {
        this.form.time = null
      }
    },
    changeLatitude() {
      this.form.orgId = []
      this.form.projectId = []
      this.form.productId = []
      this.isCheckAll = false
    },
    showChild(index) {
      this.$set(this.dataList[index], 'showUser', !this.dataList[index].showUser)
    }
  }
}
</script>
<style scoped lang="scss">
.dashboard {
  &-main {
    touch-action: none;
    background: var(--content-bg-color);
  }
  &-header {
    height: 48px;
    line-height: 48px;
    background: var(--main-bg-color);
    padding: 0px 16px;
    margin: -10px -10px 0px -10px;
    box-shadow: var(--nav-top-shadow);
    border: 1px solid var(--solid-border-color);
    display: flex;
    align-items: center;
    :deep(.el-tabs__nav-wrap::after) {
      display: none;
    }
    :deep(.vone-tabs .el-tabs__nav) {
      border: none;
    }
  }
  &-content {
    margin: 0 -10px;
  }
}
:deep(.el-row) {
  margin-left: 0px !important;
  margin-right: 0px !important;
}
:deep(.el-col) {
  padding-left: 0px !important;
  padding-right: 0px !important;
  // margin-top: 10px;
}
:deep(.el-form .el-form-item) {
  margin-bottom: unset;
}
:deep(.el-form .el-form-item__label) {
  line-height: 32px;
}
:deep(.el-form) {
  display: flex;
}
.bg {
  background: #fff;
  margin: 10px 0px;
  padding: 20px;
}
.btnbox {
  height: 26px;
  width: 26px;
  border: 1px solid var(--input-border-color);
  padding: 0px 4px;
  text-align: center;
  line-height: 26px;
  margin: 70px 20px;
  border-radius: 50%;
}
.childBox {
  max-height: 462px;
  overflow-y: auto;
  position: relative;
  // padding-bottom: 40px;
}
.small-bg {
  background: #f7f7f7;
  border-radius: 4px;
  padding: 10px 12px;
  margin-top: 10px;
}
:deep(.pagination) {
  position: static;
}
</style>
