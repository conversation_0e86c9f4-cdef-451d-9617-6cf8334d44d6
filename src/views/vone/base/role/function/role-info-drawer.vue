<template>
  <vone-drawer v-model:value="visible" size="md" :before-close="onClose">
    <template v-slot:title>
      <div class="drawer-title">
        <div class="drawer-title-text">
          {{ `角色【${roleData.name}】详情` }}
          <el-icon class="iconfont nextBtn"
            ><el-icon-yibiaopan-shangyi
          /></el-icon>
          <el-icon class="iconfont nextBtn"
            ><el-icon-yibiaopan-xiayi
          /></el-icon>
        </div>
      </div>
    </template>
    <div v-loading="pageLoading" class="pageBox">
      <div class="title">
        <strong> 基本信息 </strong>
      </div>
      <div v-if="type == 'projectRole'" class="vone-detail-row">
        <vone-desc :column="2">
          <vone-desc-item label="角色名称">
            {{ roleData.name }}
          </vone-desc-item>
          <vone-desc-item label="角色编号">
            {{ roleData.code }}
          </vone-desc-item>
          <vone-desc-item label="项目类型">
            {{ projectTypeName(roleData) }}
          </vone-desc-item>
          <vone-desc-item label="描述">
            {{ roleData.description }}
          </vone-desc-item>
        </vone-desc>
      </div>
      <div v-else class="vone-detail-row">
        <vone-desc :column="2">
          <vone-desc-item label="名称">
            {{ roleData.name }}
          </vone-desc-item>
          <vone-desc-item label="标识">
            {{ roleData.code }}
          </vone-desc-item>
          <vone-desc-item label="创建时间">
            {{ roleData.createTime }}
          </vone-desc-item>
          <vone-desc-item label="更新时间">
            {{ roleData.updateTime }}
          </vone-desc-item>
          <vone-desc-item label="状态">
            {{ roleData.state ? "启用" : "禁用" }}
          </vone-desc-item>
          <vone-desc-item label="描述">
            {{ roleData.description }}
          </vone-desc-item>
        </vone-desc>
      </div>
    </div>
    <template v-slot:footer>
      <div>
        <el-button @click="onClose">取消</el-button>
      </div>
    </template>
  </vone-drawer>
</template>

<script>
import {
  YibiaopanShangyi as ElIconYibiaopanShangyi,
  YibiaopanXiayi as ElIconYibiaopanXiayi,
} from "@element-plus/icons-vue";
import { $on, $off, $once, $emit } from "../../../../../utils/gogocodeTransfer";
import { apiAlmProjectRoleInfo } from "@/api/vone/project/setting";
import { orgList } from "@/api/vone/base/org";
import { apiBaseRoleGetInfo } from "@/api/vone/base/role";
export default {
  components: {
    ElIconYibiaopanShangyi,
    ElIconYibiaopanXiayi,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: null,
    },
    row: {
      type: Object,
      default: null,
    },
    type: {
      type: String,
      default: "platFormRole",
    },
    tableList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      orgMap: {},
      currentIndex: undefined, // 当前数据的索引
      pageLoading: false,
      roleData: {},
    };
  },
  computed: {
    projectTypeName() {
      return function (row) {
        return row.echoMap?.typeCode?.name || "";
      };
    },
  },
  mounted() {
    this.currentIndex = this.tableList.findIndex((item) => item.id === this.id);
    this.getBasicInfo();
    if (this.type == "projectRole") {
      this.getOrgList();
    }
  },
  methods: {
    // 查询所有机构
    async getOrgList() {
      await orgList().then((res) => {
        this.orgMap = res.data.reduce((acc, cur) => {
          acc[cur.id] = cur;
          return acc;
        }, {});
      });
    },
    onClose() {
      $emit(this, "update:visible", false);
    },

    // 基本信息
    async getBasicInfo(val) {
      try {
        this.pageLoading = true;
        const addApi =
          this.type == "platFormRole"
            ? apiBaseRoleGetInfo
            : apiAlmProjectRoleInfo;
        const { data, isSuccess, msg } = await addApi(val || this.id);
        this.pageLoading = false;
        if (!isSuccess) {
          return this.$message.error(msg);
        }
        this.roleData = data;
      } catch (e) {
        this.pageLoading = false;
      }
    },

    // 上一条
    dataPrev() {
      if (this.currentIndex === 0) {
        this.$message.warning("已经是第一条数据啦");
        return;
      }
      this.currentIndex--;
      this.getBasicInfo(this.tableList[this.currentIndex].id);
    },
    // 下一条
    dataNext() {
      if (this.currentIndex === this.tableList.length - 1) {
        this.$message.warning("已经是最后一条数据啦");
        return;
      }
      this.currentIndex++;
      this.getBasicInfo(this.tableList[this.currentIndex].id);
    },
  },
  emits: ["update:visible"],
};
</script>

<style lang="scss" scoped>
.pageBox {
  padding: 20px;
  .title {
    border-left: 3px solid var(--main-theme-color);
    margin-bottom: 10px;
    padding-left: 10px;
  }
}
.vone-detail-row .el-col .form-title {
  width: 90px;
}
.nextBtn {
  width: 20px;
  height: 20px;
  line-height: 20px;
  border-radius: 3px;
  text-align: center;
  cursor: pointer;
  font-size: 13px;
  margin-right: 3px;
  &:hover {
    font-weight: bold;
  }
}
</style>
