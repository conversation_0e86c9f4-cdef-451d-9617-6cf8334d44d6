import request from '@/utils/axios-api'

// 项目下成员

export function apiAlmProjectUser(data) {
  return request({
    url: '/api/alm/alm/projectUser/page',
    method: 'post',
    data
  })
}

// 项目下成员新增

export function apiAlmProjectUserAdd(data) {
  return request({
    url: '/api/alm/alm/projectUser',
    method: 'post',
    data
  })
}

// 项目下成员编辑
export function apiAlmProjectUserPut(data) {
  return request({
    url: '/api/alm/alm/projectUser',
    method: 'put',
    data
  })
}

// 项目下用户详情
export function apiAlmProjectUserInfo(id) {
  return request({
    url: `/api/alm/alm/projectUser/${id}`,
    method: 'get'
  })
}

// 项目下用户删除
export function apiAlmProjectUserDel(data) {
  return request({
    url: '/api/alm/alm/projectUser',
    method: 'DELETE',
    data
  })
}

// 项目下角色组
export function apiAlmProjectRole(data) {
  return request({
    url: '/api/alm/alm/projectRole/page',
    method: 'post',
    data
  })
}
// 项目下角色-不分页
export function apiAlmProjectRoleNoPage(data) {
  return request({
    url: '/api/alm/alm/projectRole/allRoleByProjectId/' + data.projectId,
    method: 'get'
  })
}

// 项目下角色组新增
export function apiAlmProjectRoleAdd(data) {
  return request({
    url: '/api/alm/alm/projectRole',
    method: data.id ? 'put' : 'post',
    data
  })
}

// 项目下角色组详情
export function apiAlmProjectRoleInfo(id) {
  return request({
    url: `/api/alm/alm/projectRole/${id}`,
    method: 'get'
  })
}

// 项目下角色组删除
export function apiAlmProjectRoleDel(data) {
  return request({
    url: '/api/alm/alm/projectRole',
    method: 'DELETE',
    data
  })
}

// 项目下角色--所有的功能权限
export function apiAlmProjectRoleFunction(project_id) {
  return request({
    url: `/api/alm/alm/menu/${project_id}/all`,
    method: 'get'
  })
}

// 项目下角色保存权限
export function apiAlmProjectSaveRoleFunction(project_id, role_id, data) {
  return request({
    url: `/api/alm/alm/menu/${project_id}/role/${role_id}`,
    method: 'PUT',
    data
  })
}

// 根据角色id查询角色的功能信息
export function apiAlmProjectGetRoleFunction(role_id) {
  return request({
    url: `/api/alm/alm/menu/role/${role_id}`,
    method: 'get'
  })
}

// 根据项目id、事项分类和事项类型检测工作流是否被该分类下其它事项类型引用
export function apiAlmcheckWorkflowByInfo(projectId, typeClassify, typeCode) {
  return request({
    url: `/api/alm/alm/projectWorkflow/checkWorkflowByProjectIdAndTypeClassifyAndTypeCode/${projectId}/${typeClassify}/${typeCode}`,
    method: 'get'
  })
}

// 根据项目类型查询菜单
export function getAllProjectMenusByTypeCode(type) {
  return request({
    url: `/api/alm/alm/menu/all/${type}`,
    method: 'get'
  })
}
// 保存角色配置的项目菜单
export function apiAlmSaveRoleFunction(role_id, data) {
  return request({
    url: `/api/alm/alm/menu/role/${role_id}`,
    method: 'PUT',
    data
  })
}

// 查询项目下成员分页接口
export function getProjectUser(data) {
  return request({
    url: '/api/base/base/user/queryByProjectId',
    method: 'post',
    data
  })
}
// 查询项目角色不分页
export function getProjectRole(id) {
  return request({
    url: `/api/alm/alm/projectRole/allRoleByProjectId/${id}`,
    method: 'get'
  })
}

