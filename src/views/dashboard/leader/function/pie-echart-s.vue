<template>
  <div style="width: 100%; height: 124px">
    <div class="title">{{ title }}</div>
    <vone-echarts v-if="data.length > 0" height="90px" :options="options" @chartClick="chartClick" />
    <vone-empty v-else />
    <detailDialog
      v-if="detailParams.visible"
      v-bind="detailParams"
      v-model="detailParams.visible"
      :type="detailParams.type"
      :layout="layout"
      :query-type="detailParams.queryType"
      :project-id="projectId"
      :org-id="orgId"
      :user-id="userId"
      :chart-type="'userChart'"
    />
  </div>
</template>
<script>
import detailDialog from './detail-dialog.vue'
export default {
  components: {
    detailDialog,
  },
  props: {
    title: {
      type: String,
      default: '',
    },
    layout: {
      type: Object,
      default: () => {},
    },
    data: {
      type: Array,
      default: () => [],
    },
    projectId: {
      type: String,
      default: '',
    },
    orgId: {
      type: String,
      default: '',
    },
    userId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      options: {
        color: ['#99A0AC', '#3E7BFA', '#18BF82'],
        tooltip: {
          trigger: 'item',
          axisPointer: {
            type: 'shadow',
          },
          backgroundColor: '#fff',
          borderColor: 'none',
          extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);', // 附加阴影样式
        },
        legend: {
          orient: 'vertical',
          left: 'right',
          textStyle: {
            // 图例文字的样式
            color: '#777F8E',
          },
          itemHeight: 8,
          itemWidth: 8,
          icon: 'circle',
        },
        series: [
          {
            type: 'pie',
            center: ['40%', '50%'],
            radius: ['50%', '90%'],
            avoidLabelOverlap: true,
            data: [],
            label: {
              position: 'inside',
              formatter: '{c}',
              color: '#2C2E36',
            },
          },
        ],
      },
      detailParams: {
        visible: false,
      },
      countData: {},
    }
  },
  watch: {
    data: {
      handler(val) {
        this.options.series[0].data = val
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {
    if (this.title == '工时概览') {
      this.options.color = ['#3E7BFA', '#18BF82', '#F7A14A']
    }
  },
  methods: {
    chartClick(params) {
      let type = ''
      let queryType = ''
      switch (this.title) {
        case '需求概览':
          type = 'issue'
          queryType = params.name == '待处理' ? 0 : params.name == '处理中' ? 1 : params.name == '已完成' ? 2 : ''
          break
        case '任务概览':
          type = 'task'
          queryType = params.name == '未开始' ? 0 : params.name == '进行中' ? 1 : params.name == '已完成' ? 2 : ''
          break
        case '缺陷概览':
          type = 'bug'
          queryType = params.name == '待修复' ? 0 : params.name == '修复中' ? 1 : params.name == '已修复' ? 2 : ''
          break
        case '工时概览':
          type =
            params.name == '预估'
              ? 'estimateHour'
              : params.name == '登记'
              ? 'checkHour'
              : params.name == '完成'
              ? 'completeHour'
              : ''
          break
        default:
          break
      }
      this.detailParams = { visible: true, type, queryType }
    },
  },
}
</script>
<style lang="scss" scoped>
.title {
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 22px;
  margin-bottom: 10px;
}
:deep(.empty_wrap .empty_pic) {
  width: 80px;
  height: 40px;
}
</style>
