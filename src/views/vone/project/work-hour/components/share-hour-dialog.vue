<template>
  <el-dialog
    size="md"
    title="工时分摊"
    v-model:value="visible"
    :before-close="onClose"
    :wrapper-closable="false"
    width="50%"
    :close-on-click-modal="false"
  >
    <template v-slot:title>
      <span>
        工时分摊 <span class="hour">({{ hour }}h) </span>
      </span>
    </template>
    <el-form
      ref="shareForm"
      :model="shareForm"
      label-position="left"
      label-width="100"
    >
      <el-table :data="shareForm.tableData" border>
        <el-table-column label="项目">
          <template v-slot="{ row, $index }">
            <el-form-item
              v-if="!row.verified"
              :prop="`tableData.${$index}.projectId`"
              :rules="rules.projectId"
            >
              <el-select
                v-model="row.projectId"
                clearable
                filterable
                style="width: 100%"
                @change="changeProject"
              >
                <el-option
                  v-for="i in allProjectList"
                  :key="i.id"
                  :label="i.name"
                  :value="i.id"
                  :disabled="i.disabled"
                />
              </el-select>
            </el-form-item>
            <span v-else>
              {{ row.echoMap.projectId.name }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="工时 ( h )">
          <template v-slot="{ row, $index }">
            <el-form-item
              v-if="!row.verified"
              :prop="`tableData.${$index}.duration`"
              :rules="rules.duration"
            >
              <el-input v-model="row.duration" />
            </el-form-item>
            <span v-else>
              {{ row.duration }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          field="verified"
          label="审批状态"
          width="90"
          class-name="column-style"
        >
          <template v-slot="scope">
            <el-tag v-if="!scope.row.verified" type="info">未审批</el-tag>
            <el-tag v-if="scope.row.verified && scope.row.valid" type="success"
              >已确认</el-tag
            >
            <el-tag v-if="scope.row.verified && !scope.row.valid" type="danger"
              >已驳回</el-tag
            >
          </template>
        </el-table-column>
        <el-table-column label="操作" width="60">
          <template v-slot="{ row, $index }">
            <el-tooltip
              v-if="!row.verified"
              class="item"
              content="删除"
              placement="top"
            >
              <el-button
                type="text"
                :icon="elIconApplicationDelete"
                :disabled="row.verified ? true : false"
                @click="delTime($index)"
              />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <el-row>
        <el-button :icon="ElIconPlus" type="text" @click="insertEvent"
          >添加行</el-button
        >
      </el-row>
    </el-form>

    <template v-slot:footer>
      <el-row>
        <span>
          <el-button @click="onClose">取消</el-button>
          <el-button type="primary" :loading="saveLoading" @click="saveData"
            >确定</el-button
          >
        </span>
      </el-row>
    </template>
  </el-dialog>
</template>

<script>
import { $on, $off, $once, $emit } from "../../../../../utils/gogocodeTransfer";
import {
  getAllocationSum,
  getWorkingHoursBatchSave,
} from "@/api/vone/manhour/index";
import { apiProjectInfo } from "@/api/vone/project/index";
export default {
  props: {
    id: {
      type: String,
      default: null,
    },
    visible: {
      type: Boolean,
      default: false,
    },
    hour: {
      type: Number,
      default: null,
    },
  },
  data() {
    return {
      shareForm: {
        tableData: [],
      },
      rules: {
        projectId: [
          { required: true, trigger: ["blur", "change"], message: "请选择" },
        ],
        duration: {
          required: true,
          trigger: ["blur", "change"],
          message: "请输入",
        },
      },
      allProjectList: [],
      saveLoading: false,
    };
  },
  created() {
    this.getHourInfo();
    this.getProjectInfo();
  },
  mounted() {},
  methods: {
    async getProjectInfo() {
      const { data, msg, isSuccess } = await apiProjectInfo(
        this.$route.params.id
      );
      if (!isSuccess) {
        this.$message.warning(msg);
        return;
      }
      this.allProjectList = data.projects;
    },
    changeProject() {
      const DATA = this.shareForm.tableData.filter((r) => r.valid);
      this.allProjectList.forEach((element) => {
        element.disabled = !!DATA.map((r) => r.projectId).includes(element.id);
      });
    },
    async getHourInfo() {
      const res = await getAllocationSum({
        workingHoursId: this.id,
      });
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      if (!res.data.length) {
        this.tableData = [];
      } else {
        this.shareForm.tableData = res.data;
      }
    },

    onClose() {
      $emit(this, "update:visible", false);
    },

    async saveData() {
      try {
        await this.$refs.shareForm.validate();
      } catch (e) {
        return;
      }

      // this.saveLoading = true

      try {
        this.shareForm.tableData.forEach((element) => {
          element.workingHoursId = this.id;
        });

        const res = await getWorkingHoursBatchSave(this.shareForm.tableData);

        if (!res.isSuccess) {
          this.$message.error(res.msg);
          return;
        }
        // this.saveLoading = false
        this.$message.success("保存成功");
        $emit(this, "success");
        this.onClose();
      } catch (e) {
        // this.saveLoading = false
        return;
      }
    },
    insertEvent() {
      const DATA = this.shareForm.tableData.filter((r) => r.valid || r.isAdd);

      this.allProjectList.forEach((element) => {
        element.disabled = !!DATA.map((r) => r.projectId).includes(element.id);
      });

      // const sum = DATA.reduce((accumulator, item) => accumulator + Number(item.duration), 0)

      // if (sum > this.hour || sum == this.hour) {
      //   this.$message.warning('可分摊工时已达到上限')
      //   return
      // }
      this.shareForm.tableData.push({
        projectId: "",
        duration: null,
        isAdd: true,
        verified: false,
      });
    },
    delTime(index) {
      this.shareForm.tableData.splice(index, 1);
    },
  },
  emits: ["update:visible", "success"],
};
</script>

<style lang="scss" scoped>
.hour {
  font-size: 12px;
  color: var(--font-second-color);
}
:deep(.el-dialog__body) {
  height: 500px;
  overflow-y: auto;
}
:deep(.el-table td.el-table__cell div) {
  /*// padding-top: 3px;*/
  height: 40px;
  line-height: 40px;
}
::v-eep .el-table th.el-table__cell.is-leaf {
  padding: 5px 0 !important;
  font-size: 14px !important;
}
</style>
