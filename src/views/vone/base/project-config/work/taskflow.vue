<template>
  <div>
    <vone-search-wrapper style="margin-bottom: 16px;">
      <template slot="custom">
        <span class="strong">配置</span>
      </template>
      <template slot="moreSearch">
        <el-input
          v-model="formData.name"
          style="width:300px"
          placeholder="搜索名称"
          prefix-icon="el-icon-search"
          @input="searchChange"
        />
      </template>
      <template slot="actions">
        <el-button type="primary" icon="iconfont el-icon-tips-plus-circle" size="small" :disabled="!$permission('alm_workflowInfo_add')" @click="clickAddRow">新增</el-button>
        <el-dropdown trigger="click" @command="(e) => e && e()">
          <el-button class="btnMore"><i class="iconfont el-icon-application-more " /></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="(item, index) in actions" :key="index" :icon="item.icon" :command="item.fn" :disabled="item.disabled">
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
    </vone-search-wrapper>
    <div :style="{height: tableHeight}">
      <vxe-table
        ref="taskflow-table"
        class="vone-vxe-table"
        border
        height="auto"
        show-overflow="tooltip"
        :loading="pageLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ resizable: true, minWidth: 120 }"
        :checkbox-config="{checkMethod:selectable}"
        row-id="id"
      >
        <vxe-column type="checkbox" width="37" align="center" fixed="left" />
        <vxe-column title="名称" field="name" fixed="left">
          <template #default="scope">
            <a @click="checkInfo(scope.row)">
              {{ scope.row.name }}
            </a>
          </template>
        </vxe-column>
        <vxe-column title="是否被使用" field="state" width="120">
          <template #default="scope">
            <span v-if="scope.row.state">是</span>
            <span v-else>否</span>
          </template>
        </vxe-column>
        <vxe-column title="内置" field="readonly" width="120">
          <template #default="scope">
            {{ scope.row.readonly ? '是' : '否' }}
          </template>
        </vxe-column>
        <vxe-column title="描述" field="description" width="200" />
        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <template>
              <el-tooltip class="item" content="编辑" placement="top">
                <el-button type="text" :disabled="!$permission('task_flow_edit')" icon="iconfont el-icon-application-edit icon_click" @click="editClickRow(row)" />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="配置" placement="top">
                <el-button type="text" :disabled="row.readonly || !$permission('base_work_flow_config')" icon="iconfont el-icon-application-step-setting icon_click" @click="configRow(row)" />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-dropdown trigger="click" :hide-on-click="false" @command="e => e && e()">
                <el-button type="text" icon="iconfont el-icon-application-more" />
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item icon="iconfont el-icon-application-copy" :disabled="false" :command="() =>copyRow(row)">
                    <span>复制</span>
                  </el-dropdown-item>
                  <el-dropdown-item icon="iconfont el-icon-application-delete" :disabled="row.readonly || row.isUse || !$permission('alm_workflowInfo_delete')" :command="() =>deleteRow(row)">
                    <span>删除</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <vone-pagination ref="pagination" :total="tableData.total" @update="getTableData" />

    <!-- 新增工作流 -->
    <el-dialog :title="title" width="30%" v-model="dialogFormVisible" :close-on-click-modal="false">
      <el-form ref="taskFlowForm" v-model="formLoading" :model="taskFlowForm" :rules="rules">
        <el-form-item label="名称" prop="name">
          <el-input v-model="taskFlowForm.name" placeholder="请输入名称" />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input v-model="taskFlowForm.description" type="textarea" placeholder="请输入描述" />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="sureAdd">确定</el-button>
      </div>
    </el-dialog>
    <!-- 复制工作流 -->
    <el-dialog title="复制工作流" width="30%" v-model="copyFormVisible" :close-on-click-modal="false">
      <el-form ref="copyForm" :model="copyForm">

        <el-form-item label="名称" prop="name" :rules="[{ required: true, message: '请输入名称',trigger: ['blur', 'change']}]">
          <el-input v-model="copyForm.name" placeholder="请输入名称" />
        </el-form-item>
        <!-- <el-form-item label="业务类型" prop="bizType">
          <el-select v-model="copyForm.bizType" clearable filterable disabled>
            <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>

        </el-form-item> -->
        <el-form-item label="描述" prop="description">
          <el-input v-model="copyForm.description" type="textarea" placeholder="请输入描述" />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="onCopyFormClose()">取消</el-button>
        <el-button type="primary" :loading="copyLoading" @click="copyAdd">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getFlow, apiAlmWorkflowInfoList, apiAlmWorkflowDel, apiAlmWorkflowAdd,
  apiAlmWorkflowBasicInfoPut, workflowInfoCopy
} from '@/api/vone/base/work-flow'
import _ from 'lodash'
export default {
  data() {
    return {
      formData: {
        name: ''
      },
      title: '',
      formLoading: false,
      dialogFormVisible: false,
      pageLoading: false,
      tableData: {},
      taskFlowForm: {

      },
      rules: {
        name: [
          {
            required: true,
            message: '请输入长度不超过20个字符的名称',
            trigger: 'change'
          },
          {
            max: 20,
            message: '请输入长度不超过20个字符的名称',
            trigger: 'change'
          }
        ],
        description: [
          {
            max: 250,
            message: '请输入长度不超过250个字符的描述'
          }
        ]
      },

      saveLoading: false,
      copyFormVisible: false,
      copyForm: {},
      copyLoading: false,
      actions: [
        {
          name: '批量删除',
          disabled: !this.$permission('alm_workflowInfo_delete'),
          fn: this.deleteAll
        }
      ]
    }
  },
  computed: {
    tableHeight() {
      const height = (this.extraData?.height || 0) + 'px'
      return `calc(${this.$reduceTableHeight} - ${height})`
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    // 复制工作流
    copyRow(row) {
      this.copyForm.id = row.id
      this.copyFormVisible = true
    },
    async copyAdd() {
      try {
        if (await !this.$refs.copyForm.validate()) return
        this.copyLoading = true
        const res = await workflowInfoCopy(this.copyForm)
        this.copyLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.$message.success('复制成功')
        this.onCopyFormClose()
        this.getTableData()
      } catch (e) {
        this.copyLoading = false
      }
    },
    onCopyFormClose() {
      this.$refs.copyForm.resetFields()
      this.copyFormVisible = false
    },
    searchChange: _.debounce(function(e) {
      this.getTableData()
    }, 1000),
    selectable({ row }) {
      if (row.readonly) {
        return false
      } else {
        return true
      }
    },
    getStatus(index, row) {
      return !this.$permission('task_flow_edit')
    },
    getDel(index, row) {
      return this.tableData.records[index].readonly || !this.$permission('alm_workflowInfo_delete')
    },
    getConfig(index, row) {
      return this.tableData.records[index].readonly || !this.$permission('base_work_flow_config')
    },
    onClose() {
      this.dialogFormVisible = false
      this.$refs.taskFlowForm.resetFields()
    },
    async getTableData() {
      this.pageLoading = true
      const pageObj = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
      const params = {
        ...pageObj,
        extra: {},
        model: { ...this.formData }
      }
      const res = await apiAlmWorkflowInfoList(params)
      this.pageLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.tableData = res.data
    },
    // 任务流新增
    clickAddRow() {
      this.dialogFormVisible = true
      this.title = '新增工作流'
    },
    // 任务流编辑
    editClickRow(row) {
      this.dialogFormVisible = true
      this.title = '编辑工作流'
      this.getFlowInfo(row)
    },
    // 查看任务流
    checkInfo(row) {
      this.$router.push({
        name: 'base_work_flow_config',
        params: { id: row.id },
        query: { name: row.name, type: 'details' }
      })
    },
    // 任务流配置
    configRow(row) {
      this.$router.push({
        name: 'base_work_flow_config',
        params: { id: row.id },
        query: { name: row.name }
      })
    },
    async getFlowInfo(row) {
      this.formLoading = true
      const res = await getFlow(row.id)
      this.formLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.taskFlowForm = res.data
    },
    // 任务流删除
    async deleteRow(row) {
      if (row.state) {
        this.$message.warning('使用中的工作流不允许删除')
        return
      }
      await this.$confirm(`确定删除【${row.name}】吗？`, '删除', {
        type: 'warning',
        customClass: 'delConfirm',
        showClose: false
      })
      this.pageLoading = true
      const res = await apiAlmWorkflowDel([row.id])
      this.pageLoading = false
      if (!res.isSuccess) {
        this.$message.error(res.msg)
        return
      }
      this.$message.success('删除成功')
      this.getTableData()
    },
    // 批量删除
    async deleteAll() {
      const selectData = this.getVxeTableSelectData('taskflow-table')
      if (!selectData.length) {
        this.$message.warning('请选择要删除的数据')
        return
      }
      try {
        await this.$confirm(`是否删除 ${selectData.length} 条数据?`, '删除', {
          type: 'warning',
          customClass: 'delConfirm',
          showClose: false
        })
        this.pageLoading = true
        const selectId = this.getVxeTableSelectData('taskflow-table').map(r => r.id)
        const res = await apiAlmWorkflowDel(selectId)
        this.pageLoading = false
        if (!res.isSuccess) {
          this.$message.error(res.msg)
          return
        }
        this.$message.success('删除成功')
        this.getTableData()
      } catch (e) {
        this.pageLoading = false
      }
    },
    async sureAdd() {
      try {
        await this.$refs.taskFlowForm.validate()
      } catch (e) {
        return
      }
      if (this.taskFlowForm.id) {
        this.updateInfo()
      } else {
        this.saveLoading = true
        const res = await apiAlmWorkflowAdd(
          this.taskFlowForm
        )
        this.saveLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.$message.success('新增成功')
        this.onClose()
        this.getTableData()
      }
    },
    async updateInfo() {
      this.saveLoading = true
      const res = await apiAlmWorkflowBasicInfoPut(
        this.taskFlowForm
      )
      this.saveLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('修改成功')
      this.onClose()
      this.getTableData()
    }
  }

}
</script>

<style lang="scss" scoped>
.strong{
  font-size: 16px;
  font-weight: 500;
  color: #2C2E36;
}
</style>
