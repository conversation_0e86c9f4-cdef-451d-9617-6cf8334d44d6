<template>

  <!-- 自定义饼图-->
  <vone-echarts-card :title="formInfo.name">
    <vone-echarts v-if="data.length>0" :options="options" @chartClick="chartClick" />
    <vone-empty v-else />

    <detailDialog v-if="detailParams.visible" v-bind="detailParams" :visible.sync="detailParams.visible" />
  </vone-echarts-card>

</template>

<script>
import { getChartData } from '@/api/vone/dashboard/index'
import detailDialog from '@/views/widget/detail-data-dialog.vue'

export default {
  components: { detailDialog },
  props: {
    formInfo: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      allSeries: [],
      detailParams: { visible: false },
      data: [],
      backgroundColor: '#2c343c',
      options: {
        tooltip: {
          trigger: 'item',
          axisPointer: {
            type: 'shadow'
          },
          backgroundColor: '#fff',
          borderColor: 'none',
          extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);', // 附加阴影样式
          textStyle: {
            color: '#53565C'
          }

        },
        legend: {
          orient: 'horizontal',
          x: 'center', // 可设定图例在左、右、居中
          y: 'bottom',
          textStyle: { // 图例文字的样式
            color: '#777F8E'
          },
          itemHeight: 8,
          itemWidth: 8,
          icon: 'circle'
        },
        series: [
          {
            name: this.formInfo.xName,
            type: 'pie',
            center: ['50%', '40%'],
            radius: ['55%', '80%'],
            avoidLabelOverlap: false,
            itemStyle: {
              // borderColor: '#fff',
              // borderWidth: 2
            },
            label: {

              show: true,
              position: 'center',
              formatter: '{num|' + 0 + '}' + '\n\r' + '{name|总数}',
              rich: {
                num: {
                  fontSize: 18,
                  color: '#777F8E'
                },
                name: {
                  fontFamily: '微软雅黑',
                  fontSize: 14,
                  color: '#777F8E',
                  lineHeight: 30
                }
              }

            }
          }
        ],
        graphic: {
          type: 'text',
          left: 'right',
          top: 'bottom',
          silent: true,
          style: {
            fill: '#777F8E',
            font: '14px "bold" ',
            text: this.formInfo.xName
          }
        }
      }
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    async getData() {
      await getChartData({
        x: this.formInfo.x,
        y: 'count(*)',
        tableViewId: this.formInfo.tableViewId,
        showSeries: true
      }).then(res => {
        if (res.isSuccess) {
          const data = res.data.outList
          this.allSeries = []
          this.data = data
          let count = 0
          if (data) {
            res.data.outList.map(item => {
              item.name = item.key ? item.key.split('(')[0] : '(空)'
              count = count + Number(item.value)
            })
          }
          this.options.series[0].label.formatter = '{num|' + count + '}' + '\n\r' + '{name|总数}'
          this.options.series[0].data = data

          // this.options.title = {
          //   text: this.formInfo.xName,
          //   left: 'center'
          // }
        }
      })
    },
    chartClick(params) {
      if (params.name == '(空)') {
        this.$message.warning('当前数据分组,暂不支持查看')
        return
      }

      const form = this.formInfo
      const data = {
        groupData: null,
        xdata: params.data.key
      }

      this.detailParams = { visible: true, form: form, data: data }
    }
  }

}
</script>

<style>

</style>
