<template>
  <vone-div-wrapper :title="tabName">
    <div slot="actions">
      <el-button class="con-issue" type="text" icon="el-icon-link" @click="addRelation = true">关联</el-button>
    </div>
    <div v-loading="pageLoading" class="boxContent">
      <el-form ref="defectTab" :model="defectTab" :rules="relateRules">
        <el-row v-if="addRelation" :gutter="8" class="mt-10" type="flex" justify="space-between">

          <el-col :span="4">

            <el-form-item prop="relation">
              <el-select v-model="defectTab.relation" placeholder="请选择依赖关系">
                <el-option label="关联" value="RELATION" />
                <el-option label="前置依赖" value="DEPEND" />
                <el-option label="后置影响" value="AFFECT" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col v-if="defectTab.relation == 'RELATION'" :span="6">
            <el-form-item prop="linked">
              <el-select v-model="defectTab.linked" style="width:100%">
                <el-option v-for="item,index in linkedList" :key="index" :title="item" :label="item" :value="item" style="max-width: 300px" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item prop="projectId">
              <el-select v-model="defectTab.projectId" filterable style="width:100%" @change="changeProject" @focus="setOptionWidth">
                <el-option v-for="item in projectIdList" :key="item.key" :label="item.name" :value="item.id" style="max-width: 300px" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="relationbug">
              <el-select v-model.trim="defectTab.relationbug" placeholder="请输入缺陷名称" clearable filterable remote :remote-method="getRequirementList" :loading="requireLoading" class="requireSelect" @focus="setOptionWidth">
                <el-option v-for="ele in defectList" :key="ele.id" :value="ele.id" :label="ele.name" :disabled="ele.disabled" style="max-width: 350px" :title="ele.name">
                  {{ `${ele.code}   ${ele.name}` }}
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

        </el-row>
        <div v-if="addRelation" class="minbtns">
          <el-button class="miniBtn" @click="addRelation = false">取消</el-button>
          <el-button class="miniBtn" type="primary" style="margin-left:8px" @click="addRelationShip">确定</el-button>
        </div>
      </el-form>

      <el-collapse v-model="activeNames">
        <!-- 关联/------------------- -->
        <el-collapse-item v-if="connectTable.length>0" name="RELATION">
          <template slot="title">
            <i class="iconfont el-icon-yibiaoban-shuxingyanse colorOrange" />
            <span class="centerText">
              关联
            </span>
            <span class="centerNum">
              {{ connectTable.length }}
            </span>

          </template>
          <needTable :table-data="connectTable" :project-id="projectId" :type-code="typeCode" relation-type="RELATION" :all-project-id="allProjectId" @success="getRelationTable" />

        </el-collapse-item>
        <!-- 前置依赖--------------------- -->
        <el-collapse-item v-if="dependTable.length>0" name="DEPEND">
          <template slot="title">
            <i class="iconfont el-icon-yibiaoban-shuxingyanse colorGreen" />
            <span class="centerText">
              前置依赖
            </span>
            <span class="centerNum">
              {{ dependTable.length }}
            </span>

          </template>
          <needTable :table-data="dependTable" :project-id="projectId" :type-code="typeCode" :all-project-id="allProjectId" @success="getRelationTable" />

        </el-collapse-item>
        <!-- 后置影响-------------------------- -->
        <el-collapse-item v-if="affectTable.length>0" name="AFFECT">
          <template slot="title">
            <i class="iconfont el-icon-yibiaoban-shuxingyanse colorPerple" />
            <span class="centerText">
              后置影响
            </span>
            <span class="centerNum">
              {{ affectTable.length }}
            </span>
          </template>
          <needTable :table-data="affectTable" :project-id="projectId" :type-code="typeCode" :all-project-id="allProjectId" @success="getRelationTable" />
        </el-collapse-item>
      </el-collapse>

    </div>

  </vone-div-wrapper>
</template>

<script>

import {
  apiAlmProjectRelation,
  apiAlmAddDepend,
  apiAlmPutDepend
} from '@/api/vone/project/issue'

import {
  apiAlmProjectNoPage
} from '@/api/vone/project/index'
import { debounce } from 'lodash'
import needTable from './need-table.vue'
import { apiAlmCorrelatedRequirement } from '@/api/vone/project/issue'
import { apiAlmBugNoPage } from '@/api/vone/project/defect'

export default {
  name: 'IssueToIssue',
  components: {
    needTable
  },
  props: {
    issueId: {
      type: String,
      default: undefined
    },
    projectId: {
      type: String,
      default: null
    },
    tabName: {
      type: String,
      default: null
    },
    typeCode: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      pageLoading: false,
      activeNames: ['RELATION', 'DEPEND', 'AFFECT'],
      projectIdList: [], // 项目
      defectTab: {
        relation: 'RELATION'
      },
      addRelation: false,
      tableLoading: false,
      createSimple: false,
      typeId: '',
      formData: {},

      taskList: [],
      relateRules: {
        relation: [{ required: true, message: '请选择依赖关系', trigger: 'change' }],
        projectId: [{ required: true, message: '请选择项目', trigger: 'change' }],
        relationbug: [{ required: true, message: '请选择缺陷', trigger: 'change' }]
      },
      loading: false,
      affectTable: [], // 影响
      connectTable: [], // 关联
      dependTable: [], // 依赖
      relationTable: [],
      defectList: [],
      selectOptionWidth: '',
      requireLoading: false,
      addTask: null,
      taskForm: {
        taskId: []
      },
      allProjectId: [],
      linkedList: [
        'duplicates-复制某问题', 'blocks-阻塞某问题', 'is blocked by-被某问题阻塞', 'clones-克隆某问题', 'is cloned by-被某问题克隆', 'is duplicated by-被某问题复制', 'has to be finished together with-必须与某问题一起结束(链出)', 'has to be finished together with-必须与某问题一起结束(链入)', 'has to be done before-必须在某问题之前完成', 'has to be done after-必须在某问题之后完成', 'earliest end is start of-此问题结束就可是某问题开始', 'start is earliest end of-某问题开始就可是此问题结束', 'has to be started together with-必须与某问题一起开始(链出)', 'has to be started together with必须与某问题一起开始(链入)', 'relates to-关联某问题(链出)', 'relates to-关联某问题(链入)', 'Split-拆分为(链出)', 'Split-拆分为(链入)'
      ]
    }
  },
  watch: {
    issueId() {
      this.getRelationTable()
      this.getProjectList()
    },
    immediate: true
  },

  mounted() {
    this.$set(this.taskForm, 'projectId', this.projectId || this.$route.params.id)

    this.getRelationTable()
    this.getProjectList()
  },
  methods: {
    init() {
      this.addTask = null
      this.$emit('initList')
    },

    // 下拉框弹出时，设置弹框的宽度
    setOptionWidth(event) {
      this.$nextTick(() => {
        this.selectOptionWidth = event.target.parentNode.parentNode.clientWidth + 'px'
      })
    },
    // 归属项目
    async getProjectList() {
      this.pageLoading = true
      const res = await apiAlmProjectNoPage()
      this.pageLoading = false

      if (!res.isSuccess) {
        return
      }
      this.projectIdList = res.data
      this.allProjectId = res.data.map(r => r.id)
    },
    changeProject(val) {
      this.$set(this.defectTab, 'relationbug', '')
      this.defectList = []
    },

    // 缺陷列表
    getRequirementList: debounce(async function(query, projectId) {
      try {
        this.requireLoading = true
        const res = await apiAlmBugNoPage({ name: query, projectId: this.defectTab.projectId || this.taskForm.projectId })
        this.requireLoading = false
        if (!res.isSuccess) {
          return
        }

        res.data.forEach(element => {
          element.disabled = element.id == this.issueId
        })

        this.defectList = res.data
      } catch (e) {
        this.requireLoading = false
      }
    }, 1000),

    // 查询依赖关系
    async getRelationTable() {
      if (!this.issueId) {
        return
      }
      this.pageLoading = true

      const res = await apiAlmProjectRelation('BUG', this.issueId)
      this.pageLoading = false
      if (!res.isSuccess) {
        return
      }

      this.connectTable = res.data && res.data.RELATION?.length ? res.data.RELATION : []

      this.affectTable = res.data && res.data.AFFECT?.length ? res.data.AFFECT : []

      this.dependTable = res.data && res.data.DEPEND?.length ? res.data.DEPEND : []
    },

    // 添加依赖关系
    async addRelationShip() {
      try {
        await this.$refs.defectTab.validate()
      } catch (error) {
        return
      }
      const form = {
        fromId: this.issueId,
        fromType: 'BUG',
        relationType: this.defectTab.relation,
        toId: this.defectTab.relationbug,
        toType: 'BUG'
      }
      if (this.defectTab.relation == 'RELATION') {
        form.linked = this.defectTab.linked
      }
      const { isSuccess, msg } = await apiAlmAddDepend(form)
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.$message.success(msg)
      this.getRelationTable()
      this.addRelation = false
      this.$set(this.defectTab, 'relation', 'DEPEND')
      this.$set(this.defectTab, 'relationbug', '')
    },
    // 修改依赖关系
    async EditState(row) {
      const { isSuccess, msg } = await apiAlmPutDepend(
        row.relationId, row.relationType
      )
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.$message.success(msg)
      this.getRelationTable()
    },
    // 需求拆分解除关系
    async deleteRelation(row) {
      this.$confirm(`你确定要取消与 【${row.name}】的关系吗?`, '取消关联', {
        confirmButtonText: '确认',
        type: 'warning',
        closeOnClickModal: false
      })
        .then(async() => {
          const { isSuccess, msg } = await apiAlmCorrelatedRequirement({
            correlatedType: 'DELETE',
            requirementId: this.issueId,
            requirementIds: [row.id]
          })
          if (!isSuccess) {
            this.$message.error(msg)
            return
          }
          this.$message.success(msg)
        })
        .catch(() => { })
    }

  }
}
</script>
<style lang="scss" scoped>
.con-issue {
  font-weight: 400;
  font-size: 14px;
  padding: 0 12x;
  min-width: auto;
  font-weight: 400;
}

.boxContent {
  .headerTotal {
    background: var(--disabled-bg-color);
    border-radius: 50%;
    padding: 0 5px;
    color: var(--main-font-color);
  }
  .centerText {
    font-size: 14px;
    font-weight: 400;
    margin: 0 5px;
  }
  .centerNum {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 20px;
    height: 15px;
    background: #ebeef5;
    border-radius: 14px;
    color: var(--main-font-color);
    font-size: 14px;
    font-weight: 400;
  }
  .boxTitle {
    font-weight: 600;
    color: var(--auxiliary-font-color);
  }
  :deep(.el-select .el-input__inner) {
    padding-right: 35px;
  }
}
.mt-10 {
  margin-top: 10px;
	display: flex;
	gap:12px;
	div{
		padding:0 !important;
	}
  .projectStl {
    :deep(.el-input__inner) {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    :deep(.el-input__suffix) {
      right: 2px;
    }
  }
  .btnCty {
    height: 32px;
    display: flex;
    align-items: center;
    text-align: right;
    justify-content: flex-end;
  }
}
.colorOrange {
  color: #ffd591;
}
.colorGreen {
  color: #b7eb8f;
}
.colorPerple {
  color: #adc6ff;
}
:deep(.el-collapse-item__header) {
  padding: 0;
}
:deep(.el-collapse-item__content) {
  padding: 0;
}
:deep(.el-button + .el-button) {
  margin: 0px;
}
.minbtns {
  text-align: right;
:deep(.el-button + .el-button) {
  margin: -16px 0px 12px 12px;
}
}
:deep(.el-collapse-item__header) {
  height: 40px;
  padding:0px 10px;

}
:deep(.el-collapse-item) {
border: 1rem solid var(--solid-border-color);
box-shadow: var(--main-bg-shadow);
margin-bottom: 10px;
}
</style>
