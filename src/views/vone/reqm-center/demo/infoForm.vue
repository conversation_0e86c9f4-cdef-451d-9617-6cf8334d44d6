<template>
  <div class="drawerBox">
    <el-form
      inline
      :model="infoForm"
      :disabled="infoDisabled"
      label-position="right"
      label-width="120px"
      style="margin-bottom: 100px"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="需求分类：" prop="typeCode">
            <el-select v-model="infoForm.typeCode" clearable>
              <el-option
                v-for="item in typeCodeList"
                :key="item.key"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="需求来源：" prop="sourceCode">
            <el-select v-model="infoForm.sourceCode" clearable>
              <el-option
                v-for="item in sourceList"
                :key="item.key"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="优先级：" prop="priorityCode">
            <el-select
              v-model="infoForm.priorityCode"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in prioritList"
                :key="item.key"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="处理人：" prop="handleBy">
            <el-select
              v-model="infoForm.handleBy"
              clearable
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="item in pUserList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="归属项目：" prop="projectId">
            <el-select
              v-model="infoForm.projectId"
              clearable
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="item in projectIdList"
                :key="item.key"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关联用户需求：" prop="ideaId">
            <el-select
              v-model="infoForm.ideaId"
              clearable
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="item in ideaList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="计划开始时间：" prop="planStime">
            <el-date-picker
              v-model="infoForm.planStime"
              type="datetime"
              format="yyyy-MM-dd HH:mm"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="选择计划开始时间"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="计划完成时间：" prop="planEtime">
            <el-date-picker
              v-model="infoForm.planEtime"
              format="yyyy-MM-dd HH:mm"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetime"
              placeholder="选择计划完成时间"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col v-for="item in list" :key="item.name" :span="12">
          <el-form-item :label="item.name + '：'" prop="description">
            <el-input />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item label="需求描述：" prop="description">
            <el-input
              v-model="infoForm.description"
              type="textarea"
              :rows="3"
              style="width: 600px"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row style="margin-top: 40px">
        <el-col :span="24">
          <el-form-item label="附件：">
            <el-upload
              class="upload-demo"
              action="https://jsonplaceholder.typicode.com/posts/"
              :on-preview="handlePreview"
              :on-remove="handleRemove"
              :before-remove="beforeRemove"
              multiple
              :limit="3"
              :on-exceed="handleExceed"
              :file-list="fileList"
            >
              <el-button size="small" type="primary">点击上传</el-button>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../utils/gogocodeTransfer'
import { apiAlmProjectPlanNoPage } from '@/api/vone/project/iteration'
import { apiAlmSourceNoPage } from '@/api/vone/project/issue'
import {
  apiAlmProjectNoPage,
  apiProductNoPage,
  apiProjectUserNoPage,
  apiProgramNoPage,
} from '@/api/vone/project/index'

import { apiAlmPriorityNoPage } from '@/api/vone/alm/index'
import { ideaListQuery } from '@/api/vone/reqmcenter/idea'
import { apiAlmStateNoPage } from '@/api/vone/base/work-flow'

import { requirementType } from '@/api/vone/reqmcenter/require'

export default {
  props: {
    id: {
      type: String,
      default: undefined,
    },
    infoDisabled: {
      type: Boolean,
      default: false,
    },
    partInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      list: [],
      infoForm: {},
      sourceList: [], // 需求来源
      projectIdList: [], // 归属项目
      productIdList: [], // 归属产品

      stateList: [], // 状态
      prioritList: [], // 优先级
      typeCodeList: [], // 需求分类
      planIdList: [], // 迭代计划
      pUserList: [], // 项目下人员
      programIdList: [], // 项目集
      ideaList: [], // 用户需求
      fileList: [],
    }
  },
  watch: {
    partInfo() {
      // this.infos = this.partInfo
      var item = this.partInfo
      this.list.push(item)
      // this.list = arr
    },
  },
  mounted() {
    this.getIssueType() // 需求分类
    this.getProjectList() // 归属项目
    this.productList() // 归属产品

    this.getProjectUser() // 项目下人员
    this.getStateList() // 状态
    this.getPlanList() // 迭代计划
    this.getPrioritList() // 优先级
    this.getSourceList() // 来源
    this.getProgramIdList() // 项目集
    this.getList() // 用户需求
  },
  methods: {
    async getList() {
      const { code, data } = await ideaListQuery()
      if (code == 0) {
        this.ideaList = data
      }
    },
    // 查询需求分类
    async getIssueType() {
      const res = await requirementType({
        code: 'ISSUE',
      })
      if (!res.isSuccess) {
        return
      }
      this.typeCodeList = res.data
    },
    // 查询需求来源
    async getSourceList() {
      const res = await apiAlmSourceNoPage({
        typeClassify: 'ISSUE',
      })
      if (!res.isSuccess) {
        return
      }
      this.sourceList = res.data
    },
    // 归属项目
    async getProjectList() {
      const res = await apiAlmProjectNoPage()

      if (!res.isSuccess) {
        return
      }
      this.projectIdList = res.data
    },
    // 归属项目集
    async getProgramIdList() {
      const res = await apiProgramNoPage()
      if (res.isSuccess) {
        this.programIdList = res.data
      }
    },
    // 归属产品
    async productList() {
      const res = await apiProductNoPage()

      if (!res.isSuccess) {
        return
      }
      this.productIdList = res.data
    },
    // 迭代计划
    async getPlanList() {
      const res = await apiAlmProjectPlanNoPage({
        projectId: this.$route.params.id,
      })
      if (!res.isSuccess) {
        return
      }
      this.planIdList = res.data
    },

    // 查询项目下人员
    async getProjectUser() {
      const res = await apiProjectUserNoPage({
        projectId: this.$route.params.id,
      })

      if (!res.isSuccess) {
        return
      }

      this.pUserList = res.data
    },
    // 查状态
    async getStateList() {
      const res = await apiAlmStateNoPage()
      if (!res.isSuccess) {
        return
      }
      this.stateList = res.data
    },
    // 查优先级
    async getPrioritList(val) {
      const res = await apiAlmPriorityNoPage()
      this.tableLoading = false
      if (!res.isSuccess) {
        return
      }
      this.prioritList = res.data
    },
    changeFlow() {
      $emit(this, 'changeFlow')
    },
  },
  emits: ['changeFlow'],
}
</script>

<style lang="scss" scoped>
:deep(.el-date-editor.el-input),
.el-date-editor.el-input__inner {
  width: 100%;
}
:deep(.el-form-item) {
  height: 55px;
}
:deep(.el-form-item__label) {
  text-align: right;
}
</style>
