<template>

  <vone-card-wrapper title="节点配置">
    <el-button @click="addItem">新增</el-button>
    <el-row class="slaveList">
      <el-col v-for="item in slaveList" :key="item.id" :span="6">
        <div>
          <span v-if="item.state" class="colorGreen">{{ item.name }}</span>
          <span v-else-if="!item.state" class="colorRed">{{ item.name }}</span>
          <span v-else>{{ item.name }}</span>
          <el-button type="text" class="iconfont el-icon-application-edit" @click="editItem(item)" />
          <el-button type="text" class="iconfont el-icon-application-delete" @click="deleteItem(item)" />
          <el-button type="text" class="el-icon-refresh" @click="testConnect(item)" />
        </div>
      </el-col>
    </el-row>
    <addItemDrawer :id="editId" :key="addItemDrawerKey" v-model="visible" :engine-id="id" :engine-data="engineData" v-bind="$attrs" :title="title" @success="loadList" />
  </vone-card-wrapper>

</template>

<script>

import { apiBaseEngineNoPage, apiBaseEngineDelById, apiBaseEngineTestConnect } from '@/api/vone/base/engine'
import addItemDrawer from './addItemDrawer'
export default {
  components: {
    addItemDrawer
  },
  props: {
    id: {
      type: String,
      default: undefined
    },
    engineData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      visible: false,
      editId: undefined,
      slaveList: [],
      addItemDrawerKey: Date.now(),
      title: undefined
    }
  },
  watch: {
    visible(v) {
      if (!v) {
        // 修改组件key，重置组件
        this.addItemDrawerKey = Date.now()
      }
    }
  },
  created() {
    if (this.id) this.loadList()
  },
  methods: {
    async loadList() {
      const res = await apiBaseEngineNoPage({
        parentId: this.id,
        queryJenkinsSlave: true
      })
      if (!res.isSuccess) {
        return
      }
      this.slaveList = res.data
    },
    editItem(item) {
      this.editId = item.id
      this.visible = true
      this.title = '编辑节点'
    },
    addItem() {
      if (!this.id) {
        this.$message.warning('请先保存JenkinsMaster引擎，再配置节点')
        return
      }
      this.editId = undefined
      this.visible = true
      this.title = '新增节点'
    },
    async deleteItem({ id }) {
      await this.$confirm('确定删除节点吗？', '删除', {
        type: 'warning',
        closeOnClickModal: false
      })
      const res = await apiBaseEngineDelById([id])
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success(res.msg)
      this.loadList()
    },
    async testConnect(item) {
      const res = await apiBaseEngineTestConnect({ id: item.id, name: item.name })
      if (res.isSuccess && res.data) {
        this.$message.success('引擎连接成功')
      } else {
        this.$message.warning('引擎连接失败')
      }
      this.loadList()
    }
  }
}
</script>
<style lang="scss" scoped>
.slaveList {
  margin-top: 20px;
  span {
    margin-right: 12px;
  }
}
.colorGreen{
  color: #67C23A;
}
.colorRed{
   color: #F56C6C;
}
</style>
