<template>
  <div>
    <el-select
      v-bind="$attrs"
      v-if="type == 'user'"
      ref="userSelect"
      class="vone-avatar"
      filterable
      remote
      :clearable="clearable"
      :collapse-tags="collapseTags"
      :placeholder="placeholder"
      :remote-method="remoteMethod"
      loading-text="正在查询..."
      no-match-text="暂未查询到匹配数据,请重新输入"
      :loading="loading"
      popper-class="remote-user"
      :multiple="multiple"
      @change="(val) => watchInput(val)"
      @remove-tag="removeTag"
    >
      <!-- noName 回显时不显示头像，用于工作项编辑时 -->
      <template v-slot:prefix>
        <div v-if="noName && !multiple" class="select-header">
          <vone-user-avatar
            v-if="$attrs.value && selectIconVal"
            :avatar-path="selectIconVal.avatarPath"
            :avatar-type="selectIconVal.avatarType"
            :show-name="false"
            height="24px"
            width="24px"
            :no-data="noData"
          />
          <el-icon class="iconfont" style="font-size: 24px"
            ><el-icon-icon-light-avatar
          /></el-icon>
        </div>
      </template>
      <template v-show="type === 'user'">
        <el-option
          v-for="item in selectData"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        >
          <vone-user-avatar
            :avatar-path="item.avatarPath"
            :avatar-type="item.avatarType"
            :show-name="false"
            height="24px"
            width="24px"
            :no-data="noData"
          />
          {{ item.name }}
        </el-option>
      </template>
    </el-select>
    <el-popover
      v-else
      v-model="popoverView"
      width="500"
      placement="bottom-start"
      trigger="manual"
      popper-class="popper-style"
    >
      <userTree
        ref="userTree"
        :selected="checkList"
        :multiple="multiple"
        :default-data="selectData"
        @checked="checked"
        @close="close"
      />
      <template v-slot:reference>
        <el-select
          v-bind="$attrs"
          ref="userSelect"
          :filterable="type == 'organization'"
          remote
          clearable=""
          :placeholder="placeholder"
          :remote-method="remoteMethod"
          loading-text="正在查询..."
          no-match-text="暂未查询到匹配数据,请重新输入"
          :loading="loading"
          popper-class="remote-user"
          :multiple="multiple"
          @change="(val) => watchInput(val)"
          @focus="focus"
        >
          <!-- noName 回显时不显示头像，用于工作项编辑时 -->
          <template v-slot:prefix>
            <div v-if="noName && !multiple" class="select-header">
              <vone-user-avatar
                v-if="$attrs.value && selectIconVal"
                :avatar-path="selectIconVal.avatarPath"
                :avatar-type="selectIconVal.avatarType"
                :show-name="false"
                height="24px"
                width="24px"
                :no-data="noData"
              />
              <el-icon class="iconfont" style="font-size: 24px"
                ><el-icon-icon-light-avatar
              /></el-icon>
            </div>
          </template>
          <template v-show="type === 'user'">
            <el-option
              v-for="item in selectData"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
              <vone-user-avatar
                :avatar-path="item.avatarPath"
                :avatar-type="item.avatarType"
                :show-name="false"
                height="24px"
                width="24px"
                :no-data="noData"
              />
              {{ item.name }}
            </el-option>
          </template>
        </el-select>
      </template>
    </el-popover>
  </div>
</template>

<script>
import { IconLightAvatar as ElIconIconLightAvatar } from "@element-plus/icons-vue";
import { $on, $off, $once, $emit } from "../../utils/gogocodeTransfer";
import { debounce } from "lodash";
import { getUser } from "@/utils/auth";
import { getUserList } from "@/api/common";
import { getUserDetail } from "@/api/vone/base/user";
import userTree from "./userTree.vue";
import { apiProjectUserNoPage } from "@/api/vone/project/index";

export default {
  components: {
    userTree,
    ElIconIconLightAvatar,
  },
  name: "RemoteUser",
  props: {
    placeholder: {
      type: String,
      default: "输入用户名称查询",
    },
    defaultData: {
      type: Array,
      default: () => [],
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    noName: {
      type: Boolean,
      default: true,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: "user",
    },
    selecteds: {
      type: Array,
      default: () => [],
    },
    noData: {
      // 为了处理工作项里筛选条件里默认要加上一个没有处理人的数据
      type: Boolean,
      default: true,
    },
    collapseTags: {
      type: Boolean,
      default: false,
    },
    isFilter: {
      // 为了处理列表筛选器使用user组件时候,需改变数据时触发查询事件
      type: Boolean,
      default: false,
    },
    requestConfig: {
      type: Object,
      default: () => {},
    },
    projectId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      selectData: [],
      loading: false,
      selectIconVal: null,
      defaultUser: getUser(),
      defaultoption: "",
      user: "",
      popoverView: false,
      checkList: [],
    };
  },
  watch: {
    "$attrs.value": {
      handler: function (val, old) {
        if (
          this.isFilter &&
          val != undefined &&
          val.toString() != old.toString()
        ) {
          $emit(this, "filterChange", val);
        }
        if (val != old) {
          // const defData = [...this.defaultData, this.defaultUser]
          // this.selectData = this.removeSomeData(defData)
          this.user = val;
          this.unwatch(val);
        }
        this.defaultoption = val;
      },
      deep: true,
    },
    defaultData: {
      handler: function (val, old) {
        if (val.length > 0) {
          const defData = [...this.defaultData, this.defaultUser];
          this.selectData = this.removeSomeData(defData);
        }
      },
      deep: true,
    },
  },
  mounted() {
    const defData = [...this.defaultData, this.defaultUser];
    this.selectData = this.removeSomeData(defData);
    // 初始化设置选中数据
    this.watchInput(this.$attrs.value);
    this.unwatch(this.$attrs.value);
  },
  methods: {
    removeTag(val) {
      $emit(this, "removeTag", val);
    },
    focus() {
      if (this.type == "organization") {
        this.$refs.userSelect.blur();
        this.popoverView = true;
        this.$nextTick(() => {
          this.checkList = this.selecteds;
        });
      }
    },
    checked(e) {
      if (this.multiple) {
        this.selectData = this.removeSomeData([...e, ...this.selectData]);
        $emit(
          this,
          "update:value",
          e.map((e) => e.id)
        );
        this.watchInput(e.map((e) => e.id));
      } else {
        this.$nextTick(() => {
          this.selectData = this.removeSomeData([...[e], ...this.selectData]);
          $emit(this, "update:value", e.id);
          this.watchInput(e.id);
        });
      }
      this.close();
    },
    close() {
      const userNode = this.$refs.userTree;
      userNode.activeName = "user";
      userNode.userlist = [];
      this.popoverView = false;
    },
    unwatch(val) {
      if (val) {
        const hasData = !this.multiple
          ? this.selectData.map((e) => e.id).includes(val)
          : val.length > 1
          ? false
          : this.selectData[0].id == val[0];
        if (!hasData) {
          if (Array.isArray(val) && val.length > 0) {
            val.forEach((r) => {
              this.getOptionlist(r);
            });
          }
          if (typeof val == "string" && val) {
            this.getOptionlist(val);
          }
        } else {
          this.watchInput(val);
        }
      }
    },
    getOptionlist(e) {
      getUserDetail(e).then((res) => {
        if (res.isSuccess) {
          const remoteData = [...this.selectData, ...[res.data]];
          this.selectData = this.removeSomeData(remoteData);
          this.watchInput(e);
        }
      });
    },
    remoteMethod: debounce(function (query) {
      if (query != "") {
        this.loading = true;
        if (this.projectId) {
          apiProjectUserNoPage({
            projectId: this.projectId,
            keyword: query,
          }).then((res) => {
            if (res.isSuccess) {
              const remoteData = [this.defaultUser, ...res.data];
              const defData = [
                this.defaultUser,
                ...this.defaultData,
                ...this.removeSomeData(remoteData),
              ];
              this.selectData = this.removeSomeData(defData);
              // Vue3: $listeners 已被移除，事件监听器现在包含在 $attrs 中
              this.$attrs?.onGetUser && $emit(this, "getUser", res.data);
            } else {
              this.$message.warning(res.msg);
            }
            this.loading = false;
          });
          return;
        }
        getUserList({ accountOrName: query }).then((res) => {
          if (res.isSuccess) {
            const remoteData = [this.defaultUser, ...res.data];
            const defData = [
              this.defaultUser,
              ...this.defaultData,
              ...this.removeSomeData(remoteData),
            ];
            this.selectData = this.removeSomeData(defData);
            // Vue3: $listeners 已被移除，事件监听器现在包含在 $attrs 中
            this.$attrs?.onGetUser && $emit(this, "getUser", res.data);
          } else {
            this.$message.warning(res.msg);
          }
          this.loading = false;
        });
      }
    }, 200),
    watchInput(val) {
      // Vue3: $listeners 已被移除，事件监听器现在包含在 $attrs 中
      this.$attrs?.onInputChange && $emit(this, "inputChange", val);
      // 返回选中的人员数据列表
      if (this.multiple) {
        // Vue3: $listeners 已被移除，事件监听器现在包含在 $attrs 中
        this.$attrs?.onUpdateSelectedRow &&
          $emit(
            this,
            "updateSelectedRow",
            this.selectData.filter((v) => val?.includes(v.id)) || []
          );
      } else {
        // Vue3: $listeners 已被移除，事件监听器现在包含在 $attrs 中
        this.$attrs?.onUpdateSelectedRow &&
          $emit(
            this,
            "updateSelectedRow",
            this.selectData.filter((v) => v.id === val) || []
          );
      }
      if (val && this.selectData.length > 0) {
        this.selectIconVal = this.selectData.find((item) => item.id == val);
      } else {
        this.selectIconVal = null;
      }
    },
    // 获取选中数据的全量信息
    getSelectData() {
      if (this.multiple) {
        return this.selectData.filter((v) => this.user.includes(v.id));
      } else {
        return this.selectData.filter((v) => v.id == this.user);
      }
    },
    // 用户去重
    removeSomeData(data) {
      // 去掉空值
      var dataNoNull = data.filter(function (el) {
        return el != null;
      });
      let resultData = [];
      var obj = {};
      resultData = dataNoNull.reduce((item, next) => {
        obj[next.id] ? "" : (obj[next.id] = true && item.push(next));
        return item;
      }, []);
      return resultData;
    },
  },
  emits: [
    "filterChange",
    "removeTag",
    "update:value",
    "getUser",
    "inputChange",
    "updateSelectedRow",
  ],
};
</script>

<style lang="scss" scoped>
.remote-user {
  display: none;
  padding-right: 10px;
}
:deep(.select-header) {
  min-width: 28px;
  height: 100%;
  display: flex;
  align-items: center;
  text-align: center;
  font-size: 14px;
  color: #c1c8d6;
}
</style>

<style lang="scss">
.remote-user {
  .el-select-dropdown__wrap {
    .el-select-dropdown__item {
      display: flex;
      align-items: center;
      .avatar {
        padding-right: 6px;
      }
    }
  }
}
.popper-style {
  padding: 0 !important;
}

.custom-select .el-input__inner {
  width: auto;
}

.custom-select .el-input__inner::placeholder {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.custom-select .el-select-dropdown {
  max-width: 100%;
}
</style>
