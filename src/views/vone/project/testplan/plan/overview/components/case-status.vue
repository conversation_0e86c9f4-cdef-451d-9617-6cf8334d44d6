<template>
  <vone-echarts-card :title="title">
    <!-- 缺陷趋势 -->
    <vone-echarts v-if="trendsData.length > 0" :options="options" />
    <vone-empty v-else style="height: 241px" />
  </vone-echarts-card>
</template>

<script>
import { findBugTrendByPlanId } from '@/api/vone/testplan/overview'

export default {
  props: {
    title: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      trendsData: [],
      options: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          formatter: '{b0}<br/>' + '<br/>缺陷数: {c0}个',
        },
        xAxis: {
          type: 'category',
          data: [],
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        yAxis: [
          {
            type: 'value',
            minInterval: 1,
            axisTick: {
              // y轴刻度线
              show: false,
            },
            axisLine: {
              show: false, // 不显示坐标轴轴线
            },
            splitLine: {
              // 网格线
              lineStyle: {
                type: 'dashed', // 设置网格线类型 dotted：虚线   solid:实线
                width: 1,
                color: '#EBEEF5',
              },
            },
            axisLabel: {
              show: true,
              color: '#8A8F99',
            },
          },
        ],
        series: [
          {
            data: [],
            type: 'line',
            color: ['#64BEFA'],
          },
        ],
      },
    }
  },
  watch: {},
  mounted() {
    this.getData()
  },
  methods: {
    // 测试报告新增
    async getData() {
      const res = await findBugTrendByPlanId(this.$route.params.planId)
      if (!res.isSuccess) {
        this.$message.success(res.msg)
      }
      this.trendsData = res.data.add.filter((item) => item != '0')
      this.options.xAxis.data = res.data.date
      this.options.series[0].data = res.data.add
    },
  },
}
</script>
