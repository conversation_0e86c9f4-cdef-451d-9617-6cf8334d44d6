<template>
  <page-wrapper>
    <vone-search-wrapper>
      <template v-slot:search>
        <vone-search-dynamic
          v-if="defaultFileds.length"
          ref="searchForm"
          table-search-key="task-table"
          :model="formData"
          :table-ref="$refs['task-table']"
          show-basic
          :show-column-sort="true"
          :extra="extraData"
          :hide-columns="tableOptions.hideColumns"
          :default-fileds="defaultFileds"
          :show-hierarch="true"
          @getTableData="getInitTableData"
          @showPopovers="showPopovers"
        />
      </template>
      <template v-slot:actions>
        <el-row type="flex" justify="space-between">
          <simpleAddIssue
            v-if="createSimple"
            :type-code="'TASK'"
            :biz-type="'TASK_FILE_UPLOAD'"
            @success="getInitTableData"
            @cancel="createSimple = false"
          />
          <div>
            <el-button-group class="ml-16">
              <el-tooltip content="快速新增" placement="top">
                <el-button
                  :disabled="!$permission('project_task_add')"
                  class="subBtton"
                  :icon="`iconfont  ${
                    createSimple
                      ? 'el-icon-direction-double-left'
                      : 'el-icon-direction-double-down'
                  }`"
                  type="primary"
                  @click.stop="createSimple = !createSimple"
                />
              </el-tooltip>
              <el-button
                :icon="elIconTipsPlusCircle"
                type="primary"
                :disabled="!$permission('project_task_add')"
                @click.stop="newIask"
                >新增</el-button
              >
            </el-button-group>
            <el-dropdown
              trigger="click"
              style="margin-top: 2px"
              @command="(e) => e && e()"
            >
              <el-button class="btnMore"
                ><el-icon class="iconfont"><el-icon-application-more /></el-icon
              ></el-button>
              <template v-slot:dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-for="(item, index) in actions"
                    :key="index"
                    :icon="item.icon"
                    :command="item.fn"
                    :disabled="item.disabled"
                  >
                    {{ item.name }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-row>
      </template>
      <template v-slot:fliter>
        <vone-search-filter
          v-if="defaultFileds.length"
          :extra="extraData"
          :model="formData"
          :default-fileds="defaultFileds"
          :type-code="'ISSUE'"
          @getTableData="getInitTableData"
        />
      </template>
    </vone-search-wrapper>
    <main :style="{ height: tableHeight }">
      <vxe-table
        ref="task-table"
        class="vone-vxe-table"
        border
        height="auto"
        resizable
        show-overflow="tooltip"
        :loading="tableLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth: '120px' }"
        :tree-config="{
          transform: false,
          rowField: 'id',
          parentField: 'parentId',
          reserve: true,
          hasChild: 'hasChildren',
          lazy: true,
          loadMethod: load,
        }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
        @checkbox-all="selectAllEvent"
        @checkbox-change="selectChangeEvent"
        @resizable-change="
          ({ column }) => resizableChangeEvent(column, 'task-table')
        "
      >
        <vxe-column type="checkbox" width="36" fixed="left" align="center" />
        <vxe-column
          title="标题"
          field="name"
          class-name="custom-title-style"
          show-overflow="ellipsis"
          min-width="500"
          fixed="left"
          tree-node
        >
          <template #default="{ row }">
            <!-- <div class="name_icon">
                  <a class="table_title" @click="showInfo(row)">
                    <span v-if="row.typeCode && row.echoMap && row.echoMap.typeCode">
                      <i :class="`iconfont ${row.echoMap.typeCode.icon}`" :style="{ color:`${row.echoMap.typeCode ? row.echoMap.typeCode.color : '#ccc'}`}" />
                    </span>
                    {{ row.code + " " + row.name }}</a>
                   是否延期
                  <span v-if="row.delay" style="position:absolute;left:-13px">
                    <el-tooltip :open-delay="500" content="当前工作项已延期" placement="top-start">
                      <i class="el-icon-warning-outline color-danger ml-2" />
                    </el-tooltip>
                  </span>
                </div> -->
            <span v-if="row.delay" style="position: absolute; left: 0">
              <el-tooltip
                :show-after="500"
                content="当前工作项已延期"
                placement="top"
                :visible-arrow="false"
              >
                <el-icon class="color-danger ml-2"
                  ><el-icon-warning-outline
                /></el-icon>
              </el-tooltip>
            </span>
            <el-tooltip
              v-showWorkItemTooltips
              :content="row.code + ' ' + row.name"
              placement="top-start"
              :visible-arrow="false"
            >
              <span class="custom-title-main" @click="showInfo(row)">
                <i
                  v-if="row.typeCode && row.echoMap && row.echoMap.typeCode"
                  :class="`iconfont ${row.echoMap.typeCode.icon} custom-title-style-icon `"
                  :style="{
                    color: `${
                      row.echoMap.typeCode ? row.echoMap.typeCode.color : '#ccc'
                    }`,
                  }"
                />
                <span class="custom-title-style-text">{{
                  row.code + " " + row.name
                }}</span>
              </span>
            </el-tooltip>
            <span
              class="custom-title-style-copy"
              :style="{
                position: 'absolute',
                top: ' -4px',
                right: '-40px',
                display: copyRow && copyRow.id == row.id ? 'block' : '',
              }"
            >
              <el-dropdown
                trigger="click"
                :hide-on-click="true"
                @visible-change="(e) => visibleChange(e, row)"
                @command="customCopy"
              >
                <el-button type="text" :icon="elIconApplicationMore" />
                <template v-slot:dropdown>
                  <el-dropdown-menu class="custom-title-copy-dropdown">
                    <el-dropdown-item
                      :icon="elIconEditCharacterB"
                      command="title"
                    >
                      <span>复制编号</span>
                    </el-dropdown-item>
                    <el-dropdown-item
                      :icon="elIconApplicationCopyContent"
                      command="code"
                    >
                      <span>复制标题</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </span>
          </template>
        </vxe-column>
        <vxe-column title="状态" field="stateCode" width="120">
          <template #default="{ row }">
            <taskStatus
              v-if="row"
              :workitem="row"
              :no-permission="!$permission('project_task_flow')"
              @changeFlow="getInitTableData"
            />
          </template>
        </vxe-column>
        <vxe-column title="处理人" field="handleBy" width="120">
          <template #default="{ row }">
            <span>
              <vone-remote-user
                v-model:value="row.handleBy"
                :project-id="projectId"
                class="remoteuser"
                :default-data="[row.echoMap.handleBy]"
                :disabled="!$permission('project_risk_edit')"
                @change="workitemChange(row, $event, 'handleBy')"
              />
            </span>
          </template>
        </vxe-column>
        <vxe-column title="提出人" field="createdBy" width="120">
          <template #default="{ row }">
            <span>
              <vone-remote-user
                v-model:value="row.createdBy"
                :project-id="projectId"
                class="remoteuser"
                :default-data="[row.echoMap.createdBy]"
                disabled
                @change="workitemChange(row, $event, 'createdBy')"
              />
            </span>
          </template>
        </vxe-column>
        <vxe-column title="创建时间" field="createTime" width="120">
          <template #default="{ row }">
            <span v-if="row.createTime">
              {{ dayjs(row.createTime).format("YYYY-MM-DD") }}
            </span>
            <span v-else>{{ row.createTime }}</span>
          </template>
        </vxe-column>
        <vxe-column title="负责人" field="leadingBy" width="120">
          <template #default="{ row }">
            <span>
              <vone-remote-user
                v-model:value="row.leadingBy"
                :project-id="projectId"
                class="remoteuser"
                :default-data="[row.echoMap.leadingBy]"
                :disabled="!$permission('project_risk_edit')"
                @change="workitemChange(row, $event, 'leadingBy')"
              />
            </span>
          </template>
        </vxe-column>

        <vxe-column title="计划开始时间" field="planStime" width="135">
          <template #default="{ row }">
            <span v-if="row.planStime">
              {{ dayjs(row.planStime).format("YYYY-MM-DD HH:mm") }}
            </span>
            <span v-else>{{ row.planStime }}</span>
          </template>
        </vxe-column>
        <vxe-column title="计划完成时间" field="planEtime" width="135">
          <template #default="{ row }">
            <span v-if="row.planEtime">
              {{ dayjs(row.planEtime).format("YYYY-MM-DD HH:mm") }}
            </span>
            <span v-else>{{ row.planEtime }}</span>
          </template>
        </vxe-column>
        <vxe-column title="关联需求" field="requirementId" width="100">
          <template #default="{ row }">
            <span
              v-if="
                row.requirementId && row.echoMap && row.echoMap.requirementId
              "
            >
              {{
                `${row.echoMap.requirementId.code} ${row.echoMap.requirementId.name}`
              }}
            </span>
            <span v-else>{{ row.requirementId }}</span>
          </template>
        </vxe-column>
        <vxe-column title="关联缺陷" field="bugId" width="100">
          <template #default="{ row }">
            <span v-if="row.bugId && row.echoMap && row.echoMap.bugId">
              {{ `${row.echoMap.bugId.code} ${row.echoMap.bugId.name}` }}
            </span>
            <span v-else>{{ row.bugId }}</span>
          </template>
        </vxe-column>
        <vxe-column title="进度" field="rateProgress" width="80">
          <template #default="{ row }">
            <el-tooltip
              placement="top"
              :content="` ${row.rateProgress ? row.rateProgress : 0}%`"
            >
              <el-progress
                :percentage="row.rateProgress ? parseInt(row.rateProgress) : 0"
                color="var(--main-theme-color,#3e7bfa)"
                :show-text="false"
              />
            </el-tooltip>
          </template>
        </vxe-column>
        <vxe-column title="优先级" field="priorityCode" width="100">
          <template #default="{ row }">
            <vone-icon-select
              v-model:value="row.priorityCode"
              :data="prioritList"
              filterable
              clearable
              style="width: 100%"
              class="userList"
              :no-permission="!$permission('project_task_priority_update')"
              @change="workitemChange(row, $event, 'priorityCode')"
            >
              <el-option
                v-for="item in prioritList"
                :key="item.key"
                :label="item.name"
                :value="item.code"
              >
                <i
                  :class="`iconfont ${item.icon}`"
                  :style="{
                    color: item.color,
                    fontSize: '16px',
                    paddingRight: '6px',
                  }"
                />
                {{ item.name }}
              </el-option>
            </vone-icon-select>
          </template>
        </vxe-column>
        <vxe-column
          field="planId"
          :title="
            $route.params.projectTypeCode == 'AGILE' ? '归属计划' : '归属里程碑'
          "
        >
          <template #header>
            <span>{{
              $route.params.projectTypeCode == "AGILE"
                ? "归属计划"
                : "归属里程碑"
            }}</span>
          </template>
          <template #default="{ row }">
            <span v-if="row.planId && row.echoMap && row.echoMap.planId">
              {{ row.echoMap.planId.name }}
            </span>
            <span v-else>{{ row.planId }}</span>
          </template>
        </vxe-column>
        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <el-tooltip class="item" content="编辑" placement="top">
              <el-button
                type="text"
                :disabled="
                  row.stateCode == 'DONE' || !$permission('project_task_edit')
                "
                :icon="elIconApplicationEdit"
                @click="editTask(row)"
              />
            </el-tooltip>
            <el-divider direction="vertical" />
            <el-tooltip class="item" content="删除" placement="top">
              <el-button
                type="text"
                :disabled="!$permission('project_task_del')"
                :icon="elIconApplicationDelete"
                @click="deleteTask(row)"
              />
            </el-tooltip>
            <el-divider direction="vertical" />
            <el-dropdown :hide-on-click="false" @command="(e) => e && e(row)">
              <el-button
                type="text"
                :icon="elIconApplicationMore"
                class="operation-dropdown"
              />
              <template v-slot:dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    :icon="elIconEditCharacterB"
                    :command="() => titleCopy(row, 'code')"
                  >
                    <span>复制编号</span>
                  </el-dropdown-item>
                  <el-dropdown-item
                    :icon="elIconApplicationCopyContent"
                    :command="() => titleCopy(row, 'title')"
                  >
                    <span>复制标题</span>
                  </el-dropdown-item>
                  <el-dropdown-item
                    :disabled="!$permission('project_task_add')"
                    :icon="elIconIconFuzhi"
                    :command="() => workItemCopy(row)"
                  >
                    <span>复制工作项</span>
                  </el-dropdown-item>
                  <el-dropdown-item
                    :disabled="
                      row.stateCode == 'DONE' ||
                      !$permission('project_task_edit')
                    "
                    :icon="elIconApplicationType"
                    :command="() => typeCodeChangeFn(row)"
                  >
                    <span>变更类型</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </vxe-column>
      </vxe-table>
    </main>
    <vone-pagination
      ref="pagination"
      :total="tableData.total"
      @update="getInitTableData"
    />

    <!-- 新增 -->
    <vone-custom-add
      v-bind="taskParamAdd"
      v-if="taskParamAdd.visible"
      ref="vone-custom-add"
      v-model:value="taskParamAdd.visible"
      :title="taskParamAdd.title"
      :type-code="'TASK'"
      :is-tooltip="true"
      @success="saveChildSuccess"
    />

    <!-- 编辑完整任务 -->
    <vone-custom-edit
      v-bind="taskParam"
      v-if="taskParam.visible"
      ref="vone-custom-edit"
      v-model:value="taskParam.visible"
      :type-code="'TASK'"
      :left-tabs="leftTabs"
      :right-tabs="rightTabs"
      :is-tooltip="true"
      @success="getInitTableData"
      @initList="getInitTableData"
      @add-child="addChild"
    />

    <!-- 详情 -->
    <vone-custom-info
      v-bind="taskInfoParam"
      v-if="taskInfoParam.visible"
      ref="vone-custom-info"
      v-model:value="taskInfoParam.visible"
      :type-code="'TASK'"
      :left-tabs="leftTabs"
      :right-tabs="rightTabs"
      @success="getInitTableData"
      @initList="getInitTableData"
      @add-child="addChild"
    />
    <!-- 导入 -->
    <vone-import-file
      v-bind="importParam"
      v-if="importParam.visible"
      v-model:value="importParam.visible"
      :type="'TASK'"
      @success="getInitTableData"
    />
    <!-- 批量编辑 -->
    <editAll
      v-bind="editAllParam"
      v-if="editAllParam.visible"
      v-model:value="editAllParam.visible"
      :type-code="'TASK'"
      @success="getInitTableData"
    />
    <!-- 变更工作项类型 -->
    <type-code-change
      v-bind="typeCodeChangeParam"
      v-if="typeCodeChangeParam.visible"
      v-model:value="typeCodeChangeParam.visible"
      @success="getInitTableData"
    />
  </page-wrapper>
</template>

<script>
import {
  apiAlmTaskDel,
  apiAlmTaskInfo,
  apiGetTaskList,
} from "@/api/vone/project/task";

import {
  apiAlmGetTypeNoPage,
  apiAlmPriorityNoPage,
} from "@/api/vone/alm/index";
import { apiBaseFileLoad } from "@/api/vone/base/file";
import { catchErr, download } from "@/utils";

import simpleAddIssue from "@/views/vone/project/issue/function/simple-add-issue.vue";

import taskStatus from "@/views/vone/project/common/change-status/index.vue";
import editAll from "../common/edit-all";

import typeCodeChange from "@/components/CustomEdit/components/type-code-change";
import {
  editById,
  getWorkItemState,
  productListByCondition,
} from "@/api/vone/project/index";

import { queryFieldList } from "@/api/common";
import {
  apiAlmSourceNoPage,
  requirementListByCondition,
} from "@/api/vone/project/issue";
import { apiAlmProjectPlanNoPage } from "@/api/vone/project/iteration";
import setDataMixin from "@/mixin/set-data";

export default {
  components: {
    taskStatus,
    simpleAddIssue,
    typeCodeChange,
    editAll,
  },
  mixins: [setDataMixin],
  data() {
    return {
      extraData: {},
      defaultFileds: [],
      tableList: [], // 用于编辑时切换上一个下一个
      formData: {
        projectId: [this.$route.params.id],
      },
      createSimple: false,
      taskParamAdd: { visible: false }, // 新增
      taskParam: { visible: false }, // 编辑
      taskInfoParam: { visible: false },
      tableData: {
        records: [],
      },
      tableLoading: false,
      tableOptions: {
        isOperation: true,
        isSelection: true,
        isHierarch: true,
        hideColumns: [
          "files",
          "code",
          "description",
          "delay",
          "estimateHour",
          "planStime",
          "projectId",
          "ideaId",
          "sourceCode",
          "typeCode",
          "leadingBy",
          "testPlanId",
          "rateProgress",
          "requirementId",
          "bugId",
        ], // 默认隐藏列
      },
      actions: [
        {
          name: "批量删除",
          fn: this.deleteAll,
          disabled: !this.$permission("project_task_del"),
        },
        {
          name: "批量编辑",
          fn: this.editAll,
          disabled: !this.$permission("project_task_edit"),
        },
        {
          name: "导入",
          icon: "iconfont el-icon-edit-import",
          fn: this.imPort,
          disabled: !this.$permission("project_task_import"),
        },
        {
          name: "导出",
          icon: "iconfont el-icon-edit-export",
          fn: this.exportFlie,
          disabled: !this.$permission("project_task_export"),
        },
      ],
      selecteTableData: [],
      typeCodeList: [],
      prioritList: [],
      stateCodeList: [],
      importParam: { visible: false }, // 用户导入
      editAllParam: { visible: false }, // 批量编辑
      rightTabs: [
        {
          label: "评论",
          name: "comment",
          active: true,
        },
        {
          label: "活动",
          name: "active",
        },
        {
          label: "变更记录",
          name: "activityRecord",
        },
        {
          label: "工时",
          name: "workTime",
        },
      ],
      leftTabs: [
        {
          label: "任务",
          name: "TaskTask",
        },
      ],
      headerList: [], // 表格列头
      columnsList: [], // 显示列
      filterList: [], // 筛选条件
      total: undefined,
      exportParam: { visible: false }, // 导出
      maps: new Map(),
      ids: [],
      showTag: false,
      typeCodeChangeParam: {
        visible: false,
      },
      copyRow: null,
      projectId: this.$route.params.id,
    };
  },
  // 路由离开生命周期函数
  beforeRouteLeave(to, from, next) {
    // 即将跳转的路由地址
    if (to.path != "project_task_view") {
      this.$store.state.project.itemName = undefined;
      next();
    }
  },
  computed: {
    tableHeight() {
      const height = (this.extraData?.height || 0) + "px";
      return `calc(${this.$reduceTableHeight} - ${height})`;
    },
  },
  watch: {
    $route: {
      handler(val) {
        if (val.query && val.query.queryId) {
          const { queryId, rowTypeCode, stateCode, type } = val.query;
          if (type == "edit") {
            this.editTask({
              id: queryId,
              rowTypeCode,
              stateCode,
            });
          } else {
            this.openInfo({
              id: queryId,
              rowTypeCode,
              stateCode,
            });
          }
        }

        if (val.params.type == "comment") {
          this.showInfo({ id: val.params.businessId });
        }
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {
    this.getPrioritList();
  },
  created() {
    this.getQueryFieldList();
  },
  methods: {
    saveChildSuccess() {
      this.getInitTableData();
      this.$refs["vone-custom-info"]?.$refs[
        "TaskTask"
      ]?.[0]?.getChildrenTable();
      this.$refs["vone-custom-edit"]?.$refs[
        "TaskTask"
      ]?.[0]?.getChildrenTable();
    },
    addChild() {
      this.taskParamAdd = {
        visible: true,
        key: Date.now(),
        infoDisabled: false,
        title: "新增任务",
      };

      setTimeout(async () => {
        // 获取任务详情
        const res = await apiAlmTaskInfo(
          this.taskInfoParam.visible ? this.taskInfoParam.id : this.taskParam.id
        );

        // 基本信息赋值
        const fixdForm = this.$refs["vone-custom-add"]?.fixdForm;
        fixdForm["name"] = res.data.name;
        fixdForm["typeCode"] = res.data.typeCode;
        fixdForm["handleBy"] = res.data.handleBy;
        fixdForm["planEtime"] = res.data.planEtime;
        fixdForm["description"] = res.data.description;

        // 自定义组件集合
        const customList = this.$refs["vone-custom-add"].customList || [];
        // 基本属性赋值
        const form = this.$refs["vone-custom-add"]?.form;
        form["parentId"] = res.data.id;
        form["sourceCode"] = res.data.sourceCode;
        form["priorityCode"] = res.data.priorityCode;
        form["leadingBy"] = res.data.leadingBy;
        form["requirementId"] = res.data.requirementId;
        form["planStime"] = res.data.planStime;
        form["bugId"] = res.data.bugId;
        form["productId"] = res.data.productId;
        form["projectId"] = res.data.projectId;
        form["planId"] = res.data.planId;
        form["c4"] = res.data.putBy;
      }, 500);
    },
    resizableChangeEvent(column, refName) {
      if (column.field == "name") {
        this.$refs[refName].refreshColumn();
      }
    },
    customCopy(command) {
      const _this = this;
      const message =
        command == "title" ? this.copyRow.code : this.copyRow.name;
      this.$copyText(message).then(
        function (e) {
          _this.$message.success(" 已复制到剪贴板！");
        },
        function (e) {
          _this.$message.warning(" 该浏览器不支持自动复制");
        }
      );
      this.copyRow = null;
    },
    visibleChange(e, row) {
      if (e) {
        this.copyRow = row;
      } else {
        this.copyRow = null;
      }
    },
    typeCodeChangeFn(e) {
      this.typeCodeChangeParam = {
        visible: true,
        dataId: e.id,
        typeClassfiy: "TASK",
      };
    },
    workItemCopy(e) {
      this.createSimple = false;
      this.taskParamAdd = {
        visible: true,
        key: Date.now(),
        infoDisabled: false,
        rowTypeCode: e.typeCode,
        title: "复制任务",
        id: e.id,
      };
    },
    async workitemChange(row, e, t) {
      const params = {
        id: row.id,
      };
      params[t] = e;
      const res = await editById("task", params);
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      row[t] = e;
      this.$message.success("修改成功");
      this.$forceUpdate();
    },
    showPopovers() {},
    async load({ row }) {
      // const res = await apiAlmTaskFindNextNoPage({ parentId: tree.id })
      const params = {
        current: 1,
        order: "descending",
        size: 10,
        sort: "createTime",
        extra: {},
        model: {
          projectId: [this.$route.params.id],
          parentId: [row.id],
        },
      };
      const [res, err] = await catchErr(apiGetTaskList(params));
      if (err) return;
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      res.data.records.forEach((element) => {
        element.pid = row.id;
        element.hasChildren = element.echoMap?.childrenCount != "0";
        element.tag =
          element.tagId &&
          element.tagId.length &&
          element.echoMap &&
          element.echoMap.tagId
            ? element.echoMap.tagId.map((r) => r.name)
            : [];
      });
      this.maps.set(row.id, { row });
      return res.data.records;
    },
    refresh() {
      if (this.formData.treeView && this.ids.length > 0) {
        const table = this.$refs["task-table"].attribute().store;
        this.ids.map((e) => {
          if (this.maps.get(e)) {
            const { tree, treeNode, resolve } = this.maps.get(e);
            table.states.lazyTreeNodeMap[e] = [];
            this.load(tree, treeNode, resolve);
          }
        });
      }
    },

    newIask() {
      // 隐藏快速新增
      this.createSimple = false;
      this.taskParamAdd = {
        visible: true,
        key: Date.now(),
        title: "新增任务",
        infoDisabled: false,
      };
    },
    openInfo(row) {
      if (row.stateCode !== "DONE" && this.$permission("project_task_edit")) {
        this.editTask(row);
      } else {
        this.showInfo(row);
      }
    },

    editTask(row) {
      this.taskParam = {
        visible: true,
        title: "编辑任务",
        id: row.id,
        key: Date.now(),
        infoDisabled: false,
        tableList: this.tableList,
        rowTypeCode: row.typeCode,
        stateCode: row.stateCode,
        // 拆分子项是否弹框
        showPopupForSplit: true,
      };
      if (row.pid) {
        this.ids = [row.pid];
      }
    },
    showInfo(row) {
      this.taskInfoParam = {
        visible: true,
        title: "任务详情",
        id: row.id,
        key: Date.now(),
        infoDisabled: true,
        tableList: this.tableList,
        rowTypeCode: row.typeCode,
        stateCode: row.stateCode,
        // 拆分子项是否弹框
        showPopupForSplit: true,
      };
    },
    // 更新任务状态
    async editRowStatus(row, index) {
      const [res, err] = await catchErr(apiAlmTaskInfo(row.id));
      if (err) return;
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      if (res.data.tagId && res.data.tagId.length) {
        res.data["tag"] = res.data.echoMap.tagId
          ? res.data.echoMap.tagId.map((r) => r.name)
          : [];
      }
      this.tableData.records.splice(index, 1, res.data);
    },
    selectAllEvent({ checked }) {
      this.selecteTableData = this.$refs["task-table"].getCheckboxRecords();
    },
    selectChangeEvent({ checked }) {
      this.selecteTableData = this.$refs["task-table"].getCheckboxRecords();
    },
    async getInitTableData() {
      // this.refresh()
      if (this.$store.state.project.itemName) {
        this.formData["name"] = this.$store.state.project.itemName;
      }
      const tableAttr = this.$refs?.pagination?.pageObj || {
        current: 1,
        size: 20,
      };
      const sortObj = this.$refs.searchForm?.sortObj;
      if (this.$route.params.id) {
        this.formData.projectId = [this.$route.params.id];
      }
      const params = {
        ...tableAttr,
        ...sortObj,
        extra: {
          ...this.extraData,
        },
        model: { ...this.formData },
      };
      this.tableLoading = true;
      if (this.formData.createTime && this.formData.createTime.length > 0) {
        params.model.createTime = {
          start: this.formData.createTime[0],
          end: this.formData.createTime[1],
        };
      }
      const [res, err] = await catchErr(apiGetTaskList(params));
      this.tableLoading = false;
      if (err) return;
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      res.data.records.forEach((element) => {
        // element.children = []
        if (this.formData.treeView) {
          element.hasChildren = element.echoMap?.childrenCount != "0";
        } else {
          element.hasChildren = false;
        }
        element.tag =
          element.tagId &&
          element.tagId.length &&
          element.echoMap &&
          element.echoMap.tagId
            ? element.echoMap.tagId.map((r) => r.name)
            : [];
      });
      this.tableData = res.data;
      this.tableList = res.data.records; // 用于编辑时切换上一个下一个
    },

    // 复制标题到剪贴板
    titleCopy(row, type) {
      const _this = this;
      const message = type == "code" ? row.code : row.name;
      this.$copyText(message).then(
        function (e) {
          _this.$message.success(" 已复制到剪贴板！");
        },
        function (e) {
          _this.$message.warning(" 该浏览器不支持自动复制");
        }
      );
    },
    async deleteTask(row) {
      await this.$confirm(`确定删除【${row.name}】吗?`, "删除", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        customClass: "delConfirm",
        showClose: false,
        type: "warning",
      });

      const { isSuccess, msg } = await apiAlmTaskDel([row.id]);
      if (!isSuccess) {
        this.loading = false;
        this.$message.error(msg);
        return;
      }
      this.$message.success("删除成功");
      if (row.pid) {
        this.ids = [row.pid];
      }
      this.getInitTableData();
    },
    // 批量删除
    async deleteAll() {
      if (!this.selecteTableData.length) {
        this.$message.warning("请选择要删除的数据");
        return;
      }
      try {
        await this.$confirm(`是否删除当前数据?`, "删除", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          customClass: "delConfirm",
          showClose: false,
          type: "warning",
        });
        this.tableLoading = true;
        const selectId = this.selecteTableData.map((r) => r.id);
        const res = await apiAlmTaskDel(selectId);
        this.tableLoading = false;
        if (!res.isSuccess) {
          this.$message.error(res.msg);
          return;
        }
        this.$message.success("删除成功");
        if (this.formData.treeView) {
          this.ids = this.selecteTableData.map((e) => {
            if (e.pid) {
              return e.pid;
            }
          });
        }
        this.getInitTableData();
      } catch (e) {
        this.tableLoading = false;
      }
    },
    // 导入
    imPort() {
      this.importParam = {
        visible: true,
        title: "项目-任务",
        url: `/api/alm/alm/task/excel/downloadImportTemplate/${this.$route.params.id}`,
        importUrl: `/api/alm/alm/task/excel/import?projectId=${this.$route.params.id}`,
      };
    },

    // 导出
    async exportFlie() {
      try {
        this.tableLoading = true;

        download(
          `任务信息.xls`,
          await apiBaseFileLoad("/api/alm/alm/task/excel/export", this.formData)
        );

        this.tableLoading = false;
      } catch (e) {
        this.tableLoading = false;
        return;
      }
    },
    // 批量编辑
    editAll() {
      if (!this.selecteTableData.length) {
        this.$message.warning("请至少选择一条数据");
        return;
      }
      this.editAllParam = { visible: true, tableSelect: this.selecteTableData };
      if (this.formData.treeView) {
        this.ids = this.selecteTableData.map((e) => {
          if (e.pid) {
            return e.pid;
          }
        });
      }
    },
    async getQueryFieldList(typeCodes) {
      const fixedField = [
        "name",
        "handleBy",
        "stateCode",
        "tagId",
        "createTime",
        "typeCode",
      ];
      const form = {
        projectId: this.$route.params.id,
        typeClassify: "TASK",
        typeCodes: typeCodes || [],
      };
      const res = await queryFieldList(form);
      if (!res.isSuccess) {
        return;
      }
      const vId = ["productId", "planId"];
      const filter = res.data.filter((r) => r.isSearch && r.key != "projectId");
      filter.forEach((element) => {
        element.isBasicFilter = !fixedField.includes(element.key);
        element.multiple = element.type.code != "ICON";
        element.valueType = vId.includes(element.key) ? "id" : null;
      });
      this.defaultFileds = filter;
      this.getOptions();
    },
    getOptions() {
      this.getPrioritList();
      this.getIssueType();
      this.getAllStatus();
      this.getDaley();
      this.getsourceCode();
      this.productList();
      this.getplanId();
    },
    getDaley() {
      const delay = [
        { name: "是", code: true },
        { name: "否", code: false },
      ];
      this.setData(this.defaultFileds, "delay", delay);
    },
    // 查询来源
    async getsourceCode() {
      const res = await apiAlmSourceNoPage({
        typeClassify: "TASK",
      });
      if (!res.isSuccess) {
        return;
      }
      this.setData(this.defaultFileds, "sourceCode", res.data);
    },
    // 归属产品
    async productList() {
      const res = await productListByCondition();
      if (!res.isSuccess) {
        return;
      }
      this.setData(this.defaultFileds, "productId", res.data);
    },
    // 查询全部工作流状态
    async getAllStatus() {
      const res = await getWorkItemState({
        projectId: this.$route.params.id,
        typeClassify: "TASK",
      });
      if (!res.isSuccess) {
        return;
      }
      this.stateCodeList = res.data;
      this.setData(this.defaultFileds, "stateCode", res.data);
    },
    // 查优先级
    async getPrioritList() {
      const res = await apiAlmPriorityNoPage();
      if (!res.isSuccess) {
        return;
      }
      this.prioritList = res.data;
      this.setData(this.defaultFileds, "priorityCode", res.data);
    },
    // 查询需求类型
    async getIssueType() {
      const res = await apiAlmGetTypeNoPage(this.$route.params.id, "TASK");
      if (!res.isSuccess) {
        return;
      }
      this.typeCodeList = res.data;
      this.setData(this.defaultFileds, "typeCode", res.data);
    },
    // 迭代计划
    async getplanId() {
      // if (this.maps['planId'].length > 0) return
      const res = await apiAlmProjectPlanNoPage({
        projectId: this.$route.params.id || "0",
      });
      if (!res.isSuccess) {
        return;
      }
      this.setData(this.defaultFileds, "planId", res.data);
    },
    // 查项目下需求
    async getRequirementList() {
      // if (this.maps['requirementId']?.length > 0) return
      const res = await requirementListByCondition({
        projectId: this.$route.params.id || "0",
      });
      if (!res.isSuccess) {
        return;
      }
      this.setData(this.defaultFileds, "requirementId", res.data);
    },
  },
};
</script>

<style lang="scss" scoped>
.remoteuser {
  :deep(.el-select .el-input .el-input__inner) {
    border: none;
  }

  :deep(.el-input--small .el-input__inner) {
    border: none;
  }

  :deep(.el-input__suffix) {
    display: none;
  }
}
:deep(.el-collapse-item__wrap) {
  border-bottom: none;
}
:deep(.el-tree-node__content) {
  height: 40px;
}
:deep(.name_col) {
  padding-left: 10px;
  .cell {
    display: flex;

    div.name_icon {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: normal;
      word-break: break-all;
      flex: 1;
      display: flex;
      align-items: center;
    }
  }
}
.table_title {
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
} /*// .el-table [class*="el-table__row--level"] .el-table__expand-icon*/
 {
  /*//   left: -29px;*/ /*//*/
}
</style>

<style>
.userList .el-input__inner {
  border: 0;
}

.userList .el-input__icon {
  display: none;
}
</style>
