<template>
  <div id="dashboard-main" class="dashboard-main">
    <div class="dashboard-header">
      <el-form :model="form" label-width="80px">
        <el-form-item label="纬度" prop="latitude">
          <el-select v-model="form.latitude" value-key="id" style="width: 150px" @change="changeDate('1')">
            <el-option label="组织" value="org">组织</el-option>
            <el-option label="项目" value="project">项目</el-option>
            <el-option label="产线" value="product">产线</el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="form.latitude == 'project'" label="项目" prop="projectId">
          <el-select v-model="form.projectId" clearable multiple collapse-tags filterable value-key="id" style="width: 210px" @change="changeDate('2')">
            <div style="padding-left: 12px;line-height: 34px;">
              <el-checkbox v-if="projectIdList&&projectIdList.length> 1" v-model="isCheckAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange($event, 'projectIdList', 'projectId')">全选</el-checkbox>
            </div>
            <el-option v-for="item in projectIdList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="form.latitude == 'org'" label="组织" prop="orgId">
          <vone-tree-select v-model="form.orgId" search-nested multiple collapse-tags :limit="1" :limit-text="(count => `+${count}`)" :tree-data="orgData" style="width: 210px" placeholder="请选择机构" @change="changeDate('3')" />
        </el-form-item>
        <!-- <el-form-item v-if="form.latitude == 'product'" label="产品集" prop="productId">
          <el-select v-model="form.productId" clearable filterable multiple value-key="id" collapse-tags style="width: 210px" @change="changeDate('4')">
            <div style="padding-left: 12px;line-height: 34px;">
              <el-checkbox v-if="productList&&productList.length> 1" v-model="isCheckAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange($event,'productList', 'productId')">全选</el-checkbox>
            </div>
            <el-option v-for="item in productList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item> -->
        <el-form-item label="开始时间" prop="startDate">
          <el-date-picker
            v-model="form.startDate"
            type="date"
            placeholder="选择开始时间"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            :picker-options="startPickerOptions"
            @change="changeDate('5')"
          />
        </el-form-item>
        <el-form-item label="结束时间" prop="endDate">
          <el-date-picker
            v-model="form.endDate"
            type="date"
            placeholder="选择结束时间"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            :picker-options="endPickerOptions"
            @change="changeDate('6')"
          />
        </el-form-item>
      </el-form>
    </div>
    <el-row :gutter="24">
      <el-col :span="18">
        <task :layout="form" />
      </el-col>
      <el-col :span="6">
        <workHour :layout="form" />
      </el-col>
      <el-col :span="12">
        <demand :layout="form" />
      </el-col>
      <el-col :span="12">
        <demandTrend :layout="form" />
      </el-col>
      <el-col :span="12">
        <bug :layout="form" />
      </el-col>
      <el-col :span="12">
        <bugTrend :layout="form" />
      </el-col>
      <el-col :span="24">
        <projectPower :layout="form" />
      </el-col>
    </el-row>

  </div>
</template>
<script>
import task from './function/task.vue'
import workHour from './function/workhour.vue'
import demand from './function/demand.vue'
import demandTrend from './function/demand-trend.vue'
import bug from './function/bug.vue'
import bugTrend from './function/bug-trend.vue'
import projectPower from './function/project-power.vue'
import { apiAlmProjectNoPage } from '@/api/vone/project/index'
import { getqueryList } from '@/api/vone/product/productfit'
import { orgList } from '@/api/vone/base/org'
import { gainTreeList } from '@/utils'
export default {
  components: {
    task,
    workHour,
    demand,
    demandTrend,
    bug,
    bugTrend,
    projectPower
  },
  data() {
    return {
      data: {},
      form: {
        latitude: 'project',
        projectId: [],
        time: [],
        productId: [],
        orgId: [],
        startDate: '',
        endDate: ''
      },
      projectIdList: [],
      productList: [],
      orgData: [],
      isIndeterminate: false,
      isCheckAll: false,
      startPickerOptions: {
        disabledDate: (time) => {
          if (this.form?.startDate) {
            return time.getTime() > new Date(this.form.endDate).getTime()
          }
        }
      },
      endPickerOptions: {
        disabledDate: (time) => {
          if (this.form?.startDate) {
            return time.getTime() < new Date(this.form.startDate).getTime()
          }
        }
      }
    }
  },
  mounted() {
    this.getProjectList()
    // this.getAllProductList()
    this.getOrgList()
    this.setDefaultTimeRange()
  },
  methods: {
    handleCheckAllChange(val, type, key) {
      const data = this[type].map(item => item.id)
      this.form[key] = val ? data : []
      this.isIndeterminate = false
      if (!val) return
      this.data = this.form
    },
    setDefaultTimeRange() {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 1) // 设置为当前月份的上一个月
      // this.defaultTime = [start, end]
      // this.form.time = [...this.defaultTime]
      this.form.startDate = start
      this.form.endDate = end
    },
    async getProjectList() {
      const res = await apiAlmProjectNoPage()

      if (!res.isSuccess) {
        return
      }
      this.projectIdList = res.data
      this.form.projectId = [res.data[0].id]
    },
    async getAllProductList() {
      const res = await getqueryList()
      if (!res.isSuccess) {
        return
      }
      this.productList = res.data
    },
    // 查询所有机构
    async getOrgList() {
      const res = await orgList()
      if (!res.isSuccess) {
        return
      }

      const orgTree = gainTreeList(res.data)
      this.orgData = orgTree
    },
    changeDate(e) {
      if (e == '1') {
        this.form.orgId = []
        this.form.projectId = []
        this.form.productId = []
        this.isIndeterminate = false
        return
      } else if (e == '2') {
        const checkedCount = this.form.projectId.length
        this.isCheckAll = checkedCount === this.projectIdList.length
        this.isIndeterminate = checkedCount > 0 && checkedCount < this.projectIdList.length
        if (!this.form.projectId || this.form.projectId.length == 0) {
          this.$message.warning('请选择项目')
          return
        } else {
          this.data = this.form
        }
      } else if (e == '4') {
        const checkedCount = this.form.productId.length
        this.isCheckAll = checkedCount === this.productList.length
        this.isIndeterminate = checkedCount > 0 && checkedCount < this.productList.length
        if (!this.form.productId || this.form.productId.length == 0) {
          this.$message.warning('请选择产品')
          return
        } else {
          this.data = this.form
        }
      } else if (e == '5' || e == '6') {
        if (this.form.startDate && this.form.endDate) {
          this.form.time = [this.form.startDate, this.form.endDate]
        } else {
          this.form.time = null
        }
        this.data = this.form
      } else {
        this.data = this.form
      }
    },
    changeLatitude() {

    }
  }

}
</script>
<style scoped lang='scss'>
.dashboard {
  &-main {
    touch-action: none;
    background: var(--content-bg-color);
  }
  &-header {
    height: 48px;
    line-height: 48px;
    background: var(--main-bg-color);
    padding: 0px 16px;
    margin: -10px -10px 0px -10px;
    box-shadow: var(--nav-top-shadow);
    border: 1px solid var(--solid-border-color);
    display: flex;
    align-items: center;
    :deep(.el-tabs__nav-wrap::after) {
      display: none;
    }
    :deep(.vone-tabs .el-tabs__nav) {
      border: none;
    }
  }
  &-content {
    margin:0 -10px;
  }
}
:deep(.el-row) {
	margin-left: 5px!important;
	margin-right:5px!important
}
:deep(.el-col) {
	padding-left: 5px!important;
	padding-right: 5px!important;
	margin-top: 10px;
}
:deep(.el-form .el-form-item) {
  margin-bottom: unset;
}
:deep(.el-form .el-form-item__label) {
  line-height: 32px;
}
:deep(.el-form) {
  display: flex;
}
</style>
