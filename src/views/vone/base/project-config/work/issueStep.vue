<template>
  <div>
    <vone-search-wrapper style="margin-bottom: 16px">
      <template v-slot:custom>
        <span class="strong">阶段</span>
      </template>
      <template v-slot:moreSearch>
        <template v-slot:moreSearch>
          <el-input
            v-model="formData.name"
            style="width: 300px"
            placeholder="搜索名称"
            :prefix-icon="ElIconSearch"
            @input="searchChange"
          />
        </template>
      </template>
      <template v-slot:actions>
        <el-button
          type="primary"
          :icon="elIconTipsPlusCircle"
          size="small"
          :disabled="!$permission('alm_stage_add')"
          @click="clickAddRow"
          >新增</el-button
        >
        <el-dropdown trigger="click" @command="(e) => e && e()">
          <el-button class="btnMore"
            ><el-icon class="iconfont"><el-icon-application-more /></el-icon
          ></el-button>
          <template v-slot:dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="(item, index) in actions"
                :key="index"
                :icon="item.icon"
                :command="item.fn"
                :disabled="item.disabled"
              >
                {{ item.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
    </vone-search-wrapper>
    <div :style="{ height: tableHeight }">
      <vxe-table
        ref="issue-step-table"
        class="vone-vxe-table"
        border
        height="auto"
        show-overflow="tooltip"
        :loading="pageLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ resizable: true, minWidth: 120 }"
        :checkbox-config="{ checkMethod: selectable }"
        row-id="id"
      >
        <vxe-column type="checkbox" width="37" align="center" fixed="left" />
        <vxe-column title="名称" field="name" fixed="left">
          <template v-slot="scope">
            <span
              v-if="scope.row.color"
              :style="{
                border: `1px solid ${scope.row.color}`,
                color: `${scope.row.color}`,
              }"
              class="tagCustom"
              >{{ scope.row.name }}</span
            >
            <div v-else>{{ scope.row.name }}</div>
          </template>
        </vxe-column>
        <vxe-column title="标识" field="code" />
        <vxe-column title="描述" field="description" />
        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <template>
              <el-tooltip class="item" content="编辑" placement="top">
                <el-button
                  type="text"
                  :disabled="!$permission('alm_stage_edit')"
                  :icon=" elIconApplicationEdit icon_click"
                  @click="editClickRow(row)"
                />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="删除" placement="top">
                <el-button
                  type="text"
                  :disabled="row.readonly || !$permission('alm_stage_delete')"
                  :icon="elIconApplicationDelete"
                  @click="deleteRow(row)"
                />
              </el-tooltip>
            </template>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <vone-pagination
      ref="pagination"
      :total="tableData.total"
      @update="getTableData"
    />
    <!-- 新增阶段 -->
    <el-dialog
      :title="title"
      width="30%"
      v-model:value="dialogFormVisible"
      :close-on-click-modal="false"
    >
      <el-form ref="stepForm" :rules="rules" :model="stepForm">
        <el-form-item label="名称" prop="name">
          <el-input v-model="stepForm.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="标识" prop="code">
          <el-input
            v-model="stepForm.code"
            placeholder="请输入标识"
            :disabled="title == '编辑阶段'"
          />
        </el-form-item>
        <el-form-item label="颜色" prop="color">
          <el-input
            v-model="stepForm.color"
            type="color"
            placeholder="请选择颜色"
          />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="stepForm.description"
            type="textarea"
            placeholder="请输入描述"
          />
        </el-form-item>
      </el-form>

      <template v-slot:footer>
        <div class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取消</el-button>
          <el-button type="primary" :loading="saveLoading" @click="sureAdd"
            >确定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  apiProjectmWorkflowStage,
  apiAlmStageAdd,
  apiAlmStageDel,
  apiAlmStageGetById,
} from "@/api/vone/base/work-flow";
import _ from "lodash";
export default {
  data() {
    return {
      rules: {
        name: [
          {
            required: true,
            message: "请输入长度不超过20个字符的名称",
            trigger: "change",
          },
          {
            pattern: "^[a-zA-Z0-9_\\u4e00-\\u9fa5]{1,20}$",
            message: "请输入不超过20位由中文、字母、数字或者下划线组成的名称",
          },
          {
            max: 20,
            message: "请输入长度不超过20个字符的名称",
            trigger: "change",
          },
        ],
        code: [
          {
            required: true,
            message: "请输入长度不超过20个字符的标识",
            trigger: "change",
          },
          {
            pattern: "^\\w{1,20}$",
            message: "请输入不超过20位由字母、数字或者下划线组成的标识",
          },
        ],
        color: [
          {
            required: true,
            message: "请选择颜色",
            trigger: "blur",
          },
        ],
        description: [
          {
            max: 100,
            message: "请输入长度不超过100个字符的描述",
            trigger: "change",
          },
        ],
      },
      pageLoading: false,
      tableData: {},
      stepForm: {
        color: "#000",
      },
      dialogFormVisible: false,
      saveLoading: false,
      title: "",
      formData: {
        name: "",
      },
      actions: [
        {
          name: "批量删除",
          disabled: !this.$permission("alm_stage_delete"),
          fn: this.deleteAll,
        },
      ],
    };
  },
  computed: {
    tableHeight() {
      const height = (this.extraData?.height || 0) + "px";
      return `calc(${this.$reduceTableHeight} - ${height})`;
    },
  },
  mounted() {
    this.getTableData();
  },
  methods: {
    searchChange: _.debounce(function (e) {
      this.getTableData();
    }, 1000),
    selectable({ row }) {
      if (row.readonly) {
        return false;
      } else {
        return true;
      }
    },
    getStatus(index, row) {
      return (
        this.tableData.records[index].readonly ||
        !this.$permission("alm_stage_edit")
      );
    },
    getDel(index, row) {
      return (
        this.tableData.records[index].readonly ||
        !this.$permission("alm_stage_delete")
      );
    },
    async getTableData() {
      this.pageLoading = true;
      const pageObj = this.$refs.pagination?.pageObj || {
        current: 1,
        size: 20,
      };
      const params = {
        ...pageObj,
        extra: {},
        model: { ...this.formData },
      };
      const res = await apiProjectmWorkflowStage(params);
      this.pageLoading = false;
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      this.tableData = res.data;
    },
    clickAddRow() {
      this.title = "新增阶段";
      this.dialogFormVisible = true;
      this.stepForm = { color: "#000" };
    },
    // 阶段新增
    async sureAdd() {
      try {
        await this.$refs.stepForm.validate();
      } catch (e) {
        return;
      }
      this.saveLoading = true;
      const res = await apiAlmStageAdd(this.stepForm);
      this.saveLoading = false;
      if (!res.isSuccess) {
        this.$message.error(res.msg);
      }
      this.$message.success("保存成功");
      this.dialogFormVisible = false;
      this.$refs.stepForm.resetFields();
      this.getTableData();
    },
    // 阶段编辑
    editClickRow(row) {
      this.title = "编辑阶段";
      this.dialogFormVisible = true;
      this.getInfo(row.id);
    },
    // 阶段删除
    async deleteRow(row) {
      await this.$confirm(`确定删除【${row.name}】吗？`, "删除", {
        type: "warning",
        closeOnClickModal: false,
        customClass: "delConfirm",
        showClose: false,
      });

      // this.pageLoading = true
      const res = await apiAlmStageDel([row.id]);
      // this.pageLoading = false
      if (!res.isSuccess) {
        this.$message.error(res.msg);
      }
      this.$message.success("删除成功");
      this.getTableData();
    },

    // 批量删除
    async deleteAll() {
      const selectData = this.getVxeTableSelectData("issue-step-table");
      if (!selectData.length) {
        this.$message.warning("请选择要删除的数据");
        return;
      }

      try {
        await this.$confirm(`是否删除 ${selectData.length} 条数据?`, "提示", {
          type: "warning",
          customClass: "delConfirm",
          showClose: false,
        });
        this.pageLoading = true;
        const selectId = this.getVxeTableSelectData(
          "issue-step-table"
        ).length.map((r) => r.id);
        const res = await apiAlmStageDel(selectId);
        this.pageLoading = false;
        if (!res.isSuccess) {
          this.$message.error(res.msg);
          return;
        }
        this.$message.success("删除成功");
        this.getTableData();
      } catch (e) {
        this.pageLoading = false;
      }
    },
    // 阶段详情
    async getInfo(id) {
      const res = await apiAlmStageGetById(id);
      this.pageLoading = false;
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      this.stepForm = res.data;
    },
  },
};
</script>

<style lang="scss" scoped>
.strong {
  font-size: 16px;
  font-weight: 500;
  color: #2c2e36;
}
</style>
