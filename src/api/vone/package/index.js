import axiosApi from '@/utils/axios-api'
// 制品库---列表数据
export function apiBuniessSelectByCondition(data) {
  return axiosApi({
    url: '/api/pack/pack/packBase/page',
    method: 'post',
    data
  })
}

// 业务系统--配置--tab页--制品信息---查询
export function apiBuniessSelectAllProductStatusTag() {
  return axiosApi({
    url: '/api/pack/pack/packStatus/query',
    method: 'post'
  })
}

// 制品库---服务应用下拉框数据
export function apiBuniessSelectAllByParam(data) {
  return axiosApi({
    url: '/api/cmdb/cmdb/application/query',
    method: 'post',
    data
  })
}

// 制品库---在线审核--环境标签
export function apiBuniessSelectEnvBySystemId(id) {
  return axiosApi({
    url: '/api/cmdb/cmdb/applicationEnv/findByApplicationId/' + id,
    method: 'get'
  })
}

// 制品库---上传
export function apiBuniessProductOperateUpload(data) {
  // const form = requestUtils.formData(params)
  // form.append('file', file)
  return axiosApi({
    url: `/api/pack/pack/packBase/upload`,
    method: 'post',
    data
  })
}

// 制品库--上传成功--弹出对话框
export async function apiBuniessProductAsyncMessage(data) {
  return axiosApi({
    url: `/api/pack/pack/packBase/asyncMessage`,
    method: 'get',
    data
  })
}

// 制品库--上传成功--弹出对话框
export async function apiBuniessProductAsyncMessagePropUuid(data) {
  return axiosApi({
    url: `/api/pack/pack/packBase/propUuid`,
    method: 'get',
    data
  })
}

// 字典无分页查询
export function apiBaseDictNoPage(data) {
  return axiosApi({
    url: '/api/base/base/dictionary/query',
    method: 'POST',
    data
  })
}

// 制品库---在线审核--表格数据
export function apiBuniessSelectFullById(id, data) {
  return axiosApi({
    url: `/api/pack/pack/packBase/getFilesByPackId/${id}`,
    method: 'get',
    data
  })
}

// 制品库---在线审核--Jfrog表格数据
export function apiJfrogFilesByPackId(id, data) {
  return axiosApi({
    url: `/api/pack/pack/packBase/getJfrogFilesByPackId/${id}`,
    method: 'get',
    data
  })
}

// 制品库--在线审核--点击表格每一行
export async function apiBuniessGetFileContentByProductId(id, data) {
  return axiosApi({
    url: `/api/pack/pack/packBase/getFileContentByPackId/${id}`,
    method: 'GET',
    data
  })
}
// 制品库---根据制品包id查询制品包详细信息
export function apiBuniessSelectFullInfoById(id) {
  return axiosApi({
    url: `/api/pack/pack/packBase/` + id,
    method: 'get'
  })
}

// 制品库---编辑--保存
export function apiBuniessUpdateProductBase(data) {
  return axiosApi({
    url: `/api/pack/pack/packBase`,
    method: 'put',
    data
  })
}
// 制品库---删除
export function apiBuniessDeleteProductById(data) {
  return axiosApi({
    url: `/api/pack/pack/packBase/deletePack`,
    method: 'delete',
    data
  })
}

/**
 * 制品库---下载
 */
export async function apiBuniessProductOperate(data) {
  return axiosApi({
    url: `/api/pack/pack/packBase/downLoad`,
    method: 'get',
    data,
    responseType: 'blob'
  })
}

// 以下为详情tab接口
// 制品库---变更日志
export function apiBuniessChangeLogsByProductId(data) {
  return axiosApi({
    url: `/api/pack/pack/packChangeLogs/page`,
    method: 'post',
    data
  })
}
// 制品库---根据制品包id查询制品包代码质量信息
export function apiBuniessSelectCodeQualityInfoById(data) {
  return axiosApi({
    url: `/api/pack/pack/packCodeQuality/page`,
    method: 'post',
    data
  })
}
// 制品库---根据制品包id查询制品包下载历史
export function apiBuniessSelectDownloadHistoryInfoById(data) {
  return axiosApi({
    url: `/api/pack/pack/packDownloadHistory/page`,
    method: 'post',
    data
  })
}

// 制品库---根据制品包id查询制品包流水线信息
export function apiBuniessSelectPipelineInfoByVersion(data) {
  return axiosApi({
    url: `/api/pack/pack/packBase/getBuildHistroryByVersion`,
    method: 'get',
    data
  })
}

// 制品库---根据制品包id查询制品包审核状态信息
export function apiBuniessSelectStatusTagInfoById(data) {
  return axiosApi({
    url: `/api/pack/pack/packStatusHistory/page`,
    method: 'post',
    data
  })
}
/**
 * 获取制品包部署记录列表接口
 * @param {typeId} params
 */
export async function apiPipelineProductGetDeployRecordByVersion(data) {
  return axiosApi({
    url: `/api/pack/pack/packBase/getDeployRecord`,
    method: 'get',
    data
  })
}
// 详情tab接口end

// 制品审核
// 制品库---审核状态
export function apiBuniessCheckSelectFullById(data) {
  return axiosApi({
    url: `/api/pack/pack/packStatus/query`,
    method: 'post',
    data
  })
}

// 制品库---审核状态--保存
export function apiBuniessUpdateProductStatus(data) {
  return axiosApi({
    url: `/api/pack/pack/packStatus/updatePackStatus`,
    method: 'post',
    data
  })
}
// 制品库---审核状态--保存
export function apiBuniessInsertProductStatus(data) {
  return axiosApi({
    url: `/api/pack/pack/packStatus/insertPackStatus`,
    method: 'post',
    data
  })
}
// 流水线-制品库-审核状态-删除
export async function apiBuniessDeleteProductStatusById(data) {
  return axiosApi({
    url: `/api/pack/pack/packStatus`,
    method: 'DELETE',
    data
  })
}

// 制品库---更新日志文件--删除
export function apiBuniessDeleteProductChangeLogs(data) {
  return axiosApi({
    url: `/api/pack/pack/packChangeLogs`,
    method: 'DELETE',
    data
  })
}

// 制品库---更新日志文件---新增--保存
export function apiBuniessAddProductChangeLogs(data) {
  return axiosApi({
    url: `/api/pack/pack/packChangeLogs`,
    method: 'post',
    data
  })
}

// 制品库---更新日志文件---修改--保存
export function apiBuniessUpdateProductChangeLogs(data) {
  return axiosApi({
    url: `/api/pack/pack/packChangeLogs`,
    method: 'put',
    data
  })
}

/**
 * @desc 查询制品拓扑图
 * @param {string} packId 制品id
 */
export function getProdGraph(data) {
  return axiosApi({
    url: '/api/pack/pack/packBase/selectTopologicalGraphyById',
    method: 'get',
    data
  })
}
export async function updatePackTypeByPackId(id, data) {
  return axiosApi({
    url: `/api/pack/pack/packBase/updatePackTypeByPackId/${id}`,
    method: 'post',
    data
  })
}
// 批量查询 用来设置制品宝的策略
export function packPolicy(data) {
  return axiosApi({
    url: '/api/pack/packPolicy/query',
    method: 'post',
    data
  })
}
export function packPolicyPut(data) {
  return axiosApi({
    url: '/api/pack/packPolicy',
    method: 'put',
    data
  })
}

// jfrog 获取列表第一级
export function getjfrogListFirst(data) {
  return axiosApi({
    url: '/api/pack/pack/packCatalogue/page',
    method: 'post',
    data: data
  })
}

// jfrog 获取列表
export function getjfrogAllList(data) {
  return axiosApi({
    url: '/api/pack/pack/packCatalogue/query',
    method: 'post',
    data: data
  })
}
// 获取下一级列表
export function getjfrogLastList(data) {
  return axiosApi({
    url: '/api/pack/pack/packCatalogue/getPackInfoByParentId',
    method: 'post',
    data: data
  })
}

// jfrog获取表格列表
export function getjfrogTableList(data) {
  return axiosApi({
    url: '/api/pack/pack/packBase/query',
    method: 'post',
    data: data
  })
}

// 查询制品包所在的代码库及其分支
export function findCodeRepositoryAndBranch(params) {
  return axiosApi({
    url: '/api/pack/pack/packBase/findCodeRepositoryAndBranch?version=' + params,
    method: 'get'

  })
}

/**
 *
 * @param {string} packId 制品下载
 */
export function getDownUrl(data) {
  return axiosApi({
    url: '/api/pack/pack/packBase/getDownUrl',
    method: 'get',
    data
  })
}

// harbor 下载
export function harborDownLoad(id) {
  return axiosApi({
    url: '/api/pack/pack/packBase/getDockerPullCommand/' + id,
    method: 'get'
  })
}
// 根据产品获取服务应用
export function getServiceApplication(data) {
  return axiosApi({
    url: '/api/product/product/productApplication/selectApplicationListByCondition',
    method: 'post',
    data
  })
}

// 查询JFrog用户与VA用户映射关系
export function apiPackUserList(data) {
  return axiosApi({
    url: '/api/pack/packUserMapping/page',
    method: 'post',
    data
  })
}
// 保存JFrog用户与VA用户映射关系
export function savePackUser(data) {
  return axiosApi({
    url: '/api/pack/packUserMapping/savePackUserMapping',
    method: 'post',
    data
  })
}

// 修改JFrog用户与VA用户映射关系
export function editPackUser(data) {
  return axiosApi({
    url: '/api/pack/packUserMapping/updatePackUserMapping',
    method: 'post',
    data
  })
}

// 删除JFrog用户与VA用户映射关系
export function delPackUser(data) {
  return axiosApi({
    url: '/api/pack/packUserMapping',
    method: 'DELETE',
    data
  })
}

// 保存制品库信息
export function holdPack(data) {
  return axiosApi({
    url: '/api/pack/pack/packBase/savePack',
    method: 'post',
    data
  })
}

// 获取文件下载信息
export function getDownloadMsg(params) {
  return axiosApi({
    url: '/api/pack/pack/packBase/getDownloadFileInfo',
    method: 'get',
    data: params
  })
}

// 获取制品类型树
export function getEngineList() {
  return axiosApi({
    url: '/api/pack/pack/packBase/getPackEngineInfo',
    method: 'get'
  })
}

// 新建仓库
export function creategRepository(data) {
  return axiosApi({
    url: '/api/pack/pack/packBase/createRepository',
    method: 'post',
    data
  })
}

// 更新制品
export function refreshPack(data) {
  return axiosApi({
    url: '/api/pack/pack/packCatalogue/refreshPack',
    method: 'post',
    data
  })
}

// 删除制品文件夹
export function delPackFile(data) {
  return axiosApi({
    url: '/api/pack/pack/packCatalogue/del',
    method: 'get',
    data
  })
}

// 制品下载 企业级
export function downLoadFile(data) {
  return axiosApi({
    url: '/api/pack/pack/packBase/downloadJFrogFile',
    method: 'get',
    data,
    responseType: 'blob'
  })
}
// 查询制品目录下制品文件的数量
export function getOppositePackBaseCount(data) {
  return axiosApi({
    url: '/api/pack/pack/packCatalogue/getOppositePackBaseCount',
    method: 'get',
    data
  })
}
// 查询可导入的JFrog仓库列表
export function getImportRepoList(data) {
  return axiosApi({
    url: '/api/pack/pack/packCatalogue/getImportRepoList',
    method: 'get',
    data
  })
}
// 仓库导入
export function importRepo(data) {
  return axiosApi({
    url: '/api/pack/pack/packCatalogue/importRepo',
    method: 'post',
    data
  })
}
// 文件下载
export function getJFrogFileDownloadUrl(data) {
  return axiosApi({
    url: '/api/pack/pack/packBase/getJFrogFileDownloadUrl',
    method: 'get',
    data
  })
}
// 数据同步配置
export function dataConfiguration(data) {
  return axiosApi({
    url: '/api/pack/packTimer/add',
    method: 'post',
    data
  })
}
// 查询未执行的制品同步定时任务
export function getUnexecutedPackTimer(data) {
  return axiosApi({
    url: '/api/pack/packTimer/getUnexecutedPackTimer',
    method: 'get',
    data
  })
}
// 删除制品同步定时任务
export function packTimerDel(data) {
  return axiosApi({
    url: '/api/pack/packTimer/del',
    method: 'get',
    data
  })
}
