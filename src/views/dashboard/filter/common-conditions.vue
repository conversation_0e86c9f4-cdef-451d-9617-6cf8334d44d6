<template>
  <div>
    <el-form ref="commonForm" :rules="rules" label-position="top">
      <el-row :gutter="12">
        <el-col :span="8">
          <el-form-item label="项目">
            <div class="form-main">
              <el-select v-model="setFormData('projectId').operator" placeholder="请选择" class="form-operator">
                <el-option v-for="item in operatorData('SELECT')" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
              <el-select v-model="setFormData('projectId').value" filterable multiple :collapse-tags="isCollapseTags" clearable placeholder="请选择" class="form-item" @change="changeProject">
                <el-option v-for="item in projectData" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="工作项类型">
            <div class="form-main">
              <el-select v-model="setFormData('workItem').value" multiple filterable clearable placeholder="请选择" class="form-item" style="width: 100%" @change="workItemChange">
                <el-option v-for="item in workItemData" :key="item.value" :label="item.title" :value="item.value" />
              </el-select>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="标题">
            <div class="form-main">
              <el-select v-model="setFormData('name').operator" placeholder="请选择" class="form-operator">
                <el-option v-for="item in operatorData('INPUT')" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
              <el-input v-model="setFormData('name').value" placeholder="请输入" class="form-item" />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="处理人">
            <div class="form-main">
              <el-select v-model="setFormData('handleBy').operator" placeholder="请选择" class="form-operator">
                <el-option v-for="item in operatorData('USER')" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
              <vone-remote-user v-model="setFormData('handleBy').value" collapse-tags :no-name="false" multiple class="form-item" />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item>
            <div slot="label">
              <span>计划完成时间</span>
              <el-tooltip placement="top">
                <div slot="content">x天内：最多可筛选90天</div>
                <i class="iconfont el-icon-tips-info-circle" style="font-size:14px; margin-left: 8px" />
              </el-tooltip>
            </div>
            <div class="form-main">
              <el-select v-model="setFormData('planEtime').operator" placeholder="请选择" class="form-operator" @change="dateChange">
                <el-option v-for="item in operatorData('DATE')" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
              <el-date-picker
                v-if="setFormData('planEtime').operator != 'BEFORE' && setFormData('planEtime').operator != 'BETWEEN'"
                :key="setFormData('planEtime').operator"
                v-model="setFormData('planEtime').value"
                class="form-item"
                type="datetime"
                placeholder="请选择日期"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
              <el-date-picker
                v-if="setFormData('planEtime').operator == 'BETWEEN'"
                :key="setFormData('planEtime').operator"
                v-model="setFormData('planEtime').value"
                class="form-item"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="['00:00:00', '23:59:59']"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
              <el-input-number
                v-if="setFormData('planEtime').operator == 'BEFORE'"
                v-model="setFormData('planEtime').value"
                class="form-item"
                :min="1"
                :max="90"
                controls-position="right"
                placeholder="请输入"
              />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="标签">
            <div class="form-main">
              <el-select v-model="setFormData('tagId').operator" placeholder="请选择" class="form-operator">
                <el-option v-for="item in operatorData('SELECT')" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
              <el-select v-model="setFormData('tagId').value" multiple filterable collapse-tags placeholder="请选择" class="form-item">
                <el-option v-for="item in tagData" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <item-conditions ref="custonmCondition" type="WORK_ITEM" :filter-data="customConditions" :rules="initFields" :choose-project="chooseProject" />
  </div>
</template>
<script>
const workItem = [{ value: 'ISSUE', title: '需求' }, { title: '任务', value: 'TASK' }, { title: '缺陷', value: 'BUG' }]
import { cloneDeep } from 'lodash'
import ItemConditions from './item-conditions.vue'
import { operator } from './filter-utils'
import { queryFilterConditions, querySelectData, queryprojectData } from '@/api/vone/dashboard/filter'
export default {
  components: {
    ItemConditions
  },
  props: {
    detail: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isCollapseTags: false,
      conditionGroup: {
        conditions: [
          {
            conditions: [
              {
                field: 'projectId',
                operator: 'IN',
                value: null
              },
              {
                field: 'workItem',
                operator: '',
                value: null
              },
              {
                field: 'name',
                operator: 'LIKE',
                value: ''
              },
              {
                field: 'handleBy',
                operator: 'IN',
                value: null
              },
              {
                field: 'planEtime',
                operator: 'BETWEEN',
                value: null
              },
              {
                field: 'tagId',
                operator: 'IN',
                value: null
              }
            ]
          }
        ]
      },
      initFields: ['projectId', 'workItem', 'name', 'handleBy', 'planEtime', 'tagId', 'typeCode'],
      projectData: [],
      tagData: [],
      workItemData: workItem,
      rules: {},
      loading: false,
      customConditions: [],
      chooseProject: []
    }
  },
  computed: {
    // 绑定表单数据
    setFormData() {
      return function(field) {
        return this.conditionGroup.conditions[0].conditions?.find(item => item.field == field)
      }
    },
    operatorData() {
      const operatorData = operator
      return function(type) {
        return operatorData.filter(item => item.types.includes(type))
      }
    }
  },
  watch: {
    detail: {
      handler: function(val) {
        // 数据回显逻辑处理
        if (val && Object.keys(val).length) {
          const search = this.detail?.search && JSON.parse(this.detail?.search)
          if (!search) return
          const list = search.WORK_ITEM?.conditions[0]?.conditions
          this.chooseProject = list?.find(r => r.field == 'projectId')?.value
          const workItem = []
          // 选中了哪几个工作项类型，将其添加
          for (const i in search) {
            i != 'WORK_ITEM' && workItem.push(i)
          }
          // 给公共属性赋值
          if (search && search['WORK_ITEM']) {
            this.conditionGroup.conditions[0].conditions = search['WORK_ITEM'].conditions[0].conditions?.filter(item => this.initFields.includes(item.field))
            this.customConditions = cloneDeep(search['WORK_ITEM'].conditions[0].conditions?.filter(item => !this.initFields.includes(item.field)))
            this.conditionGroup.conditions[0].conditions.push({
              field: 'workItem',
              operator: '',
              value: workItem
            })
          }
          this.$nextTick(() => {
            this.workItemChange(workItem, search)
            // this.getStateCodeList()
          })
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.getCommonData()
    this.getProjectData()
  },
  methods: {
    getFilterConditions(type) {
      queryFilterConditions(type).then(res => {
        if (res.isSuccess) {
          const data = res.data.filter(item => item.echoMap?.tableField?.tableName && item?.type?.code != 'FILE')
          this[type] = data
        }
      })
    },
    getCommonData() {
      const params = {
        'enableEcho': false,
        'table': 'issue_tag',
        'where': []
      }
      querySelectData(params).then(res => {
        if (res.isSuccess) {
          this.tagData = res.data
        }
      })
    },
    getProjectData() {
      queryprojectData().then(res => {
        if (res.isSuccess) {
          this.projectData = res.data
        }
      })
    },
    workItemChange(val, search) {
      const arr = this.workItemData?.filter(item => val.includes(item.value))
      arr.forEach(item => {
        item.type = item.value
        if (search && search[item.value]) {
          item.filterData = search[item.value]
        } else {
          item.filterData = []
        }
      })
      this.$emit('addItem', arr)
    },
    dateChange() {
      const obj = this.conditionGroup.conditions[0].conditions.find(item => item.field == 'planEtime')
      obj.value = null
    },
    // 获取公共属性选取的值
    getCommonAttrValue() {
      const commonData = cloneDeep(this.conditionGroup.conditions[0].conditions)
      const customValue = cloneDeep(this.$refs.custonmCondition.conditionGroup?.conditions?.[0]?.conditions) || []
      const attrValue = commonData.concat(customValue)
      const data = attrValue.filter(item => item.field != 'workItem')
      data.forEach(item => {
        // 将选中的值添加到selectData中
        if (item?.selectData && item?.selectKey && item?.tableName) {
          item.selectData = item.selectData?.filter(itm => item?.value?.includes(itm[item.selectKey]))
        }
      })
      return data
    },
    changeProject(val) {
      this.isCollapseTags = val.length > 2
      this.chooseProject = val
      this.$emit('changeProject', val)
    }

  }
}
</script>
<style lang="scss" scoped>
.form-main {
	display: flex;
	.form-operator {
		width: 90px;
		margin-right: 4px;
	}
	.form-item {
		width: calc(100% - 94px);
    :deep(.el-select__tags + .el-input) {
      .el-input__inner {
        height: 32px !important;
      }
    }
	}
}
:deep(.el-form .el-form-item) {
  margin-bottom: 12px;
}

    // :deep(.el-input__suffix) {
    //   right: 6px;
    // }
</style>
