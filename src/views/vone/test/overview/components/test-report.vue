<template>
  <vone-echarts-card :title="title">
    <!-- 缺陷趋势 -->
    <vone-echarts :options="options" height="300px" />
    <!-- <vone-empty v-else style="height:300px;" /> -->
  </vone-echarts-card>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import { testReportStatus } from '@/api/vone/testmanage/index'
export default {
  props: {
    title: {
      type: String,
      default: '',
    },
  },
  data() {
    const theme = document.body.className.indexOf('dark') > -1
    return {
      trendsData: null,
      options: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          formatter: '{b0}<br/>测试报告: {c0}个',
        },
        xAxis: {
          type: 'category',
          axisTick: {
            show: false,
          },
          axisLine: {
            lineStyle: {
              color: theme ? '#333947' : '#EBEEF5',
            },
          },
          axisLabel: {
            color: '#8A8F99',
          },
          data: [],
        },
        grid: {
          left: 0,
          right: 0,
          top: '16px',
          bottom: '3%',
          containLabel: true,
        },
        yAxis: {
          type: 'value',
          minInterval: 1,
          axisTick: {
            // y轴刻度线
            show: false,
          },
          axisLine: {
            show: false, // 不显示坐标轴轴线
          },
          splitLine: {
            // 网格线
            lineStyle: {
              type: 'dashed', // 设置网格线类型 dotted：虚线   solid:实线
              width: 1,
              color: theme ? '#333947' : '#EBEEF5',
            },
          },
          axisLabel: {
            show: true,
            color: '#8A8F99',
          },
        },
        series: [
          {
            data: [],
            type: 'line',
            color: ['#FFCB6C'],
          },
        ],
      },
    }
  },
  watch: {},
  mounted() {
    $on(this.$bus, 'refreshChart', () => {
      this.changeTheme()
    })
    this.getData()
  },
  methods: {
    // 测试报告新增
    async getData(val) {
      const res = await testReportStatus(val ? val.join(',') : '')
      if (!res.isSuccess) {
        this.$message.success(res.msg)
      }
      this.trendsData = res.data

      const obj = {}

      obj.date = Object.keys(res.data)
      obj.status = Object.values(res.data)

      this.options.xAxis.data = obj.date
      this.options.series[0].data = obj.status
    },
    changeTheme() {
      // 是否是暗色主题
      const theme = document.body.className.indexOf('dark') > -1
      this.options.yAxis.splitLine.lineStyle.color = theme
        ? '#333947'
        : '#EBEEF5'
      this.options.xAxis.axisLine.lineStyle.color = theme
        ? '#333947'
        : '#EBEEF5'
    },
  },
}
</script>
