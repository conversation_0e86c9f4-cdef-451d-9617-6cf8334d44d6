import request from '@/utils/axios-api'

//  项目集列表
export function apiProgramPage(data) {
  return request({
    url: '/api/alm/alm/projectProgram/page',
    method: 'post',
    data
  })
}
//  下拉条件查询项目集列表无权限
export function ProgramListByCondition(data) {
  return request({
    url: '/api/alm/alm/projectProgram/queryListByCondition',
    method: 'post',
    data
  })
}
//  项目集列表--不分页
export function apiProgramNoPage(data) {
  return request({
    url: '/api/alm/alm/projectProgram/query',
    method: 'post',
    data
  })
}

// 添加
export function apiAddProgramPage(data) {
  return request({
    url: '/api/alm/alm/projectProgram',
    method: 'post',
    data
  })
}
// 修改
export function apiEditProgramPage(data) {
  return request({
    url: '/api/alm/alm/projectProgram',
    method: 'put',
    data
  })
}
// 删除
export function apiDelProgramPage(data) {
  return request({
    url: '/api/alm/alm/projectProgram',
    method: 'delete',
    data
  })
}
// 关联项目列表
export function apiProgramList(data) {
  return request({
    url: '/api/alm/alm/project/getProjectInfoByProgramIdIsEmpty',
    method: 'post',
    data
  })
}
// 详情
export function apiProgramInfoById(id) {
  return request({
    url: `/api/alm/alm//projectProgram/${id}`,
    method: 'GET'
  })
}

