<template>

  <page-wrapper>
    <el-form ref="fileForm" :model="fileForm">
      <el-form-item label="URL" prop="URL">
        <el-input v-model="fileForm.url" placeholder="请输入URL" />
      </el-form-item>
      <el-form-item label="参数" prop="params">
        <el-input v-model="fileForm.params" type="textarea" :autosize="{ minRows: 5, maxRows: 30}" placeholder="请输入参数" />
      </el-form-item>
    </el-form>
    <el-button type="primary" :disabled="!$permission('base_upload_test') " @click="submit">确定</el-button>
    <el-button :disabled="!$permission('base_upload_test') " @click="reset">重置</el-button>
  </page-wrapper>

</template>

<script>
import { apiBaseFileLoad } from '@/api/vone/base/file'
import { download } from '@/utils'
export default {
  data() {
    return {
      fileForm: {
        url: '',
        params: ''
      }
    }
  },
  methods: {
    async submit() {
      download('file.xls', await apiBaseFileLoad(
        this.fileForm.url, JSON.parse(this.fileForm.params
        )))
    },
    reset() {
      this.$refs.fileForm.resetFields()
    }
  }

}
</script>

<style lang="scss" scoped>
.el-form {
  width: 50%;
}
</style>

