import inherits from 'inherits'
import BaseRenderer from 'diagram-js/lib/draw/BaseRenderer'
import { is } from 'bpmn-js/lib/util/ModelUtil'
import { append as svgAppend, create as svgCreate, attr as svgAttr } from 'tiny-svg'
import { getSpendTimeText, isValidTime } from '../../utils/timeFormatter'

/**
 * 进度节点渲染器
 * 用于在工作流节点上显示耗时信息和当前节点高亮
 */
function ProgressRenderer(eventBus, bpmnRenderer, textRenderer) {
  BaseRenderer.call(this, eventBus, 2000) // 提高优先级

  /**
   * 判断是否可以渲染该元素
   */
  this.canRender = function(element) {
    // 渲染所有BPMN任务类型的节点和自定义节点
    return is(element, 'bpmn:Task') ||
           is(element, 'bpmn:UserTask') ||
           is(element, 'bpmn:ServiceTask') ||
           is(element, 'bpmn:ScriptTask') ||
           is(element, 'bpmn:BusinessRuleTask') ||
           is(element, 'bpmn:ManualTask') ||
           is(element, 'bpmn:ReceiveTask') ||
           is(element, 'bpmn:SendTask') ||
           is(element, 'bpmn:StartEvent') ||
           is(element, 'bpmn:EndEvent') ||
           is(element, 'bpmn:IntermediateThrowEvent') ||
           is(element, 'bpmn:IntermediateCatchEvent') ||
           is(element, 'j:Base') // 支持自定义节点类型
  }

  /**
   * 绘制节点形状
   */
  this.drawShape = function(parentNode, element) {
    // 首先使用默认渲染器绘制基础节点
    const shape = bpmnRenderer.drawShape(parentNode, element)

    // 获取节点的业务对象
    const businessObject = element.businessObject
    const attrs = businessObject.$attrs || {}

    // 检查是否需要显示进度信息
    const showProgress = attrs.showProgress
    const spendTime = attrs.spendTime
    const currentNode = attrs.currentNode

    if (!showProgress) {
      return shape
    }

    // 如果是当前节点，添加高亮效果
    if (currentNode) {
      this._addCurrentNodeHighlight(shape, element)
    }

    // 如果有耗时信息，添加耗时显示
    if (isValidTime(spendTime)) {
      this._addSpendTimeDisplay(shape, element, spendTime)
    }

    return shape
  }

  /**
   * 后处理方法 - 在所有节点渲染完成后添加进度信息
   */
  this.postRender = function() {
    // 这个方法会在所有节点渲染完成后调用
    console.log('ProgressRenderer postRender called')
  }

  /**
   * 添加当前节点高亮效果
   */
  this._addCurrentNodeHighlight = function(shape, element) {
    // 添加外层高亮边框
    const outerHighlight = svgCreate('rect')
    svgAttr(outerHighlight, {
      x: -4,
      y: -4,
      width: element.width + 8,
      height: element.height + 8,
      rx: 6,
      ry: 6,
      fill: 'none',
      stroke: '#ff6b35',
      'stroke-width': 2,
      'stroke-dasharray': '8,4',
      opacity: 0.8
    })

    // 添加内层高亮边框
    const innerHighlight = svgCreate('rect')
    svgAttr(innerHighlight, {
      x: -1,
      y: -1,
      width: element.width + 2,
      height: element.height + 2,
      rx: 3,
      ry: 3,
      fill: 'none',
      stroke: '#ff6b35',
      'stroke-width': 1,
      opacity: 0.6
    })

    // 添加动画效果到外层边框
    const animate = svgCreate('animate')
    svgAttr(animate, {
      attributeName: 'stroke-dashoffset',
      values: '0;12',
      dur: '2s',
      repeatCount: 'indefinite'
    })

    svgAppend(outerHighlight, animate)
    svgAppend(shape, outerHighlight)
    svgAppend(shape, innerHighlight)
  }

  /**
   * 添加耗时显示
   */
  this._addSpendTimeDisplay = function(shape, element, spendTime) {
    // 使用简化格式以节省空间
    const timeText = getSpendTimeText(spendTime, true)

    // 计算文本尺寸
    const fontSize = 10
    const padding = 6
    const textWidth = Math.max(timeText.length * (fontSize * 0.6) + padding * 2, 40)
    const textHeight = 18

    // 计算位置（居中显示在节点下方）
    const bgX = (element.width - textWidth) / 2
    const bgY = element.height + 4

    // 创建文本背景容器
    const textGroup = svgCreate('g')
    svgAttr(textGroup, {
      class: 'spend-time-display'
    })

    // 创建背景矩形
    const textBg = svgCreate('rect')
    svgAttr(textBg, {
      x: bgX,
      y: bgY,
      width: textWidth,
      height: textHeight,
      rx: 3,
      ry: 3,
      fill: 'rgba(62, 123, 250, 0.9)', // 使用主题色
      stroke: 'rgba(62, 123, 250, 1)',
      'stroke-width': 1,
      filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.1))'
    })

    // 创建文本元素
    const text = svgCreate('text')
    svgAttr(text, {
      x: element.width / 2,
      y: bgY + textHeight / 2 + fontSize / 3, // 垂直居中
      'text-anchor': 'middle',
      'font-family': '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
      'font-size': fontSize + 'px',
      fill: '#ffffff',
      'font-weight': '500'
    })

    text.textContent = timeText

    // 组装元素
    svgAppend(textGroup, textBg)
    svgAppend(textGroup, text)
    svgAppend(shape, textGroup)
  }
}

inherits(ProgressRenderer, BaseRenderer)

ProgressRenderer.$inject = ['eventBus', 'bpmnRenderer', 'textRenderer']

/**
 * 进度显示模块 - 使用延迟处理确保元素已完全渲染
 */
function ProgressModule(eventBus, elementRegistry) {
  // 监听导入完成事件
  eventBus.on('import.done', function() {
    // 延迟处理，确保所有元素都已渲染
    setTimeout(function() {
      addProgressToAllElements()
    }, 100)
  })

  function addProgressToAllElements() {
    const elements = elementRegistry.getAll()

    elements.forEach(function(element) {
      if (element.businessObject && element.businessObject.$attrs) {
        const attrs = element.businessObject.$attrs

        if (attrs.showProgress) {
          addProgressOverlay(element, attrs)
        }
      }
    })
  }

  function addProgressOverlay(element, attrs) {
    const gfx = elementRegistry.getGraphics(element)
    if (!gfx) return

    const spendTime = attrs.spendTime
    const currentNode = attrs.currentNode

    // 添加当前节点高亮
    if (currentNode) {
      addCurrentNodeHighlight(gfx, element)
    }

    // 添加耗时显示
    if (isValidTime(spendTime)) {
      addSpendTimeDisplay(gfx, element, spendTime)
    }
  }

  function addCurrentNodeHighlight(gfx, element) {
    // 添加高亮边框
    const highlightRect = svgCreate('rect')
    svgAttr(highlightRect, {
      x: -4,
      y: -4,
      width: element.width + 8,
      height: element.height + 8,
      rx: 6,
      ry: 6,
      fill: 'none',
      stroke: '#ff6b35',
      'stroke-width': 2,
      'stroke-dasharray': '8,4',
      opacity: 0.8
    })

    // 添加动画
    const animate = svgCreate('animate')
    svgAttr(animate, {
      attributeName: 'stroke-dashoffset',
      values: '0;12',
      dur: '2s',
      repeatCount: 'indefinite'
    })

    svgAppend(highlightRect, animate)
    svgAppend(gfx, highlightRect)
  }

  function addSpendTimeDisplay(gfx, element, spendTime) {
    const timeText = getSpendTimeText(spendTime, true)

    // 计算位置
    const fontSize = 10
    const padding = 6
    const textWidth = Math.max(timeText.length * (fontSize * 0.6) + padding * 2, 40)
    const textHeight = 18
    const bgX = (element.width - textWidth) / 2
    const bgY = element.height + 4

    // 创建背景
    const textBg = svgCreate('rect')
    svgAttr(textBg, {
      x: bgX,
      y: bgY,
      width: textWidth,
      height: textHeight,
      rx: 3,
      ry: 3,
      fill: 'rgba(62, 123, 250, 0.9)',
      stroke: 'rgba(62, 123, 250, 1)',
      'stroke-width': 1
    })

    // 创建文本
    const text = svgCreate('text')
    svgAttr(text, {
      x: element.width / 2,
      y: bgY + textHeight / 2 + fontSize / 3,
      'text-anchor': 'middle',
      'font-family': 'Arial, sans-serif',
      'font-size': fontSize + 'px',
      fill: '#ffffff',
      'font-weight': '500'
    })
    text.textContent = timeText

    svgAppend(gfx, textBg)
    svgAppend(gfx, text)
  }
}

ProgressModule.$inject = ['eventBus', 'elementRegistry']

export default [
  {
    __init__: ['progressRenderer', 'progressModule'],
    progressRenderer: ['type', ProgressRenderer],
    progressModule: ['type', ProgressModule]
  }
]
