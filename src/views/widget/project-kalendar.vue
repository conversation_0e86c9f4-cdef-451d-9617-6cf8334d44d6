<template>
  <div class="page-container">
    <div class="container">
      <Calendar
        ref="calendar"
        class="ui-calendar"
        :date-data="dateData"
        :mode="mode"
        :render-header="renderHeader"
        :first-day="firstDay"
        @onMonthChange="onMonthChange"
        @next="onNext"
        @prev="onPrev"
      >
        <template v-slot:header-left>
          <div class="ui-calendar-header__left">
            <!-- <button
                :class="['ui-calendar-modeBtn' ,{ active: mode === 'month' }]"
                @click="mode = 'month'"
              >
                月
              </button> -->
            <!-- <button
                :class="['ui-calendar-modeBtn', { active: mode === 'week' }]"
                @click="mode = 'week'"
              >
                周
              </button> -->
          </div>
        </template>

        <template v-slot:body="{ data }">
          <transition :name="transitionName">
            <div :key="indentifier" class="calendar-body-grid">
              <div
                v-for="(row, index) in data"
                :key="index"
                class="calendar-body-row"
              >
                <div
                  v-for="(col, index) in row"
                  :key="index"
                  class="calendar-day-item"
                >
                  <div
                    v-if="col"
                    :class="[
                      'ui-calendar-item',
                      {
                        'is-otherMonth': col.isPrevMonth || col.isNextMonth,
                        'is-today': col.isToday,
                      },
                    ]"
                  >
                    <div class="ui-calendar-item-date">
                      {{ col.date.date }}
                    </div>
                    <div v-for="(item, index) in col.data" :key="index">
                      <div v-if="showMore">
                        <div
                          v-for="info in item.data"
                          :key="info.id"
                          :class="info.classifyCode"
                          class="ui-calendar-item-name task text-over"
                        >
                          <a @click="toProjectInfo(info)">
                            <span v-if="info.typeIcon">
                              <i :class="`iconfont ${info.typeIcon}`" />
                            </span>
                            &nbsp;&nbsp;
                            <el-tooltip :content="info.name" placement="top">
                              <span>
                                {{ info.name }}
                              </span>
                            </el-tooltip>
                            &nbsp;&nbsp;
                          </a>
                          <!-- <span class="del" @click="deleteItem(item.title)">✖️</span> -->
                        </div>
                      </div>
                      <div v-else>
                        <div v-if="item.data.length > 3">
                          <div
                            v-for="info in item.data.slice(0, 3)"
                            :key="info.id"
                            :class="info.classifyCode"
                            class="ui-calendar-item-name task text-over"
                          >
                            <a @click="toProjectInfo(info)">
                              <span v-if="info.typeIcon">
                                <i :class="`iconfont ${info.typeIcon}`" />
                              </span>

                              &nbsp;&nbsp;
                              <el-tooltip :content="info.name" placement="top">
                                <span>
                                  {{ info.name }}
                                </span>
                              </el-tooltip>
                              &nbsp;&nbsp;
                            </a>
                            <!-- <span class="del" @click="deleteItem(item.title)">✖️</span> -->
                          </div>
                          <div v-if="item.data.length > 3">
                            <el-button type="text" @click="showMore = true"
                              >查看更多</el-button
                            >
                          </div>
                        </div>

                        <div v-else>
                          <div
                            v-for="info in item.data"
                            :key="info.id"
                            :style="{
                              background: `${info.typeColor || '#ccc'}`,
                            }"
                            class="ui-calendar-item-name task text-over"
                          >
                            <a @click="toProjectInfo(info)">
                              <span v-if="info.typeIcon">
                                <i :class="`iconfont ${info.typeIcon}`" />
                              </span>

                              &nbsp;&nbsp;

                              <el-tooltip :content="info.name" placement="top">
                                <span>
                                  {{ info.name }}
                                </span>
                              </el-tooltip>
                              &nbsp;&nbsp;
                            </a>
                            <!-- <span class="del" @click="deleteItem(item.title)">✖️</span> -->
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </transition>
        </template>
      </Calendar>
    </div>
  </div>
</template>

<script>
import { Calendar } from '@/components/Calendar/vue2-event-calendar.esm'

import '@/components/Calendar/vue2-event-calendar.css'
import { apiInsightUserTodoWork } from '@/api/vone/weidget/index'

export default {
  name: 'App',
  components: {
    Calendar,
  },
  data() {
    return {
      indentifier: '',
      dateData: [],
      transitionName: 'slide-left',
      mode: 'month',
      firstDay: 1,
      selectedDate: null, // 当前月
      showMore: false,
      startDate: null,
      endDate: null,
    }
  },

  mounted() {
    if (this.selectedDate) {
      this.getUserTodoWork()
    }
  },
  methods: {
    async onMonthChange(val) {
      this.indentifier = val.now.full

      this.startDate = val.startDate.full
      this.endDate = val.endDay.full
      this.showMore = false

      await this.getUserTodoWork()
    },
    onNext(val) {
      this.transitionName = 'slide-left'
    },
    onPrev(val) {
      this.transitionName = 'slide-right'
    },
    changeDate() {
      this.$refs.calendar.changeDate('2017-12-12')
    },
    deleteItem(title) {
      this.dateData = this.dateData.filter((item) => item.title !== title)
    },
    renderHeader({ prev, next, selectedDate }) {
      this.selectedDate = selectedDate

      const h = this.$createElement

      const prevButton = h(
        'div',
        {
          class: ['ui-calendar-modeBtn'],
          on: {
            click: prev,
          },
        },
        ['上一页']
      )

      const nextButton = h(
        'div',
        {
          class: ['ui-calendar-modeBtn'],
          on: {
            click: next,
          },
        },
        ['下一页']
      )

      const dateText = h('div', { class: ['ui-calendar-modeBtn'] }, [
        selectedDate,
      ])
      return h('div', [prevButton, dateText, nextButton])
    },
    async getUserTodoWork() {
      if (!this.selectedDate) {
        return
      } else {
        const { data, isSuccess, msg } = await apiInsightUserTodoWork({
          startDate: this.startDate,
          endDate: this.endDate,
        })
        if (!isSuccess) {
          this.$message.error(msg)
          return
        }
        this.dateData = data
      }
    },
    toProjectInfo(info) {
      this.$store.dispatch('project/itemProject', info.projectInfo.typeCode)

      this.$store.dispatch('project/toProjectInfo', info.name)

      if (info.classifyCode == 'TASK') {
        this.$router.push({
          name: 'project_task',
          params: {
            projectKey: info.projectInfo.code,
            projectTypeCode: info.projectInfo.typeCode,
            id: info.projectId,
          },
        })
      } else if (info.classifyCode == 'ISSUE') {
        this.$router.push({
          name: 'project_issue',
          params: {
            projectKey: info.projectInfo.code,
            projectTypeCode: info.projectInfo.typeCode,
            id: info.projectId,
          },
        })
      } else {
        this.$router.push({
          name: 'project_defect',
          params: {
            projectKey: info.projectInfo.code,
            projectTypeCode: info.projectInfo.typeCode,
            id: info.projectId,
          },
        })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.page-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--solid-border-color);
  box-shadow: var(--main-bg-shadow);
  background: var(--main-bg-color);
  border-radius: 4px;
  overflow: hidden;
  .container {
    width: 100%;
    margin: 0 auto;
    flex: 1;
    height: 100%;
  }
  .ui-calendar {
    // box-shadow: 0 1px 5px darken(#fb7bb0, 20%);
    border-radius: 5px;
    height: 100%;

    &-header {
      &__left {
        > button {
          font-size: 12px;

          &:nth-child(2) {
            margin-left: -4px;
          }
        }
      }
    }

    &-modeBtn {
      position: relative;
      display: inline-block;
      padding: 5px 1em;
      font-size: 13px;
      line-height: 1;
      min-width: 5em;
      margin-right: -1px;
      text-align: center;
      cursor: pointer;

      &:first-child {
        border-top-left-radius: 3px;
        border-bottom-left-radius: 3px;
      }

      &:last-child {
        // left: -.5em;
        border-bottom-right-radius: 3px;
        border-top-right-radius: 3px;
      }

      &:active,
      &:focus {
        outline: none;
      }

      &.active,
      &:active {
        background: #64befa;
        color: #fff;
        z-index: 2;
      }
    }

    &-item {
      padding: 5px 10px;
      color: #666;

      &.is {
        &-otherMonth {
          color: #bbb;
        }

        &-today {
          .ui-calendar-item-date {
            // position: relative;
            // display: inline-block;
            // background: #64BEFA;
            color: var(--main-theme-color, #3e7bfa);
            // width: 20px;
            // height: 20px;
            // border-radius: 50%;
            // text-align: center;
            // line-height: 20px;
            // top: -1px;
          }
        }
      }

      &-name {
        font-size: 12px;
        > * {
          vertical-align: middle;
        }

        .del {
          display: inline-block;
          cursor: pointer;
          color: inherit;
          margin-bottom: -2px;
        }
      }
    }

    .calendar-body-row {
      height: auto;
    }

    .calendar-body {
      overflow: hidden;
    }
  }

  .slide-left-enter-active,
  .slide-left-leave-active,
  .fade-enter-active,
  .fade-leave-active,
  .slide-right-enter-active,
  .slide-right-leave-active {
    position: absolute;
    width: 100%;
    left: 0;
    top: 0;
  }

  .slide-left-enter-active,
  .slide-left-leave-active,
  .slide-right-enter-active,
  .slide-right-leave-active {
    transition: all 0.2s ease-out;
    transform: translate3d(0, 0, 0);
  }
  .slide-left-enter,
  .slide-left-leave-to {
    opacity: 0;
    transform: translate3d(-50px, 0, 0);
  }
  .slide-right-enter,
  .slide-right-leave-to {
    opacity: 0;
    transform: translate3d(50px, 0, 0);
  }

  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.2s;
  }
  .fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
    opacity: 0;
  }
  .ui-calendar-item-name {
    margin-top: 2px;
    color: #fff;
    border-radius: 3px;
    // margin-bottom: 3px;
    line-height: 22px;
  }
}
.BUG {
  background-color: #f46a6b;
}
.ISSUE {
  background-color: #51a6f1;
}
.TASK {
  background-color: green;
}
.iconfont {
  vertical-align: middle;
  margin-left: 2px;
  color: #fff;
}
</style>
