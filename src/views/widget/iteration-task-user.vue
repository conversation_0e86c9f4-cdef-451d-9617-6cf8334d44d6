<template>
  <!-- 事项分布 -->
  <vone-echarts-card :title="formInfo.title" :name="formInfo.name">
    <vone-echarts v-if="data.length > 0" :options="options" />
    <vone-empty v-else />
  </vone-echarts-card>
</template>

<script>
import { statusPlanId } from '@/api/vone/project/overview.js'

export default {
  props: {
    formInfo: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      data: [],
      backgroundColor: '#2c343c',
      options: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          backgroundColor: '#fff',
          borderColor: 'none',
          extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);', // 附加阴影样式
          textStyle: {
            color: '#53565C',
          },
          confine: true,
        },
        legend: {
          icon: 'circle',
          x: 'right', // 居右显示
          itemHeight: 8,
          itemWidth: 8,
          borderRadius: 8,
          textStyle: {
            // 图例文字的样式
            color: '#777F8E',
          },
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: [],
            axisLabel: {
              // 底部文字倾斜
              interval: 0,
              rotate: 25,
            },
          },
        ],
        yAxis: [
          {
            type: 'value',
            axisLine: {
              show: false, // 不显示坐标轴轴线
            },
            minInterval: 1,
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed',
              },
            },
          },
        ],
        series: [],
      },
    }
  },
  mounted() {
    if (this.formInfo.planId) {
      this.getStatus()
    }
  },
  methods: {
    async getStatus() {
      await statusPlanId(this.formInfo.planId).then((res) => {
        if (res.isSuccess) {
          this.data = res.data.data
          res.data.data.forEach((item) => {
            item.type = 'bar'
            item.stack = 'Ad'
            item.emphasis = {
              focus: 'series',
            }
            item.itemStyle = {
              normal: {
                color: item.color,
              },
            }
          })
          this.options.series = res.data.data
          this.options.xAxis[0].data = res.data.name
        }
      })
    },
  },
}
</script>
