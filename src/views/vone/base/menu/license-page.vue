<template>
  <div v-loading="pageLoading" class="pageContent">
    <header>
      <el-row class="elRow" type="flex" justify="space-between">
        <span class="flexRow">
          <span class="back" @click="back">
            <i class="iconfont el-icon-direction-back" />
          </span>
          <strong>
            Licenses信息
          </strong>
        </span>
        <el-upload
          accept=".lic"
          action="#"
          class="avatar-uploader"
          :show-file-list="false"
          :on-change="handleChange"
        >
          <el-button icon="iconfont el-icon-edit-upload" type="primary" :loading="upLoading" :disabled="!$permission('base_licenses_upload')">
            更新Licenses
          </el-button>

        </el-upload>
      </el-row>
      <main>
        <vone-desc :column="4">
          <vone-desc-item label="申请用户 :">
            {{ basicData.name }}
          </vone-desc-item>
          <vone-desc-item label="申请类型 :">
            <span v-if="basicData.hostType && types[basicData.hostType]">
              {{ types[basicData.hostType] }}
            </span>
          </vone-desc-item>
          <vone-desc-item label="失效时间 :">
            {{ minDurationTime(basicData.failureTime) }}
          </vone-desc-item>
          <vone-desc-item label="申请日期 :">
            {{ basicData.createTime }}
          </vone-desc-item>
          <vone-desc-item label="申请内容 :">
            {{ basicData.description }}
          </vone-desc-item>
        </vone-desc>
      </main>
    </header>

    <section>
      <div class="elRow">
        <strong>
          应用
        </strong>
      </div>
      <main style="height:calc(100vh - 300px)">
        <vxe-table
          ref="lecensesTable"
          class="vone-vxe-table"
          border
          resizable
          height="auto"
          show-overflow="tooltip"
          :empty-render="{ name: 'empty' }"
          :data="tableData"
          row-id="id"
          :column-config="{ minWidth:'120px' }"
        >
          <vxe-column title="应用名称" field="name" width="150" />
          <vxe-column title="状态" field="status" width="150">
            <template #default="{ row }">
              <span v-if="row.failureTime == '-1'" class="closeTime" style="backgroundColor:#00BF80">
                已授权
              </span>
              <span v-else class="closeTime" :style="{'backgroundColor': getColor(row.failureTime)}">
                {{ getText(row.failureTime) }}
              </span>
            </template>
          </vxe-column>
          <vxe-column field="failureTime" title="失效时间" width="250">
            <template #default="{ row }">
              <span>
                {{ row.endTime }}
              </span>
            </template>
          </vxe-column>
          <!-- <vxe-column field="state" title="是否授权" width="100px">
            <template #default="{ row }">
              {{ row.state == 0 ? '激活' : '未激活' }}
            </template>
          </vxe-column> -->
          <vxe-column field="description" title="描述" />
        </vxe-table>
      </main>

    </section>
  </div>
</template>

<script>
import { apiOuthUploadLicenses, apiOuthLicenses } from '@/api/vone/ouath/ouath'

import dayjs from 'dayjs'

const types = {
  0: '企业版',
  1: '标准版',
  2: 'POC版',
  3: '试用版',
  4: '内部版',
  5: '其它'
}

export default {
  data() {
    return {
      upLoading: false,
      basicData: {},
      tableData: [],
      pageLoading: false,
      types
    }
  },
  computed: {
    getColor() {
      return function(count) {
        if (!count) return
        const time = Math.ceil(
          (count - new Date().getTime()) / 1000 / 24 / 3600
        )
        if (time > 15) {
          return '#00BF80'
        } else if (time <= 15) {
          return '#F7A500'
        } else if (time <= 0) {
          return '#DB2C3A'
        } else {
          return '#BFBFBF'
        }
      }
    },
    getText() {
      return function(count) {
        if (!count) return
        const time = Math.ceil(
          (count - new Date().getTime()) / 1000 / 24 / 3600
        )
        if (time > 15) {
          return '已授权'
        } else if (time <= 15) {
          return '临期'
        } else if (time <= 0) {
          return '已失效'
        } else {
          return '未授权'
        }
      }
    }
  },
  mounted() {
    this.getLicenses()
  },
  methods: {
    async handleChange(val) {
      if (val.status != 'ready') return
      try {
        this.upLoading = true

        const { isSuccess, msg, errorMsg } = await apiOuthUploadLicenses({
          licensesFile: val.raw
        })

        this.upLoading = false
        if (!isSuccess) {
          this.$message.warning(errorMsg)
          return
        }
        this.$message.success(msg)

        this.onClose()
      } catch (e) {
        this.upLoading = false
        return
      }
    },
    back() {
      this.$router.go(-1)
    },
    async getLicenses() {
      this.pageLoading = true
      const res = await apiOuthLicenses()
      if (!res.isSuccess) {
        return this.$message.warning(res.msg)
      }
      const tData = res?.extra?.licenses?.licensesApps || []
      this.basicData = res?.extra?.licenses

      tData.forEach(element => {
        element.endTime = this.minDurationTime(element.failureTime)
        element.stateName = element.state == '0' ? '激活' : '未激活'
      })
      this.tableData = tData
      this.pageLoading = false
    },
    minDurationTime(diffValue) {
      if (diffValue == '-1') {
        return '---'
      }
      return dayjs(Number(diffValue)).format('YYYY-MM-DD HH:mm:ss')
    }
  }

}
</script>

<style lang="scss" scoped>
.pageContent{
      .elRow{
      border-bottom: 1px solid var(--el-divider);
      height: 48px;
      line-height: 48px;
      padding: 0 16px;
    }

  header {
    background-color: #fff;
    border-radius: 2px;

  }
  main{
     padding: 16px;
  }
  section{
    margin-top: 10px;
    background-color: #fff;
     border-radius: 2px;

  }
}

	.back {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		padding: 0px;
		gap: 10px;
		width: 32px;
		height: 32px;
		background-color: #FFFFFF;
		box-shadow: 0px 2px 12px rgba(0, 0, 0, 0.1);
		border-radius: 100px;
		margin-right: 16px;
		cursor: pointer;
		i {
			color: var(--main-theme-color);
		}
	}

  .flexRow{
    display: flex;
    align-items: center;
  }

.closeTime{
  color: #fff;
  padding: 3px 6px;
  border-radius: 2px;
  margin-left: 6px;
  font-size: 12px;
}

</style>
