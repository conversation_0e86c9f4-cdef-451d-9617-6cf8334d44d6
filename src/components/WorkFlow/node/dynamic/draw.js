import inherits from 'inherits'

import BaseRenderer from 'diagram-js/lib/draw/BaseRenderer'

import { is } from 'bpmn-js/lib/util/ModelUtil'
import { concat, isFunction, isArray } from 'lodash'

let i = 0

/**
 * 生成节点渲染
 */
export default props => {
  const { renders } = props
  return renders.map(render => {
    const { $inject, canRender, drawShape } = render
    const name = `dynamic_${i++}_fn`
    function dynamicRenderer(eventBus) {
      BaseRenderer.call(this, eventBus, 1500)
      const args = concat.apply([], arguments)

      this.canRender = function(element) {
        if (isArray(canRender)) {
          return canRender.some(type => is(element, type))
        }
        if (isFunction(canRender)) {
          return canRender.apply(this, [element])
        }
        return canRender
      }

      this.drawShape = function(parent, shape) {
        return drawShape.apply(this, [parent, shape, ...args.slice(1)])
      }
    }

    inherits(<PERSON><PERSON><PERSON><PERSON>, Base<PERSON><PERSON>er)

    dynamicRenderer.$inject = ['eventBus', ...$inject]

    return {
      __init__: [name],
      [name]: ['type', dynamicRenderer]
    }
  })
}
