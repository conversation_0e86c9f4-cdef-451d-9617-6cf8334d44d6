import inherits from 'inherits'

import BaseRenderer from 'diagram-js/lib/draw/BaseRenderer'

import { is } from 'bpmn-js/lib/util/ModelUtil'

function JBase(eventBus, bpmnRenderer) {
  BaseRenderer.call(this, eventBus, 1500)

  this.canRender = function(element) {
    return is(element, 'j:Base')
  }

  this.drawShape = function(parent, shape) {
    return bpmnRenderer.handlers['bpmn:Task'](parent, shape)
  }
}

inherits(JB<PERSON>, BaseRenderer)

JBase.$inject = ['eventBus', 'bpmnRenderer']

function JGroup(eventBus, bpmnRenderer) {
  BaseRenderer.call(this, eventBus, 1500)

  this.canRender = function(element) {
    return is(element, 'j:Group')
  }

  this.drawShape = function(parent, shape) {
    return bpmnRenderer.handlers['bpmn:Task'](parent, shape)
  }
}
inherits(JGroup, BaseRenderer)

JGroup.$inject = ['eventBus', 'bpmnRenderer']

export default [
  {
    __init__: ['jBase<PERSON>enderer'],
    jBase<PERSON>enderer: ['type', JBase]
  },
  {
    __init__: ['jGroupRenderer'],
    jGroupRenderer: ['type', JGroup]
  }
]
