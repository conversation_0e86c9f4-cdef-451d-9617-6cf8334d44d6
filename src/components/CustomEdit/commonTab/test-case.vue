<template>
  <vone-div-wrapper title="关联用例">
    <div slot="actions">
      <span v-if="$route.params.id">
        <el-button class="connect-case" size="mini" type="text" icon="el-icon-link" @click="addTask = 1">
          关联
        </el-button>
      </span>
    </div>

    <div class="wrapper">
      <!-- 测试用例 -->
      <div v-loading="pageLoading">

        <section>

          <!-- 关联 -->
          <div v-if="addTask !=null">
            <el-row v-if="addTask == 1" type="flex">
              <el-select v-model="issueForm.projectId" filterable style="width:100%;margin-right: 10px;" @change="changeProject" @focus="setOptionWidth">
                <!-- <el-option v-for="item in projectIdList" :key="item.key" :label="item.name" :value="item.id" :style="{ width: selectOptionWidth }" /> -->

                <el-option-group v-for="group in selectOptions" :key="group.label" :label="group.label">
                  <el-option v-for="item in group.options" :key="item.id" :style="{ width: selectOptionWidth }" :label="item.name" :value="item.id" :disabled="item.disabled" />

                </el-option-group>
              </el-select>

              <el-select
                v-model.trim="issueForm.caseId"
                placeholder="请输入用例名称"
                clearable
                filterable
                remote
                :remote-method="getRequirementList"
                :loading="requireLoading"
                class="requireSelect"
                multiple
                collapse-tags
                @focus="setOptionWidth"
              >
                <el-option v-for="ele in caseList" :key="ele.id" :value="ele.id" :label="ele.name" :disabled="ele.disabled" :style="{ width: selectOptionWidth }" :title="ele.name">
                  {{ ele.caseKey }} {{ ele.name }}
                </el-option>
              </el-select>

            </el-row>
            <div v-if="addTask == 1" class="minbtns">
              <el-button size="mini" style="margin-left:8px" class="miniBtn" @click="addTask = null">
                取消
              </el-button>
              <el-button type="primary" size="mini" style="margin-left:8px" class="miniBtn" :loading="saveLoading" @click="saveTask">
                保存
              </el-button>
            </div>

          </div>

          <el-table v-if="tableData.length != 0" ref="tableData" class="vone-table voneNobg-table" :data="tableData">
            <el-table-column prop="code" label="需求" show-overflow-tooltip min-width="250">
              <template slot-scope="scope">
                <!-- <a> -->
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#el-icon-icon-wenjian" />
                </svg>
                {{ scope.row.caseKey }}
                {{ scope.row.name }}

              </template>
            </el-table-column>
            <el-table-column prop="libraryId" label="外部" show-overflow-tooltip min-width="70">
              <template slot-scope="scope">
                <span v-if="scope.row.libraryId && scope.row.echoMap && scope.row.echoMap.libraryId">

                  <!-- 关联的测试用例库没有关联项目是外部 -->
                  <el-tag v-if="!scope.row.echoMap.libraryId.projectId " type="info">
                    外部
                  </el-tag>
                  <!-- 关联的测试用例库详情里关联的项目不是当前项目也是外部 -->
                  <el-tag v-if="scope.row.echoMap.libraryId.projectId && scope.row.echoMap.libraryId.projectId != $route.params.id" type="info">
                    外部
                  </el-tag>
                </span>

              </template>
            </el-table-column>

            <el-table-column prop="leadingBy" label="处理人" show-overflow-tooltip min-width="110">
              <template slot-scope="scope">
                <span>
                  <vone-user-avatar :avatar-path="getUserInfo(scope.row) ? getUserInfo(scope.row).avatarPath :''" :name="getUserInfo(scope.row) ? getUserInfo(scope.row).name :''" />
                </span>
              </template>
            </el-table-column>

            <el-table-column fixed="right" label="操作" width="50">
              <template slot-scope="scope">
                <i class="iconfont el-icon-icon-line-jiechuguanlian i-text" @click="deleteRelation(scope.row)" />
              </template>
            </el-table-column>
          </el-table>

          <div v-else class="empty-container">
            <svg-icon icon-class="zanwu" class-name="empty-svg" />
            暂无关联内容
          </div>

        </section>
      </div>

    </div>
  </vone-div-wrapper>

</template>

<script>

import {
  apiAlmProjectNoPage
} from '@/api/vone/project/index'

import {
  getCaseByRequirementId,
  issueRequirementTestCase,
  issueTestCaseDel
} from '@/api/vone/alm/index'

import { debounce } from 'lodash'

import { getProductCaseLibrary, getCaseByProjectIdOrLibraryId } from '@/api/vone/testmanage/index'
export default {
  name: 'TestCase',
  components: {
  },
  props: {
    issueInfo: {
      type: Object,
      default: null
    },
    issueId: {
      type: String,
      default: undefined
    },
    projectId: {
      type: String,
      default: null
    },
    tabName: {
      type: String,
      default: null
    },
    typeCode: {
      type: String,
      default: null
    },
    productId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      issueForm: {

      },
      saveLoading: false,
      addCoTask: null,
      noCoProduct: false,
      showAddIssue: null,
      noProduct: false,
      productLoading: false,
      selectOptions: [
        {
          label: '项目',
          options: [{

          }]
        },
        {
          label: '外部',
          options: []
        }
      ],

      hostProduct: '',
      tableData: [],
      productList: [],
      pageLoading: false,
      activeNames: [],
      projectIdList: [], // 项目
      issueTab: {
        relation: 'DEPEND'
      },
      addRelation: false,
      tableLoading: false,
      createSimple: false,
      typeId: '',
      formData: {},

      taskList: [],
      relateRules: {
        relation: [{ required: true, message: '请选择依赖关系', trigger: 'change' }],
        projectId: [{ required: true, message: '请选择项目', trigger: 'change' }],
        relationIssue: [{ required: true, message: '请选择需求', trigger: 'change' }]
      },
      loading: false,
      affectTable: [], // 影响
      connectTable: [], // 关联
      dependTable: [], // 依赖
      relationTable: [],
      issueTable: [], // 父需求
      caseList: [],
      selectOptionWidth: '',
      requireLoading: false,
      addTask: null,
      taskForm: {
        taskId: []
      },
      form: {},
      formTemplete: [],
      formLoading: false,
      allList: []
    }
  },
  computed: {
    getUserInfo() {
      return function(row) {
        return row?.leadingBy && row?.echoMap?.leadingBy
      }
    }
  },
  watch: {
    issueId() {
      this.getProjectList()
      this.getCaseTable() // 测试用例
    },
    immediate: true
  },

  mounted() {
    this.init()
    this.getProjectList()
    this.getCaseTable() // 测试用例
  },
  methods: {
    creactIssueSuccess() {
      this.getCaseTable()
    },
    toRouter(val) {
      if (!this.$permission('reqm-center-require-list')) {
        this.$message.warning('当前登录账户没有查看需求的权限,请联系管理员授权')
        return
      }
      this.$router.push({
        name: 'reqm-center-require-list',
        query: {
          queryId: val.id
        }
      })
    },

    // 下拉框弹出时，设置弹框的宽度
    setOptionWidth(event) {
      this.$nextTick(() => {
        this.selectOptionWidth = event.target.parentNode.parentNode.clientWidth + 'px'
      })
    },
    // 列表
    async getCaseTable() {
      if (!this.issueId) {
        return
      }
      this.pageLoading = true
      const res = await getCaseByRequirementId(
        this.issueId
      )
      this.pageLoading = false
      if (!res.isSuccess) {
        return
      }
      this.tableData = res.data
    },
    // 项目

    async getProjectList() {
      const res = await apiAlmProjectNoPage()

      if (!res.isSuccess) {
        return
      }
      if (this.productId) {
        const resCase = await getProductCaseLibrary(
          this.productId
        )
        if (!resCase.isSuccess) {
          return
        }

        resCase.data.forEach(element => {
          element.type = 'product'
        })

        this.selectOptions[1].options = resCase.data
      } else {
        this.selectOptions[1].options = [{
          id: 0,
          name: '暂无数据',
          disabled: true
        }]
      }

      res.data.forEach(element => {
        element.type = 'project'
      })
      this.selectOptions[0].options = res.data.filter(r => r.id == this.$route.params.id)

      this.allList = this.selectOptions[0].options.concat(this.selectOptions[1].options)
    },

    init() {
      this.$set(this.issueForm, 'projectId', this.projectId || this.$route.params.id)
      this.$set(this.issueForm, 'caseId', [])
      this.caseList = []
      this.addTask = null
    },
    changeProject(val) {
      this.$set(this.issueForm, 'caseId', [])
      this.caseList = []
    },

    // 查用例列表
    getRequirementList: debounce(async function(query, id) {
      const type = this.allList.find(r => r.id == this.issueForm.projectId).type

      if (!query) {
        return
      }

      try {
        this.requireLoading = true

        const res = await getCaseByProjectIdOrLibraryId(
          type, this.issueForm.projectId,
          { name: query })
        this.requireLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }

        this.caseList = res.data
      } catch (e) {
        this.requireLoading = false
      }
    }, 1000),

    async saveTask() {
      this.saveLoading = true
      const res = await issueRequirementTestCase({
        productCaseIds: this.issueForm.caseId,
        requirementIds: [this.issueId]
      })
      this.saveLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success(res.msg)

      this.getCaseTable()

      this.init()
    },
    // 删除依赖关系
    async deleteRelation(row) {
      this.$confirm(`你确定要取消 【${row.name}】的关联关系吗?`, '删除', {
        confirmButtonText: '确认',
        type: 'warning',
        closeOnClickModal: false
      })
        .then(async() => {
          const { isSuccess, msg } = await issueTestCaseDel({
            productCaseIds: [row.id],
            requirementIds: [this.issueId]
          })
          if (!isSuccess) {
            this.$message.error(msg)
            return
          }
          this.$message.success(msg)
          this.getCaseTable()
          this.init()
        })
        .catch(() => { })
    }

  }
}
</script>
<style lang="scss" scoped>
.i-text {
  color: var(--main-theme-color);
  cursor: pointer;
}
.connect-case{
	font-size: 14px;
	font-weight: 400;
}
.wrapper {
  // 列表操作列的图标显示隐藏/
  .table_handler {
    display: none;
  }
  .el-table--enable-row-hover .el-table__body tr:hover > td {
    .table_handler {
      display: inline-block;
    }
  }
  // --------------------
  .shadow_bottom {
    margin: 0 -38px 16px;
    padding-bottom:16px;
    box-shadow: 0px 15px 10px -15px #ccc;
  }
  .row_host {
    padding: 10px 48px 0px 32px;
		align-items: center;
  }
  .section-host {
    margin: 1px 0;
  }

  .empty-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 0 8px;
    // height: 100%;
    height: calc(60vh - 158px);
    .empty-svg {
      width: 50px;
      height: 40px;
    }
  }
}
:deep(.el-button) {
  line-height: 24px;
  height: 24px;
}
.minbtns {
  text-align: right;
:deep(.el-button + .el-button) {
  margin: 12px 0px 12px 12px;
}
}

</style>
