<template>
  <el-dialog
    :close-on-click-modal="false"
    title="数据详情"
    v-model:value="visible"
    :before-close="close"
  >
    <div>
      <vxe-table
        ref="issue-table"
        class="vone-vxe-table"
        border
        resizable
        min-height="200"
        max-height="300"
        show-overflow="tooltip"
        :loading="tableLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column show-overflow-tooltip field="name" title="流水线名称">
          <template v-slot="scope">
            {{
              scope.row.echoMap.pipelineId && scope.row.echoMap.pipelineId.name
            }}
          </template>
        </vxe-column>
        <vxe-column
          show-overflow-tooltip
          field="applicationName"
          title="服务应用"
        >
          <template v-slot="scope">
            {{
              scope.row.echoMap.applicationId &&
              scope.row.echoMap.applicationId.name
            }}
          </template>
        </vxe-column>
        <vxe-column
          show-overflow-tooltip
          field="status"
          title="执行结果"
          width="80"
        >
          <template v-slot="scope">
            <div v-if="scope.row.status == 'FAILED'" class="executeResult">
              <el-icon style="color: #ea6362"><CircleClose /></el-icon
              ><span class="ml-1">失败</span>
            </div>
            <div v-if="scope.row.status == 'IN_PROGRESS'" class="executeResult">
              <el-icon style="color: var(--main-theme-color, #3e7bfa)"
                ><Loading /></el-icon
              ><span class="ml-1">执行中</span>
            </div>
            <div v-if="scope.row.status == 'SUCCESS'" class="executeResult">
              <el-icon style="color: #3cb540"><CircleCheck /></el-icon
              ><span class="ml-1">成功</span>
            </div>
            <div v-if="!scope.row.status" class="executeResult">
              <el-icon style="color: #adb0b8"><QuestionFilled /></el-icon
              ><span class="ml-1">未测</span>
            </div>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <vone-pagination
      ref="pagination"
      :total="tableData.total"
      style="position: static"
      @update="getInitTableData"
    />
    <template v-slot:footer>
      <div class="dialog-footer">
        <el-button @click="close">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import {
  CircleClose,
  Loading,
  CircleCheck,
  QuestionFilled,
} from "@element-plus/icons-vue";
import { $on, $off, $once, $emit } from "../../../../../utils/gogocodeTransfer";
import { getPipDetail } from "@/api/vone/dashboard/index";
import dayjs from "dayjs";
export default {
  components: {
    CircleClose,
    Loading,
    CircleCheck,
    QuestionFilled,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    queryType: {
      type: String,
      default: "",
    },
    layout: {
      type: Object,
      default: () => {},
    },
    dateValue: {
      type: Array,
      default: null,
    },
  },
  data() {
    return {
      tableData: {},
      tableLoading: false,
      prioritList: [],
    };
  },
  computed: {},
  mounted() {
    this.getInitTableData();
  },
  methods: {
    // 初始化进入页面列表
    async getInitTableData(e, type) {
      const tableAttr = this.$refs?.pagination?.pageObj || {
        current: 1,
        size: 20,
      };
      const sortObj = this.$refs.searchForm?.sortObj;
      const params = {
        ...tableAttr,
        ...sortObj,
        extra: {
          ...this.extraData,
        },
        model: {
          projectIds: this.layout.projectId || [],
          orgIds: this.layout.orgId || [],
          productsetIds: this.layout.productId || [],
          startDateStr: dayjs(this.dateValue[0]).format("YYYY-MM"),
          endDateStr: dayjs(this.dateValue[1]).format("YYYY-MM"),
          queryType: this.queryType,
        },
      };

      this.tableLoading = true;
      // this.$set(this.formData, 'projectId', this.$route.params.id)

      const res = await getPipDetail(params);
      this.tableLoading = false;
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      this.tableData = res.data;
    },

    close() {
      $emit(this, "update:visible", false);
    },
  },
  emits: ["update:visible"],
};
</script>

<style lang="scss" scoped>
.topBox {
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  color: #2c2e36;
  width: 180px;
  a {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
  }
  span {
    margin-left: 8px;
    font-size: 26px;
    font-style: normal;
    font-weight: 700;
  }
}
:deep(.vxe-table--body-wrapper) {
  height: 200px !important;
}
</style>
