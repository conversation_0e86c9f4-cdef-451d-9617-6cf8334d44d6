<template>
  <el-form
    ref="form"
    :model="advanceForm"
    label-position="top"
    :rules="advanceFormRules"
  >
    <el-row
      v-for="(item, index) in advanceParamList"
      :key="item.key + item._index"
      :gutter="24"
    >
      <template>
        <el-col :span="8">
          <el-form-item
            v-if="item.key === 'dataStorage'"
            :prop="item.key + item._index"
            label="存储方式"
          >
            <el-select
              v-model="advanceForm[item.key + item._index]"
              :placeholder="`请选择${item.name}`"
              style="width: 100%"
            >
              <el-option label="共享存储" value="true" />
              <el-option label="非共享存储" value="false" />
            </el-select>
          </el-form-item>
          <!-- <el-form-item v-if="item.key === 'scriptDir'" :prop="item.key + item._index" label="脚本目录">
                <el-input v-model="advanceForm[item.key + item._index]" :placeholder="`请输入${item.name}`" />
              </el-form-item> -->
          <el-form-item
            v-if="item.key === 'GIT_EE_VERSION'"
            :prop="item.key + item._index"
            label="选择gitee版本"
          >
            <el-select
              v-model="advanceForm[item.key + item._index]"
              :placeholder="`请选择${item.name}`"
              style="width: 100%"
            >
              <el-option
                v-for="ite in giteeList"
                :key="ite.value"
                :label="ite.name"
                :value="ite.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="item.key === 'GIT_EE_VERSION_ACCOUNT'"
            :prop="item.key + item._index"
            label="是否使用引擎账号"
          >
            <el-select
              v-model="advanceForm[item.key + item._index]"
              :placeholder="`请选择${item.name}`"
              style="width: 100%"
            >
              <el-option label="是" value="true" />
              <el-option label="否" value="false" />
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="item.key === 'VERSION_CONTROL'"
            :prop="item.key + item._index"
            label="版本"
          >
            <el-select
              v-model="advanceForm[item.key + item._index]"
              :placeholder="`请选择版本`"
              style="width: 100%"
            >
              <el-option label="开源社区版" value="1" />
              <el-option label="企业版" value="2" />
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="item.key === 'ACCOUNT_SELECT'"
            :prop="item.key + item._index"
            label="账户"
          >
            <el-select
              v-model="advanceForm[item.key + item._index]"
              :placeholder="`请选择账户`"
              style="width: 100%"
            >
              <el-option label="平台账户" value="1" />
              <el-option label="引擎账户" value="2" />
            </el-select>
          </el-form-item>
          <el-form-item
            v-else-if="
              engineCategoryId !== 'GIT_EE' && engineCategoryId !== 'JFROG'
            "
            :prop="item.key + item._index"
            :label="item.name || item.key"
          >
            <el-input
              v-model="advanceForm[item.key + item._index]"
              :placeholder="`请输入${item.name}`"
            />
          </el-form-item>
        </el-col>

        <el-col v-if="item.isVersion" :span="8">
          <el-form-item
            :prop="`${item.key}_version_${item._index}`"
            label="版本"
          >
            <!-- <el-input :v-model="advanceForm[`${item.key}_version_${item._index}`] ? advanceForm[`${item.key}_version_${item._index}`] : ''" placeholder="请输入版本" /> -->
            <el-input
              v-model="advanceForm[`${item.key}_version_${item._index}`]"
              placeholder="请输入版本"
            />
          </el-form-item>
        </el-col>

        <el-col v-if="item.isMore" :span="8">
          <el-form-item label="操作">
            <el-button
              type="primary"
              :icon="ElIconPlus"
              @click="addRow(item, index)"
            />
            <el-button
              type="danger"
              :icon="ElIconMinus"
              :disabled="!canRemove(item.key)"
              @click="removeRow(index)"
            />
          </el-form-item>
        </el-col>
      </template>
    </el-row>
  </el-form>
</template>

<script>
import {
  Plus as ElIconPlus,
  Minus as ElIconMinus,
} from "@element-plus/icons-vue";
const fieldKeys = {
  scriptDir: "脚本目录",
  dataStorage: "存储方式",
};

let i = 0;

export default {
  data() {
    return {
      giteeList: [
        { name: "开源社区版本", value: "SAAS" },
        { name: "企业定制版本", value: "GitLab" },
        { name: "开源社区增强版本", value: "Gitee" },
      ],
      advanceParamList: [],
      advanceForm: {},
      ElIconPlus,
      ElIconMinus,
    };
  },
  props: {
    advanceParams: {
      type: Array,
      default: () => [],
    },

    engineData: {
      type: Object,
      default: null,
    },
    engineCategoryId: {
      type: String,
      default: "",
    },
  },
  computed: {
    advanceFormRules() {
      const rules = {};
      this.advanceParamList.forEach((item) => {
        rules[item.key + item._index] = [
          {
            required: item.notEmpty,
            message: `${
              fieldKeys[item.key] || item.description || item.key
            }不能为空`,
          },
        ];
        if (item.regexp) {
          rules[item.key + item._index].push({
            pattern: item.regexp,
            message: item.message,
          });
        }
      });
      return rules;
    },
  },
  watch: {
    engineData: {
      immediate: true,
      deep: true,
      handler() {
        if (this.engineData && this.engineData.engineExtendeds) {
          const paramsMap = {};
          this.advanceParams.forEach((p) => {
            paramsMap[p.key] = p;
          });
          const formData = {};
          const advanceParamList = this.engineData.engineExtendeds.map(
            (item) => {
              const param = {
                ...paramsMap[item.key],
                _index: i++,
              };
              formData[param.key + param._index] = item.value;
              return param;
            }
          );

          this.advanceParamList = advanceParamList;
          this.advanceForm = formData;
        } else {
          this.advanceParams.forEach((p) => (p._index = i++));
          this.advanceParamList = this.advanceParams;
        }
      },
    },
  },
  methods: {
    addRow(item, idx) {
      this.advanceParamList.splice(idx + 1, 0, { ...item, _index: i++ });
    },
    removeRow(idx) {
      this.advanceParamList.splice(idx, 1);
    },
    canRemove(key) {
      return (
        this.advanceParamList.filter((item) => item.key === key).length > 1
      );
    },
    async getFormData() {
      await this.$refs.form.validate();

      return this.advanceParamList.map((item) => {
        return {
          key: item.key,
          // attributesName: item.description,
          // value: this.advanceForm[item.key + item._index]
          // attributesLable: item.isVersion ? `${item.key}_${this.advanceForm[`${item.key}_version_${item._index}`]}` : item.key
          value:
            item.isVersion &&
            this.advanceForm[`${item.key}_version_${item._index}`]
              ? `${item.key}_${
                  this.advanceForm[`${item.key}_version_${item._index}`]
                }`
              : this.advanceForm[item.key + item._index],
        };
      });
    },
  },
};
</script>
