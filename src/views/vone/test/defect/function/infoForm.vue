<template>
  <div>
    <el-form ref="infoForm" :model="infoForm" :disabled="infoDisabled" :rules="rules">
      <el-form-item label="缺陷类型" prop="typeCode">
        <el-select v-model="infoForm.typeCode" clearable style="width:100%">
          <el-option v-for="item in typeCodeList" :key="item.key" :label="item.name" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item label="优先级" prop="priorityCode">

        <vone-icon-select v-model="infoForm.priorityCode" filterable :data="prioritList" clearable style="width:100%">
          <el-option v-for="item in prioritList" :key="item.key" :label="item.name" :value="item.code">
            <i :class="`iconfont ${item.icon}`" :style="{color: item.color, fontSize: '16px', paddingRight: '6px'}" />
            {{ item.name }}
          </el-option>
        </vone-icon-select>

      </el-form-item>
      <el-form-item label="缺陷来源" prop="sourceCode">
        <el-select v-model="infoForm.sourceCode" clearable style="width:100%">
          <el-option v-for="item in sourceList" :key="item.key" :label="item.name" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="id" label="状态" prop="stateCode">
        <defectStatus v-if="id" :key="Date.now()" :workitem="infoForm" :info-disabled="infoDisabled" @changeFlow="changeFlow" />
      </el-form-item>
      <el-form-item label="关联需求" prop="requirementId">
        <el-select
          v-model="infoForm.requirementId"
          placeholder="请输入需求名称"
          clearable
          filterable
          remote
          :remote-method="getRequirementList"
          :loading="requireLoading"
          class="requireSelect"
        >
          <el-option v-for="ele in requirementList" :key="ele.id" :value="ele.id" :label="ele.name" :title="ele.name">
            {{ `${ele.code}   ${ele.name}` }}
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="归属项目" prop="projectId">
        <el-select v-model="infoForm.projectId" clearable filterable style="width:100%">
          <el-option v-for="item in projectIdList" :key="item.key" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>

      <!-- <el-form-item label="归属计划" prop="planId">
        <el-select v-model="infoForm.planId" clearable filterable style="width:100%" :disabled="sprintId ? true :false">
          <el-option v-for="item in planIdList" :key="item.key" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="测试计划" prop="testPlanId">
        <el-select v-model="infoForm.testPlanId" clearable filterable style="width:100%">
          <el-option v-for="item in testPlanList" :key="item.key" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item> -->

      <el-form-item label="归属环境" prop="envCode">
        <el-select v-model="infoForm.envCode" clearable style="width:100%">
          <el-option v-for="item in envList" :key="item.key" :label="item.name" :value="item.code" />
        </el-select>
      </el-form-item>

      <!-- <el-form-item label="关联知识库" prop="projectDocuments">
        <el-select v-model="infoForm.projectDocuments" clearable filterable multiple style="width:100%">
          <el-option v-for="item in documentsList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item> -->

      <el-form-item label="提出人" prop="putBy">
        <vone-remote-user v-model="infoForm.putBy" />
      </el-form-item>
      <el-form-item label="处理人" prop="handleBy">
        <vone-remote-user v-model="infoForm.handleBy" />
      </el-form-item>
      <el-form-item label="负责人" prop="leadingBy">
        <vone-remote-user v-model="infoForm.leadingBy" />
      </el-form-item>
      <el-form-item label="规模" prop="estimateHour">
        <el-input-number v-model="infoForm.estimateHour" :min="1" :max="100" :precision="1" :step="1" label="请输入规模" style="width:100%" controls-position="right" />
      </el-form-item>

      <el-form-item label="计划开始时间" prop="planStime">
        <el-date-picker v-model="infoForm.planStime" type="datetime" format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm:ss" default-time="9:00:00" placeholder="选择计划开始时间" />
      </el-form-item>
      <el-form-item label="计划完成时间" prop="planEtime">
        <el-date-picker v-model="infoForm.planEtime" format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm:ss" type="datetime" :picker-options="pickerOptions" default-time="18:00:00" placeholder="选择计划完成时间" @change="changeDate" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { apiAlmRequirementNoPage, apiAlmSourceNoPage } from '@/api/vone/project/issue'
import { getProjectInfoByPlanId, apiAlmProjectNoPage } from '@/api/vone/project/index'

import { apiAlmPriorityNoPage } from '@/api/vone/alm/index'
import { apiBaseDictNoPage } from '@/api/vone/base/dict'
import { apiAlmStateNoPage } from '@/api/vone/base/work-flow'
import { testListByCondition } from '@/api/vone/project/defect'

import { apiAlmIssueInfo } from '@/api/vone/project/issue'
import { findByClassify } from '@/api/vone/reqmcenter/require'
import storage from 'store'
import { debounce } from 'lodash'
import { catchErr } from '@/utils'

import defectStatus from '@/views/vone/project/common/change-status/index.vue'

export default {
  components: {
    defectStatus
  },
  props: {
    id: {
      type: String,
      default: undefined
    },
    infoDisabled: {
      type: Boolean,
      default: false
    },
    sprintId: {
      type: String,
      default: undefined
    }
  },
  data() {
    var validatePass = (rule, value, callback) => {
      if (value > this.infoForm.planEtime) {
        callback(new Error('开始日期不能大于结束日期!'))
      } else {
        callback()
      }
    }
    return {
      pickerOptions: {
        disabledDate: (time) => {
          if (this.infoForm.planStime) {
            return time.getTime() < new Date(this.infoForm.planStime).getTime() - (1 * 24 * 60 * 60 * 1000) + 1
          }
        }
      },
      rules: {
        // estimateHour: [
        //   {
        //     message: '请输入数字',
        //     trigger: 'change'
        //   },
        //   {
        //     pattern: '^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,1})?$',
        //     message: '只允许输入保留一位小数的正数'
        //   }
        // ],
        planStime: [{ validator: validatePass }],
        typeCode: [{ required: true, message: '请输入缺陷类型', trigger: 'change' }],
        sourceCode: [{ required: true, message: '请输入缺陷来源', trigger: 'change' }],
        priorityCode: [{ required: true, message: '请输入优先级', trigger: 'change' }]
      },
      requireLoading: false,
      infoForm: {
        estimateHour: '1'
      },
      sourceList: [], // 缺陷来源
      projectIdList: [], // 归属项目
      stateList: [], // 状态
      prioritList: [], // 优先级
      typeCodeList: [], // 缺陷分类
      envList: [], // 环境
      requirementList: [], // 关联需求
      testPlanList: [] // 测试计划
    }
  },
  mounted() {
    this.getProjectInfo() // 查询项目基本信息

    if (this.sprintId) {
      this.$set(this.infoForm, 'planId', this.sprintId) // 迭代内新增，默认回显当前迭代
    }
  },
  methods: {
    // 回显项目已经关联的产品和项目集
    async getProjectInfo(val) {
      this.getIssueType() // 缺陷分类
      // this.getProductList() // 归属产品
      this.getEnvList() // 环境
      // this.getTestPlanList() // 测试计划
      this.getPrioritList() // 优先级
      this.getSourceList() // 来源
      this.id && this.getStateList() // 状态
      // 手动查询需求
      this.infoForm.requirementId && this.getRequirementData(this.infoForm.requirementId)
      val && val.testPlanId ? this.getProjectInfoByPlanId(val.testPlanId) : this.getProjectList()
      this.$set(this.infoForm, 'projectId', '')
      if (!this.id) {
        const userInfo = storage.get('user')
        this.$set(this.infoForm, 'putBy', userInfo.id)
        this.$set(this.infoForm, 'leadingBy', userInfo.id)
      }
    },
    changeDate(v) {
      if (this.infoForm.planStime < v) {
        this.$refs['infoForm'].clearValidate('planStime')
      }
    },
    // 查询缺陷分类
    async getIssueType() {
      const res = await findByClassify('BUG')
      if (!res.isSuccess) {
        return
      }
      this.typeCodeList = res.data
      if (!this.infoForm.typeCode) {
        const type = this.typeCodeList?.find(v => v.name.includes('功能缺陷'))
        this.$set(this.infoForm, 'typeCode', type?.code)
      }
    },
    async getProjectInfoByPlanId(val) {
      const res = await getProjectInfoByPlanId(val)

      if (!res.isSuccess) {
        return
      }
      this.projectIdList = res.data
    },
    // 查询缺陷来源
    async getSourceList() {
      const res = await apiAlmSourceNoPage({
        typeClassify: 'BUG'
      })
      if (!res.isSuccess) {
        return
      }
      this.sourceList = res.data
      if (!this.infoForm.sourceCode) {
        this.$set(this.infoForm, 'sourceCode', res.data[0]?.code)
      }
    },
    // 归属项目
    async getProjectList() {
      const res = await apiAlmProjectNoPage({})

      if (!res.isSuccess) {
        return
      }
      this.projectIdList = res.data
    },
    // 测试计划
    async getTestPlanList() {
      const res = await testListByCondition({})
      if (!res.isSuccess) {
        return
      }
      this.testPlanList = res.data
    },

    // 查状态
    async getStateList() {
      const res = await apiAlmStateNoPage()
      if (!res.isSuccess) {
        return
      }
      this.stateList = res.data
    },
    // 查优先级
    async getPrioritList() {
      const res = await apiAlmPriorityNoPage()
      this.tableLoading = false
      if (!res.isSuccess) {
        return
      }
      this.prioritList = res.data
      if (!this.infoForm.priorityCode) {
        const priorit = this.prioritList?.find(v => v.name.includes('普通'))
        this.$set(this.infoForm, 'priorityCode', priorit?.code)
      }
    },
    // 查优环境
    async getEnvList(val) {
      const res = await apiBaseDictNoPage({
        type: 'ENVIRONMENT',
        state: true
      })
      if (!res.isSuccess) {
        return
      }
      this.envList = res.data
      this.infoForm.envCode = this.envList?.find(v => v.name.includes('功能测试环境'))?.code
    },
    // 查关联需求信息
    async getRequirementData(id) {
      const [res, err] = await catchErr(apiAlmIssueInfo(id))
      if (err) return
      if (res.isSuccess) {
        this.requirementList = [res.data]
      }
    },
    // 查需求列表
    getRequirementList: debounce(async function(query) {
      if (query && query != '') {
        this.requireLoading = true
        const [res, err] = await catchErr(apiAlmRequirementNoPage({ name: query }))
        this.requireLoading = false
        if (err) return
        if (!res.isSuccess) {
          return
        }
        this.requirementList = res.data
      }
    }, 1000),

    changeFlow() {
      this.$emit('changeFlow')
    },
    async proving() {
      this.$refs.infoForm.validate((valid) => {
        if (valid) {
          this.$emit('save')
        } else {
          return
        }
      })
    },
    // 关联需求change事件,回显当前需求关联的迭代
    async changeIssue(val) {
      this.$set(this.infoForm, 'planId', '')
      const res = await apiAlmIssueInfo(val)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      if (res.data && res.data.planId) {
        this.$set(this.infoForm, 'planId', res.data.planId)
      }
    }

  }

}
</script>

<style lang="scss" scoped>
::v-deep {
  .el-date-editor.el-input,
  .el-date-editor.el-input__inner {
    width: 100%;
  }
  .el-form-item {
    margin-bottom: 6px;
  }
}
.requireSelect {
  width: 100%;
  ::v-deep .el-input__inner {
    padding-right: 30px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
