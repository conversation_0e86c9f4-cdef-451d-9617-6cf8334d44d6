<template>

  <!-- 用户需求关联用户需求 -->
  <vone-div-wrapper :title="tabName">

    <div v-loading="pageLoading" class="boxContent">

      <div>
        <el-row type="flex" style="align-items:center">
          <el-col :span="6">
            <div class="boxTitle">
              关联关系
            </div>
          </el-col>
          <el-col :span="18" style="text-align:right">

            <el-button class="con-idea-idea" type="text" icon="el-icon-link" @click="addRelation = true">关联</el-button>
          </el-col>
        </el-row>

      </div>

      <el-form v-if="addRelation" ref="issueTab" :model="issueTab" :rules="relateRules">
        <el-row :gutter="6" type="flex" class="mt-10" justify="space-between">

          <el-col :span="9">

            <el-form-item prop="relation">
              <el-select v-model="issueTab.relation" placeholder="请选择依赖关系">
                <el-option label="关联" value="RELATION" />
                <el-option label="前置依赖" value="DEPEND" />
                <el-option label="后置影响" value="AFFECT" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item prop="relationIssue">
              <el-select v-model="issueTab.relationIssue" placeholder="请输入用户需求名称" clearable filterable remote :remote-method="getIdeaList" :loading="requireLoading" class="requireSelect" @focus="setOptionWidth">
                <el-option v-for="ele in ideaList" :key="ele.id" :value="ele.id" :label="ele.name" :disabled="ele.disabled" :style="{ width: selectOptionWidth }" :title="ele.name">
                  {{ `${ele.code}   ${ele.name}` }}
                </el-option>
              </el-select>

            </el-form-item>
          </el-col>

          <el-col :span="6" style="text-align:right">

            <el-form-item>
              <el-button class="miniBtn" @click="addRelation = false">取消</el-button>
              <el-button class="miniBtn" type="primary" style="margin-left:8px" @click="addRelationShip">确定</el-button>

            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div v-if="!connectTable.length && !dependTable.length && !affectTable.length" class="empty-container">
        <svg-icon icon-class="zanwu" class-name="empty-svg" />
        暂无关联内容
      </div>

      <el-collapse v-else v-model="activeNames">
        <!-- 关联/------------------- -->
        <el-collapse-item v-if="connectTable.length" name="RELATION">
          <template slot="title">
            <i class="iconfont el-icon-yibiaoban-shuxingyanse colorOrange" />
            <span class="centerText">
              关联
            </span>
            <span class="centerNum">
              {{ connectTable.length }}
            </span>

          </template>
          <needTable :table-data="connectTable" :project-id="projectId" :type-code="typeCode" @success="getRelationTable" />

        </el-collapse-item>
        <!-- 前置依赖--------------------- -->
        <el-collapse-item v-if="dependTable.length" name="DEPEND">
          <template slot="title">
            <i class="iconfont el-icon-yibiaoban-shuxingyanse colorGreen" />
            <span class="centerText">
              前置依赖
            </span>
            <span class="centerNum">
              {{ dependTable.length }}
            </span>

          </template>
          <needTable :table-data="dependTable" :project-id="projectId" :type-code="typeCode" @success="getRelationTable" />

        </el-collapse-item>
        <!-- 后置影响-------------------------- -->
        <el-collapse-item v-if="affectTable.length" name="AFFECT">
          <template slot="title">
            <i class="iconfont el-icon-yibiaoban-shuxingyanse colorPerple" />
            <span class="centerText">
              后置影响
            </span>
            <span class="centerNum">
              {{ affectTable.length }}
            </span>
          </template>
          <needTable :table-data="affectTable" :project-id="projectId" :type-code="typeCode" @success="getRelationTable" />
        </el-collapse-item>
      </el-collapse>

    </div>

  </vone-div-wrapper>
</template>

<script>

import {
  apiAlmProjectRelation,
  apiAlmAddDepend,
  apiAlmPutDepend
} from '@/api/vone/project/issue'

import {
  ideaListByCondition
} from '@/api/vone/reqmcenter/idea'
import { debounce } from 'lodash'
import needTable from './need-table.vue'

import { apiAlmSaveIssue } from '@/api/vone/project/issue'

export default {
  name: 'IdeaToIdea',
  components: {
    needTable

  },
  props: {
    issueId: {
      type: String,
      default: undefined
    },
    projectId: {
      type: String,
      default: null
    },
    tabName: {
      type: String,
      default: null
    },
    typeCode: {
      type: String,
      default: null
    }
  },

  data() {
    return {
      ideaList: [],
      activeNames: ['RELATION', 'DEPEND', 'AFFECT'],
      projectIdList: [], // 项目
      issueTab: {
        relation: 'RELATION'
      },
      addRelation: false,
      tableLoading: false,
      createSimple: false,
      pageLoading: false,
      typeId: '',
      formData: {},
      taskList: [],
      relateRules: {
        relation: [{ required: true, message: '请选择依赖关系', trigger: 'change' }],
        projectId: [{ required: true, message: '请选择项目', trigger: 'change' }],
        relationIssue: [{ required: true, message: '请选择需求', trigger: 'change' }]
      },
      loading: false,
      affectTable: [], // 影响
      connectTable: [], // 关联
      dependTable: [], // 依赖
      relationTable: [],
      parentTable: [], // 父需求
      issueList: [],
      selectOptionWidth: '',
      requireLoading: false,
      addTask: null,
      taskForm: {
        taskId: []
      }
    }
  },
  watch: {
    issueId() {
      this.getRelationTable()
    },
    immediate: true
  },

  mounted() {
    this.getRelationTable()
  },
  methods: {
    init() {
      this.addTask = null
      this.$emit('initList')
    },

    // 下拉框弹出时，设置弹框的宽度
    setOptionWidth(event) {
      this.$nextTick(() => {
        this.selectOptionWidth = event.target.parentNode.parentNode.clientWidth + 'px'
      })
    },

    // 查意向列表
    getIdeaList: debounce(async function(query, projectId) {
      try {
        this.requireLoading = true
        const res = await ideaListByCondition({ name: query })
        this.requireLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }

        res.data.forEach(element => {
          element.disabled = element.id == this.issueId
        })

        this.ideaList = res.data
      } catch (e) {
        this.requireLoading = false
      }
    }, 1000),

    // 查询依赖关系
    async getRelationTable() {
      if (!this.issueId) {
        return
      }
      this.pageLoading = true

      const res = await apiAlmProjectRelation('IDEA', this.issueId)
      this.pageLoading = false
      if (!res.isSuccess) {
        return
      }

      this.connectTable = res.data && res.data.RELATION.length ? res.data.RELATION : []

      this.affectTable = res.data && res.data.AFFECT.length ? res.data.AFFECT : []

      this.dependTable = res.data && res.data.DEPEND.length ? res.data.DEPEND : []
    },

    // 添加依赖关系
    async addRelationShip() {
      try {
        await this.$refs.issueTab.validate()
      } catch (error) {
        return
      }
      const { isSuccess, msg } = await apiAlmAddDepend({
        fromId: this.issueId,
        fromType: this.typeCode,
        relationType: this.issueTab.relation,
        toId: this.issueTab.relationIssue,
        toType: 'IDEA'

      })
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.$message.success(msg)
      this.getRelationTable()
      this.addRelation = false
      this.$set(this.issueTab, 'relation', 'DEPEND')
      this.$set(this.issueTab, 'relationIssue', '')
    },
    // 修改依赖关系
    async EditState(row) {
      const { isSuccess, msg } = await apiAlmPutDepend(
        row.relationId, row.relationType
      )
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.$message.success(msg)
      this.getRelationTable()
    },
    async saveTask() {
      this.saveLoading = true
      const params = this.issueList.find(r => r.id == this.taskForm.relationIssue)
      params.projectId = this.taskForm.projectId || this.$route.params.id
      params.issueId = this.issueId
      const res = await apiAlmSaveIssue(params)
      this.saveLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('创建成功')
      this.$emit('success')
      this.form = {
        name: '',
        typeCode: this.typeCodeList[0].code,
        userId: this.userList[0].id
      }
      this.fileList = []
    }

  }
}
</script>
<style lang="scss" scoped>
.con-idea-idea {
  font-weight: 400;
  font-size: 14px;
}
.boxContent {
  .centerText {
    font-size: 14px;
    font-weight: 400;
    margin: 0 5px;
  }
  .centerNum {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 20px;
    height: 15px;
    background: #ebeef5;
    border-radius: 14px;
    color: var(--main-font-color);
    font-size: 14px;
    font-weight: 400;
  }
  .boxTitle {
    font-weight: 600;
    color: var(--auxiliary-font-color);
  }
}
.mt-10 {
  margin-top: 10px;
}
.colorOrange {
  color: #ffd591;
}
.colorGreen {
  color: #b7eb8f;
}
.colorPerple {
  color: #adc6ff;
}
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 0 8px;
  height: calc(35vh);
  .empty-svg {
    width: 50px;
    height: 40px;
  }
}
</style>
