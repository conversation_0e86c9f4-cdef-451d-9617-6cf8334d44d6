<template>
  <div class="pageBox pageContentNoH">
    <main style="height: calc(100vh - 158rem)">
      <vxe-table
        ref="p-type-table"
        class="vone-vxe-table"
        border
        resizable
        height="auto"
        show-overflow="tooltip"
        :loading="pageLoading"
        :span-method="arraySpanMethod"
        :empty-render="{ name: 'empty' }"
        :scroll-y="{
          enabled: false,
        }"
        :data="typeData"
        row-id="id"
        :column-config="{ minWidth: '120px' }"
      >
        <vxe-column title="分类" field="classify" />
        <vxe-column title="事项类型" field="typeName" />
        <vxe-column title="描述" field="desc" />
        <vxe-column title="操作" fixed="right" align="left" width="140">
          <template #header>
            <el-switch
              v-model="batchSwitch"
              active-color="#13ce66"
              inactive-color="#909399"
              active-text="批量开启"
              :disabled="!$permission('project_setting_work_switch')"
              @change="allSwitch"
            />
          </template>
          <template #default="{ row }">
            <el-switch
              v-model="row.enable"
              active-color="#13ce66"
              inactive-color="#909399"
              :disabled="!$permission('project_setting_work_switch')"
              @change="changeState(row)"
            />
          </template>
        </vxe-column>
      </vxe-table>
    </main>
  </div>
</template>

<script>
import {
  apiProjectFindAllType,
  apiProjectChangeStatePut,
} from '@/api/vone/project/index'
export default {
  data() {
    return {
      batchSwitch: false,
      typeData: [],
      allData: [],
      sourceData: {},
      pageLoading: false,
      // 表格配置项
      tableOptions: {},
      tableOptionsSource: {},
      mergeTableRow(data, merge) {
        if (!merge || merge.length === 0) {
          return data
        }
        merge.forEach((m) => {
          const mList = {}
          data = data.map((v, index) => {
            const rowVal = v[m]
            if (mList[rowVal] && mList[rowVal].newIndex === index) {
              mList[rowVal]['num']++
              mList[rowVal]['newIndex']++
              data[mList[rowVal]['index']][m + '-span'].rowspan++
              v[m + '-span'] = {
                rowspan: 0,
                colspan: 0,
              }
            } else {
              mList[rowVal] = { num: 1, index: index, newIndex: index + 1 }
              v[m + '-span'] = {
                rowspan: 1,
                colspan: 1,
              }
            }
            return v
          })
        })
        return data
      },
    }
  },
  mounted() {
    this.getTableTypeData()
  },
  methods: {
    async getTableTypeData() {
      // this.pageLoading = true
      const { data, isSuccess, msg } = await apiProjectFindAllType(
        this.$route.params.id
      )
      // this.pageLoading = false
      if (!isSuccess) {
        this.$message.error(msg)
        return
      }
      this.allData = data

      this.batchSwitch = data.every((r) => r.enable)

      data.forEach((element) => {
        // 类型名称
        element.typeName =
          element.typeCode && element.echoMap && element.echoMap.typeCode
            ? element.echoMap.typeCode.name
            : element.typeCode
        // 分类名称
        element.classify =
          element.typeCode && element.echoMap && element.echoMap.typeCode
            ? element.echoMap.typeCode.classify.desc
            : ''
        //  描述
        element.desc =
          element.typeCode && element.echoMap && element.echoMap.typeCode
            ? element.echoMap.typeCode.description
            : ''
      })
      // this.typeData = data
      this.typeData = this.mergeTableRow(data, ['classify', 'typeName'])
    },
    async changeState(row) {
      const { isSuccess, msg } = await apiProjectChangeStatePut({
        enable: row.enable,
        projectId: this.$route.params.id,
        typeCode: row.typeCode,
      })
      if (!isSuccess) {
        this.$message.error(msg)
        return
      }
      this.$message.success('修改成功')
      this.getTableTypeData()
    },
    async selecteTableSourceData() {},
    handleClick() {},
    // 合并单元格
    arraySpanMethod({ row, column, columnIndex }) {
      if (columnIndex > 0) {
        return { rowspan: 1, colspan: 1 }
      }
      const span = column['property'] + '-span'
      if (row[span]) {
        return row[span]
      } else {
        return { rowspan: 1, colspan: 1 }
      }
    },
    async allSwitch() {
      const { isSuccess, msg } = await apiProjectChangeStatePut({
        enable: this.batchSwitch,
        projectId: this.$route.params.id,
        typeCodes: this.allData.map((r) => r.typeCode),
      })
      if (!isSuccess) {
        this.$message.error(msg)
        return
      }
      this.$message.success('修改成功')
      this.getTableTypeData()
    },
  },
}
</script>

<style lang="scss" scoped>
.pageBox {
  height: calc(100vh - 48px - 48px - 20px - 10px);
  padding: 16px;
  overflow-y: auto;
}
</style>
