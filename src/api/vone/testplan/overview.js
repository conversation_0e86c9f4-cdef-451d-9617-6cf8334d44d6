import request from '@/utils/axios-api'
// 测试计划下的用例数
export function getOverview(id) {
  return request({
    url: `/api/testm/testPlan/getOverview/${id}`,
    method: 'get'
  })
}
// 测试计划下的用例数
export function findTrendByPlanId(id) {
  return request({
    url: `/api/testm/testPlan/findTrendByPlanId/${id}`,
    method: 'get'
  })
}
// 测试计划下的用例数
export function findBugTrendByPlanId(id) {
  return request({
    url: `/api/testm/testPlan/findBugTrendByPlanId/${id}`,
    method: 'get'
  })
}
// 测试计划下的用例数
export function findUserTrendByPlanId(id) {
  return request({
    url: `/api/testm/testPlan/findUserTrendByPlanId/${id}`,
    method: 'get'
  })
}
