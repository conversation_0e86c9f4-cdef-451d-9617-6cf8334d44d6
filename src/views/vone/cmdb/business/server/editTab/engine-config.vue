<template>
  <!-- 引擎信息 -->
  <div v-loading="loading">
    <vone-search-wrapper v-if="$route.params.type == 2">
      <template v-slot:actions>
        <el-button type="primary" :loading="saveLoading" @click="saveEngineInfo"
          >保存</el-button
        >
      </template>
    </vone-search-wrapper>
    <!-- <el-alert title="未配置引擎标签时，引擎无法使用" type="warning" show-icon :closable="false" class="mb-3" /> -->
    <main
      style="margin-top: 16px"
      :style="{
        height:
          $route.params.type == 2
            ? 'calc(100vh - 418px)'
            : 'calc(100vh - 385px)',
      }"
    >
      <vxe-table
        class="vone-vxe-table"
        border
        resizable
        height="auto"
        show-overflow="tooltip"
        :loading="tableLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
        :span-method="arraySpanMethod"
      >
        <vxe-column title="分类" field="engineClassifyKey">
          <template #default="{ row }">
            {{ row.typeName }}
          </template>
        </vxe-column>
        <vxe-column field="engineInstanceKey" title="实例">
          <template #default="{ row }">
            <span>{{ row.engineInstanceKey }}</span>
          </template>
        </vxe-column>
        <vxe-column field="envKey" title="环境">
          <template #default="{ row }">
            <el-tag v-if="row.envKey">{{ row.envKey }}</el-tag>
            <span v-else>-----</span>
          </template>
        </vxe-column>
        <vxe-column field="engineId" title="引擎">
          <template #default="{ row }">
            <el-select
              v-if="row.engineInstanceKey === 'SCM_AGENT'"
              v-model="row.engineId"
              clearable
              placeholder="请选择名称"
              :disabled="$route.params.type == 1"
            >
              <el-option
                v-for="(item, index) in ScmAgentEngines"
                :key="index"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
            <el-select
              v-else-if="row.engineClassifyKey === 'PACK_MANAGEMENT'"
              v-model="row.engineId"
              clearable
              placeholder="请选择名称"
              :disabled="$route.params.type == 1"
            >
              <el-option
                v-for="(item, index) in row.engineList"
                :key="index"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
            <el-select
              v-else
              v-model="row.engineId"
              multiple
              placeholder="请选择名称"
              :disabled="$route.params.type == 1"
            >
              <el-option
                v-for="(item, index) in row.engineList"
                :key="index"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </template>
        </vxe-column>
      </vxe-table>
    </main>
    <!-- <el-row type="flex" class="mt-3" justify="center">
          <el-button v-if="$route.params.type == 2" type="primary" :loading="saveLoading" @click="saveEngineInfo">保存</el-button>
        </el-row> -->
  </div>
</template>

<script>
const ENGINE_TYPE = {
  VERSION_MANAGEMENT: '版本管理引擎',
  PACK_MANAGEMENT: '制品库引擎',
  SERVER_MANAGEMENT: '服务器管理引擎',
  DB_ACTUATOR: 'DB执行器引擎',
}

import reduce from 'lodash/reduce'
import pick from 'lodash/pick'
import isArray from 'lodash/isArray'

import {
  apiBaseSelectBaseDatagById,
  apiBuniesUpdateEngineConfig,
  apiBuniesSelectEngineBySystemId,
} from '@/api/vone/cmdb/server'

import { apiBaseEngineNoPage } from '@/api/vone/base/engine'

export default {
  data() {
    return {
      saveLoading: false,
      tableData: [],
      tableDataMap: {},
      tagListMap: {},
      ScmAgentEngines: [],
      engineListMap: {},
      ENGINE_TYPE,
      baseData: {},
      loading: true,
      tableLoading: false,
      mergeTableRow(data, merge) {
        if (!merge || merge.length === 0) {
          return data
        }
        merge.forEach((m) => {
          const mList = {}
          data = data.map((v, index) => {
            const rowVal = v[m]
            if (mList[rowVal] && mList[rowVal].newIndex === index) {
              mList[rowVal]['num']++
              mList[rowVal]['newIndex']++
              data[mList[rowVal]['index']][m + '-span'].rowspan++
              v[m + '-span'] = {
                rowspan: 0,
                colspan: 0,
              }
            } else {
              mList[rowVal] = { num: 1, index: index, newIndex: index + 1 }
              v[m + '-span'] = {
                rowspan: 1,
                colspan: 1,
              }
            }
            return v
          })
        })
        return data
      },
    }
  },
  async mounted() {
    await this.getBaseData()
    await this.getEngineInfo()
    await Promise.all([this.loadScmEngines()])
    this.loading = false
  },
  methods: {
    async getBaseData() {
      const res = await apiBaseSelectBaseDatagById(this.$route.params.systemId)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.baseData = res.data
      this.createTableData()
    },
    async createTableData() {
      // 实例
      const categoryIds = ['HARBOR', 'FTP', 'SALT_STACK', 'DB_EXECUTOR']

      // 分类
      const categoryParentKeyMap = {
        HARBOR: 'PACK_MANAGEMENT',
        // JFROG: 'PACK_MANAGEMENT',
        FTP: 'PACK_MANAGEMENT',
        SALT_STACK: 'SERVER_MANAGEMENT',
        DB_EXECUTOR: 'DB_ACTUATOR',
      }

      const envs = this.baseData.applicationEnvs.map((r) => r.envKey)

      const tableData = [
        {
          engineClassifyKey: 'VERSION_MANAGEMENT',
          engineInstanceKey: 'SCM_AGENT',
          rowSpan: 1,
        },
      ]
      categoryIds.forEach((categoryId) => {
        envs.forEach((envKey, idx) => {
          tableData.push({
            engineClassifyKey: categoryParentKeyMap[categoryId],
            engineInstanceKey: categoryId,
            envKey: envKey,
            // 用于合并行
            rowSpan: idx === 0 ? envs.length : 0,
          })
        })
      })

      tableData.forEach(async (row) => {
        row.id = `${row.engineInstanceKey}_${row.envKey || ''}`

        if (row.engineInstanceKey != 'SCM_AGENT') {
          const res = await apiBaseEngineNoPage({
            instance: row.engineInstanceKey,
          })

          if (!res.isSuccess) {
            this.$message.warning(res.msg)
            return
          }
          row['engineList'] = res.data
        }
      })

      tableData.forEach((element) => {
        // 名称
        element.typeName = this.ENGINE_TYPE[element.engineClassifyKey]
      })

      this.tableData = this.mergeTableRow(tableData, [
        'engineClassifyKey',
        'typeName',
      ])

      // 列表Map，用于数据回显
      this.tableDataMap = reduce(tableData, (r, v) => (r[v.id] = v) && r, {})
    },
    async getEngineInfo() {
      const res = await apiBuniesSelectEngineBySystemId(
        this.$route.params.systemId
      )
      if (!res.isSuccess) {
        return
      }

      res.data.forEach((item) => {
        const row =
          this.tableDataMap[`${item.engineInstanceKey}_${item.envKey || ''}`]
        if (!row) return
        if (
          row.engineInstanceKey === 'SCM_AGENT' ||
          row.engineClassifyKey === 'PACK_MANAGEMENT'
        ) {
          row['engineId'] = item.engineId
        } else {
          row['engineId'] = row.engineId || []
          row.engineId.push(item.engineId)
        }
      })
    },

    async saveEngineInfo() {
      this.saveLoading = true
      const applicationEngines = []
      this.tableData.forEach((row) => {
        if (isArray(row.engineId)) {
          row.engineId.forEach((engineId) => {
            applicationEngines.push({
              ...pick(row, [
                'engineClassifyKey',
                'engineInstanceKey',
                'envKey',
              ]),
              engineId,
            })
          })
        } else if (row.engineId) {
          const engine = this.ScmAgentEngines.find((e) => e.id == row.engineId)
          if (engine) {
            row.engineTypeid = engine.typeId
          }
          applicationEngines.push(row)
        }
      })
      applicationEngines.forEach((row) => {
        row.applicationId = this.$route.params.systemId
      })

      const res = await apiBuniesUpdateEngineConfig({
        applicationId: this.$route.params.systemId,
        applicationEngines,
      })
      this.saveLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('保存成功')
      // this.getEngineInfo()
    },

    // 合并单元格
    arraySpanMethod({ row, column }) {
      const span = column['property'] + '-span'
      if (row[span]) {
        return row[span]
      }
    },

    async loadScmEngines() {
      const res = await apiBaseEngineNoPage({
        instance: 'SCM_AGENT',
      })
      if (!res.isSuccess) {
        return
      }
      this.ScmAgentEngines = res.data
    },
  },
}
</script>
