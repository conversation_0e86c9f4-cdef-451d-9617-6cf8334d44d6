<template>
  <div class="table-search-view">
    <div class="table-search-main">
      <!-- 筛选器 -->
      <el-dropdown
        v-if="showFilter"
        trigger="click"
        :hide-on-click="false"
        placement="bottom-start"
        @command="handleCommand"
      >
        <span class="el-dropdown-link">
          <span class="fontBlod">
            {{
              dropdownName.length > 10
                ? dropdownName.slice(0, 10) + "..."
                : dropdownName
            }}
          </span>
          <el-icon class="iconfont el-icon--right"
            ><el-icon-direction-down
          /></el-icon>
        </span>
        <template v-slot:dropdown>
          <el-dropdown-menu
            :class="['table-dropdown', showMenu ? 'dropdown-menu' : '']"
          >
            <!-- 保护视图,不可编辑删除 -->
            <el-dropdown-item
              v-for="item in tableFilterData.filter((r) => r.isProtect)"
              :key="item.id"
              :command="item.id"
            >
              {{ item.name }}
            </el-dropdown-item>
            <!-- 自定义视图,可以编辑删除 -->
            <el-dropdown-item
              v-for="(item, index) in tableFilterData.filter(
                (r) => !r.isProtect
              )"
              :key="item.id"
              :divided="index == 0"
              :command="item.id"
            >
              <span>
                {{ item.name }}
                <el-icon class="iconfont del-style"
                  ><el-icon-icon-system-delete
                /></el-icon>
                <!-- <el-tag v-if="item.isDefault">默认</el-tag> -->
              </span>
              <div @click.stop>
                <el-dropdown
                  v-if="showMenu"
                  class="menu-r"
                  trigger="click"
                  placement="bottom-end"
                  @command="menuCommand($event, item, '2')"
                >
                  <el-icon><el-icon-more /></el-icon>
                  <template v-slot:dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="edit">编辑</el-dropdown-item>
                      <el-dropdown-item v-if="!item.isProtect" command="delete"
                        >删除</el-dropdown-item
                      >
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <el-divider v-if="showFilter" direction="vertical" />
      <!-- 显示列 -->
      <el-dropdown
        v-if="showColumn && tableRef"
        :hide-on-click="false"
        trigger="click"
        placement="bottom-start"
        @command="(e) => e && e()"
      >
        <span class="label">
          <span class="weak-title">表头</span>
          显示
          <el-icon class="iconfont el-icon--right"
            ><el-icon-direction-down
          /></el-icon>
        </span>
        <template v-slot:dropdown>
          <el-dropdown-menu class="column-el-dropdown">
            <div class="drag-group">
              <span
                >表头设置
                <el-tooltip
                  content="固定列不允许禁用、拖拽及移动位置"
                  placement="top"
                >
                  <el-icon
                    class="iconfont"
                    style="font-size: 14px; padding-left: 2px"
                    ><el-icon-tips-info-circle
                  /></el-icon>
                </el-tooltip>
              </span>
              <a @click="resetColumns">恢复默认</a>
            </div>
            <draggable
              v-model:value="columns"
              class="drag-wrap"
              handle=".mover"
              animation="300"
              :move="dragMove"
              @end="dragEnd"
            >
              <transition-group tag="span">
                <el-dropdown-item
                  v-for="col in columns"
                  v-show="col.type != 'checkbox' && col.title != '操作'"
                  :key="col.field || col.id"
                >
                  <div class="item-wrap">
                    <div class="drag-item">
                      <span v-if="col.fixed" class="disable-move">
                        <el-icon class="iconfont"
                          ><el-icon-icon-tuodong
                        /></el-icon>
                      </span>
                      <span v-else class="mover">
                        <el-icon class="iconfont"
                          ><el-icon-icon-tuodong
                        /></el-icon>
                      </span>
                      <span>{{ col.title }}</span>
                    </div>
                    <el-switch
                      v-model="col.visible"
                      active-color="#3E7BFA"
                      inactive-color="#BCC2CB"
                      :disabled="col.fixed != undefined"
                      :class="['drag-switch', !col.visible ? 'switch-off' : '']"
                      @change="(val) => operationColum(val, col.field)"
                    />
                  </div>
                </el-dropdown-item>
              </transition-group>
            </draggable>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <!-- 看板卡片展示字段,用于需求模块的需求看板 -->
      <el-dropdown
        v-if="boardColumns.length"
        :hide-on-click="false"
        trigger="click"
        placement="bottom-start"
        @command="(e) => e && e()"
      >
        <span class="label">
          <span class="weak-title">看板</span>
          显示
          <el-icon class="iconfont el-icon--right"
            ><el-icon-direction-down
          /></el-icon>
        </span>
        <template v-slot:dropdown>
          <el-dropdown-menu class="column-el-dropdown">
            <div class="drag-group">
              <span>看板设置 </span>
              <a @click="resetField">恢复默认</a>
            </div>

            <transition-group tag="span">
              <el-dropdown-item
                v-for="col in boardColumns"
                v-show="col.type != 'checkbox' && col.title != '操作'"
                :key="col.field || col.id"
              >
                <div class="item-wrap">
                  <div class="drag-item">
                    <span>{{ col.title }}</span>
                  </div>
                  <el-switch
                    v-model="col.visible"
                    active-color="#3E7BFA"
                    inactive-color="#BCC2CB"
                    :disabled="col.fixed != undefined"
                    :class="['drag-switch', !col.visible ? 'switch-off' : '']"
                    @change="(val) => operationField(val, col.field)"
                  />
                </div>
              </el-dropdown-item>
            </transition-group>
          </el-dropdown-menu>
        </template>
      </el-dropdown>

      <!-- 根据列属性排序 -->
      <show-sort
        v-if="showColumnSort && tableRef"
        :sort-obj="sortObj"
        :columns="columns"
        @changeSort="changeSort"
      />

      <!-- 分组 -->
      <show-group
        v-if="groupingOptions.length != 0"
        :value-obj="valueObj"
        :grouping-options="groupingOptions"
        @change="groupingCommand"
      />

      <!-- 层级 -->
      <show-tree
        v-if="showHierarch"
        :value-obj="valueObj"
        @success="hierarchyCommand"
      />

      <!--布局 -->
      <show-layout
        v-if="showCard"
        :value-obj="valueObj"
        :card-options="cardOptions"
        @success="cardCommand"
      />

      <!-- 设置筛选条件 -->
      <el-popover
        v-if="!hideFilterForm"
        v-model="visible"
        placement="bottom-start"
        :width="width"
        popper-class="table-search-form"
        @show="showPopover"
        @hide="popoverHide"
      >
        <div class="search-main">
          <div class="search-header">
            <span
              style="flex: 1; color: var(--font-main-color); font-weigth: 500"
              >设置筛选条件</span
            >
            <span @click="closePopover">
              <el-icon class="iconfont"><el-icon-icon-shanchu /></el-icon>
            </span>
          </div>
          <div class="search-form">
            <el-form
              v-bind="$attrs"
              ref="searchForm"
              inline
              label-position="top"
            >
              <slot />
              <search-form-model
                v-if="showBasic"
                ref="searchFormModel"
                :default-fileds="defaultFileds"
                :basic-field-form-list="basicFieldFormList"
                @update:basic-field-form-list="basicFieldFormList = $event"
                @updateDefaultFileds="updateDefaultFileds"
                @onTypeChange="onTypeChange"
              />
            </el-form>
          </div>
          <div class="footer">
            <el-button plain @click="cancel">重置</el-button>
            <el-button type="primary" @click="search(false, 'sure')"
              >确定</el-button
            >
          </div>
        </div>
        <template v-slot:reference>
          <span
            :class="[
              'footer',
              fieldNum != 0 ? 'selectStatus' : 'filterBackGround',
            ]"
            :style="{
              color: fieldNum != 0 ? 'var(--main-theme-color,#3e7bfa)' : '',
            }"
          >
            <span>筛选</span>
            <span v-if="fieldNum != 0" class="margin3">
              {{ fieldNum }}
            </span>

            <el-icon class="iconfont" style="padding-left: 4px"
              ><el-icon-application-filter
            /></el-icon>
          </span>
        </template>
      </el-popover>
    </div>

    <span>
      <el-tooltip
        v-if="showSaveAs && showFilter && checkViewObj.isProtect"
        class="item"
        effect="dark"
        content="将当前变更视图另存为新视图"
        placement="top-start"
      >
        <el-button class="actionBtn" @click="viewCommand('add')">
          另存为
        </el-button>
      </el-tooltip>
      <el-tooltip
        v-if="showSaveAs && showFilter && !checkViewObj.isProtect"
        class="item"
        effect="dark"
        content="保存数据到当前视图"
        placement="top-start"
      >
        <el-button class="actionBtn" @click="viewCommand('update')">
          更新
        </el-button>
      </el-tooltip>
      <!-- <el-tooltip v-if="showSaveAs && showFilter" class="item" effect="dark" content="还原当前视图变更" placement="top-start">
            <el-button class="actionBtn" @click="viewCommand('reset')">
              还原
            </el-button>
          </el-tooltip> -->
    </span>

    <!-- 视图新增 -->
    <view-dialog
      v-if="dialogVisible"
      :dialog-visible="dialogVisible"
      @update:dialog-visible="dialogVisible = $event"
      :title="title"
      :view-data="viewForm"
      @update:view-data="viewForm = $event"
      :table-filter-data="tableFilterData"
      :form-json="formJson"
      :table-search-key="tableSearchKey"
      @newSuccess="newSuccess"
      @success="getTableFilter"
    />
  </div>
</template>

<script>
import {
  ArrowUp as ElIconDirectionDown,
  ArrowUp as ElIconIconSystemDelete,
  ArrowUp as ElIconMore,
  ArrowUp as ElIconTipsInfoCircle,
  ArrowUp as ElIconIconTuodong,
  ArrowUp as ElIconIconShanchu,
  ApplicationFilter as ElIconApplicationFilter,
} from "@element-plus/icons-vue";
import { $on, $off, $once, $emit } from "../../utils/gogocodeTransfer";

// columView, treeView , cardView ,  groupView , fixedView , tableId , tableSave
import _, { cloneDeep } from "lodash";
import { getTableFilter, operationFilter } from "@/api/common";
import ViewDialog from "./view-dialog.vue";

import Draggable from "vuedraggable";

import SearchFormModel from "./searchFormModel.vue";
import ShowTree from "./components/searchTree.vue";
import ShowLayout from "./components/searchLayout.vue";
import ShowGroup from "./components/searchGroup.vue";
import ShowSort from "./components/searchSort.vue";
export default {
  components: {
    ViewDialog,
    Draggable,
    SearchFormModel,
    ShowTree,
    ShowLayout,
    ShowGroup,
    ShowSort,
    ElIconDirectionDown,
    ElIconIconSystemDelete,
    ElIconMore,
    ElIconTipsInfoCircle,
    ElIconIconTuodong,
    ElIconIconShanchu,
    ElIconApplicationFilter,
  },
  name: "SearchDynamic",
  props: {
    model: {
      // formdata里只存放业务数据
      type: Object,
      default: () => {},
    },
    extra: {
      // extradata里列表视图相关的数据
      type: Object,
      default: () => {},
    },
    width: {
      type: Number,
      default: 610,
    },
    showFilter: {
      type: Boolean,
      default: true,
    },
    // 表格关联筛选条件key，后端使用。替换tableSearchKey
    tableSearchKey: {
      type: String,
      default: null,
    },
    showMenu: {
      type: Boolean,
      default: true,
    },
    groupingOptions: {
      type: Array,
      default: () => [],
    },
    // 是否显示层级
    showHierarch: {
      type: Boolean,
      default: false,
    },
    showCard: {
      type: Boolean,
      default: false,
    },
    // 是否显示列操作项
    showColumn: {
      type: Boolean,
      default: true,
    },
    cardOptions: {
      type: Array,
      default: () => [],
    },
    // vxe-table 组件对象
    tableRef: {
      type: Object,
      default: () => null,
    },
    // 展示表格列keys
    showColumnsData: {
      type: Array,
      default: () => [],
    },
    // 隐藏表格列keys
    hideColumns: {
      type: Array,
      default: () => [],
    },
    // 展示列sort排序
    showColumnSort: {
      type: Boolean,
      default: false,
    },
    // 禁用初始化请求
    disableInitRequest: {
      type: Boolean,
      default: false,
    },
    // 隐藏筛选条件表单
    hideFilterForm: {
      type: Boolean,
      default: false,
    },
    defaultFileds: {
      type: Array,
      default: () => [],
    },
    showBasic: {
      // 是否为改造后的表格
      type: Boolean,
      default: false,
    },
    boardColumns: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      originalData: [],
      basicFieldFormList: [], // 筛选器回显数据
      tableFilterData: [],
      dropdownName: "全部",
      showSaveAs: false,
      visible: false,
      isDefault: "",
      dialogVisible: false,
      viewForm: null, // 视图表单信息
      valueObj: {}, // 当前视图数据的表单过滤条件search
      checkViewObj: {}, // 当前视图数据
      formJson: {}, // 更新视图传递的表单参数
      viewId: null,
      fieldNum: 0,
      columns: [], // 表格列，用于拖拽排序
      originColumns: [], // 原始列，用于重置
      title: "新增视图",
      columnSortName: "未排序",
      sortObj: {}, // 排序对象
      isBtnSure: false,
      allColumList: [],
      isInit: false,
    };
  },
  computed: {},
  watch: {
    tableRef: {
      deep: true,

      handler(val, oldVal) {
        if (val != oldVal) {
          if (!this.columns.length) {
            this.$nextTick(() => {
              this.getTableColumns();
            });
          }
        }
      },

      immediate: true,
    },
  },
  async mounted() {
    (await !this.disableInitRequest) && this.getTableFilter();
  },
  methods: {
    operationField() {
      // 获取表格所有的列
      const tableColum = this.boardColumns;
      // 获取未选中的列
      const showField = tableColum
        .filter((item) => item.visible)
        ?.map((itm) => itm.title);
      if (!this.showBasic) return;
      this.extra.boardField = showField;
    },
    resetField() {
      this.boardColumns.forEach((element) => {
        element.visible = true;
      });
      const showField = this.boardColumns
        .filter((item) => item.visible)
        ?.map((itm) => itm.title);
      if (!this.showBasic) return;
      this.extra.boardField = showField;
      // this.boardColumns = cloneDeep(this.boardColumns)
    },
    // 获取表格列
    getTableColumns() {
      if (!this.columns.length) {
        const tableColum =
          (this.tableRef && this.tableRef.getTableColumn()?.fullColumn) || [];
        if (this.hideColumns.length > 0) {
          tableColum.forEach((item) => {
            item.visible = !this.hideColumns.includes(item.field);
          });
        }
        if (this.showColumnsData.length > 0) {
          tableColum.forEach((item) => {
            item.visible = this.showColumnsData.includes(item.field);
          });
        }
        this.originColumns = cloneDeep(tableColum);
        this.columns = tableColum;
        this.tableRef && this.tableRef.refreshColumn();

        // 获取表格所有的列
        const allColum = this.tableRef.getTableColumn()?.fullColumn || [];

        this.allColumList = allColum
          .map((itm) => itm.field)
          ?.filter((r) => r != undefined);
      }
    },
    // 拖动过程中的元素校验
    dragMove(e) {
      // 设置fixed属性的列不允许停靠
      if (e?.relatedContext?.element?.fixed != undefined) {
        return false;
      }
    },
    // 拖拽排序结束
    dragEnd(e) {
      if (e.oldIndex == e.newIndex) return;
      // 刷新列
      this.$nextTick(() => {
        this.tableRef?.loadColumn(this.columns);
      });
    },
    // 重置列
    resetColumns() {
      this.columns = cloneDeep(this.originColumns);
      this.tableRef?.loadColumn(this.columns);
    },
    // 操作列字段显示/隐藏
    operationColum(val, col) {
      // 获取表格所有的列
      const tableColum = this.tableRef.getTableColumn()?.fullColumn || [];
      // 获取未选中的列
      const hideColumn = tableColum
        .filter((item) => !item.visible)
        ?.map((itm) => itm.field);

      // 获取选中的列
      const showColumn = _.difference(this.allColumList, hideColumn);

      // 刷新列
      this.tableRef.refreshColumn();

      this.showSaveAs = true;
      this.extra.tableId = null;
      this.extra.columView = showColumn;
      this.formJson.extra = this.extra;
      this.search(false);
    },

    showPopover() {
      // // 切换了固定住的筛选器的数据之后,刷新表单数据
      this.basicFieldFormList.forEach((element) => {
        const val =
          element.type.code == "DATE"
            ? [this.model[element.key].start, this.model[element.key].end]
            : this.model[element.key];
        element.value = val;
        element.optionList = this.defaultFileds.find(
          (r) => r.key == element.key
        ).optionList;
      });

      if (this.$refs.searchFiled) this.$refs.searchFiled.getOptions();
      // Vue3: $listeners 已被移除，事件监听器现在包含在 $attrs 中
      if (this.$attrs?.onShowPopovers) $emit(this, "showPopovers");
    },
    popoverHide() {
      this.$nextTick(() => {
        if (!this.isBtnSure) {
          const initData = cloneDeep(this.originalData);
          this.basicFieldFormList = initData;
        }
        this.isBtnSure = false;
      });
    },
    closePopover() {
      this.visible = false;
    },
    // 层级
    hierarchyCommand(id) {
      this.showSaveAs = true;
      this.extra.tableId = null;
      this.extra.treeView = id;
      this.formJson.extra = this.extra;
      this.search(false);
    },
    // 卡片
    cardCommand(id, type) {
      // 更改父组件参数
      // Vue3: $listeners 已被移除，事件监听器现在包含在 $attrs 中
      if (this.$attrs?.onChangeLayoutType) $emit(this, "changeLayoutType", id);
      this.showSaveAs = true;
      this.extra.tableId = null;
      this.extra.cardView = id;
      this.formJson.extra = this.extra;
      this.search(false);
    },
    // 分组
    groupingCommand(id, type) {
      this.showSaveAs = true;
      this.extra.tableId = null;
      this.extra.groupView = id;
      this.extra.tableSave = true;
      this.formJson.extra = this.extra;
      this.search(false);
    },
    // 根据列sort进行数据排序
    changeSort(val) {
      this.showSaveAs = true;
      this.extra.tableId = null;
      this.sortObj = { ...val };
      this.formJson.sort = val.sort;
      this.formJson.order = val.order;
      this.$nextTick(() => {
        $emit(this, "getTableData");
      });
    },
    viewCommand(val) {
      this.extra.tableId = this.viewId;
      if (val == "add") {
        const hasFilter = this.tableFilterData.filter(
          (item) =>
            item.search && _.isEqual(JSON.parse(item.search), this.formJson)
        );
        if (hasFilter.length) {
          this.$message.warning("已存在相同的视图，名称为" + hasFilter[0].name);
          return;
        }
        this.dialogVisible = true;
        this.title = "新增视图";
      } else {
        this.saveFilterEdit(this.formJson);
      }
      // else if (val == 'reset') {
      //   this.resetForm()
      // }
    },
    // 还原
    // async  resetForm() {
    //   const orginModel = JSON.parse(this.checkViewObj?.search)?.model || {}
    //   const orginExtra = JSON.parse(this.checkViewObj?.search)?.extra || {}
    //   await this.$emit('update:extra', { ...orginExtra })
    //   await this.$emit('update:model', { ...orginModel })

    //   this.showSaveAs = false
    //   this.handleCommand(this.viewId)
    // },
    menuCommand(e, item, type) {
      switch (e) {
        case "delete":
          this.delFilter(item.id);
          break;
        case "edit":
          this.showEdit(item);
          break;
      }
    },
    // 编辑
    showEdit(item) {
      this.viewForm = item;
      this.title = "编辑视图";
      this.dialogVisible = true;
    },
    async newSuccess(data) {
      this.isDefault = data.id;
      this.checkViewObj = data; // 拿到当前数据，用于编辑视图
      this.dropdownName = data?.name;
      this.valueObj = JSON.parse(data?.search);

      const currentModel = JSON.parse(data?.search)?.model;
      await $emit(this, "update:model", currentModel);

      const params = {
        tableCode: this.tableSearchKey,
      };
      getTableFilter(params).then((res) => {
        if (res.isSuccess) {
          this.tableFilterData = res.data;
        }
      });
    },
    // 查询视图
    getTableFilter() {
      if (!this.tableSearchKey) {
        return;
      }
      const params = {
        tableCode: this.tableSearchKey,
      };
      getTableFilter(params).then((res) => {
        if (res.isSuccess) {
          this.tableFilterData = res.data;

          const isDefaultd = res.data.find((e) => e.isDefault) || res.data[0];
          this.handleCommand(isDefaultd?.id, isDefaultd || "true");
        }
      });
    },
    // 选择视图
    async handleCommand(id, type) {
      await $emit(this, "update:extra", {});
      // this.showSaveAs = false
      this.viewId = id;

      // 默认展示的筛选器完整信息
      // 如果是重置, 就默认切回展示全部
      const filterItem =
        this.tableFilterData.find((item) => item.id == id) ||
        this.tableFilterData[0];
      this.checkViewObj = filterItem; // 拿到当前数据，用于编辑视图
      this.dropdownName = filterItem?.name;

      this.valueObj = JSON.parse(filterItem?.search) || {};
      const currentModel = JSON.parse(filterItem?.search)?.model || {};
      const currentExtra = JSON.parse(filterItem?.search)?.extra || {};

      if (this.extra) {
        this.extra.tableId = this.viewId;
        this.extra.tableSave = true;
        this.formJson.extra = currentExtra;
        await $emit(
          this,
          "update:extra",
          currentExtra ? { ...this.extra, ...currentExtra } : currentExtra
        );
      }
      if (this.valueObj.order && this.valueObj.sort) {
        this.sortObj.order = this.valueObj.order;
        this.sortObj.sort = this.valueObj.sort;
      } else {
        this.sortObj = {};
      }

      await $emit(this, "update:model", currentModel);

      // 筛选条件回显
      if (this.valueObj && this.valueObj.model) {
        const keys = Object.keys(this.valueObj?.model) || [];

        const hJson = this.defaultFileds.filter(
          (i, j) => keys.indexOf(i.key) !== -1
        );

        // && i.value !== undefined && i.value !== '' && i.value.length

        const fixedList = currentExtra?.fixedView || [];

        hJson.forEach((element) => {
          const val =
            element.type.code == "DATE"
              ? [
                  this.valueObj.model[element.key].start,
                  this.valueObj.model[element.key].end,
                ]
              : this.valueObj.model[element.key];
          element.value = val;

          element.isFixed = !!fixedList.includes(element.key);
        });

        this.basicFieldFormList = JSON.parse(JSON.stringify(hJson));

        this.originalData = cloneDeep(hJson);
        this.isInit = true;
      }

      // 回显显示列
      if (this.extra?.columView?.length) {
        this.$nextTick(() => {
          this.columns.forEach((item) => {
            const hideCloum = _.difference(
              this.allColumList,
              currentExtra.columView
            );
            if (hideCloum.includes(item.field)) {
              item.visible = false;
            } else {
              item.visible = true;
            }
          });

          // 刷新列
          this.tableRef.refreshColumn();
        });
      } else {
        this.columns.forEach((item) => {
          item.visible = true;
          // 刷新列
          this.tableRef.refreshColumn();
        });
      }

      this.search(true);
    },

    // 更新表单
    updateForm(e) {
      const form = { ...this.valueObj.model, ...e };
      this.valueObj.model = form;
      // this.$emit('update:model', form)
    },
    // 删除视图
    delFilter(id) {
      this.$confirm("是否删除当前视图?", "删除", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        operationFilter([id], "delete").then((res) => {
          if (res.isSuccess) {
            this.$message.success("删除成功");
            if (
              this.dropdownName ==
              this.tableFilterData.find((item) => item.id == id).name
            ) {
              this.cancel();
            }
            this.getTableFilter();
          } else {
            this.$message.warning(res.msg);
          }
        });
      });
    },
    // 重置
    async cancel() {
      this.originalData = [];
      this.visible = false;
      this.showSaveAs = false;
      this.fieldNum = 0;
      this.handleCommand("all");
    },
    // 查询
    async search(flag, type) {
      this.resetField();
      this.isBtnSure = !!type;
      if (this.extra) {
        this.extra.tableId = this.isBtnSure ? null : this.viewId;
      }

      this.visible = false;
      this.showSaveAs = !flag;

      if (this.showBasic) {
        const basicFieldFormList = cloneDeep(this.basicFieldFormList);
        this.originalData = basicFieldFormList;

        const list = this.basicFieldFormList.filter(
          (item) =>
            item.value !== null && item.value !== undefined && item.value !== ""
        );

        this.fieldNum = list.length;
        const fixedList = this.basicFieldFormList
          .filter((r) => r.isFixed)
          .map((j) => j.key);
        this.extra.fixedView = fixedList;
        const bForm = list.reduce((result, item) => {
          if (item.type.code == "DATE") {
            result[item.key] =
              item.value.length > 0
                ? {
                    start: item.value[0],
                    end: item.value[1],
                  }
                : {};
          } else {
            result[item.key] = item.value;
          }
          return result;
        }, {});
        // this.updateDefaultFileds(list)
        this.formJson.model = bForm;
        this.formJson.extra = this.extra;

        await $emit(this, "update:extra", this.extra);
        await $emit(this, "update:model", { ...bForm });
        $emit(this, "getTableData");
        return;
      } else {
        const originalForm = this.$refs.searchForm?.fields?.map((e) => e.prop);
        const formData = _.cloneDeep(this.model);
        // 获取对象中value值不为空的数据
        const filteredObj = Object.entries(formData).reduce(
          (acc, [key, value]) => {
            if (value !== "" && value !== null && value.length) {
              acc[key] = value;
            }
            return acc;
          },
          {}
        );
        // 判断是否有查询条件
        this.fieldNum = _.intersection(
          originalForm,
          Object.keys(filteredObj)
        ).length;
        await $emit(this, "update:model", {
          ...this.valueObj.model,
          ...this.model,
        });
        $emit(this, "getTableData", this.formJson);
      }
    },
    // 更新视图
    saveFilterEdit(val) {
      if (this.checkViewObj.isProtect) {
        return this.$message.warning("该视图为保护视图,不可操作");
      }

      const params = {
        id: this.checkViewObj.id,
        name: this.checkViewObj.name,
        search: JSON.stringify(val),
        table_code: this.tableSearchKey,
        isDefault: true,
        isProtect: false,
        isPublic: false,
      };
      operationFilter(params, "put").then((res) => {
        if (res.isSuccess) {
          this.$message.success(res.msg);
          for (var i in this.tableFilterData) {
            if (this.tableFilterData[i].id == this.checkViewObj.id) {
              this.tableFilterData.splice(i, 1, params);
            }
          }
        } else {
          this.$message.warning(res.msg);
        }
      });
    },
    onTypeChange(val, item) {
      $emit(this, "onTypeChange", val, item);
    },

    // 更新defaultFileds 字段属性
    updateDefaultFileds(data) {
      $emit(this, "update:defaultFileds", data);
    },
  },
  emits: [
    "changeLayoutType",
    "update:model",
    "update:extra",
    "getTableData",
    "onTypeChange",
    "update:defaultFileds",
    "showPopovers",
  ],
};
</script>

<style lang="scss" scoped>
@use "./index.scss" as *;
</style>

<style lang="scss">
@use "./index-global.scss" as *;
</style>
