<template>
  <el-card
    class="progressCard"
    shadow="false"
    :body-style="{ padding: '16px' }"
  >
    <template v-slot:header>
      <span class="milestone">{{ planData.name }} </span>
      <el-button
        class="viewBtn"
        :disabled="!$permission('testm_plan_overview')"
        @click="overview"
      >
        <svg class="icon" aria-hidden="true">
          <use xlink:href="#el-icon-VSMP-duidiezhexiantu" />
        </svg>
        计划概览
      </el-button>
    </template>
    <div class="processLayout">
      <div class="progressBar">
        <div class="header">
          <span class="left">
            <span class="textTitle"
              >{{
                progressData.totalSum
                  ? (
                      (progressData.systemNum / progressData.totalSum) *
                      100
                    ).toFixed(2)
                  : 0
              }}%
            </span>
            <span class="textTitle" style="font-weight: 400">测试通过</span>
            <el-divider direction="vertical" />
            <span class="textTitle" style="font-weight: 400">已测用例</span>
            <span class="textTitle"
              >{{ progressData.totalSum - progressData.undoNum || 0 }}/{{
                progressData.totalSum || 0
              }}</span
            >
          </span>
          <div class="commitBox">
            <span class="childText child-success">成功</span>
            <span class="childText child-warning">阻塞</span>
            <span class="childText child-danger">失败</span>
            <span class="childText child-primary">跳过</span>
            <span class="childText child-info">未测</span>
          </div>
        </div>
        <div class="pB_Container">
          <!-- 成功进度 -->
          <div
            v-if="systemPercent != 0"
            class="successProgress"
            :style="{ width: systemPercent + '%' }"
          >
            <el-popover placement="top-start" width="200" trigger="hover">
              <div>{{ systemPercent }}%测试成功</div>
              <div>
                {{ progressData.systemNum + "/" + progressData.totalSum }}用例
              </div>
              <template v-slot:reference>
                <div class="pointer" />
              </template>
            </el-popover>
          </div>
          <!-- 阻塞进度 -->
          <div
            v-if="blockPercent != 0"
            class="warningProgress"
            :style="{ width: blockPercent + '%' }"
          >
            <el-popover placement="top-start" width="200" trigger="hover">
              <div>{{ blockPercent }}%测试阻塞</div>
              <div>
                {{ progressData.blockingNum + "/" + progressData.totalSum }}用例
              </div>
              <template v-slot:reference>
                <div class="pointer" />
              </template>
            </el-popover>
          </div>
          <!-- 失败进度 -->
          <div
            v-if="smokePercent != 0"
            class="dangerProgress"
            :style="{ width: smokePercent + '%' }"
          >
            <el-popover placement="top-start" width="200" trigger="hover">
              <div>{{ smokePercent }}%测试失败</div>
              <div>
                {{ progressData.smokeNum + "/" + progressData.totalSum }}用例
              </div>
              <template v-slot:reference>
                <div class="pointer" />
              </template>
            </el-popover>
          </div>
          <!-- 跳过进度 -->
          <div
            v-if="skipPercent != 0"
            class="passProgress"
            :style="{ width: skipPercent + '%' }"
          >
            <el-popover placement="top-start" width="200" trigger="hover">
              <div>{{ skipPercent }}%测试跳过</div>
              <div>
                {{ progressData.skipNum + "/" + progressData.totalSum }}用例
              </div>
              <template v-slot:reference>
                <div class="pointer" />
              </template>
            </el-popover>
          </div>
          <!-- 未执行进度 -->
          <div
            v-if="undoPercent != 0"
            class="infoProgress"
            :style="{ width: undoPercent + '%' }"
          >
            <el-popover placement="top-start" width="200" trigger="hover">
              <div>{{ undoPercent }}%未执行测试</div>
              <div>
                {{ progressData.undoNum + "/" + progressData.totalSum }}用例
              </div>
              <template v-slot:reference>
                <div class="pointer" />
              </template>
            </el-popover>
          </div>
        </div>
      </div>
      <div class="progressCount">
        <div class="right-work">
          <div class="inner">
            {{
              Number(progressData.blockingNum) +
                Number(progressData.smokeNum) || 0
            }}
          </div>
          <div class="innerText">失败用例</div>
        </div>
        <div class="right-work">
          <div class="inner">
            {{ defectList.length || 0 }}
          </div>
          <div class="innerText">打开的缺陷</div>
        </div>
        <div class="right-work">
          <div class="inner">
            {{ isNaN(casePercent) ? 0 : casePercent }}
            <span class="percent">%</span>
          </div>
          <div class="innerText">
            <el-tooltip
              style="cursor: pointer"
              effect="dark"
              content="当前计划下的用例数占全部测试用例数的比值"
              placement="right-start"
            >
              <el-icon style="color: var(--auxiliary-font-color)"
                ><el-icon-warning-outline
              /></el-icon>
            </el-tooltip>
            <span>用例库覆盖率</span>
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script>
import { WarningOutline as ElIconWarningOutline } from "@element-plus/icons-vue";
import { apiAlmBugNoPage } from "@/api/vone/project/defect";
export default {
  components: {
    ElIconWarningOutline,
  },
  name: "ProcessBar",
  props: {
    progressData: {
      type: Object,
      default: () => ({}),
    },
    // 项目下所有用例数据
    proCasesLen: {
      type: Number,
      default: 0,
    },
    // 用例覆盖率
    rateOfCoverage: {
      type: Number,
      default: null,
    },
    // 测试计划数据
    planData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      defectList: [],
    };
  },
  computed: {
    // 成功百分比
    systemPercent() {
      return (
        (this.progressData.systemNum / this.progressData.totalSum) *
        100
      ).toFixed(2);
    },
    // 阻塞百分比
    blockPercent() {
      return (
        (this.progressData.blockingNum / this.progressData.totalSum) *
        100
      ).toFixed(2);
    },
    // 失败百分比
    smokePercent() {
      return (
        (this.progressData.smokeNum / this.progressData.totalSum) *
        100
      ).toFixed(2);
    },
    // 跳过百分比
    skipPercent() {
      return (
        (this.progressData.skipNum / this.progressData.totalSum) *
        100
      ).toFixed(2);
    },
    // 未执行百分比
    undoPercent() {
      return (
        (this.progressData.undoNum / this.progressData.totalSum) *
        100
      ).toFixed(2);
    },
    // 覆盖率
    casePercent() {
      return (
        (this.progressData &&
          this.progressData.echoMap &&
          this.progressData.echoMap.rateOfCoverage) ||
        0
      );
    },
  },
  watch: {
    planData(v) {
      if (v && v.id) {
        this.getInitTableData();
      }
    },
  },
  created() {
    this.getInitTableData();
  },
  methods: {
    overview() {
      this.$router.push({
        path: `/test/plan/overview/${this.planData.id}`,
        query: {
          planName: this.planData.name,
          bug: this.planData.bug,
        },
      });
    },
    // 查询用例缺陷列表
    async getInitTableData() {
      const { id } = this.$route.params;
      // 计划不存在，默认缺陷为空
      if (!this.planData.id) {
        this.defectList = [];
        return;
      }
      const params = {
        projectId: id,
        testPlanId: this.planData.id, // 计划id
      };
      const res = await apiAlmBugNoPage(params);
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      this.defectList = res.data;
    },
  },
};
</script>

<style lang="scss" scoped>
.progressCard {
  height: 100%;
  border: none;
  :deep(.el-card__header) {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 56px;
  }
}
.processLayout {
  display: flex;
  align-items: center;
  gap: 16px;
}
.progressBar {
  width: 60%;
  padding: 14px 24px 14px 0;
}
.progressCount {
  display: flex;
  flex: 1;
  height: 94px;
  padding: 16px 0;
  border-radius: 4px;
  background-color: #f0f5ff;
}
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 28px;
  .textTitle {
    color: #262626;
    font-size: 14px;
    font-weight: 600;
  }
}
.milestone {
  display: inline-block;
  height: 24px;
  line-height: 24px;
  margin-left: -16px;
  padding-left: 12px;
  font-weight: 600;
  font-size: 16px;
  border-left: 4px solid var(--main-theme-color, #3e7bfa);
}
.viewBtn {
  background-color: #f5f6fa;
  border-color: #f5f6fa;
}
.pB_Container {
  display: inline-flex;
  width: 100%;
  height: 20px;
  line-height: 20px;
  text-align: right;
  font-size: 12px;
  box-sizing: border-box;
  color: #fff;
  background-color: #e6f3ff;
  box-shadow: inset 0 2px 2px rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  div {
    &:last-child {
      margin-right: 0;
      border-bottom-right-radius: 3px;
      border-top-right-radius: 3px;
    }
    &:first-child {
      border-bottom-left-radius: 3px;
      border-top-left-radius: 3px;
    }
  }
} /*// 进度条显示样式*/
@mixin hoverall {
  border: 2px solid transparent;
  margin: -2px 0px -2px -2px;
}
@mixin transall {
  margin-right: 2px;
  border: 0px solid transparent;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}
.successProgress {
  background-color: #3cb540;
  @include transall;
}
.warningProgress {
  background-color: #ffbf47;
  @include transall;
  &:hover {
    @include hoverall;
  }
}
.dangerProgress {
  background-color: #fa6b57;
  @include transall;
}
.passProgress {
  background-color: #bd7ffa;
  @include transall;
  &:hover {
    @include hoverall;
  }
}
.infoProgress {
  background-color: #adb0b8;
  @include transall;
  &:hover {
    @include hoverall;
  }
} /*// 右侧区域样式*/

.right-work {
  position: relative;
  flex: 1;
  .inner {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0 5px;
    font-weight: 600;
    font-size: 28px;
    color: var(--main-theme-color, #3e7bfa);
  }
  .innerText {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0 5px;
    margin-top: 10px;
  }
  .percent {
    color: #8a8f99;
    font-size: 12px;
  }

  &::after {
    content: "";
    position: absolute;
    right: 0;
    top: calc(50% - 12px);
    width: 1px;
    height: 24px;
    background-color: #c1c8d6;
  }
  &:last-child::after {
    content: initial;
  }
}
.pointer {
  width: 100%;
  height: 100%;
  cursor: pointer;
} /*// 圆圈样式*/
@mixin ball {
  content: "";
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 4px;
} /*// 提示区域*/
.commitBox {
  display: inline-flex;
  align-items: center;
  .child- {
    &success {
      &::before {
        @include ball;
        background: #3cb540;
      }
    }
    &warning {
      &::before {
        @include ball;
        background: #ffbf47;
      }
    }
    &danger {
      &::before {
        @include ball;
        background: #fa6b57;
      }
    }
    &primary {
      &::before {
        @include ball;
        background: #bd7ffa;
      }
    }
    &info {
      &::before {
        @include ball;
        background: #adb0b8;
      }
    }
  }
  .childText {
    font-size: 14px;
    color: #adb0b8;
    &:not(:last-child) {
      margin-right: 10px;
    }
  }
}
.custom-theme-dark {
  .header {
    .textTitle {
      color: #e6e9f0;
    }
  }
  .viewBtn {
    background-color: #3e4657;
    border-color: #3e4657;
    color: var(--main-theme-color, #3e7bfa);
  }
  .progressBar {
    border-color: #495266;
  }
  .progressCount {
    background-color: #333947;
  }
  .right-work {
    &::after {
      background-color: #495266;
    }
  }
}
</style>
