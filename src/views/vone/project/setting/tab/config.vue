<template>
  <user v-if="type == 'user'" />
  <group v-else-if="type == 'group'" />
  <typeSwitch v-else-if="type == 'type_switch'" />
  <kanbanConfig v-else-if="type === 'kanban_config'" />
  <team v-else-if="type === 'team'" />
  <config v-else :type="type" />
</template>

<script>
import user from './user'
import group from './group.vue'
import typeSwitch from './switch.vue'
import kanbanConfig from './kanban/index.vue'
import team from './team/index.vue'
import config from './config-info/index.vue'
export default {
  components: { config, group, user, typeSwitch, kanbanConfig, team },
  props: {
    type: {
      default: undefined,
      type: String,
    },
    name: {
      default: undefined,
      type: String,
    },
  },
  data() {
    return {}
  },
  methods: {},
}
</script>
