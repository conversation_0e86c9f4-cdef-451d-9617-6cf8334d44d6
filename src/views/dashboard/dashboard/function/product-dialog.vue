<template>
  <el-dialog :close-on-click-modal="false" :title="formInfo ? formInfo.title : '通用配置'" v-model="productvisible" @closed="close">
    <el-form ref="form" :model="formData" :rules="rules">
      <el-form-item label="标题" label-width="80px">
        <el-input v-model="formData.title" style="width: 90%" />
      </el-form-item>
      <el-form-item label="产品" label-width="80px" prop="projectId">
        <el-select v-model="formData.projectId" clearable style="width:90%" filterable>
          <el-option v-for="item in projectList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button
        type="primary"
        @click="save"
      >确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { apiProductNoPage } from '@/api/vone/product/index'
import _ from 'lodash'
export default {
  name: 'PipelineDialog',
  props: {
    isEditForm: Boolean,
    productvisible: {
      type: Boolean,
      default: false
    },
    formInfo: {
      type: Object,
      default: null
    },
    layout: {
      type: [Object, Array],
      default: null
    }
  },
  data() {
    return {
      formData: {
        title: this.formInfo.title,
        projectId: ''
      },
      rules: {
        projectId: [{ required: true, message: '请选择产品', trigger: 'blur' }]
      },
      projectList: []
    }
  },
  mounted() {
    if (this.isEditForm) {
      this.formData = this.formInfo
    }
    this.getEnvList()
  },
  methods: {
    changeProject(val) {
      this.getPlanList(val)
    },
    // 获取环境标签
    getEnvList() {
      apiProductNoPage().then(res => {
        if (res.isSuccess) {
          this.projectList = res.data
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    async save() {
      try {
        await this.$refs.form.validate()
      } catch (e) {
        return
      }
      const layouts = _.cloneDeep(this.layout)
      if (this.isEditForm) {
        layouts.map(item => {
          if (item.id == this.formData.id && item.widgetCode == this.formData.key) {
            item.queryParams = JSON.stringify({ ...this.formData })
          }
        })
      } else {
        this.$emit('update:formInfo', {
          ...this.formInfo,
          ...this.formData
        })
        if (this.formData.projectId) {
          this.formData.name = this.projectList.find(item => item.id == this.formData.projectId).name
        }
        layouts.push({
          x: (this.layout.length * 2) % (12),
          y: this.layout.length + (12), // puts it at the bottom
          w: 3,
          h: 3,
          i: this.layout.length + 1,
          queryParams: JSON.stringify({ ...this.formInfo, ...this.formData }),
          widgetCode: this.formInfo.key
        })
      }
      this.$emit('update:layout', layouts)
      this.$emit('update:productvisible', false)
      this.$emit('success')
    },
    close() {
      this.$emit('update:productvisible', false)
      this.$refs.form.resetFields()
    }
  }
}
</script>

