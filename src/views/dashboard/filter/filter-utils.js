
export const operator = [
  { label: '等于', value: '=', types: ['INPUT', 'NUMBER', 'EDITOR', 'INT', 'ICON'] },
  { label: '不等于', value: '!=', types: ['INPUT', 'NUMBER', 'EDITOR', 'INT'] },
  { label: '大于', value: '>', types: ['NUMBER', 'DATE', 'INT'] },
  { label: '小于', value: '<', types: ['NUMBER', 'DATE', 'INT'] },
  { label: '大于等于', value: '>=', types: ['NUMBER', 'INT'] },
  { label: '小于等于', value: '<=', types: ['NUMBER', 'INT'] },
  { label: 'X天内', value: 'BEFORE', types: ['DATE'] },
  { label: '范围', value: 'BETWEEN', types: ['DATE'] },
  { label: '包含', value: 'IN', types: ['SELECT', 'USER', 'LINKED'] },
  { label: '不包含', value: 'NOT IN', types: ['SELECT', 'USER', 'LINKED'] },
  { label: '包含', value: 'LIKE', types: ['INPUT', 'EDITOR'] },
  { label: '不包含', value: 'NOT LIKE', types: ['INPUT', 'EDITOR'] }
]

export const filterApi = {
  'project': 'xxx'
}
