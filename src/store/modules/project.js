import storage from 'store'
const state = {
  routerData: {}, // 项目路由数据
  itemName: undefined,
  requierName: '',
  list: [] // 项目列表
}
const mutations = {
  // 保存项目路由数据

  SAVE_ROUTER(state, payload) {
    state.routerData = payload
    storage.set('projectType', payload)
  },

  SAVE_NAME(state, payload) {
    state.itemName = payload

    // storage.set('projectType', payload)
  },
  SAVE_RE_NAME(state, payload) {
    state.requierName = payload

    // storage.set('projectType', payload)
  },
  UPDATE_LIST(state, payload) {
    state.list = payload
  }
}
const actions = {
  itemProject({ commit }, data) {
    commit('SAVE_ROUTER', data)
  },
  toProjectInfo({ commit }, data) {
    commit('SAVE_NAME', data)
  },
  getName({ commit }, data) {
    commit('SAVE_RE_NAME', data)
  }
}
export default {
  namespaced: true,
  state,
  mutations,
  actions
}
