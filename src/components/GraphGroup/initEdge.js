import { registerEdge } from '@antv/g6'

function initEdge() {
  registerEdge('dice-er-edge', {
    draw(cfg, group) {
      const edge = group.cfg.item
      const sourceNode = edge.getSource().getModel()
      const targetNode = edge.getTarget().getModel()
      // 获取开始节点的索引
      const sourceIndex = sourceNode.attrs.findIndex(
        e => e.key === cfg.sourceKey
      )
      // 获取开始节点索引
      const sourceStartIndex = sourceNode.startIndex || 0

      let sourceY = 45

      if (sourceIndex > sourceStartIndex - 1) {
        sourceY = 45 + (sourceIndex - sourceStartIndex + 0.5) * 100
        sourceY = Math.min(sourceY, 600)
      }
      // 获取指向目标节点的索引
      const targetIndex = targetNode.attrs.findIndex(
        e => e.key === cfg.targetKey
      )
      // 目标节点当前索引
      const targetStartIndex = targetNode.startIndex || 0

      let targetY = 45

      if (targetIndex > targetStartIndex - 1) {
        targetY = (targetIndex - targetStartIndex + 0.5) * 100 + 45
        targetY = Math.min(targetY, 600)
      }

      const startPoint = {
        ...cfg.startPoint
      }
      const endPoint = {
        ...cfg.endPoint
      }

      startPoint.y = startPoint.y + sourceY
      endPoint.y = endPoint.y + targetY
      let shape
      if (sourceNode.id !== targetNode.id) {
        shape = group.addShape('path', {
          attrs: {
            stroke: '#8862FA',
            lineWidth: 1.5,
            path: [
              ['M', startPoint.x, startPoint.y],
              ['L', endPoint.x / 3 + (2 / 3) * startPoint.x, startPoint.y],
              ['L', endPoint.x / 3 + (2 / 3) * startPoint.x, endPoint.y],
              ['L', endPoint.x, endPoint.y]
            ],
            endArrow: true
          },
          visible: cfg.lineType !== 'placeholder',
          name: 'path-shape'
        })
      } else {
        let gap = Math.abs((startPoint.y - endPoint.y) / 3)
        if (startPoint['index'] === 1) {
          gap = -gap
        }
        shape = group.addShape('path', {
          attrs: {
            stroke: '#8862FA',
            path: [
              ['M', startPoint.x, startPoint.y],
              [
                'C',
                startPoint.x - gap,
                startPoint.y,
                startPoint.x - gap,
                endPoint.y,
                startPoint.x,
                endPoint.y
              ]
            ],
            endArrow: true
          },
          name: 'path-shape'
        })
      }

      return shape
    },
    afterDraw(cfg, group) {}
  })
}
export default initEdge
