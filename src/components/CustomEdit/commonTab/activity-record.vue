<template>
  <div v-loading="loading" class="active-style">
    <div v-if="data.length>0">
      <div v-for="item in data" :key="item.id" class="activeStl">
        <ul class="list">
          <li>
            <span v-if="item.echoMap.createdBy">
              <vone-user-avatar :avatar-path="item.echoMap.createdBy.avatarPath" />
            </span>
            <span class="itemTitle text-over">
              <span v-if="item.echoMap && item.echoMap.createdBy" class="username">
                {{ item.echoMap.createdBy.name }}
              </span>
              <span class="desc">
                <span>{{ item.type == 'create'? '创建': '编辑' }}工作项 </span>
                {{ item.day }}前
              </span>
            </span>
          </li>
        </ul>
        <div class="content">
          <div v-for="e in item.items" :key="e.id" class="content-list">
            <div class="content-title">{{ e.recordName }}</div>
            <div class="status" type="danger">
              {{ e.newObject? e.newObject: e.newValue }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <vone-empty v-else />
  </div>
</template>
<script>
import { getIssueUpdateRecord } from '@/api/vone/project/index'
import dayjs from 'dayjs'
export default {
  name: 'Active',
  props: {
    sourceId: {
      type: String,
      default: ''
    },
    sourceType: {
      type: String,
      default: ''
    },
    viewType: { // 判断是详情页面
      type: String,
      default: ''
    },
    projectId: {
      type: String,
      default: undefined
    },
    formId: {
      type: String,
      default: undefined
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      data: [],
      loading: false
    }
  },
  mounted() {
    this.getIssueUpdateRecordFn()
  },
  methods: {
    async getIssueUpdateRecordFn() {
      this.loading = true
      await getIssueUpdateRecord(this.type, this.formId).then(res => {
        if (res.isSuccess) {
          this.data = res.data
          this.data.forEach(item => {
            item.day = dayjs(item.createTime).fromNow(true)
          })
          this.loading = false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.active-style {
  overflow-y: auto;
  height: calc(90vh - 235px - 48px);
  padding:16px;
}
.activeStl {
  padding-bottom: 8px;
  .list {
    margin-right: 4px;
    li {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      height: 34px;
      width: 100%;
      cursor: pointer;
      .username{
        color: #000;
      }
      .itemTitle {
        flex: 1;

        font-weight: 400;

        line-height: 18px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        span {
          margin-right: 12px;
        }
        .desc {
          font-size: 12px;
          color: var(--font-second-color);
        }
      }
    }

    &::-webkit-scrollbar {
      width: 6px;
    }
  }
  .content{
    margin-left: 32px;
    padding-left: 12px;
    border-left: 4px solid #99A0AC;
    .content-list {
      display: flex;
      align-items: center;
      margin-bottom: 4px;
      & + & {
        margin-bottom: 4px;
      }
      .content-title {
        color: var(--font-second-color);
        width: 72px;
        font-size: 12px;
        margin-right: 4px;
      }

      .status {
        max-width: calc(100% - 72px);
        line-height: 18px;
        padding: 0 4px;
        border-radius: 2px;
        font-size: 12px;
        color: #00996B;
        background: #E1FAED;
      }
    }

  }
  .content-list:last-child {
    margin-bottom: 0px;
  }
}

</style>
