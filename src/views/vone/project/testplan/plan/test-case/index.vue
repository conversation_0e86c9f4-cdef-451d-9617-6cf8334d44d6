<template>
  <div>
    <el-row class="header">
      <h4>用例</h4>
      <div class="btnBox">
        <el-button class="btnMore">
          <el-icon class="iconfont"
            ><el-icon-direction-fullscreen-exit
          /></el-icon>
          <el-icon class="iconfont"><el-icon-direction-fullscreen /></el-icon>
        </el-button>
        <el-button
          :icon="elIconEditRelate"
          type="primary"
          :disabled="finished || !$permission('project_test_plan_release')"
          @click="openTestCase"
          >关联用例</el-button
        >
        <el-dropdown trigger="click" @command="(e) => e && e()">
          <el-button class="btnMore"
            ><el-icon class="iconfont"><el-icon-application-more /></el-icon
          ></el-button>
          <template v-slot:dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                :command="unLink"
                :disabled="
                  controllBtn || !$permission('project_test_plan_unrelease')
                "
              >
                <span>批量取消关联</span>
              </el-dropdown-item>
              <el-dropdown-item
                :command="showBatch"
                :disabled="controllBtn || !$permission('test_cases_excute')"
              >
                <span>批量执行用例</span>
              </el-dropdown-item>
              <el-dropdown-item
                :command="setGroup"
                :disabled="controllBtn || !$permission('testm_plan_assign')"
              >
                <span>批量指派</span>
              </el-dropdown-item>
              <el-dropdown-item
                :command="showTestDetails"
                :icon="ElIconVideoPlay"
                :disabled="controllBtn || !$permission('test_cases_excute')"
              >
                <span>执行用例</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-row>
    <main style="height: calc(100vh - 386rem)">
      <vxe-table
        ref="projectTestPlanTable"
        class="vone-vxe-table"
        height="auto"
        border
        resizable
        show-overflow="tooltip"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
        @checkbox-all="selectAllEvent"
        @checkbox-change="selectChangeEvent"
      >
        <vxe-column type="checkbox" width="36" fixed="left" align="center" />
        <vxe-column
          title="用例"
          field="name"
          min-width="290"
          class-name="name_col"
          fixed="left"
        >
          <template #default="{ row }">
            <el-icon
              class="iconfont"
              style="color: var(--main-theme-color, #3e7bfa); margin-right: 4px"
              ><el-icon-testcase
            /></el-icon>
            <span>{{ row.caseKey + " " + row.name }}</span>
          </template>
        </vxe-column>
        <vxe-column title="版本" field="version" />
        <vxe-column title="优先级" field="priority">
          <template #default="{ row }">
            <vone-icon-select
              v-model:value="row.priority"
              :data="prioritList"
              filterable
              :no-permission="
                controllBtn || !$permission('testm_plan_usecase_eidt')
              "
              style="width: 100%"
              @change="changePriority(row, $index)"
            >
              <el-option
                v-for="item in prioritList"
                :key="item.key"
                :label="item.name"
                :value="item.code"
              >
                <i
                  :class="`iconfont ${item.icon}`"
                  :style="{
                    color: item.color,
                    fontSize: '16px',
                    paddingRight: '6px',
                  }"
                />
                {{ item.name }}
              </el-option>
            </vone-icon-select>
          </template>
        </vxe-column>
        <vxe-column title="执行人" field="execBy" width="150">
          <template #default="{ row }">
            <span v-if="row.execBy && row.userData">
              <vone-user-avatar
                :avatar-path="row.userData.avatarPath"
                :name="row.userData.name"
              />
            </span>
          </template>
        </vxe-column>
        <vxe-column title="执行结果" field="testCaseResult">
          <template #default="{ row }">
            <el-tag v-if="row.status === 'system'" :class="row.status"
              >成功</el-tag
            >
            <el-tag v-else-if="row.status === 'smoke'" :class="row.status"
              >失败</el-tag
            >
            <el-tag v-else-if="row.status === 'block'" :class="row.status"
              >阻塞</el-tag
            >
            <el-tag v-else-if="row.status === 'skip'" :class="row.status"
              >跳过</el-tag
            >
            <el-tag v-else class="untest">未测</el-tag>
          </template>
        </vxe-column>
        <vxe-column title="操作" fixed="right" align="left" width="60">
          <template #default="{ row }">
            <span class="operation-icon-main">
              <el-tooltip
                class="tooltipItem"
                content="取消关联"
                placement="top"
              >
                <el-button
                  size="mini"
                  type="text"
                  :icon="ElIconLink"
                  :disabled="
                    planData.stateId != 1 ||
                    !$permission('project_test_plan_unrelease')
                  "
                  @click="delRow(row)"
                />
              </el-tooltip>
            </span>
          </template>
        </vxe-column>
      </vxe-table>
    </main>

    <!-- 新增关联用例 -->
    <relateCase
      v-model:value="relateCaseVisible"
      :library-id="libraryId"
      :case-list="tableData.records"
      :plan-data="planData"
      :tree-data="treeData"
      :user-map="userMap"
      @success="saveCases"
    />
    <!-- 批量执行 -->
    <el-dialog
      title="批量执行"
      width="400px"
      v-model:value="batchVisible"
      :close-on-click-modal="false"
    >
      <el-row type="flex" align="middle" justify="center">
        <el-button
          type="success"
          class="system"
          @click="saveCaseExecuteResult('system')"
        >
          <el-icon class="iconfont"><el-icon-tips-check-circle-fill /></el-icon>
          通过
        </el-button>
        <el-button
          type="danger"
          class="smoke"
          @click="saveCaseExecuteResult('smoke')"
        >
          <el-icon class="iconfont"><el-icon-tips-close-circle-fill /></el-icon>
          失败
        </el-button>
        <el-button
          type="warning"
          class="block"
          @click="saveCaseExecuteResult('block')"
        >
          <el-icon class="iconfont"><el-icon-tips-minus-circle-fill /></el-icon>
          阻塞
        </el-button>
        <el-button
          type="primary"
          class="skip"
          @click="saveCaseExecuteResult('skip')"
        >
          <el-icon class="iconfont"><el-icon-shijian1 /></el-icon>
          跳过
        </el-button>
      </el-row>
    </el-dialog>
    <!-- 分组指派  -->
    <setGroupDialog
      v-bind="setGroupParam"
      v-if="setGroupParam.visible"
      v-model:value="setGroupParam.visible"
      :plan-data="planData"
      @success="$emit('success')"
    />
  </div>
</template>

<script>
import {
  $on,
  $off,
  $once,
  $emit,
} from "../../../../../../utils/gogocodeTransfer";
import storage from "store";
import { savePlanReleaseCases } from "@/api/vone/testmanage/plan";
import {
  deletePlanCases,
  batchExecutionCase,
} from "@/api/vone/testmanage/case";
import { catchErr } from "@/utils";
import setGroupDialog from "./set-group.vue";
import relateCase from "./relate-case.vue";

import { updatePrioritys } from "@/api/vone/testmanage/case";
export default {
  components: {
    relateCase,
    setGroupDialog,
  },
  props: {
    // 用例库
    libraryId: {
      type: String,
      default: "",
    },
    treeData: {
      type: Array,
      default: () => [],
    },
    tableData: {
      type: Object,
      default: () => {},
    },
    planData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      // <el-button icon="el-icon-link" :disabled="controllBtn || !$permission('project_test_plan_unrelease')" @click="unLink">取消关联</el-button>
      //   <el-button icon="el-icon-video-play" :disabled="controllBtn ||!$permission('test_cases_excute')" @click="showTestDetails">执行用例</el-button>
      //   <el-button t icon="el-icon-video-play" :disabled="controllBtn || !$permission('test_cases_excute')" @click="showBatch">批量执行</el-button>
      //   <el-button icon="iconfont el-icon-application-appoint" :disabled="controllBtn||!$permission('testm_plan_assign')" @click="setGroup">批量指派</el-button>

      actions: [
        {
          disabled: !this.$permission("test_cases_excute") || this.controllBtn,
          name: "批量执行用例",
          fn: this.showBatch,
        },
        {
          disabled:
            !this.$permission("project_test_plan_unrelease") ||
            this.controllBtn,
          name: "批量取消关联",
          fn: this.unLink,
        },
        {
          disabled: !this.$permission("testm_plan_assign") || this.controllBtn,
          name: "批量指派",
          fn: this.setGroup,
        },
        {
          disabled: !this.$permission("test_cases_excute") || this.controllBtn,
          name: "执行用例",
          icon: "el-icon-video-play",
          fn: this.showTestDetails,
        },
      ],
      isLarge: false,
      setGroupParam: {
        visible: false,
      },
      currentCasekey: "",

      prioritList: [
        {
          name: "最高",
          code: 5,
          icon: "el-icon-icon-dengji-zuigao2",
          color: "#FA6A69",
        },
        {
          name: "较高",
          code: 4,
          icon: "el-icon-icon-dengji-jiaogao2",
          color: "#FA8669",
        },
        {
          name: "普通",
          code: 3,
          icon: "el-icon-icon-dengji-putong2",
          color: "var(--main-theme-color,#3e7bfa)",
        },
        {
          name: "较低",
          code: 2,
          icon: "el-icon-icon-dengji-jiaodi2",
          color: "#5ACC5E",
        },
        {
          name: "最低",
          code: 1,
          icon: "el-icon-icon-dengji-zuidi2",
          color: "#4ECF95",
        },
      ],
      caseExctBy: {}, // 用例执行人数据

      batchVisible: false, // 批量执行弹框
      relateCaseVisible: false, // 关联用例弹窗
      userMap: {},
      selection: [], // 选中的用例数据
      isLarge: false,
    };
  },
  computed: {
    loginUser() {
      return storage.get("user");
    },
    controllBtn() {
      return !this.planData.id || this.planData.stateId != 1;
    },
    finished() {
      return !this.planData.id || this.planData.stateId == 2;
    },
  },
  created() {
    // this.getUserList()
  },
  methods: {
    // 批量指派保存
    appointSuccess() {
      $emit(this, "getPlanCaseList", this.planData.id);
    },
    async getUserDatas(userId) {
      const res = await this.$store.dispatch("user/getUserData", userId);
      if (!res.isSuccess) {
        return;
      }
      this.caseExctBy = res.data || {};
    },
    // 批量指派
    setGroup() {
      if (!this.selection.length) {
        this.$message.warning("请选择用例");
        return;
      }
      this.setGroupParam = { visible: true, selectData: this.selection };
    },
    async changePriority(row, index) {
      const [res, err] = await catchErr(
        updatePrioritys({
          planId: this.planData.id,
          priority: row.priority,
          testcaseId: row.testcaseId,
        })
      );
      if (err) return;
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      this.$message.success("修改成功");
      res.data.execBy && (await this.getUserDatas(res.data.execBy));
      res.data.userData = this.caseExctBy; // 执行人

      this.tableData.records.splice(index, 1, res.data);
    },
    saveCaseExecuteResult(val) {
      var derArr = [];
      this.selection.map((r) =>
        derArr.push({ id: r.testcaseId, version: r.version })
      );
      batchExecutionCase(this.planData.id, val, derArr).then((res) => {
        if (res.isSuccess) {
          this.$message.success("执行计划用例成功");
          this.batchVisible = false;
          $emit(this, "success");
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    showBatch() {
      if (this.selection.length === 0) {
        this.$message.warning("请选择批量执行的用例");
        return;
      }
      this.batchVisible = true;
    },
    // 关联用例弹窗
    openTestCase() {
      this.relateCaseVisible = true;
    },
    // 执行用例
    showTestDetails(row) {
      const { projectKey, projectTypeCode, id } = this.$route.params;
      this.$router.push({
        path: `/project/testplan/excute/${projectKey}/${projectTypeCode}/${id}`,
        query: {
          planId: this.planData.id,
          planName: this.planData.name,
        },
      });
    },
    unLink() {
      if (this.selection.length === 0) {
        this.$message.warning("请选择取消关联的用例");
        return;
      }
      this.delRow(this.selection);
    },
    // 获取选择值
    selectAllEvent({ checked }) {
      this.selection = this.$refs.projectTestPlanTable.getCheckboxRecords();
    },
    selectChangeEvent({ checked }) {
      this.selection = this.$refs.projectTestPlanTable.getCheckboxRecords();
    },
    // 删除用例
    async delRow(row) {
      try {
        await this.$confirm("确认取消关联吗？", "提示", {
          type: "warning",
        });
        const testCaseIds = Array.isArray(row)
          ? row.map((v) => v.testcaseId)
          : [row.testcaseId];
        const params = {
          testCaseIds,
          planId: this.planData.id,
        };
        const res = await deletePlanCases(params);
        if (res.isSuccess) {
          this.$message.success("取消关联成功");
          // 刷新用例
          $emit(this, "success");
        }
      } catch (e) {
        if (e === "cancel") return;
      }
    },
    // 关联用例
    async saveCases({ caseIds, treeMap, execBy }) {
      const params = {
        createdBy: this.loginUser.id,
        execBy: execBy,
        connectTreeId: [],
        planId: this.planData.id,
        testcaseIds: caseIds,
        caseAndTreeReturnVo: treeMap,
      };
      const [res, err] = await catchErr(savePlanReleaseCases(params));
      if (err) return;
      if (res.isSuccess) {
        this.$message.success("关联成功");
        // this.getTreeData()
        $emit(this, "success");
      } else {
        this.$message.error(res.msg);
      }
    },
    changeLarge(type) {
      this.isLarge = type;
      $emit(this, "changeLarge", type);
    },
  },
  emits: ["success", "getPlanCaseList", "changeLarge"],
};
</script>

<style lang="scss" scoped>
.header {
  margin: -16px -16px 16px -16px;
  border-bottom: 1px solid var(--solid-border-color);
  padding: 0px 16px;
  height: 48px;
  h4 {
    display: inline-block;
  }
  .btnBox {
    float: right;
    display: inline-block;
    padding-top: 8px;
  }
}
.system {
  color: #fff;
  background-color: #3cb540;
  border-color: #3cb540;
}
.smoke {
  color: #fff;
  background-color: #fa6b57;
  border-color: #fa6b57;
}
.block {
  color: #fff;
  background-color: #ffbf47;
  border-color: #ffbf47;
}
.skip {
  color: #fff;
  background-color: #bd7ffa;
  border-color: #bd7ffa;
}
.untest {
  color: #fff;
  background-color: #adb0b8;
  border-color: #adb0b8;
}
.operation-icon-main {
  display: flex;
  align-items: center;
  .el-button {
    padding: 0px;
    height: unset;
    line-height: unset;
    min-width: unset;
    font-size: 16px;
  }
}
</style>
