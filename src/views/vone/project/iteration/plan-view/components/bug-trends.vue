<template>
  <!-- 缺陷趋势 -->
  <vone-echarts-card :title="'缺陷趋势'">
    <vone-echarts :options="options" />
    <!-- <vone-empty v-else /> -->
  </vone-echarts-card>
</template>

<script>
import { apiProjectBugComponent } from '@/api/vone/project/index'
export default {
  props: {
    keys: {
      type: String,
      default: null,
    },
  },

  data() {
    return {
      trendsData: null,
      options: {},
    }
  },
  watch: {},
  mounted() {
    this.getProjectOptions()
  },
  methods: {
    async getProjectOptions() {
      const { data, isSuccess, msg } = await apiProjectBugComponent(
        this.$route.params.id
      )
      if (!isSuccess) {
        this.$message.error(msg)
        return
      }
      this.trendsData = data
      this.options = {
        color: ['#7486eb', '#6ad2a8', '#f68483'],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          backgroundColor: '#fff',
          borderColor: 'none',
          extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);', // 附加阴影样式
          textStyle: {
            color: '#53565C',
          },
        },
        legend: {
          x: 'right', // 居右显示
          itemHeight: 12,
          itemWidth: 24,
          borderRadius: 5,
          data: [
            {
              name: '剩余',
              icon: 'rect',
            },
            {
              name: '修复',
              icon: 'rect',
            },
            {
              name: '新增',
              icon: 'rect',
            },
          ],
          textStyle: {
            // 图例文字的样式
            color: '#8A8F99',
          },
        },
        grid: {
          left: '6%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: data.date,
          boundaryGap: false,
          splitLine: {
            show: false, // 去掉网格线
          },
          axisLabel: {
            show: true,
            // interval: 5,
            // rotate: 40,
            textStyle: {
              color: '#8A8F99', // 更改坐标轴文字颜色
            },
          },
          axisLine: {
            lineStyle: {
              color: '#EBEEF5', // 更改坐标轴颜色
            },
          },
        },
        yAxis: {
          type: 'value',
          minInterval: 1,
          axisTick: {
            // y轴刻度线
            show: false,
          },
          splitLine: {
            // 网格线
            lineStyle: {
              type: 'dashed', // 设置网格线类型 dotted：虚线   solid:实线
              width: 1,
              color: '#EBEEF5',
            },
          },
          axisLabel: {
            show: true,

            textStyle: {
              color: '#8A8F99', // 更改坐标轴文字颜色
            },
          },
          axisLine: {
            lineStyle: {
              color: '#EBEEF5', // 更改坐标轴颜色
            },
          },
        },
        series: [
          {
            name: '剩余',
            type: 'line',
            // stack: 'Total',
            symbol: 'none',
            data: data.residue,
          },
          {
            name: '修复',
            type: 'line',
            // stack: 'Total',
            symbol: 'none',
            data: data.close,
            smooth: false, // 关键点，为true是不支持虚线的，实线就用true
            itemStyle: {
              normal: {
                lineStyle: {
                  width: 2,
                  type: 'dotted', // 'dotted'虚线 'solid'实线
                },
              },
            },
          },
          {
            name: '新增',
            type: 'line',
            // stack: 'Total',
            symbol: 'none',
            data: data.add,
            smooth: false, // 关键点，为true是不支持虚线的，实线就用true
            itemStyle: {
              normal: {
                lineStyle: {
                  width: 2,
                  type: 'dotted', // 'dotted'虚线 'solid'实线
                },
              },
            },
          },
        ],
      }
    },
  },
}
</script>
