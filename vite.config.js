import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import { fileURLToPath, URL } from 'node:url'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
// import { plugin as markdown } from 'vite-plugin-markdown'
import { lazyImport, VxeResolver } from 'vite-plugin-lazy-import'

export default ({ command, mode }) => { 
  const root = process.cwd()
  const env = loadEnv(mode, root)
  return defineConfig({
    root: process.cwd(),
    publicDir: 'public',
    assetsInclude: ['**/*.bpmn'],
    plugins: [
      vue(),
      // 自动导入Element Plus
      AutoImport({
        resolvers: [ElementPlusResolver()]
      }),
      Components({
        resolvers: [ElementPlusResolver()],
        // 配置组件扫描目录，避免冲突
        dirs: ['src/components'],
        // 排除有冲突的组件目录
        exclude: [
          /\/CustomEdit\/commonTab\//,
          /\/Lane\//
        ],
        // 只扫描特定文件类型
        extensions: ['vue'],
        // 生成类型声明文件
        dts: true
      }),
      // SVG图标插件
      createSvgIconsPlugin({
        iconDirs: [resolve(process.cwd(), 'src/icons/svg')],
        symbolId: 'icon-[dir]-[name]'
      }),
      // 按需引入vxe-pc-ui
      lazyImport({
        resolvers: [
          VxeResolver({
            libraryName: 'vxe-table'
          }),
          VxeResolver({
            libraryName: 'vxe-pc-ui'
          })
        ]
      })
    ],

    esbuild: {
      loader: 'jsx',
      include: /src\/.*\.[jt]sx?$/,
      exclude: []
    },

    define: {
      __VUE_OPTIONS_API__: true,
      __VUE_PROD_DEVTOOLS__: false
    },

    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        'vue': '@vue/compat'
      }
    },

    css: {
      preprocessorOptions: {
        scss: {
          // 使用现代Sass API
          api: 'modern-compiler',
          // 移除additionalData避免循环导入
          // additionalData: `@import "@/styles/variables.scss";`,
        }
      }
    },

    server: {
      port: 8888,
      open: true,
      proxy: {
        '/api': {
          target: 'http://172.16.10.135:8770',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, '')
        }
      }
    },

    build: {
      outDir: 'dist',
      assetsDir: 'static',
      sourcemap: false,
      rollupOptions: {
        output: {
          chunkFileNames: 'static/js/[name]-[hash].js',
          entryFileNames: 'static/js/[name]-[hash].js',
          assetFileNames: 'static/[ext]/[name]-[hash].[ext]',
          manualChunks: {
            'element-plus': ['element-plus'],
            'vue-vendor': ['vue', 'vue-router', 'vuex'],
            'echarts': ['echarts'],
            'codemirror': ['codemirror']
          }
        }
      }
    },

    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'vuex',
        'element-plus',
        'echarts',
        'dayjs',
        'axios',
        'lodash',
        'qs',
        'js-cookie',
        'nprogress'
      ]
    }
  })
}
