import request from '@/utils/axios-api'

//  项目集风险列表
export function apiProgramPage(data) {
  return request({
    url: '/api/alm/alm/risk/page',
    method: 'post',
    data
  })
}
// 添加
export function apiAddProgramPage(data) {
  return request({
    url: '/api/alm/alm/risk',
    method: 'post',
    data
  })
}
// 修改
export function apiEditProgramPage(data) {
  return request({
    url: '/api/alm/alm/risk',
    method: 'put',
    data
  })
}
// 删除
export function apiDelProgramPage(data) {
  return request({
    url: '/api/alm/alm/risk',
    method: 'delete',
    data
  })
}
// details
export function detailRisk(id) {
  return request({
    url: `/api/alm/alm/risk/${id}`,
    method: 'get'
  })
}
