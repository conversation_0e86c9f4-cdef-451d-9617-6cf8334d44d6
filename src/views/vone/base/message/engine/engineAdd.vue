<template>
  <el-dialog
    size="md"
    :title="title"
    v-model:value="visible"
    :before-close="onClose"
    :wrapper-closable="false"
    width="50%"
    :close-on-click-modal="false"
  >
    <el-form
      ref="addForm"
      v-loading="loading"
      :model="addForm"
      :rules="rules"
      label-position="top"
    >
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="名称" prop="name">
            <el-input v-model="addForm.name" placeholder="请输入名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="引擎类型" prop="type">
            <el-select
              v-model="addForm.type"
              placeholder="请选择引擎类型"
              filterable
              :disabled="id ? true : false"
              @change="changeEngine"
            >
              <el-option
                v-for="item in engineTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="引擎维护人" prop="engineMaintainers">
            <vone-remote-user
              v-model:value="addForm.engineMaintainers"
              multiple
            />
          </el-form-item>
        </el-col>

        <el-col v-for="(template, i) in advanceTemplete" :key="i" :span="12">
          <el-form-item :prop="template.key" :label="template.name">
            <el-input
              v-if="template.inputType.code === 'INPUT'"
              v-model="addForm[template.key]"
              :placeholder="template.name"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="addForm.status">
              <el-radio :label="true">启用</el-radio>
              <el-radio :label="false">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="描述" prop="description">
            <el-input
              v-model.trim="addForm.description"
              type="textarea"
              :rows="2"
              placeholder="请输入描述"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template v-slot:footer>
      <el-row>
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="saveUser"
          >确定</el-button
        >
      </el-row>
    </template>
  </el-dialog>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'

import {
  apiBaseMessageEngineHandle,
  apiBaseMessageEngineInfo,
  apiBaseMsgEngineExtend,
} from '@/api/vone/base/message'
export default {
  props: {
    id: {
      type: String,
      default: null,
    },
    title: {
      type: String,
      default: null,
    },
    visible: {
      type: Boolean,
      default: false,
    },
    engineTypes: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      advanceTemplete: [],
      loading: false,
      saveLoading: false,
      save: '保存并添加下一个',
      rules: {
        name: [
          {
            required: true,
            message: '请输入长度不超过50个字符的名称',
            trigger: 'change',
          },
          {
            max: 50,
            message: '请输入长度不超过50个字符的名称',
            trigger: 'change',
          },
        ],
        engineMaintainers: [
          {
            required: true,
            message: '请选择引擎维护人',
            trigger: 'change',
            type: 'array',
          },
        ],

        type: [
          {
            required: true,
            message: '请选择类型',
            trigger: 'change',
          },
        ],

        description: [
          {
            required: false,
            max: 250,
            message: '请输入长度不超过250个字符的描述',
            trigger: 'change',
          },
        ],
      },
      addForm: {
        name: '',
        state: true,
        status: true,
        description: '',
        type: '',
      },
    }
  },
  mounted() {
    if (this.id) {
      this.getUserDetail()
    }
    this.$nextTick(() => {
      this.$refs.addForm.clearValidate()
    })
  },
  methods: {
    onClose() {
      $emit(this, 'update:visible', false)
      this.$refs.addForm.resetFields()
    },
    async changeEngine(val) {
      const res = await apiBaseMsgEngineExtend(val)
      if (!res.isSuccess) {
        return
      }
      this.advanceTemplete = res.data
      const advanceRoule = this.advanceTemplete.map((r) => ({
        required: r.notEmpty,
        message: r.message,
        max: r.maxlength,
        trigger: 'change',
        pattern: r.regexp,
        key: r.key,
      }))

      advanceRoule.forEach((item) => {
        if (!this.rules[item.key]) {
          this.rules[item.key] = [item]
        } else {
          this.rules[item.key].push(item)
        }
      })
    },
    async getUserDetail() {
      this.loading = true
      const res = await apiBaseMessageEngineInfo(this.id)
      this.loading = false

      if (!res.isSuccess) {
        return
      }

      res.data['engineMaintainers'] = res.data.engineMaintainers.map(
        (r) => r.userId
      )
      res.data['type'] = res.data?.type?.code
      await this.changeEngine(res.data.type)
      const obj = {}
      if (this.advanceTemplete.length) {
        res.data.engineExtendeds.map((item) => {
          obj[item.key] = item.value || ''
        })
      }
      this.addForm = { ...res.data, ...obj }
    },

    async saveUser() {
      try {
        await this.$refs.addForm.validate()
      } catch (e) {
        return
      }
      try {
        this.saveLoading = true

        // 应用组件扩展信息
        const engineExtendeds = this.advanceTemplete.map((r, i) => ({
          engineId: r.id,
          key: r.key,
          value: this.addForm[r.key],
        }))
        const engineMaintainers = this.addForm.engineMaintainers.map((r) => ({
          userId: r,
        }))

        const res = await apiBaseMessageEngineHandle(
          {
            ...this.addForm,
            engineMaintainers,
            engineExtendeds,
          },
          this.id ? 'put' : 'post'
        )

        if (!res.isSuccess) {
          this.$message.error(res.msg)
          return
        }
        this.saveLoading = false
        this.$message.success(this.id ? '修改成功' : '保存成功')
        $emit(this, 'success')
        this.onClose()
      } catch (e) {
        this.saveLoading = false
        return
      }
    },
  },
  emits: ['update:visible', 'success'],
}
</script>

<style lang="scss" scoped>
.mb-10 {
  margin-bottom: 10px;
}
:deep(.el-dialog__body) {
  max-height: 520px;
  overflow-y: auto;
  overflow-x: hidden;
}
</style>
