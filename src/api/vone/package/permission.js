import axiosApi from '@/utils/axios-api'
// 人员权限列表
export function getUserPermissionList(data) {
  return axiosApi({
    url: '/api/pack/packPermissionFile/selectForUser',
    method: 'get',
    data
  })
}

// 新增用户制品权限
export function addUserPermissionList(data) {
  return axiosApi({
    url: '/api/pack/packPermissionFile/saveForUser',
    method: 'post',
    data
  })
}

// 修改用户制品权限
export function editUserPermissionList(data) {
  return axiosApi({
    url: '/api/pack/packPermissionFile/updateForUser',
    method: 'post',
    data
  })
}

// 删除用户制品权限
export function delUserPermissionList(data) {
  return axiosApi({
    url: '/api/pack/packPermissionFile/delForUser',
    method: 'post',
    data
  })
}

// 搜索人员
export function packSearchUser(data) {
  return axiosApi({
    url: '/api/pack/packPermissionFile/selectUser',
    method: 'get',
    data
  })
}

// 组织机构
export function getOrgList(data) {
  return axiosApi({
    url: '/api/pack/packPermissionFile/selectOrgUserByOrgId',
    method: 'get',
    data
  })
}

// 新增修改成员组
export function saveGroup(data) {
  return axiosApi({
    url: '/api/pack/packPermission/save ',
    method: 'post',
    data
  })
}

// 删除组
export function delGroup(data) {
  return axiosApi({
    url: '/api/pack/packPermission/del',
    method: 'post',
    data
  })
}

// 查询组
export function getGroupList(data) {
  return axiosApi({
    url: '/api/pack/packPermission/get',
    method: 'get',
    data
  })
}

// 组新增用户
export function addUserForGroup(data) {
  return axiosApi({
    url: '/api/pack/packPermissionUser/save',
    method: 'post',
    data
  })
}

// 删除权限组用户
export function delUserForGroup(data) {
  return axiosApi({
    url: '/api/pack/packPermissionUser/del',
    method: 'post',
    data
  })
}

// 保存制品权限信息
export function holdPackForGroup(data) {
  return axiosApi({
    url: '/api/pack/packPermission/saveFileForPermission',
    method: 'post',
    data
  })
}

// 修改人员权限信息
export function editPackForGroup(data) {
  return axiosApi({
    url: '/api/pack/packPermission/updateUserOperatePermission',
    method: 'post',
    data
  })
}

// 删除组制品权限信息
export function delPackForGroup(data) {
  return axiosApi({
    url: '/api/pack/packPermission/delFileForPermission',
    method: 'post',
    data
  })
}

// 查询人员和制品列表
export function getListFromGroupId(data) {
  return axiosApi({
    url: '/api/pack/packPermission/getPermissionUserAndFile',
    method: 'get',
    data
  })
}

// 查询制品子级
export function getPackListByParent(data) {
  return axiosApi({
    url: '/api/pack/packPermission/getPackInfoByParentId',
    method: 'get',
    data
  })
}

// 模糊查询用户或部门
export function searchUserAndOrg(data) {
  return axiosApi({
    url: '/api/pack/packPermissionUser/selectUserOrOrg',
    method: 'get',
    data
  })
}
