<template>
  <div class="pageBox pageContentNoH">
    <vone-search-wrapper>
      <template v-slot:search>
        <vone-search-dynamic
          ref="searchForm"
          table-search-key="projectUserTable"
          :model="formData"
          :default-fileds="defaultFileds"
          show-basic
          :extra="extraData"
          @getTableData="getTableData"
        />
      </template>
      <template v-slot:fliter>
        <vone-search-filter
          :extra="extraData"
          :model="formData"
          :default-fileds="defaultFileds"
          @getTableData="getTableData"
        />
      </template>
    </vone-search-wrapper>
    <!-- <el-row slot="actions">
            <el-button type="primary" icon="iconfont el-icon-tips-plus-circle" size="small" :disabled="!$permission('project_setting_user_add')" @click="clickAddUser">
              添加成员</el-button>
          </el-row> -->
    <div style="height: calc(100vh - 192px - 50px)">
      <vxe-table
        ref="projectUserTable"
        class="vone-vxe-table"
        border
        auto-resize
        height="auto"
        show-overflow="tooltip"
        :loading="pageLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column type="checkbox" width="36" fixed="left" align="center" />
        <vxe-column title="账号" field="account" min-width="150" fixed="left">
          <template #default="{ row }">
            {{ row.account }}
          </template>
        </vxe-column>
        <vxe-column title="名称" field="name" min-width="150">
          <template #default="{ row }">
            <vone-user-avatar
              :avatar-path="row.avatarPath"
              :avatar-type="row.avatarType"
              :name="row.name"
            />
          </template>
        </vxe-column>
        <vxe-column title="邮箱" field="email" min-width="150" />
        <vxe-column title="角色" field="role" min-width="150">
          <template #default="{ row }">
            {{ row.roleNames }}
          </template>
        </vxe-column>
        <vxe-column title="用户类型" field="type" min-width="100">
          <template #default="{ row }">
            {{ userTypeName(row) }}
          </template>
        </vxe-column>
        <vxe-column field="mobile" title="手机号" />
        <!-- <vxe-column field="createTime" show-overflow="tooltip" title="加入项目时间" min-width="180" /> -->
      </vxe-table>
    </div>
    <vone-pagination
      ref="pagination"
      :total="tableData.total"
      @update="getTableData"
    />
    <!-- 添加人员 -->
    <el-dialog
      title="添加成员"
      width="30%"
      v-model:value="dialogFormVisible"
      :close-on-click-modal="false"
      :before-close="onClose"
    >
      <el-form
        ref="userForm"
        :model="userForm"
        :rules="rules"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="项目角色" prop="roleIds">
          <el-select
            v-model="userForm.roleIds"
            placeholder="请选择项目角色"
            multiple
            filterable
          >
            <el-option
              v-for="item in groupData"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="用户" prop="userIds">
          <vone-remote-user
            v-model:value="userForm.userIds"
            :selecteds="pUserList"
            type="organization"
            multiple
          />
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <div class="dialog-footer">
          <el-button @click="onClose">取消</el-button>
          <el-button type="primary" :loading="saveLoading" @click="sureAdd"
            >确定</el-button
          >
        </div>
      </template>
    </el-dialog>

    <!-- 编辑人员 -->
    <el-dialog
      v-if="updateFormVisible"
      title="编辑成员"
      width="30%"
      v-model:value="updateFormVisible"
      :close-on-click-modal="false"
      :before-close="updateClose"
    >
      <el-form
        ref="updateForm"
        :model="updateForm"
        :rules="updateRules"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="项目角色" prop="roleIds">
          <el-select
            v-model="updateForm.roleIds"
            placeholder="请选择项目角色"
            multiple
            filterable
          >
            <el-option
              v-for="item in groupData"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="用户" prop="userId">
          <vone-remote-user v-model:value="updateForm.userId" disabled />
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <div class="dialog-footer">
          <el-button @click="updateClose">取消</el-button>
          <el-button type="primary" :loading="updateLoading" @click="sureUpdate"
            >确定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  getProjectRole,
  apiAlmProjectUserAdd,
  apiAlmProjectUserPut,
  apiAlmProjectUserInfo,
  apiAlmProjectUserDel,
} from '@/api/vone/project/setting'
import { apiProjectUserNoPage } from '@/api/vone/project/index'
import { getProjectUserList } from '@/api/vone/project/team'
import setDataMixin from '@/mixin/set-data'
export default {
  mixins: [setDataMixin],
  data() {
    return {
      defaultFileds: [
        {
          key: 'name',
          name: '名称',
          type: {
            code: 'INPUT',
          },
          placeholder: '请输入名称',
        },
        {
          key: 'roleIds',
          name: '角色',
          type: {
            code: 'SELECT',
          },
          placeholder: '请选择角色',
          multiple: true,
          valueType: 'id',
        },
      ],
      extraData: {},
      rules: {
        roleIds: [
          { required: true, message: '请选择项目角色', trigger: 'blur' },
        ],
        userIds: [
          {
            required: true,
            message: '请选择用户',
          },
        ],
      },
      updateRules: {
        roleIds: [
          { required: true, message: '请选择项目角色', trigger: 'blur' },
        ],
        userId: [
          {
            required: true,
            message: '请选择用户',
          },
        ],
      },

      formData: {
        projectId: this.$route.params.id,
      },
      pageLoading: false,
      tableData: {},
      updateFormVisible: false,
      dialogFormVisible: false,
      saveLoading: false,
      checkData: [],
      userForm: {
        roleIds: [],
        projectId: this.$route.params.id,
        userIds: [],
      },
      updateForm: {
        roleIds: [],
        userId: '',
      },
      formLoading: false,
      groupData: [],
      title: undefined,
      updateLoading: false,
      pUserList: [],
    }
  },
  computed: {
    userTypeName() {
      return function (row) {
        return row.echoMap?.userType?.name || ''
      }
    },
    userRoleName() {
      return function (row) {
        return row.echoMap?.roles.join('，') || ''
      }
    },
  },
  mounted() {
    // this.getTableData()
    this.getGroup()
  },
  methods: {
    async getProjectUser() {
      const res = await apiProjectUserNoPage({
        projectId: this.$route.params.id,
      })

      if (!res.isSuccess) {
        return
      }
      this.pUserList = res.data.map((e) => e.id)
    },
    onClose() {
      this.dialogFormVisible = false
      this.$refs.userForm.resetFields()
      this.userForm = {
        projectId: this.$route.params.id,
        userIds: [],
        roleIds: [],
      }
    },
    updateClose() {
      this.updateFormVisible = false
      this.$refs.updateForm.resetFields()
    },

    // 查询列表
    async getTableData() {
      this.pageLoading = true
      const pageObj = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
      pageObj['sort'] = 'createTime'
      this.formData['projectId'] = this.$route.params.id
      const params = {
        ...pageObj,
        extra: {
          ...this.extraData,
        },
        model: { ...this.formData },
      }
      const res = await getProjectUserList(params)
      this.pageLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      for (const element of res.data.records) {
        const roleMap = new Map()
        // 角色去重
        const roleIds = () => {
          for (const item of element.echoMap?.projectRoles) {
            if (!roleMap.has(item.id)) {
              roleMap.set(item.id, item)
            }
          }
          return [...roleMap.values()]
        }
        element.roleNames = roleIds().length
          ? roleIds()
              .map((r) => r.name)
              .join('、')
          : ''
      }
      this.tableData = res.data
    },
    async sureUpdate() {
      try {
        await this.$refs.updateForm.validate()
      } catch (e) {
        return
      }
      try {
        this.updateLoading = true
        const res = await apiAlmProjectUserPut({
          ids: [this.updateForm.id],
          roleIds: this.updateForm.roleIds,
        })
        this.updateLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.$message.success('保存成功')
        this.updateClose()
        this.getTableData()
      } catch (e) {
        this.updateLoading = false
      }
    },
    async sureAdd() {
      try {
        await this.$refs.userForm.validate()
      } catch (e) {
        return
      }

      try {
        this.saveLoading = true
        const res = await apiAlmProjectUserAdd(this.userForm)
        this.saveLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.$message.success('保存成功')
        this.onClose()
        this.getTableData()
      } catch (e) {
        this.saveLoading = false
      }
    },

    clickAddUser() {
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs.userForm.clearValidate()
      })
      this.getProjectUser()
    },
    // 编辑
    editClickRow(row) {
      this.updateFormVisible = true
      this.getUserInfo(row.id)
    },
    // 详情
    async getUserInfo(val) {
      this.formLoading = true
      const res = await apiAlmProjectUserInfo(val)
      this.formLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.updateForm = res.data
      // this.$set(this.updateForm, 'userIds', res.data.userId)
    },
    async getGroup() {
      const res = await getProjectRole(this.$route.params.id)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.groupData = res.data
      this.setData(this.defaultFileds, 'roleIds', res.data)
    },
    // 删除
    async deleteRow(row) {
      await this.$confirm(`确定删除该用户吗?`, '删除', {
        type: 'warning',
        closeOnClickModal: false,
      })
      const { isSuccess, msg } = await apiAlmProjectUserDel([row.id])
      if (!isSuccess) {
        this.$message.error(msg)
        return
      }
      this.$message.success('删除成功')
      this.getTableData()
    },
  },
}
</script>

<style lang="scss" scoped>
.pageBox {
  height: calc(100vh - 48px - 48px - 26px - 4px);
  padding: 16px 16px 10px 16px;
  overflow-y: auto;
}
.user {
  text-align: right;
  margin-bottom: 14px;
}
:deep(.el-form-item) {
  margin-bottom: 25px !important;
}
.mr-3 {
  margin-right: 5px;
}
</style>
