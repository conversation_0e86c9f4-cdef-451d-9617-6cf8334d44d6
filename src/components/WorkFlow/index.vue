<template>
  <div ref="main" class="bpmn el-container is-vertical" v-bind="$attrs">
    <!-- 顶部工具栏 -->
    <toolbar v-if="bpmnjs && showBar" :main="$refs.main" :single="single" :preview="preview" :btn-edit="btnEdit">
      <slot name="toolbar-actions" />
    </toolbar>
    <section class="el-container">
      <!-- 左侧 -->
      <palette v-if="bpmnjs && paletteData && !preview" :data="paletteData" :title="paletteTitle" :node-style="nodeStyle" :single="single">
        <template v-if="$scopedSlots['palette-item']">
          <template slot="palette-item" slot-scope="scope">
            <slot name="palette-item" v-bind="scope" />
          </template>
        </template>
      </palette>
      <main ref="bpmn" class="el-main" style="padding:0;" />
      <!-- 右侧配置项 -->
      <div v-if="showNodeProperties" :style="{ width: propertiesProps.width + 'px' }">
        <el-card class="vone-bpmn-properties">
          <el-form label-position="top">
            <slot name="properties" :element="selectedFirstNode" :selected="selectedNode" />
          </el-form>
        </el-card>
      </div>
      <div v-if="showConnectProperties" :style="{ width: propertiesProps.width + 'px' }">
        <el-card class="vone-bpmn-properties">
          <el-form label-position="top">
            <slot name="connect" :element="selectedFirstConnect" :selected="selectedConnect" />
          </el-form>
        </el-card>
      </div>

    </section>
    <div class="bpmn-grid" />
    <el-popover v-if="showNodePopover" ref="nodePopfover" :key="nodePopover.key" v-model="nodePopover.visible" :reference="nodePopover.reference" :width="nodePopoverWidth">
      <slot name="node-popover" :data="nodePopover.data" />
    </el-popover>
  </div>
</template>

<script>
import BpmnModeler from 'bpmn-js/lib/Modeler'
import BaseViewer from 'bpmn-js/lib/NavigatedViewer'

import 'bpmn-js/dist/assets/diagram-js.css'
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css'
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn.css'

// import { on } from 'element-ui/src/utils/dom' // 注释掉element-ui导入
// 替换为原生DOM事件监听
const on = (element, event, handler) => element.addEventListener(event, handler)

import CustomContextPad from './CustomContextPad'

import toolbar from './toolbar/toolbar.vue'
import palette from './palette/pelette.vue'

import defaultBpmn from './default.bpmn'
import { getBusinessObject, isActivity, isConnection, isTextAnnotation, updateProperties } from './utils/ModelUtil'
import cloneDeep from 'lodash/cloneDeep'
import assign from 'lodash/assign'
import pick from 'lodash/pick'

import baseExt from './node/base/ext.json'
import baseDraw from './node/base/draw'
import dynamicDraw from './node/dynamic/draw'

import json2Bpmn from './utils/json2Bpmn'
import concat from 'lodash/concat'
import { snowGuid } from '@/utils'
export default {
  name: 'WorkFlow',
  components: {
    toolbar,
    palette
  },
  props: {
    data: {
      type: Object,
      default: undefined
    },
    xml: {
      type: String,
      default: undefined
    },
    // 左侧面板标题
    paletteTitle: {
      type: String,
      default: undefined
    },
    // 左侧面板数据
    paletteData: {
      type: Array,
      default: null
    },
    nodeStyle: {
      type: Object,
      default: () => ({})
    },
    // 单一模式，节点不允许重复
    single: {
      type: Boolean,
      default: false
    },
    // 节点类型扩展
    typeExt: {
      type: Object,
      default: null
    },
    // 节点文本可以编辑
    nodeLabelEditable: {
      type: Boolean,
      default: false
    },
    nodePopoverWidth: {
      type: Number,
      default: 250
    },
    hideTextAnnotation: Boolean,
    propertiesProps: {
      type: Object,
      default: () => ({ width: 400 })
    },
    preview: {
      type: Boolean,
      default: false
    },
    btnEdit: { // 自定义表单参数,允许编辑节点的字段，但不允许添加删除节点
      type: Boolean,
      default: false
    },
    showBar: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      bpmnjs: null,
      nodePopover: {
        reference: null,
        visible: null,
        key: null
      },
      sharedState: {
        bpmnjs: null,
        selected: []
      },
      // 选中节点时，获取当前画布所有的边
      selectedConnects: {
        connects: []
      }
    }
  },
  computed: {
    selectedNode() {
      return this.sharedState.selected.filter(s => isActivity(s.el))
    },
    selectedConnect() {
      return this.sharedState.selected.filter(s => isConnection(s.el))
    },
    selectedFirstNode() {
      return this.selectedNode[0]
    },
    selectedFirstConnect() {
      return this.selectedConnect[0]
    },
    showNodeProperties() {
      this.selectedFirstNode && this.$emit('changeTab', this.selectedFirstNode.data) // 获取工作流状态
      return !!(this.bpmnjs && (this.$slots.properties || this.$scopedSlots.properties) && (this.selectedFirstNode))
    },
    showConnectProperties() {
      this.selectedFirstConnect && this.$emit('selectLine', this.selectedConnect[0].data, this.selectedConnect[0]) // 获取工作流线的id
      return !!(this.bpmnjs && !this.btnEdit && (this.$slots.properties || this.$scopedSlots.properties) && (this.selectedFirstConnect))
    },
    showNodePopover() {
      return this.$slots['node-popover'] || this.$scopedSlots['node-popover']
    },
    dragging() {
      return this.bpmnjs.get('dragging')
    }
  },
  provide() {
    return {
      sharedState: this.sharedState
    }
  },
  watch: {
    data: {
      handler(data) {
        if (data) this._importXML()
      },
      immediate: true
    },
    xml: {
      handler(data) {
        if (data) this._importXML()
      },
      immediate: true
    },
    paletteData: {
      handler(n, o) {
        if (n !== o) this._importXML()
      },
      immediate: true
    }
  },
  async mounted() {
    await this._initBpmn()
  },
  destroyed() {
    this.bpmnjs.detach()
  },
  methods: {
    createBpmn() {
      let bpmnjs
      if (this.preview) {
        let additionalModules = [...baseDraw]
        const moddleExtensions = { baseExt }
        if (this.typeExt) {
          additionalModules = concat(additionalModules, dynamicDraw(this.typeExt))
          if (this.typeExt.ext) {
            moddleExtensions.typeExt = {
              uri: 'http://' + this.typeExt.ext.name,
              ...this.typeExt.ext
            }
          }
        }
        bpmnjs = new BaseViewer({
          container: this.$refs.bpmn,
          position: 'absolute',
          additionalModules,
          moddleExtensions
        })
      } else {
        let additionalModules = [CustomContextPad(this.hideTextAnnotation), ...baseDraw]
        const moddleExtensions = { baseExt }
        if (this.typeExt) {
          additionalModules = concat(additionalModules, dynamicDraw(this.typeExt))
          if (this.typeExt.ext) {
            moddleExtensions.typeExt = {
              uri: 'http://' + this.typeExt.ext.name,
              ...this.typeExt.ext
            }
          }
        }
        bpmnjs = new BpmnModeler({
          container: this.$refs.bpmn,
          keyboard: {
            bindTo: window
          },
          position: 'absolute',
          additionalModules,
          moddleExtensions
        })
      }
      return bpmnjs
    },
    _initBpmn() {
      const bpmnjs = this.createBpmn()
      const _eventBus = bpmnjs.get('eventBus')
      bpmnjs.on('selection.changed', ({ newSelection }) => {
        if (newSelection[0]?.type == 'bpmn:SequenceFlow') {
          this.sharedState.selected = newSelection.map(this._getEdge)
          return
        }
        this.sharedState.selected = newSelection.map(this._getNode)
        // 选中节点时，获取和当前节点相关的边
        if (this.sharedState?.selected?.length > 0) {
          this.selectedConnects = {
            connects: this.sharedState.selected[0]?.el?.incoming.concat(this.sharedState.selected[0]?.el?.outgoing)
          }
        }
      })

      bpmnjs.on('shape.added', e => {
        const _set = e.element.businessObject.set
        const nSet = function(name, value) {
          const oldValue = e.element.businessObject.get(name)
          if (oldValue === value) return
          _set.apply(e.element.businessObject, [name, value])
          _eventBus.fire('shape.properties.changed', { element: e.element, name, value, oldValue })
        }

        if (e.element.businessObject.set === nSet) {
          return
        }

        e.element.businessObject.set = nSet
        if (!this.preview) {
          this.$nextTick(function() {
            const modeling = bpmnjs.get('modeling')
            if (e.element.type == 'label') return
            if (!e.element.businessObject.get('code')) {
              modeling.updateProperties(e.element, { onlyId: snowGuid() })
            }
          })
        }
      })

      bpmnjs.on('shape.properties.changed', e => {
        this.sharedState.selected = this.sharedState.selected.map(e => e.el).map(this._getNode)
      })

      bpmnjs.on('connection.added', e => {
        if (!this.preview) {
          this.$nextTick(function() {
            const modeling = bpmnjs.get('modeling')

            if (!e.element.businessObject.get('onlyId')) {
              modeling.updateProperties(e.element, { onlyId: snowGuid() })
            }
            if (typeof e.element.businessObject.get('permission') == 'string') {
              modeling.updateProperties(e.element, { permission: e.element.businessObject.get('permission').split(',') })
            }
            if (typeof e.element.businessObject.get('role') == 'string') {
              const roleData = e.element.businessObject.get('role') == '' ? [] : e.element.businessObject.get('role').split(',')
              modeling.updateProperties(e.element, { role: roleData })
            }
            if (typeof e.element.businessObject.get('user') == 'string') {
              modeling.updateProperties(e.element, { user: e.element.businessObject.get('user').split(',') })
            }
          })
        }
      })
      // bpmnjs.on('shape.removed', e => {
      //   // this.sharedState.selected = this.sharedState.selected.map(e => e.el).map(this._getNode)
      // })
      bpmnjs.on('shape.removed', e => {
        if (this.selectedConnects.connects.length > 0) {
          // 将相关联的线删除
          this.$nextTick(() => {
            this.selectedConnects.connects.forEach(item => {
              bpmnjs.get('modeling').removeElements([item])
            })
          })
        }
      })

      // Object.keys(bpmnjs.injector._instances.eventBus._listeners).forEach(key => {
      //   bpmnjs.on(key, e => {

      //   });
      // });

      this.sharedState.bpmnjs = this.bpmnjs = bpmnjs

      this._importXML()

      this._initLabelEditable()
      this._initNodePopover()

      // window.bpmnjs = bpmnjs
    },
    _importXML() {
      if (!this.bpmnjs) return
      let xml
      if (this.xml) {
        xml = this.xml
      } else if (this.data) {
        xml = json2Bpmn(this.data, this.nodeStyle, this.paletteData)
      }
      this.bpmnjs.importXML(xml || defaultBpmn).then(() => {
        this.bpmnjs.get('canvas').zoom('fit-viewport', true)
      })
    },
    _initLabelEditable() {
      if (!this.nodeLabelEditable) {
        this.bpmnjs.on('directEditing.activate', ({ active: { element }}) => {
          if (isActivity(element)) {
            this.bpmnjs.get('directEditing').cancel()
          }
        })
      }
    },
    /**
     * 初始化节点气泡
     * 允许在气泡悬停
     */
    _initNodePopover() {
      this.bpmnjs.on('element.hover', ({ element, gfx }) => {
        // 拖拽时不显示气泡
        if (isActivity(element) && (this.preview || !this.dragging.context())) {
          const bs = getBusinessObject(element)
          this.nodePopover = {
            key: element.id,
            reference: gfx,
            data: assign({ name: bs.name }, cloneDeep(bs.$attrs)),
            openTimer: setTimeout(() => {
              this.$set(this.nodePopover, 'visible', true)
              this.$nextTick(() => {
                if (!this.$refs.nodePopover) return
                on(this.$refs.nodePopover.popperElm, 'mouseenter', () => {
                  clearTimeout(this.nodePopover.closeTimer)
                })
                on(this.$refs.nodePopover.popperElm, 'mouseleave', () => {
                  this.$set(this.nodePopover, 'visible', false)
                })
              })
            }, 500)
          }
        }
      })
      this.bpmnjs.on('element.out', () => {
        clearTimeout(this.nodePopover.openTimer)
        this.nodePopover.closeTimer =
          this.nodePopover.closeTimer ||
          setTimeout(() => {
            this.$set(this.nodePopover, 'visible', false)
          }, 200)
      })
    },
    /**
     * 获取XML
     */
    async getXML(format = true) {
      return (await this.bpmnjs.saveXML({ format })).xml
    },
    _getNode(node) {
      const bs = getBusinessObject(node)
      const nodeData = assign({ id: bs.id, name: bs.name, type: node.type, data: cloneDeep(bs.$attrs) }, this._getNodeSize(node))
      nodeData.key = nodeData.data.key
      nodeData.el = node
      delete nodeData.data.key
      if (node.parent && isActivity(node.parent)) {
        nodeData.pid = node.parent.id
      }
      return nodeData
    },
    _getEdge(edge) {
      const bs = getBusinessObject(edge)
      return {
        id: bs.id,
        el: edge,
        name: bs.name,
        source: edge.source.id,
        target: edge.target.id,
        isAnnotation: isTextAnnotation(edge.target),
        waypoints: edge.waypoints.map(w => pick(w, ['x', 'y'])),
        data: cloneDeep(bs.$attrs)
      }
    },
    /**
     * 获取JSON数据
     */
    getJSON() {
      const bNodes = this.bpmnjs.get('elementRegistry').filter(el => isActivity(el))
      const bAnnotations = this.bpmnjs.get('elementRegistry').filter(el => isTextAnnotation(el))
      const bEdges = this.bpmnjs.get('elementRegistry').filter(el => isConnection(el))

      const nodes = bNodes.map(this._getNode)
      const edges = bEdges.map(this._getEdge)

      const result = { nodes, edges }

      if (!this.hideTextAnnotation) {
        result.annotations = bAnnotations.map(node => {
          const bs = getBusinessObject(node)
          return assign({ id: bs.id, name: bs.text }, this._getNodeSize(node))
        })
      }

      return result
    },
    _getNodeSize(node) {
      return {
        x: node.x,
        y: node.y,
        w: node.width,
        h: node.height
      }
    },
    updateProperties(el, attr) {
      return updateProperties(this.bpmnjs, el, attr)
    }
  }
}
</script>
<style lang="scss" scoped>
.bpmn {
  height: calc(100vh - 48px - 50px - 16px * 3);
  position: relative;
  background-color: var(--main-bg-color,#fff);
  transition: filter 0.5s;
  border: 1px solid var(--disabled-bg-color,#ebeef5);
  :deep() {
    .bjs-powered-by,
    .djs-palette {
      display: none;
    }
  }
}

.el-container {
  z-index: 2;
  overflow: hidden;
}
.el-main {
  position: relative;
  :deep() {
    svg {
      &:hover g.viewport {
        transition: none;
      }
      g.viewport {
        transition: all 0.5s;
      }
    }
  }
}
.vone-bpmn-properties {
  height: 100%;
  width: 400px;
  border-radius: 0;
  border: 0;
  border-left: 1px solid var(--disabled-bg-color,#ebeef5);
  display: flex;
  flex-direction: column;
  :deep() {
    > .el-card__body {
      height: 100%;
      overflow: auto;
    }
  }
}
// 背景网格
.bpmn-grid {
  position: absolute;
  transform-origin: 0% 0% 0px;
  background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHBhdHRlcm4gaWQ9ImdyaWQiIHdpZHRoPSI0MCIgaGVpZ2h0PSI0MCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+PHBhdGggZD0iTSAwIDEwIEwgNDAgMTAgTSAxMCAwIEwgMTAgNDAgTSAwIDIwIEwgNDAgMjAgTSAyMCAwIEwgMjAgNDAgTSAwIDMwIEwgNDAgMzAgTSAzMCAwIEwgMzAgNDAiIGZpbGw9Im5vbmUiIHN0cm9rZT0iI2UwZTBlMCIgb3BhY2l0eT0iMC4yIiBzdHJva2Utd2lkdGg9IjEiLz48cGF0aCBkPSJNIDQwIDAgTCAwIDAgMCA0MCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZTBlMGUwIiBzdHJva2Utd2lkdGg9IjEiLz48L3BhdHRlcm4+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JpZCkiLz48L3N2Zz4=);
  background-position-x: -1px;
  left: 0px;
  top: 0px;
  transform: matrix(1, 0, 0, 1, 0, 0);
  width: 100%;
  height: 100%;
}
</style>
