<template>
  <el-dialog :close-on-click-modal="false" :title="formInfo ? formInfo.title : '通用配置'" v-model="visible" @closed="close">
    <el-form ref="form" :model="formData" :rules="rules">
      <el-form-item label="标题" label-width="80px" prop="title">
        <el-input v-model="formData.title" style="width: 90%" />
      </el-form-item>
      <el-form-item v-if="!hiddenProject" label="项目" label-width="80px" prop="projectId">
        <el-select v-model="formData.projectId" :multiple="multiple" clearable filterable style="width:90%" @change="changeProject">
          <el-option v-for="item in projectIdList" :key="item.key" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="!hiddenPlan" label="迭代" label-width="80px" prop="planId">
        <el-select v-model="formData.planId" clearable filterable style="width:90%">
          <el-option v-for="item in planIdList" :key="item.key" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button
        type="primary"
        @click="save"
      >确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { apiAlmProjectNoPage } from '@/api/vone/project/index'
import { apiAlmProjectPlanNoPage } from '@/api/vone/project/iteration'
import _ from 'lodash'
export default {
  name: 'CardDialog',
  props: {
    isEditForm: Boolean,
    visible: {
      type: Boolean,
      default: false
    },
    formInfo: {
      type: Object,
      default: null
    },
    layout: {
      type: [Object, Array],
      default: null
    }
  },
  data() {
    return {
      formData: {
        title: this.formInfo.title,
        projectId: undefined,
        planId: '',
        name: ''
      },
      rules: {
        title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
        projectId: [{ required: true, message: '请选择项目', trigger: 'blur' }],
        planId: [{ required: true, message: '请选择迭代', trigger: 'blur' }]
      },
      projectIdList: [],
      planIdList: []
    }
  },
  computed: {
    // 是否隐藏迭代
    hiddenPlan() {
      // 产品缺陷趋势，项目信息,项目缺陷概览，需求概览，任务概览，任务分布,延期事项
      const hidden = ['product-bug-trends', 'project-card', 'project-bug', 'project-require', 'project-task', 'project-task-user', 'delay-event', 'project-kalendar', 'project-task-table', 'project-bug-trends']
      const key = this.formInfo.key
      return hidden.includes(key)
    },
    // 是否隐藏项目
    hiddenProject() {
      const hidden = ['project-kalendar']
      const key = this.formInfo.key
      return hidden.includes(key)
    },
    multiple() {
      return this.formInfo.key == 'project-task-table'
    }
  },
  mounted() {
    if (this.isEditForm) {
      this.formData = this.formInfo
    }
    this.getProjectList()
    this.getPlanList()
  },
  methods: {
    changeProject(val) {
      if (this.hiddenPlan) return
      this.getPlanList(val)
    },
    // 项目
    async getProjectList() {
      const res = await apiAlmProjectNoPage()

      if (!res.isSuccess) {
        return
      }
      this.projectIdList = res.data
    },
    // 迭代
    async getPlanList(val) {
      const res = await apiAlmProjectPlanNoPage({ projectId: val })
      if (!res.isSuccess) {
        return
      }
      this.planIdList = res.data
    },
    async save() {
      try {
        await this.$refs.form.validate()
      } catch (e) {
        return
      }
      const layouts = _.cloneDeep(this.layout)
      if (this.isEditForm) {
        layouts.map(item => {
          if (item.id == this.formData.id && item.widgetCode == this.formData.key) {
            item.queryParams = JSON.stringify({ ...this.formData })
          }
        })
      } else {
        if (this.formData.projectId && !this.formInfo.key == 'project-task-table') {
          this.$emit('update:formInfo', {
            ...this.formInfo,
            ...this.formData
          })
          this.formData.name = this.projectIdList.find(item => item.id == this.formData.projectId).name
        }
        layouts.push({
          x: (this.layout.length * 2) % (12),
          y: this.layout.length + (12), // puts it at the bottom
          w: 3,
          h: 3,
          i: this.layout.length + 1,
          queryParams: JSON.stringify({ ...this.formInfo, ...this.formData }),
          widgetCode: this.formInfo.key
        })
      }

      this.$emit('update:layout', layouts)
      this.$emit('update:visible', false)
      this.$emit('success')
    },
    close() {
      this.$emit('update:visible', false)
      this.$refs.form.resetFields()
    }
  }
}
</script>

