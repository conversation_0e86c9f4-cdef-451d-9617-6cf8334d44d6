<template>
  <el-dialog
    title="批量指派执行人"
    :model-value="visible"
    :before-close="onClose"
    :close-on-click-modal="false"
    width="30%"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="执行人" prop="user">
        <vone-remote-user
          v-model:value="form.user"
          :default-data="defaultData"
          @updateSelectedRow="getSelectedUsers"
        />
      </el-form-item>
    </el-form>

    <template v-slot:footer>
      <div class="dialog-footer">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" :loading="btnLoading" @click="saveInfo"
          >确定</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script>
import {
  $on,
  $off,
  $once,
  $emit,
} from '../../../../../../utils/gogocodeTransfer'

import { groupForUser } from '@/api/vone/testmanage/index'

export default {
  props: {
    visible: {
      type: Boolean,
      default: true,
    },
    selectData: {
      type: Array,
      default: () => [],
    },
    planData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      form: {
        user: '',
      },
      selectedUsers: [],
      rules: {
        user: [
          {
            required: true,
            message: '请选择指派人',
          },
        ],
      },
      defaultData: [],
      btnLoading: false,
    }
  },
  methods: {
    onClose() {
      $emit(this, 'update:visible', false)
    },
    getSelectedUsers(users) {
      this.selectedUsers = users
    },
    // 保存选中用例
    async saveInfo() {
      await this.$refs.form.validate()
      try {
        this.btnLoading = true
        const res = await groupForUser({
          caseId: this.selectData.map((r) => r.testcaseId),
          planId: this.planData.id,
          user: this.form.user,
        })
        this.btnLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }

        this.$message.success('批量指派执行人成功')

        $emit(this, 'success', { users: this.selectedUsers })
        this.onClose()
      } catch (e) {
        this.btnLoading = false
      }
    },
  },
  emits: ['update:visible', 'success'],
}
</script>
