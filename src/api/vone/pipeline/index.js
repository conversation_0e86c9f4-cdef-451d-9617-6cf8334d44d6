import axiosApiUtils from '@/utils/requestUtils'
import axiosApi from '@/utils/axios-api'
// 业务系统--配置--新增服务应用---保存并开始配置

export function apiBuniessSelectBuildFileBySystemId(params) {
  return axiosApi({
    url: '/api-base/applicationBuildFile/selectBuildFileBySystemId',
    method: 'get',
    params
  })
}

// 业务系统--配置--编辑--流水线--新增--查询数据

export function apiBuniessSelectList(params) {
  return axiosApi({
    url: '/api-cmdb/api/cmdb/businessSystem',
    method: 'get',
    params
  })
}

// 业务系统--配置--编辑--流水线--新增--查询数据

export function apiBuniessGetAllAgileProjectsFromDb(params) {
  return axiosApi({
    url: '/api-agile/agileCenter/getAllAgileProjectsFromDb',
    method: 'get',
    params
  })
}

// 业务系统--配置--编辑--仓库配置

export function apiBuniesSselectEngineByCategoryId(params) {
  return axiosApi({
    url: '/api-base/engine/selectEngineByCategoryId',
    method: 'get',
    params
  })
}
// 业务系统--配置--编辑--仓库配置

export function apiBuniesSelectCodeRepByCategoryIdAndEngineId(params) {
  return axiosApi({
    url: '/api-base/applicationEngine/selectCodeRepByCategoryIdAndEngineId',
    method: 'get',
    params
  })
}

// 业务系统--配置--编辑--仓库配置

export function apiBuniessGetStrage(params) {
  return axiosApi({
    url: '/api-base/dictCodeLine/strategy/2',
    method: 'get',
    params
  })
}

// 业务系统--配置--编辑--工程配置

export function apiBuniesSelectWarehouseBySystemId(params) {
  return axiosApi({
    url: '/api-base/applicationEngine/selectWarehouseBySystemId',
    method: 'get',
    params
  })
}

// 业务系统--配置--tab页--构建部署---删除

export function apiBaseDeleteBuildFileById(params) {
  return axiosApi({
    url: '/api-base/applicationBuildFile/deleteBuildFileById',
    method: 'DELETE',
    params
  })
}

// 业务系统--配置--tab页--DB配置---保存

export function apiBuniessUpdateDbCompentsBySystemId(data) {
  return axiosApi({
    url: '/api-base/applicationDbCompents/updateDbCompentsBySystemId',
    method: 'POST',
    data: axiosApiUtils.formData(data)
  })
}
// 业务系统--配置--tab页--DB配置---保存--查询

export function apiBuniessSelectDbCompentsBySystemId(params) {
  return axiosApi({
    url: '/api-base/applicationDbCompents/selectDbCompentsBySystemId',
    method: 'get',
    params
  })
}

// 业务系统--配置--tab页--制品信息---查询

export function apiBuniessSelectAllProductStatusTag(params) {
  return axiosApi({
    url: '/api-pipeline/productStatus/selectAllProductStatusTag',
    method: 'get',
    params
  })
}

// 业务系统--配置--tab页--操作历史--执行

export function apiBuniessGetDetailById(params) {
  return axiosApi({
    url: '/api-base/operationLog/getDetailById',
    method: 'get',
    params
  })
}

// 制品库---列表数据
export function apiBuniessSelectByCondition(params) {
  return axiosApi({
    url: '/api-pipeline/product/selectByCondition',
    method: 'get',
    params
  })
}

// 制品库---服务应用下拉框数据
export function apiBuniessSelectAllByParam(data) {
  return axiosApi({
    url: '/api/cmdb/cmdb/application/query',
    method: 'post',
    data
  })
}

// 制品库---在线审核--环境标签
export function apiBuniessSelectEnvBySystemId(params) {
  return axiosApi({
    url: '/api-pipeline/applicationBase/selectEnvBySystemId',
    method: 'get',
    params
  })
}

// 制品库---在线审核--环境标签
export function apiBuniessSelectEnvByBusinessSystemId(params) {
  if (params) {
    return axiosApi({
      url: '/api-cmdb/api/cmdb/businessSystemExhibit/selectEnvBybusinessSystemId/' + params,
      method: 'get'
    })
  } else {
    return axiosApi({
      url: '/api-cmdb/api/cmdb/businessSystemExhibit/selectEnvBybusinessSystemId/' + undefined,
      method: 'get'
    })
  }
}
// 制品库---在线审核--表格数据
export function apiBuniessSelectFullById(id, params) {
  return axiosApi({
    url: `/api-pipeline/product/getFilesByProductId/${id}`,
    method: 'get',
    params
  })
}

// 制品库---变更日志
export function apiBuniessSelectProductChangeLogsByProductId(id) {
  return axiosApi({
    url: `/api-pipeline/product/selectProductChangeLogsByProductId/${id}`,
    method: 'get'
  })
}

// 制品库---审核状态--表格数据
export function apiBuniessCheckSelectFullById(params) {
  return axiosApi({
    url: `/api-pipeline/product/selectFullById`,
    method: 'get',
    params
  })
}

// 制品库---审核状态--保存
export function apiBuniessUpdateProductStatus(data) {
  return axiosApi({
    url: `/api-pipeline/productStatus/updateProductStatus`,
    method: 'post',
    data
  })
}

/**
 * 制品库---下载
 */
export async function apiBuniessProductOperate(params) {
  return axiosApi({
    url: `/api-pipeline/productOperate/downLoad`,
    method: 'get',
    params,
    // 下载文件配置
    transformResponse: [axiosApiUtils.download({ fileName: '' })],
    responseType: 'blob'
  })
}

// 制品库---删除
export function apiBuniessDeleteProductById(id) {
  return axiosApi({
    url: `/api-pipeline/productOperate/deleteProduct/${id}`,
    method: 'delete'
  })
}

// 制品库---上传
export function apiBuniessProductOperateUpload({ file, ...params }) {
  const form = axiosApiUtils.formData(params)

  form.append('file', file)
  return axiosApi({
    url: `/api-pipeline/productOperate/upload`,
    method: 'post',
    data: form
  })
}

// 制品库---编辑
export function apiBuniessProductEditById(params) {
  return axiosApi({
    url: `/api-pipeline/product/selectById`,
    method: 'get',
    params
  })
}

// 制品库---编辑--保存
export function apiBuniessUpdateProductBase(data) {
  return axiosApi({
    url: `/api-pipeline/productOperate/updateProductBase`,
    method: 'post',
    data: axiosApiUtils.formData(data)
  })
}
// 制品库---变更日志
export function apiBuniessChangeLogsByProductId(id, params) {
  return axiosApi({
    url: `/api-pipeline/product/selectProductChangeLogsByProductId/${id}`,
    method: 'get',
    params
  })
}

// 制品库---更新日志文件---新增--保存
export function apiBuniessAddProductChangeLogs(data) {
  return axiosApi({
    url: `/api-pipeline/product/addProductChangeLogs`,
    method: 'post',
    data
  })
}
// 制品库---更新日志文件---修改--保存
export function apiBuniessUpdateProductChangeLogs(data) {
  return axiosApi({
    url: `/api-pipeline/product/updateProductChangeLogs`,
    method: 'post',
    data
  })
}

// 制品库---更新日志文件--删除
export function apiBuniessDeleteProductChangeLogs(params) {
  return axiosApi({
    url: `/api-pipeline/product/deleteProductChangeLogs`,
    method: 'DELETE',
    params
  })
}

// 流水线---查询列表数据
export function apiBuniessPipelineJobs(data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineJob/page`,
    method: 'post',
    data: data
  })
}

// 流水线---查询列表数据无分页
export function apiBuniessPipelineJobsNoPage(data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineJob/query`,
    method: 'post',
    data: data
  })
}
// 流水线---查询我创建的列表数据
export function apiBuniessPipelineJobsByAuthor(params) {
  return axiosApi({
    url: `/api-pipeline/publish/pipelineJobs/getPipelineByAuthor`,
    params
  })
}

// 获取所有阶段
export async function queryPipelineStep(params) {
  return axiosApi({
    url: '/api/pipeline/pipeline/pipelineJob/' + params.id + '/stages',
    method: 'get'
  })
}

// 流水线-新增-根据服务应用查询引擎
export function apiBuniessSelectJenkinsMaster(data) {
  return axiosApi({
    url: `/api/tm-base/base/engine/query`,
    method: 'post',
    data: {
      instance: 'JENKINS_MASTER'
    }
  })
}

// 流水线---新增--业务系统下拉框数据查询
export function apiBuniessSelectJenkinsMasterNodeLabelById(data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/engine/jenkins/selectJenkinsMasterNodeLabelById`,
    method: 'get',
    data
  })
}

// 流水线---新增--服务应用下拉框数据查询分支
export function apiBuniessSelectCodeLineBySystemId(data) {
  return axiosApi({
    url: `/api/cmdb/cmdb/applicationBranch/query`,
    method: 'POST',
    data
  })
}
// 流水线-新增--服务应用下拉框数据查询环境
export function getEnvByApp(id) {
  return axiosApi({
    url: `/api/pipeline/pipeline/application/env/selectEnvByApplicationId/${id}`,
    method: 'get'
  })
}
// 流水线---新增--判断名称是否重复
export function apiBuniesSpipelineExists(data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineJob/pipelineExists`,
    method: 'post',
    data
  })
}

// 流水线---删除列表数据
export function apiDeleteBuniessPipelineJobs(data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineJob`,
    method: 'delete',
    data
  })
}

// 流水线-查询流水线详情
export function apiBuniessPipelineJobsById(id) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineJob/${id}`,
    method: 'get'
  })
}

// 新建流水线保存方法

export async function pipelineSava(data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineJob/tempateId`,
    method: 'POST',
    data: data
  })
}

// 流水线--查询步骤类型
export function apiBuniessGetStepDefines(data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineJobSteps/getStepDefines`,
    method: 'get',
    data
  })
}

// 流水线-根据服务应用查询引擎
export function apiBuniessSelectEngineDetailBySystemId(data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/engine/query`,
    method: 'post',
    data
  })
}

// 流水线-查询流水线是否在jenkins上存在
export function apiBuniessGetJenkinsExist(id) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineBuilds/getJenkinsExist/${id}`,
    method: 'get'
  })
}

// 流水线-查询流水线阶段
export function apiBuniessPipelineJobsStages(id) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineJob/${id}/stages`,
    method: 'get'
  })
}

// 流水线--查询单个流水线阶段
export function apiBuniessChangePipelineJobsStages(id, index) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineJob/${id}/stages/${index}`,
    method: 'get'
  })
}

// 流水线--查找流水线中某个阶段的所有步骤
export function apiBuniessSelectPipelineJobsStagesSteps(id, index) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineJobSteps/${id}/stages/${index}/steps`,
    method: 'get'
  })
}

// 流水线--新增阶段--检查名称是否已存在
export function apiBuniessAddPipelineJobsSteps(id, data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineJob/${id}/stageNameExists`,
    method: 'get',
    data
  })
}

// 流水线--新增步骤--保存
export function apiBuniessAddPipelineJobsStepsSave(id, data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineJob/${id}/stages`,
    method: 'POST',
    data
  })
}

export function apiBunieUpdatePipelineJobsStepsSave(id, stageId, params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineJob/${id}/stages/${stageId}`,
    method: 'PUT',
    data: params
  })
}

// 获取流水线历史列表
export async function queryPipelineHistory(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineBuildHistory/query`,
    method: 'post',
    data: params
  })
}

// 获取流水线构建历史日志
export async function queryPipelineBuildHistoryLog(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineBuildLogs/getBuildLogByPipelineIdAndBuildId/${params.pipelineId}/${params.buildId}`,
    method: 'get'
  })
}

// 获取流水线步骤
export async function queryPipelineStages(id) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineJob/${id}/stages`,
    method: 'get'
  })
}

// 修改流水线步骤配置
export async function saveStepsEnables(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineStepConfig/saveStepsEnables?pipelineId=` + params.id,
    method: 'POST',
    data: params.stages
  })
}

// 流水线编辑查询--脚本功能
export async function apiBuniessSelectScriptFunctionsByTypeId(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineScript/selectScriptFunctionsByType?type=` + params.typeId,
    method: 'get',
    params
  })
}
// 获取构建类型的脚本功能
export async function apiFunctionByBuild(params) {
  return axiosApi({
    url: '/api/pipeline/pipeline/pipelineScript/selectScriptFunctionsByType?type=build',
    method: 'get'
  })
}

// 获取调度类型的脚本功能
export async function apiFunctionByDispatch(params) {
  return axiosApi({
    url: '/api/pipeline/pipeline/pipelineScript/selectScriptFunctionsByType?type=dispatch',
    method: 'get'
  })
}

// 流水线编辑查询--流水线执行状态
export async function apiBuniessGetPipelineStageStatusById(id) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineBuilds/getPipelineStageStatusById/${id}`,
    method: 'get'
  })
}

// 流水线编辑查询--发版时间
export async function apiBuniessGetPipelineTimeWindowTimes(param) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineReleaseWindowTime/query`,
    method: 'post',
    data: param
  })
}
// 流水线编辑查询--发版时间
export async function apiBuniessTimeWindows(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineReleaseWindow/query`,
    method: 'post',
    data: params
  })
}
// 流水线编辑查询--发版时间--更改发布窗口时间限制状态
export async function apiBuniessTimeWindowsUpdateStatus(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineReleaseWindow`,
    method: 'put',
    data: params
  })
}

// 流水线编辑查询--发版时间--删除
export async function apiBuniessDelTimeStatusByTimeIds(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineReleaseWindowTime`,
    method: 'delete',
    data: params
  })
}

// 流水线编辑查询--发版时间--更改监控开关
export async function apiBuniessUpdateTimeStatus(status, timeIds) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineReleaseWindowTime/updateTimeStatus/` + status,
    method: 'put',
    data: timeIds
  })
}

// 流水线编辑查询--发版时间--新增
export async function apiBuniessAddTimeWindowTimes(data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineReleaseWindowTime`,
    method: 'post',
    data
  })
}
// 流水线编辑查询-用户通知
export async function apiBuniessAdvancedSearch(data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineNotifyUser/selectNotNotifyUserList`,
    method: 'post',
    data: data
  })
}

// 流水线编辑查询-授权--查询选中节点
export async function apiBuniessListPipelineAuthorize(id) {
  return axiosApi({
    url: `/api-pipeline/publish/pipelineAuthorize/listPipelineAuthorize/${id}`,
    method: 'get'
  })
}
// 流水线编辑查询-授权--保存
export async function apiBuniessSavePipelineAuthorize(data) {
  return axiosApi({
    url: `/api-pipeline/publish/pipelineAuthorize/savePipelineAuthorize`,
    method: 'post',
    data
  })
}

// 流水线编辑查询
export async function apiBuniessPipelineJobStepsStages(id, stageId, stepId) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineJobSteps/${id}/stages/${stageId}/steps/${stepId}`,
    method: 'get'
  })
}
// 流水线--改变focus状态
export async function apiBuniessChangePipelineFocus(id) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineFavourites/byPipelineId/${id}`,
    method: 'post'
  })
}
// 流水线--改变focus状态
export async function apiBuniessChangePipelineFocusDelete(id) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineFavourites/byPipelineId/${id}`,
    method: 'DELETE'
  })
}

// 根据流水线ID获取部署策略列表
export async function listExecutePolicyByPipelineId(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineDeployType/query`,
    method: 'POST',
    data: params
  })
}

// 根据部署类型和策略标识查询部署目标
export async function getPipelineDeploy(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineDeploy/getPipelineDeployByDeployTypeAndPolicyKey?pipelineId=${params.pipelineId}&deployType=${params.deployType}&policyKey=${params.policyKey}`,
    method: 'GET'
  })
}

// 获取流水线计划配置信息  '/api-pipeline/publish/timeTasks/' + params.id
export async function queryPipelineSchedule(id) {
  return axiosApi({
    url: `/api-pipeline/publish/timeTasks/${id}`,
    method: 'GET'
  })
}

// 获取流水线计划配置信息  '/api-pipeline/publish/timeTasks/' + params.id
export async function apiBuniessGetPipelineDeployType(data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineDeployType/getPipelineDeployType`,
    method: 'get',
    data
  })
}

// 获取流水线---用户通知
export async function apiBuniessPipelineNotifyUserArr(data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineNotifyUser/batchInsert`,
    method: 'post',
    data
  })
}

// 获取流水线---用户通知
export async function apiBuniessNotifyUserArrDeleteByIds(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineNotifyUser`,
    method: 'DELETE',
    data: params
  })
}

// 获取流水线---用户通知已通知用户
export async function apiBuniessNotifyFindByPipelineId(data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineNotifyUser/query`,
    method: 'post',
    data: data
  })
}

// 获取流水线--日志
export async function apiBuniessGetBuildLog(id) {
  return axiosApi({
    url: `/api-pipeline/publish/pipelineExhibit/getBuildLogByPipelineIdAndBuildId/${id}`,
    method: 'get'
  })
}
// 根据服务应用id获取数据库执行路径
export async function selectSqlPathBySystemId(systemId) {
  return axiosApi({
    url: `/api-cmdb/api/cmdb/applicationExhibit/selectSqlPathBySystemId?systemId=${systemId}`,
    method: 'GET'
  })
}

// 根据服务应用ID和环境获取数据库组件信息
export async function selectDbComponent(params) {
  return axiosApi({
    url: `/api/cmdb/cmdb/dbComponents/findListByQuery`,
    method: 'post',
    data: params
  })
}

// 根据数据库组件ID获取数据库实例信息
export async function selectInstanceByDbCompentsId(params) {
  return axiosApi({
    url: `/api-cmdb/api/cmdb/dbcompentsExhibit/selectInstanceByDbCompentsId`,
    method: 'GET',
    params
  })
}

// 根据数据库实例ID获取数据库用户信息
// export async function selectUserByDbCompentsInstanceId(params) {
//   return axiosApi({
//     url: `/api-cmdb/api/cmdb/dbcompentsExhibit/selectUserByDbCompentsInstanceId`,
//     method: 'GET',
//     params
//   })
// }

// 流水线新增部署策略 查询信息
export async function apiBuniessGetDeployHostInfo(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineDeploy/getDeployHostInfo?pipelineId=` + params.pipelineId,
    method: 'GET',
    params
  })
}

// 流水线新增部署策略 查询信息
export async function apiBuniessDelExecutePolicy(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineDeploy/delDeployPolicy?pipelineId=` + params.pipelineId + '&policyKey=' + params.policyKey,
    method: 'DELETE',
    params
  })
}

// 流水线新增部署策略 查询信息
export async function apiBuniessTimeTasks(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineTimeTaskRecord/page`,
    method: 'POST',
    data: params
  })
}

// 流水线新增计划配置
export async function apiBuniessAddTimeTask(data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineTimeTaskRecord`,
    method: 'post',
    data
  })
}

// 计划配置 -- 删除
export async function apiBuniessDelTimeTask(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineTimeTaskRecord`,
    method: 'DELETE',
    data: params.ids
  })
}
// 判断指定流水线中是否存在特定的步骤步骤名称(只能是英文名称)
export async function stepNameExists(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineJobSteps/${params.id}/stepNameExists?stepName=${params.stepName}&stepId=${params.stepId}`,
    method: 'GET'
  })
}

// 判断指定流水线中是否存在特定的步骤显示名称
export async function descExists(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineJobSteps/${params.id}/descExists?desc=${params.desc}&stepId=${params.stepId}`,
    method: 'GET'
  })
}

// 新增步骤保存按钮
export async function saveStep(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineJobSteps/${params.id}/stages/${params.stageId}/steps/${params.stepId}`,
    method: 'POST',
    data: params.data
  })
}

// 保存流水线stage
export async function saveStages(params) {
  return axiosApi({
    url: `/api-pipeline/publish/pipelineJobs/${params.id}/stages`,
    method: 'POST',
    data: params.data
  })
}

// 复制流水线
export async function apiBuniessCopyPipeline(id, data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineJob/copyPipeline/${id}`,
    method: 'get',
    data
  })
}

// 创建或者更新流水线
export async function apiBuniessCreateOrUpdatePipeline(id, data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineBuilds/createOrUpdatePipeline/${id}`,
    method: 'get',
    data
  })
}
// 保存流水线基本信息
export async function apiBuniessSavePipeline(id, data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineJob/updatePipelineJob?id=${id}`,
    method: 'PUT',
    data
  })
}

// 编辑步骤保存按钮
export async function editStep(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineJobSteps/${params.id}/stages/${params.stageId}/steps/${params.stepId}`,
    method: 'PUT',
    data: params.data
  })
}

// 删除步骤保存按钮
export async function deleteStep(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineJobSteps/${params.id}/stages/${params.stageId}/steps/${params.stepId}`,
    method: 'DELETE'
  })
}

// 删除阶段信息
export async function apiBuniessDeleteStagesById(jobId, stageId) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineJob/${jobId}/stages/${stageId}`,
    method: 'DELETE'
  })
}

// 流水线-制品库-审核状态-删除
export async function apiBuniessDeleteProductStatusById(data) {
  return axiosApi({
    url: `/api-pipeline/productStatus/deleteProductStatus`,
    method: 'DELETE',
    data
  })
}
// 根据服务应用和环境查询制品版本
export async function selectProductVersion(data) {
  return axiosApi({
    url: '/api/pack/pack/packBase/query',
    method: 'post',
    data
  })
}

// 构建约束条件检查
export async function checkBuildRestriction(id) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineBuilds/checkBuildRestriction?pipelineId=${id}`,
    method: 'GET'
  })
}
// 根据服务应用ID获取分支信息
export async function getCodeLineById(params) {
  return axiosApi({
    url: '/api/cmdb/cmdb/applicationBranch/query',
    method: 'post',
    data: params
  })
}
// 根据部署类型获取部署策略名称
export async function getPolicyKey(params) {
  return axiosApi({
    url: '/api-pipeline/publish/pipelineExecutePolicy/getPolicyKey?pipelineId=' + params.pipelineId + '&deployType=' + params.deployType,
    method: 'GET'
  })
}
// 执行流水线
export async function buildPipeline(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineBuilds/buildPipeline?pipelineId=${params.id}`,
    method: 'POST',
    data: params.buildParams
  })
}

// 获取步骤信息
export async function getStepBuildLog(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineBuildLogs/getStepBuildLog/${params.pipelineId}/${params.buildId}/${params.stepId}`,
    method: 'GET'
  })
}

// 制品库--在线审核--点击表格每一行
export async function apiBuniessGetFileContentByProductId(id, params) {
  return axiosApi({
    url: `/api-pipeline/product/getFileContentByProductId/${id}`,
    method: 'GET',
    params
  })
}

// 制品库--上传成功--弹出对话框
export async function apiBuniessProductAsyncMessage(params) {
  return axiosApi({
    url: `/api-pipeline/productOperate/asyncMessage`,
    method: 'GET',
    params
  })
}

// 制品库--上传成功--弹出对话框
export async function apiBuniessProductAsyncMessagePropUuid(params) {
  return axiosApi({
    url: `/api-pipeline/productOperate/propUuid`,
    method: 'GET',
    params
  })
}

// 流水线执行面板--下载id
export async function apiBuniessDownloadBuildLogById(id, fileName) {
  return axiosApi({
    url: `/api-pipeline/publish/pipelineBuildLogs/downloadBuildLogByPipelineIdAndBuildId/${id}/0`,
    method: 'GET',
    // 下载文件配置
    transformResponse: [axiosApiUtils.download({ fileName: fileName })],
    responseType: 'blob'
  })
}

// 业务系统--仓库配置--数据回显
export async function apiBuniessSelectWarehouseBySystemId(params) {
  return axiosApi({
    url: `/api-base/applicationEngine/selectWarehouseBySystemId`,
    method: 'GET',
    params
  })
}
// 业务系统--仓库配置--仓库名称下拉框
export async function apiBuniessSelectCodeRepByCategoryIdAndEngineId(params) {
  return axiosApi({
    url: `/api-base/applicationEngine/selectCodeRepByCategoryIdAndEngineId`,
    method: 'GET',
    params
  })
}

// 业务系统--分支配置

export async function apiBuniessSelectSvnCodeLineDetail(params) {
  return axiosApi({
    url: `/api-base/applicationCodeLine/selectSvnCodeLineDetail`,
    method: 'GET',
    params
  })
}

// 业务系统--分支配置--检查
export async function apiBuniessCheckCodeLinePath(params) {
  return axiosApi({
    url: `/api-base/applicationCodeLine/checkCodeLinePath`,
    method: 'GET',
    params
  })
}

// 业务系统--分支配置--检查
export async function apiBuniessModuleCodeLineBySystemId(params) {
  return axiosApi({
    url: `/api-base/applicationModuleCodeLine/selectModuleCodeLineBySystemId`,
    method: 'GET',
    params
  })
}
// 流水线--执行历史--历史快照
export async function apiBuniessPipelineBuildHistories(data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineBuildHistory/getParams`,
    method: 'GET',
    data
  })
}
// 流水线--执行历史--历史快照--需求清单
export async function apiBuniessChangeLogsById(id) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineBuildHistory/selectPackChangeLogsByVersion/${id}`,
    method: 'GET'
  })
}

// 流水线--执行历史--历史快照--流水线步骤
export async function apiBuniessgGtPipelineStageStatusById(id, buildId) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineBuildHistory/getPipelineStageStatusById/${id}/${buildId}`,
    method: 'GET'
  })
}

// 流水线--执行历史---流水线步骤
export async function apiBuniessGetPipelineStageByBuildId(id, buildId) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineBuilds/getPipelineStageStatusByIdAndBuildId/${id}/${buildId}`,
    method: 'GET'
  })
}
// 业务系统--配置--分支配置--新增--源分支
export async function apiBuniessDictCodeLineStrategy(id) {
  return axiosApi({
    url: `/api-base/dictCodeLine/strategy/${id}`,
    method: 'GET'
  })
}

// 业务系统--配置-分支配置--保存
export async function apiBuniessImportSvnCodeLine(data) {
  return axiosApi({
    url: `/api-base/applicationCodeLine/importSvnCodeLine`,
    method: 'POST',
    data: axiosApiUtils.formData(data)
  })
}

// 业务系统--配置-分支配置--删除
export async function apiBuniessDeleteCodeLineBySystemId(params) {
  return axiosApi({
    url: `/api-base/applicationCodeLine/deleteCodeLineBySystemIdAndCodelineId`,
    method: 'DELETE',
    params
  })
}

// 业务系统--工程分支配置

export async function apiBuniesSelectGitCodeLineDetail(params) {
  return axiosApi({
    url: `/api-base/applicationCodeLine/selectGitCodeLineDetail`,
    method: 'GET',
    params
  })
}
// 业务系统--工程分支配置--保存

export async function apiBuniesSaveModuleCodeLine(data) {
  return axiosApi({
    url: `/api-base/applicationModuleCodeLine/saveModuleCodeLine`,
    method: 'POST',
    data: axiosApiUtils.formData(data)
  })
}

// 业务系统--仓库配置---新增组

export async function apiBuniesGitCreateGroup(data) {
  return axiosApi({
    url: `/api-base/applicationEngine/gitCreateGroup`,
    method: 'POST',
    data: axiosApiUtils.formData(data)
  })
}

// 业务系统--仓库配置---新增用户

export async function apiBuniesGitCreateUser(data) {
  return axiosApi({
    url: `/api-base/applicationEngine/gitCreateUser`,
    method: 'POST',
    data: axiosApiUtils.formData(data)
  })
}

// 业务系统--工程分支配置--新增仓库地址

export async function apiBuniesGetRepNameListByEngineId(params) {
  return axiosApi({
    url: `/api-scm/api/codeManage/getRepNameListByEngineId`,
    method: 'GET',
    params
  })
}

// 业务系统--配置--编辑--仓库配置--保存

export function apiBuniesScreateSvnRepPath(data) {
  return axiosApi({
    url: '/api-base/applicationEngine/createSvnRepPath',
    method: 'post',
    data: axiosApiUtils.formData(data)
  })
}

// 业务系统-仓库配置--保存

export function apiBuniesUpdateWarehouseConfig(data) {
  return axiosApi({
    url: '/api-base/applicationEngine/updateWarehouseConfig',
    method: 'post',
    data: axiosApiUtils.formData(data)
  })
}

// 流水线--修改--拖拽

export function apiBuniesUpdateStagesOrder(id, data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineBuilds/updateStagesOrder/${id}/stagesOrder`,
    method: 'put',
    data
  })
}

// 流水线--修改--拖拽

export function apiBuniesUpdateStagesOrderSteps(id, index, data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineBuilds/updateStepsOrder/${id}/${index}`,
    method: 'put',
    data
  })
}

// 流水线--停止

export async function apiBuniesStopPipeline(id) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineBuilds/stopPipeline/${id}`,
    method: 'GET'
  })
}

// 流水线

export async function apiBuniesPipelineStepLabels(id) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineJob/${id}/stepLabels`,
    method: 'GET'
  })
}

// 根据业务系统ID获取服务应用信息
export async function selectApplicationBaseByBusinessSystemId(params) {
  if (params.businessSystemId != '') {
    return axiosApi('/api-pipeline/applicationBase/selectAllByParam?businessSystemId=' + params.businessSystemId)
  } else {
    return axiosApi('/api-pipeline/applicationBase/selectAllByParam')
  }
}

// 获取发布记录接口
export async function getRecordByParam(data) {
  return axiosApi({
    url: '/api/pipeline/pipeline/pipelineBuildHistory/page',
    method: 'post',
    data: data
  })
}

// 获取发布记录执行参数接口
export async function getBuildHistoryParam(data) {
  return axiosApi({
    url: '/api/pipeline/pipeline/pipelineBuildHistoryParam/getBuildHistoryParam',
    method: 'post',
    data: data
  })
}

// 获取发布记录结果参数接口
export async function getBuildHistoryResult(data) {
  return axiosApi({
    url: '/api/pipeline/pipeline/pipelineBuildHistoryResult/getBuildHistoryResult',
    method: 'post',
    data: data
  })
}

// 根据服务应用和分支名称获取库名称信息
export async function apiPacName(params) {
  return axiosApi({
    url: '/api/pipeline/pipeline/application/module/selectModuleMapByApplicationId/' + params.applicationId,
    method: 'get'
  })
}

// 根据服务应用,分支,工程名称获取仓库类型和地址和账号密码
export async function getWarehouse(params) {
  return axiosApi({
    url: '/api/pipeline/pipeline/application/selectWarehousePathAndEngineByApplicationIdAndCodeAndModuleId?applicationId=' + params.applicationId + '&code=' + params.code + (params.moduleId == undefined ? '' : '&moduleId=' + params.moduleId),
    method: 'GET'
  })
}

// 标签模式下，根据引擎slaveId和高级属性的key,获取相应的环境变量
export async function selectCustomAttByKey(params) {
  return axiosApi({
    url: '/api/pipeline/pipeline/engine/selectExtendedByKey?engineId=' + params.slaveId + '&key=' + params.key,
    method: 'get'
  })
}

// 标签节点模式下，根据引擎masterId,标签Id,和高级属性的key,获取相应的环境变量
export async function selectAdvancedAttByKeyAndTag(params) {
  return axiosApi({
    url: '/api/pipeline/pipeline/engine/selectExtendedByKey?id=' + params.engineId + '&tagId=' + params.tagId + '&key=' + params.key,
    method: 'get'
  })
}

export async function postPipelineJobUrl(data) {
  return axiosApi('/api-pipeline/publish/pipelineBuilds/postPipelineJobUrl', {
    method: 'POST',
    data
  })
}

// 根据服务应用ID获取代码质量引擎
export async function selectCodeQualityEngineById(params) {
  return axiosApi('/api-cmdb/api/cmdb/businessSystemExhibit/selectCodeQualityEngineById?id=' + params.businessSystemId)
}
// 通用模块 获取自定义步骤脚本命名称变量
export async function scriptSendHistoryByScriptType(params) {
  return axiosApi({
    url: '/api/pipeline/pipeline/pipelineScriptSend/query',
    method: 'post',
    data: params
  })
}
// 测试模块 获取自动化测试变量
export async function selectJettoProEngineById(params) {
  return axiosApi('/api-cmdb/api/cmdb/businessSystemExhibit/selectJettoProEngineById?businessSystemId=' + params.businessSystemId)
}
// 测试模块 获取自动化测试 测试用例集变量
export async function selectByTestProjectBusinessSystemId(params) {
  return axiosApi('/api-cmdb/api/cmdb/businessSystemExhibit/selectByTestProjectBusinessSystemId?businessSystemId=' + params.businessSystemId)
}

// 判断流水线的策略标识是否存在
export async function policyKeyExist(params) {
  return axiosApi({
    url: '/api/pipeline/pipeline/pipelineDeployType/policyKeyExist?pipelineId=' + params.pipelineId + '&policyKey=' + params.policyKey,
    method: 'GET'
  })
}

// 添加流水线部署策略
export async function addPipelinePolicy(data) {
  return axiosApi({
    url: '/api/pipeline/pipeline/pipelineDeploy/addPipelinePolicy',
    method: 'POST',
    data
  })
}

// 切换默认部署策略
export async function updateIsDefaultApi(params) {
  return axiosApi({
    url: '/api/pipeline/pipeline/pipelineDeployType/updateIsDefault?policyKey=' + params.policyKey + '&pipelineId=' + params.pipelineId,
    method: 'get'
  })
}

// 根据业务系统查询版本
export async function selectBusinessSystemVersionList(params) {
  return axiosApi('/api-cmdb/api/cmdb/businessSystemVersionExhibit/selectList?businessSystemId=' + params.businessSystemId)
}

// 获取当前流水线中的步骤标签
export async function getStepLabels(params) {
  return axiosApi({
    url: '/api/pipeline/pipeline/pipelineJob/' + params.id + '/stepLabels',
    method: 'get'
  })
}

// 查询默认部署策略
export async function getPipelineDeployType(params) {
  return axiosApi({
    url: '/api/pipeline/pipeline/pipelineDeployType/getPipelineDeployType?pipelineId=' + params.pipelineId,
    method: 'get'
  })
}

// 查询默认部署策略
export async function getPipelineRevertFile(params) {
  return axiosApi({
    url: '/api/pipeline/pipeline/pipelineRevertFile/page',
    method: 'post',
    data: params
  })
}

export async function delRevertFileById(id) {
  return axiosApi('/api-pipeline/publish/pipelineRevertFile/delRevertFileById/' + id, {
    method: 'DELETE'
  })
}

export async function batchDelRevertFile(params) {
  return axiosApi({
    url: '/api/pipeline/pipeline/pipelineRevertFile',
    method: 'DELETE',
    data: params.ids
  })
}

export async function getRevertFileContent(params) {
  return axiosApi({
    url: '/api/pipeline/pipeline/pipelineRevertFile/getRevertFileContent/' + params.id + '?slaveIp=' + params.slaveIp,
    method: 'get'
  })
}

export async function addRevertFile(params) {
  const form = new FormData()
  form.append('file', params.file)
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineRevertFile/` + params.pipelineId + `/insertRevertFile?pacName=` + params.pacName,
    method: 'post',
    data: form
  })
}

// 查询数据库执行的SQL文件列表
export async function listSqlFile(params) {
  return axiosApi({
    url: '/api/pipeline/pipeline/pipelineSqlOrder/listSqlFile?pipelineId=' + params.pipelineId + '&version=' + params.version + '&executePath=' + params.executePath,
    method: 'get'
  })
}

// 根据条件查询未授权人员列表
export async function apiGetAllUser(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineAuthorize/selectUnauthorizedUserList`,
    method: 'post',
    data: params
  })
}

// 查询选中的用户
export async function apiGetSelectUser(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineAuthorize/query`,
    method: 'post',
    data: params
  })
}

// 保存授权用户
export async function apiSaveAuthorizedUser(data) {
  return axiosApi({
    url: '/api/pipeline/pipeline/pipelineAuthorize/batchInsert',
    method: 'post',
    data: data
  })
}
// 删除授权用户
export async function apiDeleteAuthorizedUser(data) {
  return axiosApi({
    url: '/api/pipeline/pipeline/pipelineAuthorize',
    method: 'delete',
    data: data
  })
}

// 获取项目信息
export async function getProjectInfo(params) {
  return axiosApi({
    url: `/api-alm/project/getProjectInfo`,
    method: 'get',
    params
  })
}
// 修改脚本  /api-base/system/scriptFileFunction/as
export async function apiBaseDicUpdateScript(data) {
  return axiosApi({
    url: `/api-base/system/scriptFileFunction/${data.id}`,
    method: 'put',
    data: axiosApiUtils.fileFormData(data)
  })
}
// 新增脚本提交数据
export async function apiBasePostscriptFile(data) {
  return axiosApi({
    url: `/api-base/system/scriptFileFunction/${data.typeId}`,
    method: 'post',
    data: axiosApiUtils.stringify(data)
  })
}

// 流水线--修改--拖拽

export function apiPipelineUpdateSqlFile(data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineSqlOrder/updateSqlFile`,
    method: 'put',
    data
  })
}

// 流水线-授权
export function getUserGroupByOrgList() {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineAuthorize/selectUserGroupByOrg`,
    method: 'get'
  })
}
// 查询已授权人员
export function getCheckUserList(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineAuthorize/query`,
    method: 'post',
    data: params
  })
}

// 保存授权人员
export function holdUserList(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineAuthorize/batchInsertList`,
    method: 'post',
    data: params
  })
}
// 人工确认
export function peoplePipeUrl(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineBuilds/postPipelineJobUrl`,
    method: 'post',
    data: params
  })
}

// 保存授权信息集合
export function holdPipeAllPermission(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineAuthorize/batchInsertList`,
    method: 'post',
    data: params
  })
}

// 查询授权信息并按机构进行分组
export function getUserListByPipId(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineAuthorize/selectUserAuthorizeByPipeLineId`,
    method: 'get',
    data: params
  })
}
// 根据流水线id和用户名称查询信息(单)
export function searchUserName(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineAuthorize/selectUserNameAndDeptOne`,
    method: 'post',
    data: params
  })
}

// 根据流水线id和用户id删除权限信息
export function delUserByIds(id, params) {
  return axiosApi({
    url: '/api/pipeline/pipeline/pipelineAuthorize/deleteByPipelineIdAndUserId?pipelineId=' + id,
    method: 'DELETE',
    data: params
  })
}

// 根据流水线id和用户名称查询信息(批量)
export function searchUserNameByMore(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineAuthorize/selectUserNameAndDeptMore`,
    method: 'post',
    data: params
  })
}

// 初始化保存接口（批量）
export function holdUserListInit(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineAuthorize/batchInsertList`,
    method: 'post',
    data: params
  })
}

// 增量保存接口（批量）
export function holdUserListAdd(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineAuthorize/batchInsertListNotRemove`,
    method: 'post',
    data: params
  })
}

// 根据流水线集合、人员集合和机构集合保存授权信息
export function holdOrgAndUserInfo(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineAuthorize/batchInsertListByPipelineIdAndOrgAndUserInfo`,
    method: 'post',
    data: params
  })
}

// 根据流水线Id、部门Id查下一级得部门和人
export function getOrgUserBypipelineId(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineAuthorize/selectOrgUserBypipelineIdOrgId`,
    method: 'get',
    data: params
  })
}

// 根据流水线id和用户名称查询信息(批树形结构)
export function searchUserByIdAndName(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineAuthorize/selectDeptAndUserByNameToDeptMore`,
    method: 'post',
    data: params
  })
}
// 根据部门Id查询下面所有的部门和人员信息，根据人id返回部门信息
export function selectOrgUserList(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineAuthorize/selectOrgUserByOrgIdUser`,
    method: 'post',
    data: params
  })
}

// 飞书webhook用户
export function getWebhookUser(params) {
  return axiosApi({
    url: `/api/base/base/webhook/query`,
    method: 'get',
    data: params
  })
}

// 以关联webhook用户
export function getCheckWebhookUser(params) {
  return axiosApi({
    url: `/api/base/base/webhookBiz/query`,
    method: 'get',
    data: params
  })
}

// 新增webhook用户
export function holdWebhookUser(params) {
  return axiosApi({
    url: `/api/base/base/webhookBiz/saveBatch`,
    method: 'post',
    data: params
  })
}

// 删除关联webhook用户
export function delWebhookUser(webhookIds, bizType, bizId) {
  return axiosApi({
    url: `/api/base/base/webhookBiz/` + bizType + '/' + bizId,
    method: 'DELETE',
    data: webhookIds
  })
}

// 根据节点标签获取节点列表
export function getJenkinsCloudById(params) {
  return axiosApi({
    url: `/api/pipeline/pipeline/engine/jenkins/selectJenkinsCloudById`,
    method: 'get',
    data: params
  })
}
// 获取流水线授权团队列表
export function getPipelineTeam(pipelineId) {
  return axiosApi({
    url: `/api/pipeline/pipelineTeam/queryTeam/${pipelineId}`,
    method: 'get'
  })
}
// 项目下流水线授权团队列表
export function getPipelineTeamByProjectId(data) {
  return axiosApi({
    url: `/api/alm/projectTeam/getPipelineTeam`,
    method: 'get',
    data
  })
}
// 保存授权的团队
export function savePipelineTeam(pipelineId, data) {
  return axiosApi({
    url: `/api/pipeline/pipelineTeam/addTeam/${pipelineId}`,
    method: 'post',
    data
  })
}
// 删除流水线授权的团队
export function delPipelineTeam(pipelineId, teamId) {
  return axiosApi({
    url: `/api/pipeline/pipelineTeam/deleteTeam/${pipelineId}/${teamId}`,
    method: 'delete'
  })
}

// 获取流水线执行历史记录分页列表
export function getPipelineHistortData(data) {
  return axiosApi({
    url: `/api/pipeline/pipeline/pipelineBuildHistory/getPipelineBuildHistoryPage`,
    method: 'post',
    data
  })
}
