<template>
  <div class="assign-node">
    <header>
      代码库
      <span class="count">
        {{ list.length }}
      </span>
    </header>
    <section>
      <div
        v-for="item in list.slice((currentPage - 1) * 2, currentPage * 2)"
        :key="item.id"
      >
        <div class="reference">
          <a @click="showCommit(item)">
            {{ item.repositoryName }}
          </a>

          <span class="preTag">
            {{ item.repositoryBranch }}
          </span>
        </div>
      </div>
    </section>
    <el-pagination
      layout="prev, pager, next"
      small
      :page-size="2"
      class="paginationCode"
      :current-page="currentPage"
      :total="list.length"
      @current-change="handleCurrentChange"
    />

    <!-- commit -->
    <el-dialog
      v-model:value="visible"
      close-on-click-modal
      width="30%"
      :modal="false"
      :modal-append-to-body="false"
      append-to-body
    >
      <template v-slot:title>
        <div>
          {{ title }} <el-divider direction="vertical" />
          {{ codeName }}
        </div>
      </template>
      <section v-if="commitList.length" v-loading="commitLoading">
        <el-table :data="commitList" :show-header="false" style="width: 100%">
          <el-table-column prop="title" label="提交信息" show-overflow-tooltip>
            <template v-slot="scope">
              {{ scope.row.title }}
            </template>
          </el-table-column>
          <el-table-column
            prop="committerName"
            label="提交人"
            show-overflow-tooltip
            width="100"
          />
          <el-table-column
            prop="committedDate"
            label="时间"
            show-overflow-tooltip
            width="80"
          >
            <template v-slot="scope">
              {{ $filters.fromNow(scope.row.committedDate) }}
            </template>
          </el-table-column>
        </el-table>
      </section>
      <vone-empty v-else />

      <template v-slot:footer>
        <div>
          <!-- <el-pagination
              small
              layout="prev, pager, next"
              :total="totalPage"
              class="pagination"
            /> -->
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { apiBranchIssueCommits } from '@/api/vone/code/index'
export default {
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      visible: false,
      title: '提交记录',
      codeName: '',
      total: 0,
      current: 1,
      page: 10,
      commitList: [],
      commitLoading: false,
      totalPage: 0,
      currentPage: 1,
    }
  },
  computed: {
    getUserInfo() {
      return function (row) {
        return row?.leadingBy && row?.echoMap?.leadingBy
      }
    },
  },
  mounted() {},
  methods: {
    handleCurrentChange(val) {
      this.currentPage = val
    },
    showCommit(row) {
      this.getCommoitLogs(row.branchId)

      this.visible = true
      this.codeName = row.repositoryName
    },
    async getCommoitLogs(id) {
      // this.commitList = []
      this.commitLoading = true
      const res = await apiBranchIssueCommits(id, this.$route.query.code)
      if (!res.isSuccess) {
        this.$message.error(res.msg)
        return
      }
      this.total = res.data.length
      this.commitList = res.data
      this.commitLoading = false
    },
  },
}
</script>

<style lang="scss" scoped>
.assign-node {
  box-sizing: border-box;
  width: 378px;
  height: 125px;
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.08);
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  header {
    background-color: #eaedf0;
    padding: 0 12px;
    height: 36px;
    line-height: 36px;
    .moreRow {
      float: right;
      font-size: 12px;
      color: var(--main-theme-color);
    }
    .count {
      font-size: 12px;
      font-weight: 600;
    }
  }
  section {
    .reference {
      // background-color: #F5F7FA;
      padding: 0 12px;
      height: 26px;
      line-height: 26px;
      display: flex;
      justify-content: space-between;
      a {
        color: var(--main-theme-color);
      }
      cursor: pointer;
      .preTag {
        // border: 1px solid #8C8C8C;
        color: #8c8c8c;
        font-size: 12px;
        border-radius: 4px;
        display: inline-block;
      }
    }
  }
  .popoverRow {
    margin: 5px 0;
  }
  .paginationCode {
    position: absolute;
    right: 6px;
    bottom: 6px;
  }
  :deep() {
    .el-pagination .el-pager li:not(.disabled).active {
      background-color: #fff;
      color: #2c2e36;
      border: 1px solid var(--input-border-color);
    }
    .el-pagination .btn-prev,
    .el-pagination .btn-next {
      background: none;
    }
  }
}
.rowCommit {
  margin-bottom: 5px; /*// height: 22px;*/ /*// line-height: 22px;*/
  color: #2c2e36;
  font-size: 14px;
  font-family: 'PingFang SC';
}
:deep() {
  .el-dialog .el-dialog__body {
    padding: 4px 8px;
  }
  .el-table td.el-table__cell {
    border-bottom: none;
  }

  .el-table__footer-wrapper {
    border-bottom: none !important;
  }
  .el-pagination .el-pager li:not(.disabled).active {
    background-color: #fff;
    color: #2c2e36;
    border: 1px solid var(--input-border-color);
  }
  .el-pagination .btn-prev,
  .el-pagination .btn-next {
    background: none;
  }
}
</style>
