<template>
  <page-wrapper v-loading="tableLoading" class="wrapper">
    <el-table
      border
      :data="tableData"
      :header-cell-style="{ background: '#F3F7FD', height: '48px' }"
      :cell-style="{ verticalAlign: 'top', paddingTop: '8px' }"
      :row-style="{ height: '80px' }"
      style="width: 100%"
    >
      <el-table-column
        fixed
        class-name="columns-tyle"
        prop="name"
        label="产品/版本看板"
        width="192px"
        show-overflow-tooltip
      />
      <el-table-column
        label-class-name="label-style"
        :resizable="false"
        label="规划中"
      >
        <template v-slot="scope">
          <div v-if="failInfo(scope.row.planIng)">
            <div
              v-for="item in scope.row.planIng"
              :key="item.id"
              class="p-item-purple"
            >
              <div @click="toInfo(scope.row, item)">
                <el-icon><el-icon-document-copy /></el-icon>
                {{ item.name }}
              </div>
            </div>
          </div>
          <div v-else-if="successInfo(scope.row.planIng)">
            <div
              class="p-item-purple"
              @click="toInfo(scope.row, scope.row.planIng[0])"
            >
              {{
                scope.row.planIng &&
                scope.row.planIng.length > 0 &&
                scope.row.planIng[0].name
              }}
            </div>
            <el-popover
              placement="right"
              width="216"
              trigger="click"
              visible-arrow
            >
              <div class="p-item-box">
                <div
                  v-for="it in dataFilter(scope.row.planIng)"
                  :key="it.id"
                  class="p-item-purple"
                >
                  <div @click="toInfo(scope.row, it)">
                    <el-icon><el-icon-document-copy /></el-icon>
                    {{ it.name }}
                  </div>
                </div>
              </div>
              <template v-slot:reference>
                <div class="more">
                  {{ scope.row.planIng && scope.row.planIng.length - 1 }} 版本
                </div>
              </template>
            </el-popover>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label-class-name="label-style"
        :resizable="false"
        prop="address"
        label="进行中"
      >
        <template v-slot="scope">
          <div v-if="failInfo(scope.row.developmentIng)">
            <div
              v-for="item in scope.row.developmentIng"
              :key="item.id"
              class="p-item-blue"
            >
              <div @click="toInfo(scope.row, item)">
                <el-icon><el-icon-document-copy /></el-icon>
                {{ item.name }}
              </div>
            </div>
          </div>
          <div v-else-if="successInfo(scope.row.developmentIng)">
            <div
              class="p-item-blue"
              @click="toInfo(scope.row, scope.row.developmentIng[0])"
            >
              {{
                scope.row.developmentIng &&
                scope.row.developmentIng.length > 0 &&
                scope.row.developmentIng[0].name
              }}
            </div>

            <el-popover
              placement="right"
              width="216"
              trigger="click"
              visible-arrow
            >
              <div class="p-item-box">
                <div
                  v-for="it in dataFilter(scope.row.developmentIng)"
                  :key="it.id"
                  class="p-item-blue"
                >
                  <div @click="toInfo(scope.row, it)">
                    <el-icon><el-icon-document-copy /></el-icon>
                    {{ it.name }}
                  </div>
                </div>
              </div>
              <template v-slot:reference>
                <div class="more">
                  +{{
                    scope.row.developmentIng &&
                    scope.row.developmentIng.length - 1
                  }}版本
                </div>
              </template>
            </el-popover>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label-class-name="label-style"
        :resizable="false"
        prop="address"
        label="发布成功"
      >
        <template v-slot="scope">
          <div v-if="failInfo(scope.row.successIng)">
            <div
              v-for="item in scope.row.successIng"
              :key="item.id"
              class="p-item-green"
            >
              <div @click="toInfo(scope.row, item)">
                <el-icon><el-icon-document-copy /></el-icon>
                {{ item.name }}
              </div>
            </div>
          </div>
          <div v-else-if="successInfo(scope.row.successIng)">
            <div
              class="p-item-green"
              @click="toInfo(scope.row, scope.row.successIng[0])"
            >
              {{
                scope.row.successIng &&
                scope.row.successIng.length > 0 &&
                scope.row.successIng[0].name
              }}
            </div>
            <el-popover
              placement="right"
              width="216"
              trigger="click"
              visible-arrow
            >
              <div class="p-item-box">
                <div
                  v-for="it in dataFilter(scope.row.successIng)"
                  :key="it.id"
                  class="p-item-green"
                >
                  <div @click="toInfo(scope.row, it)">
                    <el-icon><el-icon-document-copy /></el-icon>
                    {{ it.name }}
                  </div>
                </div>
              </div>
              <template v-slot:reference>
                <div class="more">
                  +{{ scope.row.successIng && scope.row.successIng.length - 1 }}
                  版本
                </div>
              </template>
            </el-popover>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label-class-name="label-style"
        :resizable="false"
        prop="address"
        label="发布失败"
      >
        <template v-slot="scope">
          <div v-if="failInfo(scope.row.failIng)">
            <div
              v-for="item in scope.row.failIng"
              :key="item.id"
              class="p-item-orange"
            >
              <div @click="toInfo(scope.row, item)">
                <el-icon><el-icon-document-copy /></el-icon>
                {{ item.name }}
              </div>
            </div>
          </div>
          <div v-else-if="successInfo(scope.row.failIng)">
            <div
              class="p-item-orange"
              @click="toInfo(scope.row, scope.row.failIng[0])"
            >
              {{
                scope.row.failIng &&
                scope.row.failIng.length > 0 &&
                scope.row.failIng[0].name
              }}
            </div>

            <el-popover
              placement="right"
              width="216"
              trigger="click"
              visible-arrow
            >
              <div class="p-item-box">
                <div
                  v-for="it in dataFilter(scope.row.failIng)"
                  :key="it.id"
                  class="p-item-orange"
                >
                  <div @click="toInfo(scope.row, it)">
                    <el-icon><el-icon-document-copy /></el-icon>
                    {{ it.name }}
                  </div>
                </div>
              </div>
              <template v-slot:reference>
                <div class="more">
                  {{ scope.row.failIng && scope.row.failIng.length - 1 }} 版本
                </div>
              </template>
            </el-popover>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </page-wrapper>
</template>

<script>
import { DocumentCopy as ElIconDocumentCopy } from "@element-plus/icons-vue";
import { getProductVersionKanBan } from "@/api/vone/product/board";
const colorlist = [
  {
    type: "1",
    color: "#FC9772",
    background: "rgba(252, 151, 114, 0.12)",
  },
  {
    type: "2",
    color: "#3CB540",
    background: "rgba(60, 181, 64, 0.12)",
  },
  {
    type: "3",
    color: "#64BEFA",
    background: "rgba(100, 190, 250, 0.12)",
  },
  {
    type: "4",
    color: "#BD7FFA",
    background: "rgba(189, 127, 250, 0.12)",
  },
  {
    type: "5",
    color: "#ADB0B8",
    background: "rgba(173, 176, 184, 0.12)",
  },
];
export default {
  components: {
    ElIconDocumentCopy,
  },
  data() {
    return {
      tableLoading: false,
      tableData: [],
      colorlist,
    };
  },
  computed: {
    failInfo() {
      return function (row) {
        return row && row.length > 0 && row.length <= 2;
      };
    },
    successInfo() {
      return function (row) {
        return row && row.length > 0 && row.length > 2;
      };
    },
    dataFilter() {
      return function (row) {
        return row.filter((v) => v.id !== row[0].id);
      };
    },
  },
  mounted() {
    this.getBoard();
  },
  methods: {
    toInfo(item, val) {
      this.$router.push(
        `/product/version/detail/${item.code}/${item.id}/${val.id}`
      );
    },
    async getBoard() {
      this.tableLoading = true;
      const { data } = await getProductVersionKanBan();

      // 固定
      const fixedRoule = data.map((r) => ({
        ...r,
        planIng: r.productVersionList?.filter((v) => v.stateId == 1), // 规划中
        developmentIng: r.productVersionList?.filter((v) => v.stateId == 2), // 开发中
        successIng: r.productVersionList?.filter((v) => v.stateId == 3), // 发布成功
        failIng: r.productVersionList?.filter((v) => v.stateId == 4), // 发布失败
      }));
      this.tableLoading = false;
      this.tableData = fixedRoule;
    },
  },
};
</script>

<style lang="scss" scoped>
.p-item-blue {
  height: 30px;
  line-height: 30px;
  color: #64befa;
  background: rgba(100, 190, 250, 0.12);
  border-left: 3px solid #64befa;
  padding: 0 8px;
  margin-bottom: 4px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  cursor: pointer;
}
.p-item-purple {
  height: 30px;
  line-height: 30px;
  color: #bd7ffa;
  background: rgba(189, 127, 250, 0.12);
  border-left: 3px solid #bd7ffa;
  padding: 0 8px;
  margin-bottom: 4px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  cursor: pointer;
}
.p-item-green {
  height: 30px;
  line-height: 30px;
  color: #3cb540;
  background: rgba(60, 181, 64, 0.12);
  border-left: 3px solid #3cb540;
  padding: 0 8px;
  margin-bottom: 4px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  cursor: pointer;
}
.p-item-orange {
  height: 30px;
  line-height: 30px;
  color: #fc9772;
  background: rgba(252, 151, 114, 0.12);
  border-left: 3px solid #fc9772;
  padding: 0 8px;
  margin-bottom: 4px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  cursor: pointer;
}
.el-table :deep(.cell) {
  padding: 0 8px;
}
.more {
  height: 30px;
  line-height: 30px;
  background: #f5f6fa;
  text-align: center;
  color: #8a8f99;
  cursor: pointer;
}
.el-table__body .el-table__row :deep(.hover-row td) {
  background-color: transparent;
  cursor: pointer;
}
:deep(.el-table .el-table__body tr.hover-row > td) {
  background-color: transparent;
}
</style>

<style>
.label-style {
  font-weight: normal;
}
.columns-tyle {
  font-weight: bold;
}
</style>
