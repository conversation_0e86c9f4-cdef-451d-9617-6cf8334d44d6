<template>
  <!--按项目角色维度统计任务 -->
  <vone-echarts-card :title="formInfo.name" :name="formInfo.name">
    <vone-echarts :options="options" @chartClick="chartClick" />

    <detailDialog
      v-bind="detailParams"
      v-if="detailParams.visible"
      v-model:value="detailParams.visible"
    />
  </vone-echarts-card>
</template>

<script>
import detailDialog from '@/views/widget/detail-data-dialog.vue'
import { apiInsightProjectRoleTask } from '@/api/vone/weidget/index'
import _ from 'lodash'

export default {
  components: {
    detailDialog,
  },
  props: {
    formInfo: {
      type: Object,
      default: null,
    },
    keys: {
      type: String,
      default: null,
    },
  },

  data() {
    return {
      detailParams: { visible: false },
      data: [],
      options: {},
      pieOption: {
        tooltip: {
          trigger: 'item',
          axisPointer: {
            type: 'shadow',
          },
          backgroundColor: '#fff',
          borderColor: 'none',
          extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);', // 附加阴影样式
          textStyle: {
            color: '#53565C',
          },
        },
        legend: {
          orient: 'horizontal',
          x: 'center', // 可设定图例在左、右、居中
          y: 'bottom',
          textStyle: {
            // 图例文字的样式
            color: '#777F8E',
          },
          itemHeight: 8,
          itemWidth: 8,
          icon: 'circle',
        },
        series: [
          {
            name: this.formInfo.roleName || '角色任务统计',
            type: 'pie',
            center: ['50%', '40%'],
            radius: ['55%', '80%'],
            avoidLabelOverlap: false,
            itemStyle: {
              // borderColor: '#fff',
              // borderWidth: 2
            },
            label: {
              show: true,
              position: 'center',
              formatter: '{num|' + 0 + '}' + '\n\r' + '{name|总数}',
              rich: {
                num: {
                  fontSize: 18,
                  color: '#777F8E',
                },
                name: {
                  fontFamily: '微软雅黑',
                  fontSize: 14,
                  color: '#777F8E',
                  lineHeight: 30,
                },
              },
            },
          },
        ],
      },
    }
  },
  watch: {},
  mounted() {
    if (this.formInfo.projectId) {
      this.getOptions()
    }
  },
  methods: {
    async getOptions() {
      const { data, isSuccess, msg } = await apiInsightProjectRoleTask({
        classify: 'TASK',
        y: 'count(*)',
        projectId: this.formInfo.projectId,
        roleId: this.formInfo.roleId,
      })
      if (!isSuccess) {
        this.$message.error(msg)
        return
      }
      data.outList.forEach((element) => {
        element.name = element.key
      })

      // 柱状图数据处理
      const xData = data.outList.map((r) =>
        r.key ? r.key.split('(')[0] : '(空)'
      )

      const yData = data.outList.map((r) => ({
        value: r.value,
        name: r.key,
      }))

      // 折线图数据处理
      const count = _.sum(yData.map((r) => r.value).map(Number))
      const barOption = {
        color: ['#7486eb', '#6ad2a8', '#f68483'],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          backgroundColor: '#fff',
          borderColor: 'none',
          extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);', // 附加阴影样式
          textStyle: {
            color: '#53565C',
          },
          confine: true,
        },
        legend: {
          x: 'right', // 居右显示
          itemHeight: 12,
          itemWidth: 24,
          borderRadius: 5,
          data: [],
          textStyle: {
            // 图例文字的样式
            color: '#777F8E',
          },
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          data: xData,
          splitLine: {
            show: false, // 去掉网格线
          },
          axisLabel: {
            show: true,
            interval: 'auto',
            rotate: 30, // 设置倾斜角度，数值 可设置 正负 两种，
            // interval: 0,
            textStyle: {
              color: '#777F8E', // 更改坐标轴文字颜色
            },
          },
          axisLine: {
            lineStyle: {
              color: '#EBEEF5', // 更改坐标轴颜色
            },
          },
        },
        yAxis: {
          type: 'value',
          axisTick: {
            // y轴刻度线
            show: false,
          },
          minInterval: 1,
          splitLine: {
            // 网格线
            lineStyle: {
              type: 'dashed', // 设置网格线类型 dotted：虚线   solid:实线
              width: 1,
              color: '#EBEEF5',
            },
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#777F8E', // 更改坐标轴文字颜色
            },
          },
          axisLine: {
            lineStyle: {
              color: '#EBEEF5', // 更改坐标轴颜色
            },
          },
        },
        series: [
          {
            name: this.formInfo.roleName || '角色任务统计',
            type: 'bar',
            barWidth: 50,

            data: yData,
          },
        ],
      }

      this.pieOption.series[0].label.formatter =
        '{num|' + count + '}' + '\n\r' + '{name|总数}'
      this.pieOption.series[0].data = yData

      this.options =
        this.formInfo.viewType == 'bar' ? barOption : this.pieOption
    },
    chartClick(params) {
      const form = this.formInfo
      const data = {
        groupData: null,
        xdata: params.data.name,
        isRoleTask: true,
      }

      this.detailParams = { visible: true, form: form, data: data }
    },
  },
}
</script>
