<template>
  <el-dialog
    class="fullBox"
    :show-close="false"
    fullscreen
    :model-value="visible"
    :before-close="onClose"
    append-to-body
  >
    <template v-slot:title>
      <vone-back :title="reportData.testReportName" @back="onClose">
        <template v-slot:toolbar>
          <span>
            <el-button
              type="text"
              :icon="elIconEditDownload"
              @click="createReport"
              >下载</el-button
            >
            <el-button
              type="text"
              :icon="elIconApplicationDelete"
              @click="delFile"
              >删除</el-button
            >
            <el-button
              type="text"
              style="width: 30px"
              :icon="elIconTipsClose"
              @click="onClose"
            />
          </span>
        </template>
      </vone-back>
    </template>
    <div id="pdfDom" class="mainContent">
      <div class="subTitle">
        <el-row>
          <div class="headerTitle">
            <span>概览</span>
          </div>
        </el-row>
        <el-row class="headerRow">
          <el-col :span="12"
            ><span>关联测试计划：</span>
            <span
              v-if="
                reportData && reportData.echoMap && reportData.echoMap.planName
              "
            >
              <span
                v-for="(item, index) in reportData.echoMap.planName"
                :key="index"
                style="color: #202124"
              >
                {{ item ? item : "" }}
              </span>
            </span>
          </el-col>
          <el-col :span="12"
            ><span>生成时间：</span>{{ reportData.createTime }}</el-col
          >
        </el-row>
      </div>
      <div class="rowLine" />
      <div class="tableBox">
        <div class="boxTitle">
          <span class="title">测试结论</span>
        </div>

        <el-row class="contentbox">
          <div class="item">
            <span>总用例数</span>
            <p>{{ reportData.sum }}</p>
          </div>
          <el-divider direction="vertical" class="divider" />
          <div class="item">
            <span>通过数</span>
            <p>{{ reportData.systemNum }}</p>
          </div>
          <el-divider direction="vertical" class="divider" />
          <div class="item">
            <span>失败数</span>
            <p>{{ reportData.smokeNum }}</p>
          </div>
          <el-divider direction="vertical" class="divider" />
          <div class="item">
            <span>阻塞数</span>
            <p>{{ reportData.blockingNum }}</p>
          </div>
          <el-divider direction="vertical" class="divider" />
          <div class="item">
            <span> 跳过数</span>
            <p>{{ reportData.skipNum }}</p>
          </div>
          <el-divider direction="vertical" class="divider" />
          <div class="item">
            <span>未测数</span>
            <p>{{ reportData.undoNum }}</p>
          </div>
          <el-divider direction="vertical" class="divider" />
          <div class="item">
            <span>
              <el-tooltip
                class="item"
                effect="dark"
                :content="'执行率不包括未测用例数'"
                placement="left-start"
              >
                <el-icon class="iconfont"
                  ><el-icon-tips-exclamation-circle
                /></el-icon>
              </el-tooltip>
              执行率
            </span>
            <p>{{ reportData.executedRate }}%</p>
          </div>
        </el-row>
      </div>
      <div class="rowLine" />
      <div class="tableBox">
        <div class="boxTitle"><span class="title">用例执行结果</span></div>
        <el-row type="flex" align="middle">
          <el-col :span="10">
            <vone-echarts
              :options="planResultOptions"
              :style="{ height: '300px' }"
            />
          </el-col>
          <el-col :span="8">
            <el-table class="vone-table" :data="tableDataList">
              <el-table-column prop="name" label="结果" />
              <el-table-column prop="num" label="数量" />
              <el-table-column prop="rate" label="占比(%)" />
            </el-table>
          </el-col>
          <el-col :span="6" class="reportList">
            <div class="executeBox">
              <div class="item">
                <span>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="'执行率不包括未测用例数'"
                    placement="left-start"
                  >
                    <el-icon class="iconfont"
                      ><el-icon-tips-exclamation-circle
                    /></el-icon> </el-tooltip
                  >执行率</span
                >
                <p>{{ reportData.executedRate }}%</p>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <div v-if="defectData.length > 0" class="rowLine" />
      <div v-if="defectData.length > 0" class="tableBox">
        <div class="boxTitle"><span class="title">缺陷分布</span></div>
        <el-row type="flex" align="middle">
          <el-col :span="10">
            <vone-echarts
              :options="defectOption"
              :style="{ height: '300px' }"
            />
          </el-col>
          <el-col :span="8">
            <el-table class="vone-table" :data="defectData">
              <el-table-column prop="name" label="严重程度" />
              <el-table-column prop="value" label="数量" />
              <el-table-column prop="prio" label="占比(%)" />
            </el-table>
          </el-col>
        </el-row>
      </div>
      <div v-if="personalData && personalData.length > 0" class="rowLine" />
      <div v-if="personalData && personalData.length > 0" class="tableBox">
        <div class="boxTitle"><span class="title">成员用例执行统计</span></div>
        <el-row type="flex" align="middle">
          <el-col :span="10">
            <vone-echarts
              :options="personalOption"
              :style="{ height: '300px' }"
            />
          </el-col>
        </el-row>
        <el-col :span="8">
          <el-table ref="table" class="vone-table" :data="personalData">
            <el-table-column prop="userName" label="执行人" />
            <el-table-column prop="successNum" label="通过" />
            <el-table-column prop="failureNum" label="失败" />
            <el-table-column prop="blockNum" label="阻塞" />
            <el-table-column prop="skipNum" label="跳过" />
            <el-table-column prop="sum" label="总计" />
          </el-table>
        </el-col>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { $on, $off, $once, $emit } from "../../../../utils/gogocodeTransfer";
import htmlToPdf from "@/utils/html-to-pdf";
import { apiAlmGetDefectData } from "@/api/vone/project/defect";
import { deleteReport } from "@/api/vone/testmanage/case";
export default {
  props: {
    visible: {
      type: Boolean,
      default: true,
    },
    // 报告详情数据
    reportData: {
      type: Object,
      default: () => ({}),
    },
    planData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      loading: false,
      tableData: [],
      tableDataList: [], // 详情列表
      planResultOptions: {}, // 用例执行结果饼图
      defectData: [], // 缺陷分布数据
      defectMap: {
        HIGHEST: 0,
        HIGH: 0,
        MEDIUM: 0,
        LOW: 0,
        LOWEST: 0,
      },
      defectOption: {}, // 缺陷分布饼图
      personalData: [], // 成员用例数据
      personalOption: {}, // 成员用例执行统计
      showFlag: false,
      darkTheme: false,
    };
  },
  watch: {
    visible(v) {
      if (!v) return;
      this.showFlag = true;
      this.darkTheme = document.body.className.indexOf("dark") > -1;
      this.getPlanReportAll();
      // this.getPlanReportDefect()
      this.getInitTableData();
      // this.getPlanReportPersonal()
    },
  },
  methods: {
    // 下载pdf报告
    createReport() {
      this.showFlag = false;
      setTimeout(() => {
        htmlToPdf("测试报告-" + this.reportData.testReportName, "#pdfDom");
        this.onClose();
      }, 0);
    },
    // 获取计划详情表格信息
    async getPlanReportAll() {
      this.tableData = [this.reportData];
      const { undoNum, systemNum, smokeNum, skipNum, blockingNum } =
        this.reportData;
      // 总数
      const sum =
        Number(undoNum) +
          Number(systemNum) +
          Number(smokeNum) +
          Number(skipNum) +
          Number(blockingNum) || 0;
      this.reportData.sum = sum;
      // 执行率
      this.reportData.executedRate =
        sum > 0 ? (((sum - undoNum) / sum) * 100).toFixed(2) : 0;
      // 设置表格显示数据
      this.tableDataList = [
        {
          name: "未测数",
          num: undoNum,
          rate: sum > 0 ? ((undoNum / sum) * 100).toFixed(2) : 0,
        },
        {
          name: "通过数",
          num: systemNum,
          rate: sum > 0 ? ((systemNum / sum) * 100).toFixed(2) : 0,
        },
        {
          name: "跳过数",
          num: skipNum,
          rate: sum > 0 ? ((skipNum / sum) * 100).toFixed(2) : 0,
        },
        {
          name: "失败数",
          num: smokeNum,
          rate: sum > 0 ? ((smokeNum / sum) * 100).toFixed(2) : 0,
        },
        {
          name: "阻塞数",
          num: blockingNum,
          rate: sum > 0 ? ((blockingNum / sum) * 100).toFixed(2) : 0,
        },
      ];
      // 设置饼图
      this.getPlanReportAllEchart(this.reportData);
    },
    // 获取计划详情图表信息
    async getPlanReportAllEchart(data) {
      this.planResultOptions = {
        color: ["#ADB0B8", "#3CB540", "#BD7FFA", "#FA6B57", "#FFBF47"],
        backgroundColor: this.darkTheme ? "#1b1d23" : "#fff",
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          backgroundColor: this.darkTheme ? "#1b1d23" : "#fff",
          borderColor: "none",
          extraCssText: "box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);", // 附加阴影样式
          textStyle: {
            color: this.darkTheme ? "#e6e9f0" : "#53565C",
          },
        },
        legend: {
          orient: "vertical",
          right: 20,
          bottom: 20,
          data: ["未测", "通过", "跳过", "失败", "阻塞"],
          icon: "circle",
          itemWidth: 8,
          itemHeight: 8,
          textStyle: {
            // 图例文字的样式
            color: this.darkTheme ? "#e6e9f0" : "#8A8F99",
          },
        },
        title: {
          text: data.sum,
          left: "center",
          top: "50%",
          textStyle: {
            color: this.darkTheme ? "#fff" : "#000",
            fontSize: 28,
            align: "center",
          },
        },
        graphic: {
          type: "text",
          left: "center",
          top: "44%",
          style: {
            text: "总用例数",
            textAlign: "center",
            fill: this.darkTheme ? "#e6e9f0" : "#333",
            fontSize: 14,
          },
        },
        series: [
          {
            name: "用例执行结果",
            type: "pie",
            radius: ["50%", "70%"],
            avoidLabelOverlap: false,
            label: {
              normal: {
                show: false,
              },
            },
            emphasis: {
              label: {
                show: true,
                fontSize: "30",
                fontWeight: "bold",
              },
            },
            labelLine: {
              normal: {
                show: false,
              },
            },
            data: [
              { value: data.undoNum, name: "未测" },
              { value: data.systemNum, name: "通过" },
              { value: data.skipNum, name: "跳过" },
              { value: data.smokeNum, name: "失败" },
              { value: data.blockingNum, name: "阻塞" },
            ],
            itemStyle: {
              normal: {
                borderWidth: 2,
                borderColor: this.darkTheme ? "#1b1d23" : "#fff",
              },
              emphasis: {
                borderWidth: 0,
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      };
    },
    // 查询用例缺陷列表
    async getInitTableData() {
      const { id } = this.$route.params;
      const params = {
        current: 1,
        size: 9999,
        extra: {},
        model: {
          projectId: [id],
          testPlanId: [this.reportData.planId],
        },
      };
      const res = await apiAlmGetDefectData(params);
      if (!res.isSuccess) {
        return;
      }
      // 缺陷总数
      const total = res.data.records.length;
      res.data.records.map((item) => {
        const prio = item.priorityCode;
        if (prio) {
          this.defectMap[prio]++;
        }
      });
      console.log(this.defectData, "12121212");
      this.defectData = Object.entries(this.defectMap).map((item) => {
        const [code, value] = item;
        return {
          name:
            code === "HIGHEST"
              ? "最高"
              : code === "HIGH"
              ? "较高"
              : code === "MEDIUM"
              ? "普通"
              : code === "LOW"
              ? "较低"
              : "最低",
          value: value,
          prio: total > 0 ? ((value / total) * 100).toFixed(2) : 0,
        };
      });
      this.getPlanReportDefect();
    },
    // 缺陷严重程度分布
    async getPlanReportDefect() {
      this.defectOption = {
        color: ["#FA6B57", "#FC9772", "#3E7BFA", "#3CB540", "#4BCCBB"],
        backgroundColor: this.darkTheme ? "#1b1d23" : "#fff",
        tooltip: {
          trigger: "item",
          axisPointer: {
            type: "shadow",
          },
          backgroundColor: this.darkTheme ? "#1b1d23" : "#fff",
          borderColor: "none",
          extraCssText: "box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);", // 附加阴影样式
          textStyle: {
            color: this.darkTheme ? "#e6e9f0" : "#53565C",
          },
        },
        legend: {
          orient: "vertical",
          right: 20,
          bottom: 20,
          data: ["最高", "较高", "普通", "较低", "最低"],
          icon: "circle",
          itemWidth: 8,
          itemHeight: 8,
          textStyle: {
            // 图例文字的样式
            color: this.darkTheme ? "#e6e9f0" : "#8A8F99",
          },
        },
        graphic: {
          type: "text",
          left: "center",
          top: "44%",
          style: {
            text: "用例数",
            textAlign: "center",
            fill: this.darkTheme ? "#e6e9f0" : "#333",
            fontSize: 14,
          },
        },
        series: [
          {
            name: "缺陷分布",
            type: "pie",
            // radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: {
              normal: {
                show: false,
              },
            },
            emphasis: {
              label: {
                show: true,
                fontSize: "30",
                fontWeight: "bold",
              },
            },
            labelLine: {
              normal: {
                show: false,
              },
            },
            data: [
              {
                value: this.defectMap.HIGHEST,
                name: "最高",
              },
              {
                value: this.defectMap.HIGH,
                name: "较高",
              },
              {
                value: this.defectMap.MEDIUM,
                name: "普通",
              },
              {
                value: this.defectMap.LOW,
                name: "较低",
              },
              {
                value: this.defectMap.LOWEST,
                name: "最低",
              },
            ],
            itemStyle: {
              normal: {
                borderWidth: 2,
                borderColor: this.darkTheme ? "#1b1d23" : "#fff",
              },
              emphasis: {
                borderWidth: 0,
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      };
    },

    // 查询计划报告人员数据
    async getPlanReportPersonal() {
      const data = [];

      this.personalData = data;

      const personlFilter = data.filter((item) => item.userName !== "总计");

      this.personalOption = {
        color: ["#67C23A", "#FA5555", "#E6A23C", "#0085FF", "#DCDFE6"],
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          backgroundColor: this.darkTheme ? "#1b1d23" : "#fff",
          borderColor: "none",
          extraCssText: "box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);", // 附加阴影样式
          textStyle: {
            color: this.darkTheme ? "#e6e9f0" : "#53565C",
          },
        },
        legend: {
          right: 20,
          data: ["通过", "失败", "阻塞", "跳过"],
          icon: "circle",
          itemWidth: 8,
          itemHeight: 8,
          textStyle: {
            // 图例文字的样式
            color: this.darkTheme ? "#e6e9f0" : "#8A8F99",
          },
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        yAxis: {
          type: "value",
          axisTick: {
            // y轴刻度线
            show: false,
          },
          splitLine: {
            // 网格线
            lineStyle: {
              type: "dashed", // 设置网格线类型 dotted：虚线   solid:实线
              width: 1,
              color: this.darkTheme ? "#495266" : "#EBEEF5",
            },
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: this.darkTheme ? "#e6e9f0" : "#8A8F99", // 更改坐标轴文字颜色
            },
          },
          axisLine: {
            lineStyle: {
              color: this.darkTheme ? "#495266" : "#EBEEF5", // 更改坐标轴颜色
            },
          },
        },
        xAxis: {
          type: "category",
          data: personlFilter.map((item) => {
            return item.userName;
          }),
          boundaryGap: false,
          splitLine: {
            show: false, // 去掉网格线
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: this.darkTheme ? "#e6e9f0" : "#8A8F99", // 更改坐标轴文字颜色
            },
          },
          axisLine: {
            lineStyle: {
              color: this.darkTheme ? "#495266" : "#EBEEF5", // 更改坐标轴颜色
            },
          },
        },
        series: [
          {
            name: "通过",
            type: "bar",
            stack: "总量",
            label: {
              show: false,
            },
            barWidth: 30,
            data: personlFilter.map((item) => {
              return item.successNum;
            }),
          },
          {
            name: "失败",
            type: "bar",
            stack: "总量",
            barWidth: 30,
            label: {
              show: false,
            },
            data: personlFilter.map((item) => {
              return item.failureNum;
            }),
          },
          {
            name: "阻塞",
            type: "bar",
            stack: "总量",
            barWidth: 40,
            label: {
              show: false,
            },
            data: personlFilter.map((item) => {
              return item.blockNum;
            }),
          },
          {
            name: "跳过",
            type: "bar",
            stack: "总量",
            barWidth: 40,
            label: {
              show: false,
            },
            data: personlFilter.map((item) => {
              return item.skipNum;
            }),
          },
        ],
      };
    },
    onClose() {
      $emit(this, "update:visible", false);
    },
    async delFile() {
      try {
        await this.$confirm("确认删除当前项？", "删除", {
          type: "warning",
          customClass: "delConfirm",
        });
        const res = await deleteReport([this.reportData.id]);

        if (!res.isSuccess) {
          return this.$message.error(res.msg);
        }
        this.$message.success("删除成功");
        this.onClose();
        $emit(this, "delSuccess");
      } catch (e) {
        if (e === "cancel") return;
      }
    },
  },
  emits: ["update:visible", "delSuccess"],
};
</script>

<style lang="scss" scoped>
.fullBox {
  #pdfDom {
    padding: 12px;
  }
}
.subTitle {
  padding: 0 6px;
  .headerTitle {
    font-weight: 600;
    font-size: 16px;
    height: 32px;
    line-height: 32px;
    color: #202124;
  }
  .headerRow {
    height: 32px;
    line-height: 32px;
    color: #202124;
    font-weight: 400;
    span {
      color: #8a8f99;
    }
  }
}
.reportList {
  display: flex;
  justify-content: center;
  align-items: center;
  ul {
    display: flex;
    justify-content: center;
    &:nth-child(1) {
      color: #8c8c8c;
    }
    li {
      width: 100px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
:deep(.vone-echarts div) {
  margin: 0 auto;
}
.h40 {
  margin-top: 30px;
  :deep(.el-card__body) {
    padding: 0px;
  }
} /*// 统计数据样式*/
.tableBox {
  margin: 16px 0 0;
  padding: 0 6px;
  .vone-table {
    border: 1px solid #ebeef5;
  }
} /*// 模块标题样式*/
.boxTitle {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  .title {
    font-size: 16px;
    font-weight: 600;
    color: #202124;
    display: flex;
    align-content: center;
  }
  i {
    font-size: 26px;
    color: var(--main-theme-color, #3e7bfa);
    cursor: pointer;
  }
}
.contentbox,
.executeBox {
  display: flex;
  align-items: center;
  border-radius: 6px;
  background: #f7f7f7; /*// margin: 0 12px;*/
  padding: 30px 0;
  .item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 18px;
    width: 196px;
    color: #8a8f99;
    span {
      font-size: 14px;
      color: #2c2e36;
      display: flex;
      i {
        font-size: 12px;
        margin-right: 4px;
        width: auto;
        cursor: pointer;
      }
    }
    p {
      display: flex;
      align-items: center;
      font-size: 22px;
      color: #202124;
      font-weight: bold;
    }
  }
  .divider {
    height: 24px;
    color: #c1c8d6;
  }
}
:deep(.el-card) {
  border-radius: 0px;
  margin-bottom: 0;
}
.fullBox {
  .dialog-title {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  :deep(.el-dialog) {
    min-height: 100vh;
    border-radius: 0;
  }
  :deep(.el-dialog__body) {
    height: calc(100vh - 60px);
    max-height: calc(100vh - 60px);
    background-color: #fafafa;
    padding: 6px;
  }
  :deep(.el-dialog .el-dialog__header) {
    border-bottom: none;
    padding: 12px 20px 0 20px;

    .el-button {
      color: #777f8e;
      &:hover {
        color: var(--main-theme-color);
      }
    }
  }

  :deep(.el-button + .el-button) {
    margin-left: 0px !important;
  }
  .header {
    margin-left: -20px;
    margin-right: -20px;
    padding: 8px 0px 8px 16px;
    box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.1);
  }
  .toolbar {
    :deep(.el-button) {
      padding: 0 !important;
    }
  }
  .mainContent {
    width: 80%;
    margin: 10px auto;
    border-radius: 4px;
    border: 1px solid #f0f0f0;
    background-color: #fff;
  }
  .rowLine {
    border-bottom: 1px solid #f2f3f5;
    margin: 0 -13px 16px;
    padding: 0 16px 16px;
  }
}
</style>
