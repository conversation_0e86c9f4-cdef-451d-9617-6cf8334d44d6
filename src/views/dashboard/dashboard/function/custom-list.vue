<template>
  <el-dialog
    :visible="visible"
    :before-close="close"
    title="编辑部件"
    :close-on-click-modal="false"
    width="600px"
   
  >
    <el-form ref="form" :model="form" :rules="formRule">
      <el-form-item prop="name" label="部件名称">
        <el-input ref="name" v-model.trim="form.name" placeholder="请输入" />
      </el-form-item>
      <el-row :gutter="12">
        <el-col :span="12">
          <el-form-item prop="tableViewId" label="筛选器">
            <el-select v-model="form.tableViewId" filterable>
              <el-option v-for="(item,index) in filterList" :key="index" :label="item.name" :value="item.id">{{ item.name }}</el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="listType" label="二维表类型">
            <el-radio-group v-model="form.listType" style="width: 100%">
              <el-radio :label="0">普通列表</el-radio>
              <el-radio :label="1">统计列表</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

      </el-row>

      <el-row v-if="form.listType == 1" :gutter="12">
        <el-col :span="12">
          <el-form-item prop="group" label="行">
            <el-select v-model="form.group" placeholder="请选择" filterable>
              <el-option v-for="(item,index) in typeList" :key="index" :disabled="item.key == form.group" :value="item.key" :label="item.name" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="x" label="列">
            <el-select v-model="form.x" placeholder="请选择" filterable>
              <el-option v-for="(item,index) in typeList" :key="index" :disabled="item.key == form.group" :value="item.key" :label="item.name" />
            </el-select>
          </el-form-item>

        </el-col>

      </el-row>

      <el-form-item prop="sortT" label="排序">
        <el-select v-model="form.sortT" placeholder="请选择">
          <el-option label="自然排序" value="1" />
          <el-option label="总计排序" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="form.sortT == 2" prop="sortrule" label="排序规则">
        <el-select v-model="form.sortrule" placeholder="请选择">
          <el-option label="升序" value="1" />
          <el-option label="降序" value="2" />
        </el-select>
      </el-form-item>
      <!-- <el-form-item prop="isGroup">
        <el-checkbox v-model="form.isGroup">分组聚合</el-checkbox>
      </el-form-item> -->
      <!-- <el-form-item v-if="form.isGroup" prop="group" label="分组聚合">
        <el-select v-model="form.group" placeholder="请选择">
          <el-option v-for="(item,index) in typeList" :key="index" :disabled="item.key == form.x" :value="item.key" :label="item.name" />
        </el-select>
      </el-form-item> -->

    </el-form>
    <div slot="footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="submit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import _ from 'lodash'
import { getFilterList } from '@/api/vone/dashboard'
import { queryFilterConditions } from '@/api/vone/dashboard/filter'
export default {
  props: {
    visible: Boolean,
    isEditForm: Boolean,
    isEdit: Boolean,
    formInfo: {
      type: Object,
      default: () => {}
    },
    layout: {
      type: [Object, Array],
      default: null
    }
  },
  data() {
    return {
      form: {
        name: '',
        listType: 1,
        y: 'count(*)',
        sortT: '1'
      },
      formRule: {
        name: [{ required: true, message: '请输入名称' }],
        tableViewId: [{ required: true, message: '请选择筛选器' }],
        x: [{ required: true, message: '请选择行' }],
        group: [{ required: true, message: '请选择列' }],
        listType: [{ required: true, message: '请选择二维表类型' }]
        // sortT: [{ required: true, message: '请选择排序' }],
        // sortrule: [{ required: true, message: '请选择排序规则' }],

      },
      filterList: [],
      typeList: [],
      saveLoading: false
    }
  },
  watch: {
    // 'form.isGroup'(val) {
    //   if (!val) {
    //     this.form.group = ''
    //   }
    // }
  },
  mounted() {
    if (this.isEditForm) {
      this.form = this.formInfo
    }
    this.getList()
    this.getTypeList()
  },
  methods: {
    getList() {
      getFilterList({
        table: 'work_item_entity'
      }).then(res => {
        if (res.isSuccess) {
          this.filterList = res.data
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    getTypeList() {
      queryFilterConditions('WORK_ITEM').then(res => {
        if (res.isSuccess) {
          this.typeList = res.data.filter(r => r.key != 'tagId')
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    close() {
      this.$emit('update:visible', false)
      this.$refs.form.resetFields()
    },
    async submit() {
      try {
        await this.$refs.form.validate()
      } catch (e) {
        return
      }
      const layouts = _.cloneDeep(this.layout)
      if (this.form.listType == 1) {
        this.$set(this.form, 'groupName', this.typeList.find(r => r.key == this.form.group).name)
      }
      if (this.isEditForm) {
        layouts.map(item => {
          if (item.id == this.form.id && item.widgetCode == this.form.key) {
            item.queryParams = JSON.stringify({ ...this.form })
          }
        })
      } else {
        layouts.push({
          x: (this.layout.length * 2) % (12),
          y: this.layout.length + (12), // puts it at the bottom
          w: 4,
          h: 4,
          i: this.layout.length + 1,
          queryParams: JSON.stringify({ ...this.formInfo, ...this.form }),
          widgetCode: this.formInfo.key
        })
      }
      this.$emit('update:layout', layouts)
      this.$emit('update:visible', false)
      this.$emit('success')
    }
  }
}
</script>
