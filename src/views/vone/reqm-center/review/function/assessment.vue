<template>
  <page-wrapper class="wrapper">
    <el-row class="content">
      <el-col :span="6" class="left">
        <vone-search-dynamic
          class="search"
          :show-filter="false"
          :model="formData"
          label-position="top"
          @getTableData="getInitTableData"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="名称" prop="name">
                <el-input v-model="formData.name" placeholder="请输入名称" />
              </el-form-item>
            </el-col>
          </el-row>
        </vone-search-dynamic>
        <div class="title">
          <div>
            <span>需求评审</span>
            <span class="title_num">4</span>
          </div>
          <div class="progress">
            <div :style="{ width: 20 + '%', background: '#3CB540' }" />
            <div :style="{ width: 30 + '%', background: '#FFBF47' }" />
            <div :style="{ width: 50 + '%', background: '#ADB0B8' }" />
          </div>
        </div>
        <ul class="workitems-list" style="overflow: auto">
          <li
            v-for="(item, index) in workitemslist"
            :key="index"
            :class="['workitems-list-item', active == index ? 'isActive' : '']"
            @click="selectFn(item, index)"
          >
            <span>
              <i
                :class="`iconfont ${item.icon}`"
                :style="{ color: `${item.color ? item.color : '#ccc'}` }"
              />
              <span class="item-name">{{ item.name }}</span>
            </span>
            <span
              class="item-status"
              :style="{
                color: `${item.status == '1' ? '#3CB540' : '#ADB0B8'}`,
              }"
              >{{ item.status == "1" ? "已评" : "未评" }}</span
            >
          </li>
        </ul>
        <el-pagination
          layout="prev, pager, next"
          class="pagination"
          :total="1000"
        />
      </el-col>
      <el-col :span="18">
        <div class="header-r fixed-top">
          <div class="header-item">
            <span class="r-title">需求评估工单-20220510</span>
            <vone-user-avatar
              avatar-path="avatar16"
              :avatar-type="true"
              height="22px"
              width="22px"
              name="张三"
              :show-name="true"
            />
          </div>
          <div v-if="!auditStatus">
            <el-tag type="danger">剩余5天</el-tag>
            <el-button type="primary">完成评估</el-button>
          </div>
          <div v-else>
            <el-button disabled type="primary">完成评估</el-button>
          </div>
        </div>
        <div class="content-scroll">
          <div class="content-r">
            <div class="item-title">需求基本信息-需求标题</div>
            <el-tag type="success">进行中</el-tag>
          </div>
          <el-form
            class="information"
            label-position="right"
            label-width="80px"
          >
            <el-row>
              <el-col :span="5">
                <el-form-item label="优先级:">123123</el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item label="优先级:">123123</el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item label="优先级:">123123</el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item label="优先级:">123123</el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="描述:"
                  >这里是内容这里是内容这里是内容这里是内容这里是内容这里是内容这里是内容这里是内容这里是内容这里是内容这里是内容这里是内容这里
                  这里是内容这里是内容这里是内容这里是内容这里是内容
                  这里是内容这里是内容这里是内容这里是内容</el-form-item
                >
              </el-col>
              <el-col :span="24">
                <el-form-item label="计划完成:"
                  >2022/05/10-2022/06/07</el-form-item
                >
              </el-col>
            </el-row>
          </el-form>
          <div class="content-r">
            <div class="item-title">相关需求</div>
            <span class="item-num">共<span>6</span>个工作项</span>
          </div>
          <div class="work-list">
            <div class="work-title">
              <el-icon class="iconfont"><el-icon-direction-down /></el-icon>
              <span class="work-title-item" />
              前置依赖
            </div>
            <el-table :data="workitemslist" :show-header="false">
              <el-table-column prop="name" label="姓名" width="180" />
              <el-table-column prop="name" label="地址" width="180" />
            </el-table>
            <div class="work-title">
              <el-icon class="iconfont"><el-icon-direction-down /></el-icon>
              <span class="work-title-item" />
              关联需求
            </div>
            <el-table :data="workitemslist" :show-header="false">
              <el-table-column prop="name" label="姓名" width="180" />
              <el-table-column prop="name" label="地址" width="180" />
            </el-table>
          </div>
          <div class="content-r">
            <div class="item-title">评估</div>
            <span v-if="auditStatus" class="item-num">总分：65</span>
          </div>
          <el-form
            v-if="!auditStatus"
            ref="dynamicValidateForm"
            :model="dynamicValidateForm"
            label-width="110px"
          >
            <el-row>
              <el-col
                v-for="(item, index) in dynamicValidateForm.item"
                :key="index"
                :span="12"
              >
                <el-form-item
                  :label="item.label"
                  :prop="'item.' + index + '.value'"
                  :rules="{
                    required: true,
                    message: '不能为空',
                    trigger: 'blur',
                  }"
                >
                  <el-input v-model="item.value" />
                </el-form-item>
              </el-col>
            </el-row>
            <div class="submit">
              <span
                ><el-icon><el-icon-warning-outline /></el-icon
                >预估总分：78</span
              >
              <el-button type="primary" @click="submitForm">提交</el-button>
            </div>
          </el-form>
          <el-table v-if="auditStatus" :data="workitemslist">
            <el-table-column prop="name" label="评估人" width="180" />
            <el-table-column prop="name" label="类型" width="180" />
            <el-table-column prop="name" label="需求价值" width="180" />
            <el-table-column prop="name" label="需求价值" width="180" />
            <el-table-column prop="name" label="总分" width="180" />
          </el-table>
        </div>
      </el-col>
    </el-row>
  </page-wrapper>
</template>

<script>
import {
  DirectionDown as ElIconDirectionDown,
  WarningOutline as ElIconWarningOutline,
} from "@element-plus/icons-vue";
export default {
  components: {
    ElIconDirectionDown,
    ElIconWarningOutline,
  },
  data() {
    return {
      formData: {},
      workitemslist: [
        {
          name: "这是工作项",
          icon: "el-icon-icon-fengxian",
          color: "#ffbf47",
          status: "1",
        },
        {
          name: "这是工作项2",
          icon: "el-icon-icon-fengxian",
          color: "#ffbf47",
          status: "2",
        },
        {
          name: "这是工作项2",
          icon: "el-icon-icon-fengxian",
          color: "#ffbf47",
          status: "2",
        },
        {
          name: "这是工作项2",
          icon: "el-icon-icon-fengxian",
          color: "#ffbf47",
          status: "2",
        },
        {
          name: "这是工作项2",
          icon: "el-icon-icon-fengxian",
          color: "#ffbf47",
          status: "2",
        },
        {
          name: "这是工作项2",
          icon: "el-icon-icon-fengxian",
          status: "2",
        },
      ],
      dynamicValidateForm: {
        item: [
          {
            label: "Kano类型",
            value: "理应如此",
          },
          {
            label: "需求价值;",
            value: "需求价值",
          },
          {
            label: "符合规划目标:",
            value: "符合规划目标",
          },
          {
            label: "实现难度:",
            value: "理应如此",
          },
        ],
      },
      auditStatus: false,
      active: 0,
    };
  },
  methods: {
    selectFn(e, i) {
      this.active = i;
    },
    getInitTableData() {},
    submitForm() {
      this.auditStatus = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.wrapper {
  padding: 0;
}
.content {
  height: 100%;
}
.left {
  border-right: 1px solid #ebeef5;
  height: calc(100vh - 86px);
  .search {
    padding: 16px;
    :deep(.table-search-main) {
      padding-left: 0;
    }
  }

  .title {
    padding: 16px;
    color: #202124;
    display: flex;
    // margin-top: 18px;
    justify-content: space-between;
    align-items: center;
    .title_num {
      padding: 0 6px;
      height: 15px;
      line-height: 15px;
      font-size: 12px;
      border-radius: 7.5px;
      background: #ebeef5;
      margin-left: 25px;
    }
    .progress {
      width: 120px;
      height: 6px;
      display: flex;
      div {
        height: 100%;
      }
    }
  }
  .workitems-list {
    height: calc(100vh - 243px);
    overflow-y: auto;
    .workitems-list-item {
      cursor: pointer;
      padding: 0 16px;
      height: 36px;
      line-height: 36px;
      display: flex;
      justify-content: space-between;
      &:hover {
        background: #f5f6fa;
      }
      .item-name {
        margin-left: 9px;
      }
      .item-status {
        &::before {
          content: "\2022";
          font-size: 20px;
          display: inline-block;
          margin-right: 8px;
        }
      }
    }
  }
  .pagination {
    height: 60px;
    box-shadow: 0px -2px 14px rgba(41, 82, 166, 0.08);
    display: flex;
    align-items: center;
    :deep(.el-pager li) {
      margin: 0;
    }
  }
}
.header-r {
  height: 56px;
  padding: 0 16px;
  line-height: 56px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  .header-item {
    display: flex;
    align-items: center;
    .r-title {
      font-weight: 600;
      font-size: 16px;
      line-height: 24px;
      color: #202124;
      margin-right: 16px;
    }
  }
  .el-tag {
    margin-right: 22px;
  }
}
.content-r {
  position: relative;
  display: flex;
  align-items: center;
  .item-title {
    height: 56px;
    line-height: 56px;
    display: flex;
    align-items: center;
    padding: 16px;
    &::before {
      content: "";
      width: 4px;
      height: 18px;
      background: #3e7bfa;
      border-radius: 1px;
      position: absolute;
      left: 0px;
    }
  }
  .item-num {
    color: #aeb5c2;
  }
}
.label-style {
  display: inline-block;
  width: 180px;
  text-align: right;
}
.information {
  :deep(.el-form-item__label) {
    color: #8a8f99;
  }
  :deep(.el-form-item) {
    margin-bottom: 0;
  }
}
.work-list {
  padding: 0 47px;
  .work-title {
    margin: 10px 0 25px;
    cursor: pointer;
    .work-title-item {
      display: inline-block;
      width: 10px;
      height: 10px;
      background: #8862fa;
      border-radius: 1.5px;
      margin: 0 10px;
    }
  }
}
.content-scroll {
  height: calc(100vh - 142px);
  overflow-y: auto;
  padding: 0 16px 16px 0;
}
.submit {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  span {
    margin-right: 32px;
    i {
      margin-right: 6px;
    }
  }
}
.isActive {
  .item-name {
    color: #3e7bfa;
  }
}
</style>
