import request from '@/utils/axios-api'

// 查询产品工作项
export function productWork(data) {
  return request({
    url: `/api/alm/alm/requirement/queryByProductIdAndTypeClassifyAndTypeCode`,
    method: `post`,
    data
  })
}
//  查询发布记录
export function recordList(data) {
  return request({
    url: `/api/product/productRelease/page`,
    method: `post`,
    data
  })
}
// 新增发布记录
export function productReleaseAdd(data) {
  return request({
    url: `/api/product/productRelease`,
    method: `post`,
    data
  })
}
// 删除发布记录
export function productReleaseDel(data) {
  return request({
    url: `/api/product/productRelease`,
    method: `DELETE`,
    data
  })
}
