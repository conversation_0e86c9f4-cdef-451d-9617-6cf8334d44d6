<template>
  <page-wrapper>
    <vone-search-wrapper>
      <template v-slot:search>
        <vone-search-dynamic
          ref="searchForm"
          table-search-key="baseUserTable"
          :model="formData"
          :default-fileds="defaultFileds"
          show-basic
          :extra="extraData"
          :table-ref="$refs['baseUserTable']"
          @getTableData="getTableData"
        />
      </template>
      <template v-slot:actions>
        <el-button
          :icon="elIconApplicationRenew"
          size="small"
          @click="syncUserData"
          >同步用户</el-button
        >
        <el-button
          type="primary"
          :icon="elIconTipsPlusCircle"
          size="small"
          :disabled="!$permission('base_user_add')"
          @click="clickAddRow"
          >新增</el-button
        >
        <el-dropdown trigger="click" @command="(e) => e && e()">
          <el-button class="btnMore"
            ><el-icon class="iconfont"><el-icon-application-more /></el-icon
          ></el-button>
          <template v-slot:dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="(item, index) in actions"
                :key="index"
                :icon="item.icon"
                :command="item.fn"
                :disabled="item.disabled"
              >
                {{ item.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
      <template v-slot:fliter>
        <vone-search-filter
          :extra="extraData"
          :model="formData"
          :default-fileds="defaultFileds"
          @getTableData="getTableData"
        />
      </template>
    </vone-search-wrapper>
    <div :style="{ height: tableHeight }">
      <vxe-table
        ref="baseUserTable"
        class="vone-vxe-table"
        border
        auto-resize
        height="auto"
        show-overflow="tooltip"
        :loading="pageLoading"
        :empty-render="{ name: 'empty' }"
        :data="data.records"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column type="checkbox" width="36" fixed="left" align="center" />
        <vxe-column title="账号" field="account" width="150" fixed="left">
          <template #default="{ row }">
            <a @click="showInfo(row)">{{ row.account }}</a>
          </template>
        </vxe-column>
        <vxe-column title="名称" field="name" width="150">
          <template #default="{ row }">
            <vone-user-avatar
              :avatar-path="row.avatarPath"
              :avatar-type="row.avatarType"
              :name="row.name"
            />
          </template>
        </vxe-column>
        <vxe-column title="邮箱" field="email" width="150" />
        <vxe-column title="角色" field="role" width="150">
          <template #default="{ row }">
            {{ row.allRole }}
          </template>
        </vxe-column>
        <vxe-column title="用户类型" field="type" width="120">
          <template #default="{ row }">
            {{ userTypeName(row) }}
          </template>
        </vxe-column>
        <vxe-column title="机构" field="org" width="150">
          <template #default="{ row }">
            {{ orgName(row) }}
          </template>
        </vxe-column>
        <vxe-column title="是否报工" field="reportWork" width="100">
          <template #default="{ row }">
            <span>
              {{ row.reportWork ? "是" : "否" }}
            </span>
          </template>
        </vxe-column>
        <vxe-column title="状态" field="state" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.state"
              class="openSwitch"
              :disabled="row.readonly || !$permission('base_user_edit')"
              active-text="启用"
              inactive-text="禁用"
              @change="editStatus(row)"
            />
          </template>
        </vxe-column>
        <vxe-column
          field="updateTime"
          show-overflow="tooltip"
          title="更新时间"
          width="180"
        />
        <vxe-column field="mobile" title="手机号" />
        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <template>
              <el-tooltip class="item" content="编辑" placement="top">
                <el-button
                  type="text"
                  :disabled="row.readonly || !$permission('base_user_edit')"
                  :icon=" elIconApplicationEdit icon_click"
                  @click="editClickRow(row)"
                />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="重置密码" placement="top">
                <el-button
                  type="text"
                  :disabled="row.readonly || !$permission('base_user_password')"
                  :icon=" elIconApplicationPasswordReset icon_click"
                  @click="resetPassWord(row)"
                />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="删除" placement="top">
                <el-button
                  type="text"
                  :disabled="row.readonly || !$permission('base_user_delete')"
                  :icon=" elIconApplicationDelete icon_click"
                  @click="deleteRow(row)"
                />
              </el-tooltip>
            </template>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <vone-pagination
      ref="pagination"
      :total="data.total"
      @update="getTableData"
    />
    <!-- 重置密码对话框 -->
    <passWordDialog
      v-bind="passWordDialogParam"
      v-model:value="passWordDialogParam.visible"
      @success="getTableData"
    />
    <!-- 用户信息抽屉 -->
    <userInfoDrawer
      v-bind="userDrawerParam"
      v-if="userDrawerParam.visible"
      v-model:value="userDrawerParam.visible"
      :table-list="tableList"
    />
    <!-- 新增编辑用户 -->
    <addOrEditDrawer
      v-bind="addParam"
      v-if="addParam.visible"
      v-model:value="addParam.visible"
      @success="getTableData"
    />
    <!-- 导入 -->
    <!-- <vone-import-file v-if="importParam.visible" v-bind="importParam" v-model="importParam.visible" @success="getTableData" /> -->
    <importDialog
      v-bind="importParam"
      v-if="importParam.visible"
      v-model:value="importParam.visible"
      @success="getTableData"
    />
  </page-wrapper>
</template>

<script>
import {
  getUserList,
  deleteUser,
  // apiBaseUserExport,
  updateStateById,
  syncUser,
} from "@/api/vone/base/user";
import passWordDialog from "./function/passwors-dialog.vue";
import userInfoDrawer from "./function/userInfo-drawer.vue";
import addOrEditDrawer from "./addOrEditDrawer.vue";
import importDialog from "./function/import-dialog.vue";

import { download } from "@/utils";
import { gainTreeList } from "@/utils";
import { orgList } from "@/api/vone/base/org";

import { apiBaseFileLoad } from "@/api/vone/base/file";
import { getUser } from "@/utils/auth";
export default {
  components: {
    passWordDialog,
    userInfoDrawer,
    addOrEditDrawer,
    importDialog,
  },
  data() {
    return {
      extraData: {},
      defaultFileds: [
        {
          key: "name",
          name: "用户名称",
          type: {
            code: "INPUT",
          },
          placeholder: "请输入用户名称",
        },
        {
          key: "orgId",
          name: "所属机构",
          type: {
            code: "TREE",
          },
          placeholder: "请选择所属机构",
        },
        {
          key: "account",
          name: "登录账号",
          type: {
            code: "INPUT",
          },
          placeholder: "请输入登录账号",
        },
      ],
      formData: {
        name: "",
        account: "",
        orgId: null,
      },
      tableList: [],
      exportLoading: false,
      pageLoading: false,
      actions: [
        {
          name: "批量删除",
          fn: this.deleteAll,
          disabled: !this.$permission("base_user_delete"),
        },
        {
          name: "导入",
          icon: "iconfont el-icon-edit-import",
          fn: this.imPort,
          disabled: !this.$permission("base_user_import"),
        },
        {
          name: "导出",
          icon: "iconfont el-icon-edit-export",
          fn: this.exportFlie,
          disabled: !this.$permission("base_user_export"),
        },
      ],
      data: {},
      passWordDialogParam: { visible: false },
      userDrawerParam: { visible: false },
      addParam: { visible: false },
      importParam: { visible: false }, // 用户导入
      loginUser: getUser(),
    };
  },
  computed: {
    userTypeName() {
      return function (row) {
        return row.echoMap?.type?.name || "";
      };
    },
    orgName() {
      return function (row) {
        return row.echoMap?.orgId?.name || "";
      };
    },
    tableHeight() {
      const height = (this.extraData?.height || 0) + "px";
      return `calc(${this.$reduceTableHeight} - ${height})`;
    },
  },
  mounted() {
    this.getOrgList();
  },
  methods: {
    // 同步用户
    syncUserData() {
      syncUser().then((res) => {
        if (res.isSuccess) {
          this.$message.success(res.msg);
        } else {
          this.$message.warning(res.msg);
        }
      });
    },
    // 导入用户
    imPort() {
      this.importParam = {
        visible: true,
        title: "用户",
        url: "/api/base/base/user/downloadImportTemplate",
        importUrl: "/api/base/base/user/import",
      };
    },
    // 用户详情
    showInfo(row) {
      this.userDrawerParam = { visible: true, id: row.id };
    },
    // 导出用户
    async exportFlie() {
      try {
        this.exportLoading = true;

        download(
          `用户信息.xls`,
          await apiBaseFileLoad("/api/base/base/user/export", {})
        );

        this.exportLoading = false;
      } catch (e) {
        this.exportLoading = false;
        return;
      }
    },
    // 数据源接口返回数据以后,把值塞到表单模板里
    setData(list, key, data) {
      list.forEach((e, index) => {
        if (e.key == key) {
          e["optionList"] = data;
        }
      });
    },
    // 查询所有机构
    getOrgList() {
      orgList().then((res) => {
        const orgTree = gainTreeList(res.data);

        this.setData(this.defaultFileds, "orgId", orgTree);
      });
    },
    // 冻结用户
    async editStatus(row) {
      const { isSuccess, msg } = await updateStateById(row.id, row.state);
      if (!isSuccess) {
        this.$message.error(msg);
        return;
      }
      this.$message.success("修改用户状态成功");
      this.getTableData();
    },

    // 获取表格数据
    getTableData() {
      let params = {};
      this.pageLoading = true;
      const pageObj = this.$refs.pagination?.pageObj || {
        current: 1,
        size: 20,
      };
      const sortObj = this.$refs.searchForm?.sortObj;
      params = {
        ...pageObj,
        ...sortObj,
        extra: {
          ...this.extraData,
        },
        model: { ...this.formData },
      };
      getUserList(params).then((res) => {
        if (!res.isSuccess) {
          this.$message.warning(res.msg);
          return;
        }
        this.pageLoading = false;

        res.data.records.forEach((element) => {
          element.allRole =
            element.userRoles && element.userRoles.length
              ? element.userRoles.map((r) => r.echoMap.roleId.name).join("、")
              : "";

          element.allGroup =
            element.userGroupUsers && element.userGroupUsers.length
              ? element.userGroupUsers
                  .map((r) => r.echoMap.userGroupId.name)
                  .join(",")
              : "";

          element.allOrgPath =
            element.orgs && element.orgs.length
              ? element.orgs.map((r) => r.name).join(" / ")
              : "";
        });

        this.data = res.data;
        this.tableList = res.data.records;
      });
    },
    // 删除当前行
    deleteRow(row) {
      if (row.id == this.loginUser?.id) {
        this.$message.warning(
          `用户${this.loginUser?.name}正在使用，不允许删除`
        );
        return;
      }
      this.$confirm(`是否删除用户【${row.name}】?`, "删除", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        customClass: "delConfirm",
        showClose: false,
        type: "warning",
      })
        .then(() => {
          deleteUser([row.id]).then((res) => {
            if (res.isSuccess) {
              this.$message.success("删除成功");
              this.getTableData();
            } else {
              this.$message.warning(res.msg);
            }
          });
        })
        .catch(() => {});
    },
    // 批量删除
    async deleteAll() {
      const selectData = this.getVxeTableSelectData("baseUserTable");
      if (!selectData.length) {
        this.$message.warning("请选择要删除的数据");
        return;
      }
      const useUser = selectData.filter(
        (item) => item.id == this.loginUser?.id
      );
      if (useUser?.length > 0) {
        this.$message.warning(
          `用户${this.loginUser?.name}正在使用，不允许删除`
        );
        return;
      }
      const orgUser = selectData.filter((item) => item.type == "LOCAL_TYPE");
      if (orgUser?.length > 0) {
        this.$message.warning(`集团用户不允许删除`);
        return;
      }
      try {
        await this.$confirm(
          `是否删除 ${selectData.length} 条数据?`,
          "批量删除",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            customClass: "delConfirm",
            showClose: false,
            type: "warning",
          }
        );
        this.pageLoading = true;
        const selectId = selectData.map((r) => r.id);
        const res = await deleteUser(selectId);
        this.pageLoading = false;
        if (!res.isSuccess) {
          this.$message.error(res.msg);
          return;
        }
        this.$message.success("删除成功");
        this.getTableData();
      } catch (e) {
        this.pageLoading = false;
      }
    },
    clickAddRow() {
      this.addParam = { visible: true, title: "新增用户" };
    },
    // 编辑当前行
    editClickRow(row) {
      this.addParam = {
        visible: true,
        title: "编辑用户",
        id: row.id,
        orgId: row.orgId,
      };
    },
    // 修改密码
    resetPassWord(row) {
      this.passWordDialogParam = { visible: true, id: row.id };
    },
  },
};
</script>

<style lang="scss" scoped>
:deep(.el-col-12) {
  height: 80px;
}
</style>
