
<template>
  <page-wrapper id="graph" ref="graph" class="graph" />
</template>
<script>
import { data } from './data'
import G6 from '@antv/g6'
import customNode from './registerNode'
import customEdge from './registerEdge'
export default {
  name: 'Graph',
  props: {
    graphData: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      data: data,
      graph: null,
      container: null
    }
  },
  watch: {
    async graphData(val) {
      if (val) {
        this.data = val
        // this.graph.changeData(val)
        // this.graph.fitView()
        // 自定义节点
        customNode.init()
        // 自定义边
        customEdge.init()
        await this.initGraph(this.data)
        window.addEventListener('resize', this.handleWindowResize)
      }
    }
  },
  async mounted() {

  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleWindowResize)
    this.graph = null
    this.container = null
  },
  methods: {
    initGraph(data) {
      const container = this.$refs.graph.$el
      const width = container.clientWidth
      const height = container.clientHeight
      const graph = new G6.TreeGraph({
        container: container,
        width,
        height,
        fitView: true,
        modes: {
          default: [
            'drag-canvas',
            {
              type: 'zoom-canvas',
              minZoom: 0.5,
              maxZoom: 1.5
            }
          ]
        },
        defaultNode: {
          type: 'flow-rect'
        },
        defaultEdge: {
          type: 'hvh',
          style: {
            lineWidth: 2,
            stroke: '#8862FA'
          }
        },
        layout: {
          type: 'mindmap',
          direction: 'H',
          getHeight: () => {
            return 60
          },
          getVGap: () => {
            return 40
          },
          getHGap: () => {
            return 100
          }
        }
      })
      this.container = container
      this.graph = graph
      graph.data(data)
      graph.render()
      graph.fitView()
      const handleCollapse = (e) => {
        const target = e.target
        const id = target.get('modelId')
        const item = graph.findById(id)
        const nodeModel = item.getModel()
        nodeModel.collapsed = !nodeModel.collapsed
        graph.layout()
        graph.setItemState(item, 'collapse', nodeModel.collapsed)
      }
      graph.on('collapse-text:click', (e) => {
        handleCollapse(e)
      })
      graph.on('collapse-back:click', (e) => {
        handleCollapse(e)
      })
    },
    handleWindowResize() {
      if (typeof window !== 'undefined') {
        window.onresize = () => {
          if (!this.graph || this.graph.get('destroyed')) return
          if (!this.container || !this.container.scrollWidth || !this.container.scrollHeight) return
          this.graph.changeSize(this.container.scrollWidth, this.container.scrollHeight)
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
#graph {
  min-height: calc(100vh - 320px);
}
</style>
