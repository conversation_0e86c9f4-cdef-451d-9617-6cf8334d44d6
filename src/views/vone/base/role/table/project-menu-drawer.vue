<template>
  <!-- 配置项目角色资源 -->
  <vone-drawer
    v-loading="loading"
    :title="title"
    v-model:value="visible"
    size="sm"
    :before-close="onClose"
  >
    <div class="treeActions">
      <el-input
        v-model="functionName"
        style="width: 100%; margin-bottom: 5px"
        placeholder="输入关键字过滤"
      />
      <el-checkbox
        v-if="title == '配置菜单资源'"
        v-model="checkedMenu"
        class="all"
        :indeterminate="isIndeterminate"
        @change="(val) => checkedAll(val, true)"
        >全选/反选</el-checkbox
      >
    </div>

    <div>
      <el-tree
        ref="tree"
        v-model="checkData"
        v-loading="treeLoading"
        :check-strictly="true"
        :default-checked-keys="checkData"
        :data="treeData"
        :props="defaultProps"
        default-expand-all
        show-checkbox
        :filter-node-method="filterNode"
        node-key="id"
        @check="checkMenu"
      >
        <template v-slot="{ node, data }">
          <span class="custom-node">
            <el-tag v-if="data.isButton" size="mini" class="menuTag"
              >功能</el-tag
            >
            <el-tag v-else size="mini" class="funcTag">菜单</el-tag>
            <span>{{ node.label }}</span>
            <!-- <span>
                  {{ data.code }}
                </span> -->
          </span>
        </template>
      </el-tree>
    </div>
    <template v-slot:footer>
      <div>
        <el-button
          v-if="title == '配置菜单资源'"
          type="primary"
          :loading="saveLoading"
          @click="sureAdd"
          >确定</el-button
        >
        <el-button @click="onClose">取消</el-button>
      </div>
    </template>
  </vone-drawer>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import {
  getAllProjectMenusByTypeCode,
  apiAlmSaveRoleFunction,
  apiAlmProjectGetRoleFunction,
} from '@/api/vone/project/setting'
import { checkRole } from '@/api/vone/base/role'
import { list2Tree } from '@/utils/list2Tree'

// 切换id选中
const triggerCheckId = (list, v, flag) => {
  let index = list.indexOf(v)
  // 全选且默认菜单不包括当前项
  if (flag && index === -1) {
    list.push(v)
  } else if (!flag) {
    // 取消全选且默认菜单包括当前项
    while (index !== -1) {
      list.splice(index, 1)
      index = list.indexOf(v)
    }
  }
}

export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: undefined,
    },
    typeCode: {
      type: String,
      default: undefined,
    },
    title: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      allData: [],
      timer: null,
      checkedMenu: false, // 是否全选
      isIndeterminate: false,
      saveLoading: false,
      functionName: '',
      loading: false,
      treeLoading: false,
      treeData: [],
      checkData: [],
      defaultProps: {
        children: 'children',
        label: 'name',
      },
    }
  },
  watch: {
    functionName(val) {
      // 过滤节点树数据
      clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.$refs.tree.filter(val)
      }, 1000)
    },
  },
  async mounted() {
    await this.getAllRoleFunction()
    await this.getRoleFunction()
  },
  methods: {
    onClose() {
      $emit(this, 'update:visible', false)
    },
    checkedAll(val) {
      if (val) {
        this.$refs.tree.setCheckedKeys(this.allData.map((r) => r.id))
      } else {
        this.$refs.tree.setCheckedKeys([])
      }
    },
    // 过滤节点方法
    filterNode(value, data) {
      if (!value) return true
      return data.name?.indexOf(value) !== -1
    },
    async getRoleFunction() {
      this.treeLoading = true
      const res = await apiAlmProjectGetRoleFunction(this.id)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$refs.tree.setCheckedKeys(res.data.map((r) => r.id))
      this.treeLoading = false

      if (this.allData.length === res.data.length) {
        this.checkedMenu = true
      } else {
        this.checkedMenu = false
      }
    },
    async getAllRoleFunction() {
      this.treeLoading = true

      const res = await getAllProjectMenusByTypeCode(this.typeCode)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.allData = res.data
      this.treeData = list2Tree(res.data, { parentKey: 'parentId' })
      this.treeLoading = false
    },
    sureAdd() {
      try {
        checkRole(this.id).then((res) => {
          if (res.isSuccess) {
            if (res.data) {
              this.$confirm(
                '该角色被使用是否继续保存，保存后将同步修改所有使用该角色的项目成员权限',
                '提示',
                {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning',
                }
              ).then(() => {
                this.hold()
              })
            } else {
              this.hold()
            }
          } else {
            this.$message.warning(res.msg)
          }
        })
      } catch (e) {
        this.saveLoading = false
      }
    },
    async hold() {
      this.saveLoading = true

      const checkData = this.$refs.tree.getCheckedNodes()
      const halfCheckedKeys = this.$refs.tree.getHalfCheckedKeys()

      const Data = [
        ...halfCheckedKeys,
        ...checkData.filter((r) => r.id != undefined).map((r) => r.id),
      ]

      const res = await apiAlmSaveRoleFunction(this.id, Data)
      this.saveLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('保存成功')
      $emit(this, 'success')
      this.onClose()
    },
    checkMenu(data, node) {
      // 当前节点是否被选中
      const selected = node.checkedKeys.indexOf(data.id) > -1 // -1未选中
      if (data.parentId) {
        this.$refs.tree.setChecked(data.parentId, true)
      }
      //  处理当前节点子节点选中状态
      this.uniteChildSame(data, selected)
    },
    // 统一处理子节点为相同的勾选状态
    uniteChildSame(data, isSelected) {
      // 设置当前节点选中
      this.$refs.tree.setChecked(data.id, isSelected)
      const list = []
      // // 类型为菜单
      // if (data.type?.code === 'MENU') {
      //   list = this.roleAuthority.menuIdList
      // } else if (data.type?.code === 'FUNCTION') {
      //   // 类型为功能
      //   list = this.roleAuthority.functionIdList
      // }
      // 设置节点id选中状态
      triggerCheckId(list, data.id, isSelected)
      // 是否存在子数据
      if (data.children?.length > 0) {
        for (let i = 0; i < data.children.length; i++) {
          this.uniteChildSame(data.children[i], isSelected)
        }
      }
    },
  },
  emits: ['update:visible', 'success'],
}
</script>

<style lang="scss" scoped>
:deep(.vone-el-drawer__layout) {
  padding: 20px;
}
.custom-node {
  .menuTag {
    margin-right: 8px;
    color: #bd7ffa;
    border-color: #bd7ffa;
    background-color: var(--main-bg-color, #fff);
  }
  .funcTag {
    margin-right: 8px;
    color: #ffbf47;
    border-color: #ffbf47;
    background-color: var(--main-bg-color, #fff);
  }
}
.treeActions {
  padding-left: 8px;
  .all {
    margin: 16px 0;
  }
}
</style>
