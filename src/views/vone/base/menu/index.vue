<template>
  <div class="wapper-content page-wrapper-content">
    <header>
      <strong>应用中心</strong>
      <div class="table-actions">
        <el-button
          :disabled="!$permission('base_menu_license')"
          @click="clickToLicense"
          >Licenses信息</el-button
        >

        <el-button
          type="primary"
          :disabled="!$permission('base_menu_add')"
          :icon="elIconTipsPlusCircle"
          @click="clickAddApp"
          >新增</el-button
        >
      </div>
    </header>

    <section v-loading="pageLoading">
      <aside>
        <ul>
          <li
            v-for="(item, idx) in menuGroup"
            :key="idx"
            :class="idx == index ? 'selected' : ''"
            @click.stop="changeId(idx)"
          >
            <div class="row-li">
              {{ item }}
            </div>
          </li>
        </ul>
      </aside>

      <main
        ref="itemList"
        :style="index > 0 ? 'box-shadow: 0 -2px 0 rgba(84, 151, 215,.2)' : ''"
        @scroll="handleScroll($event)"
      >
        <div ref="rigth">
          <div v-for="item in menuList" :key="item.id">
            <div class="card">
              <strong>{{ item.groupName }}</strong>
              <el-row :gutter="16" style="margin: 16px 0">
                <el-col v-for="menu in item.children" :key="menu.id" :span="6">
                  <el-card class="card-content" @click="toEdit(menu)">
                    <el-row type="flex" style="margin-bottom: 26px">
                      <svg-icon
                        v-if="menu.code == 'vone_chat'"
                        icon-class="chat"
                      />
                      <svg-icon
                        v-else-if="menu.code == 'vone_copilot'"
                        icon-class="copilot"
                      />
                      <svg v-else class="icon svg-icon" aria-hidden="true">
                        <use :xlink:href="`#${menu.icon}`" />
                      </svg>
                      <span class="ml-16">
                        <el-row type="flex" justify="space-between">
                          <span>
                            <strong>{{ menu.name }}</strong>
                            <span v-if="!menu.state" class="closeTime bgGry">
                              未授权
                            </span>
                            <span
                              v-else
                              class="closeTime"
                              :style="{
                                backgroundColor: getColor(menu.failureTime),
                              }"
                            >
                              {{ getText(menu.failureTime) }}
                            </span>
                          </span>

                          <!-- <el-switch v-model="menu.isActive" :active-value="true" :inactive-value="false" size="mini" active-color="#3E7BFA" /> -->
                        </el-row>

                        <article>
                          {{ menu.description }}
                        </article>
                      </span>
                    </el-row>
                    <footer @click.stop>
                      <el-button
                        :disabled="
                          !$permission('base_menu_edit') || menu.readonly
                        "
                        class="miniBtn"
                        @click.stop="toEditApp(menu)"
                      >
                        编辑
                      </el-button>
                      <el-button
                        :disabled="
                          !$permission('base_menu_delete') || menu.readonly
                        "
                        class="miniBtn"
                        @click.stop="delApp(menu)"
                      >
                        删除
                      </el-button>
                    </footer>
                  </el-card>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>
      </main>
    </section>

    <!-- 新增编辑抽屉 -->
    <menuDrawer
      v-bind="menuParam"
      v-if="menuParam.visible"
      v-model:value="menuParam.visible"
      @success="getMenuList"
    />
  </div>
</template>

<script>
import { apiBaseMenuNoPage, apiBaseMenuDelApp } from "@/api/vone/base/meun";
import menuDrawer from "./menuDrawer.vue";
import _ from "lodash";
export default {
  components: {
    menuDrawer,
  },
  data() {
    return {
      menuGroup: ["协同管理", "持续交付", "基础应用", "外部应用"],
      menuList: [],
      index: 0,
      scrollY: 0, // 左侧列表滑动的y轴坐标
      scorllEvent: false,
      menuParam: { visible: false },
      pageLoading: true,
      dataMax: 0,
    };
  },
  computed: {
    getColor() {
      return function (count) {
        if (!count || count == "-1") return;
        const time = Math.ceil(
          (count - new Date().getTime()) / 1000 / 24 / 3600
        );
        if (time <= 15) {
          return "#F7A500";
        } else if (time <= 0) {
          return "#DB2C3A";
        }
      };
    },
    getText() {
      return function (count) {
        if (!count || count == "-1") return;
        //  Math.ceil((Number(count)) / 1000 / 24 / 3600)
        const time = Math.ceil(
          (count - new Date().getTime()) / 1000 / 24 / 3600
        );

        if (time <= 15) {
          return "临期";
        } else if (time <= 0) {
          return "已失效";
        }
      };
    },
  },
  watch: {
    scrollY() {
      this.initRightBoxHeight();
    },
  },
  mounted() {
    this.getMenuList();
    // 启动鼠标滚动监听
    window.addEventListener("mousewheel", this.setScorllEvent, true) ||
      window.addEventListener("DOMMouseScroll", this.setScorllEvent, false);
  },
  methods: {
    // 新增菜单组
    clickAddApp() {
      this.menuParam = {
        visible: true,
        title: "新增菜单",
        dataMax: this.dataMax,
      };
    },
    toEditApp(item) {
      this.menuParam = { visible: true, title: "编辑菜单", id: item.id };
    },

    changeId(idx) {
      this.index = idx;
      // 点击事件标识非滚动事件
      this.scorllEvent = false;
      this.initRightBoxHeight();
      // this.$refs.rigth.scrollTop = this.rightLiTops[idx]

      this.$refs["itemList"].scrollTo({
        behavior: "smooth", // 平滑过渡
        top: this.rightLiTops[idx],
        // block: 'start' // 上边框与视窗顶部平齐。默认值
      });
    },

    async getMenuList() {
      this.pageLoading = true;
      const { data, isSuccess, msg } = await apiBaseMenuNoPage({
        parentId: 0,
      });

      this.pageLoading = false;
      if (!isSuccess) {
        this.$message.warning(msg);
        return;
      }
      // 获取当前数据的排序最大值,用于新增时设置排序字段的默认值,保证不重复
      this.dataMax = _.sortBy(data, [
        function (o) {
          return o.sort;
        },
      ])
        .map((r) => r.sort)
        .reverse()[0];

      const groupMap = {
        base: "基础应用",
        project: "协同管理",
        producm: "协同管理",
        projectm: "协同管理",
        reqm_center: "协同管理",
        productfit: "协同管理",
        code: "持续交付",
        pipeline: "持续交付",
        package: "持续交付",
        cmdb: "持续交付",
        dashboard: "协同管理",
      };

      data.forEach((element) => {
        // console.log(groupMap[element.code], 'groupMap[element.code]')
        element.groupName = groupMap[element.code] || "外部应用";
      });

      const map = {};
      data.forEach((item) => {
        map[item.groupName] = map[item.groupName] || [];
        map[item.groupName].push(item);
      });
      const sort = {
        协同管理: 1,
        持续交付: 2,
        基础应用: 4,
        外部应用: 6,
      };
      const DATA = Object.keys(map).map((orgName) => {
        return {
          groupName: orgName,
          children: map[orgName],
          sort: sort[orgName] || 6,
        };
      });
      DATA.sort((a, b) => {
        return a.sort > b.sort ? 1 : -1;
      });
      this.menuList = DATA;
      // console.log(this.menuList, ' this.menuList')

      // console.log(this.menuList.length, '9090')
    },
    compare(prop) {
      return function (obj1, obj2) {
        var val1 = obj1[prop];
        var val2 = obj2[prop];
        if (val1 < val2) {
          return -1;
        } else if (val1 > val2) {
          return 1;
        } else {
          return 0;
        }
      };
    },
    setScorllEvent() {
      this.scorllEvent = true;
    },
    handleScroll(event) {
      this.scrollY = event.target.scrollTop;
    },
    /* 计算每个小盒子高度 */
    initRightBoxHeight() {
      const itemArray = [];
      let top = 0;
      itemArray.push(top);
      // 获取右边所有子盒子高度集合
      const allList = this.$refs.itemList.getElementsByClassName("card");
      // allList伪数组转化成真数组
      Array.prototype.slice.call(allList).forEach((li) => {
        top += li.clientHeight; // 获取所有li的每一个高度
        itemArray.push(top);
      });
      this.rightLiTops = itemArray;
      this.rightLiTops.forEach((item, index) => {
        if (item < this.scrollY) {
          // 当为鼠标滚动时重新赋值
          if (this.scorllEvent) {
            this.index = index;
          }
        }
      });
    },
    async delApp(item) {
      await this.$confirm(
        `删除【${item.name}】前请先删除模块下所有的【菜单和功能】`,
        "删除",
        {
          type: "warning",
          customClass: "delConfirm",
          showClose: false,
        }
      );
      const res = await apiBaseMenuDelApp([item.id]);
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      this.$message.success("删除成功");
      this.getMenuList();
    },
    toEdit(item) {
      if (!this.$permission("base_menu_config")) return;
      this.$router.push({
        name: "base_menu_config",
        params: {
          id: item.id,
          path: item.path,
        },
      });
    },
    clickToLicense() {
      this.$router.push({
        name: "base_menu_license",
        // params: {
        //   id: item.id,
        //   path: item.path
        // }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@use "@/styles/variables.scss";
.wapper-content {
  min-height: calc(
    100vh - #{$nav-top-height} - #{$main-margin} - #{$main-margin}
  );
  border-radius: 4px;
  background-color: var(--main-bg-color);
  header {
    height: 54px;
    line-height: 54px;
    padding: 0 16px;
    border-bottom: 1px solid var(--el-divider);
    display: flex;
    justify-content: space-between;
    .table-actions {
      line-height: 54px;
    }
  }
  section {
    display: flex;
    aside {
      width: 216px;
      border-right: 1px solid var(--el-divider);
      height: calc(100vh - #{$hasHeader} - 54px);
      overflow-y: auto;
      ul {
        .selected {
          background-color: var(--col-form-hover);
          color: var(--main-theme-color);
        }
        li {
          .row-li {
            line-height: 22px;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            padding: 8px 16px;
            cursor: pointer;
            span {
              a {
                display: none;
              }
            }
            a {
              color: var(--main-theme-color);
            }

            a + a {
              margin-left: 6px;
            }
          }
          .row-input {
            padding: 8px 16px;
          }
        }
        .row-li:hover {
          background-color: var(--content-bg-hover-color);
          a {
            display: inline-block;
          }
        }
      }
    }

    main {
      position: relative;
      padding: 16px;
      flex: 1;
      height: calc(100vh - #{$hasHeader} - 54px);
      overflow-y: scroll;

      .el-row + .el-row {
        margin-top: 16px;
      }
      article {
        margin: 10px 0;
        color: var(--auxiliary-font-color);
        line-height: 22px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box; //使用自适应布局
        -webkit-line-clamp: 2; //设置超出行数，要设置超出几行显示省略号就把这里改成几
        -webkit-box-orient: vertical;
      }

      .card-content {
        height: 142px;
        position: relative;
        // border: none;
        box-shadow: 0px 4px 12px rgba(32, 33, 36, 0.05);
        .svg-icon {
          width: 50px;
          font-size: 42px;
        }
        .ml-16 {
          flex: 1;
        }
        footer {
          position: absolute;
          bottom: 16px;
          right: 16px;
        }
      }
      .el-col + .el-col {
        margin-bottom: 16px;
      }
      .el-card :hover {
        cursor: pointer;
      }
    }
  }
}
.closeTime {
  /*// background-color: var(--Red);*/
  color: #fff;
  padding: 1px 6px 1px 3px;
  border-radius: 2px;
  margin-left: 6px;
  font-size: 12px;
}
.bgGry {
  background-color: #bfbfbf;
}
</style>
