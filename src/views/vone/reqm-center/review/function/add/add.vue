<template>
  <div class="dialogBox">
    <el-dialog
      :title="title"
      v-model:value="visible"
      :before-close="onClose"
      :modal="false"
      size="lg"
    >
      <el-form
        ref="issueForm"
        v-loading="pageLoading"
        :disabled="infoDisabled"
        label-width="80px"
        :model="issueForm"
        label-position="right"
        :rules="rules"
      >
        <div class="form_group">基本信息：</div>
        <el-form-item label="评审工单" prop="name">
          <el-input
            v-model="issueForm.name"
            placeholder="请输入评审工单"
            :disabled="infoDisabled"
          />
        </el-form-item>
        <el-form-item label="参与人" prop="putBys">
          <vone-remote-user v-model:value="issueForm.putBys" multiple />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="截止时间" prop="datime">
              <el-date-picker
                v-model="issueForm.datime"
                type="date"
                placeholder="选择日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="评审规则" prop="typeCode">
              <el-select
                v-model="issueForm.typeCode"
                clearable
                style="width: 100%"
              >
                <el-option label="评审规则1" value="1" />
                <el-option label="评审规则2" value="1" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="检查清单" prop="files" class="fileLoad">
          <vone-upload
            ref="bugUploadFile"
            biz-type="IDEA_FILE_UPLOAD"
            :files-data="[]"
          />
        </el-form-item>
        <div class="form_group">评审项：</div>
        <el-radio-group v-model="issueForm.radio" size="mini">
          <el-radio-button label="1">用户需求</el-radio-button>
          <el-radio-button label="2">需求</el-radio-button>
        </el-radio-group>
      </el-form>
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="name" label="需求" />
        <el-table-column prop="source" label="来源" />
        <el-table-column prop="date" label="提出时间" />
      </el-table>
      <template v-slot:footer>
        <div class="footer">
          <div class="sender">
            <span>发起人</span>
            <vone-user-avatar
              avatar-path="avatar16"
              :avatar-type="true"
              height="22px"
              width="22px"
              name="张三"
              :show-name="true"
            />
            <span>2016-05-03</span>
          </div>
          <div>
            <el-button
              v-if="!infoDisabled"
              type="primary"
              :loading="saveLoading"
              @click="saveInfo"
              >确定</el-button
            >
            <el-button @click="onClose">取消</el-button>
          </div>
        </div>
      </template>
    </el-dialog>
    <demo
      v-bind="issueParam"
      v-if="issueParam.demoDiolog"
      v-model:value="issueParam.demoDiolog"
      @success="getInitTableData"
    />
  </div>
</template>

<script>
import {
  $on,
  $off,
  $once,
  $emit,
} from '../../../../../../utils/gogocodeTransfer'

import { apiAlmIdeaAddOrEdit } from '@/api/vone/reqmcenter/idea'
import demo from '../../../demo/edit'
export default {
  components: {
    demo,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: undefined,
    },
    infoDisabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      rules: {
        name: [{ required: true, message: '请输入评审工单', trigger: 'blur' }],
      },
      issueParam: {
        demoDiolog: false,
      },
      issueForm: {
        radio: '1',
      },
      saveNext: false,
      saveSplit: false,
      noEdit: null,
      saveLoading: false,
      pageLoading: false,
      tableData: [
        {
          date: '2016-05-02',
          name: '这是需求1',
          source: '这是来源',
        },
        {
          date: '2016-05-04',
          name: '这是需求1',
          source: '这是来源',
        },
        {
          date: '2016-05-01',
          name: '这是需求1',
          source: '这是来源',
        },
        {
          date: '2016-05-03',
          name: '这是需求1',
          source: '这是来源',
        },
      ],
    }
  },
  methods: {
    eventDisposalRangeChange(value) {
      const textLength = this.$refs.editor.$el.innerText.replace(
        /[|]*\n/,
        ''
      ).length
      if (textLength >= 1000) {
        this.$refs.issueForm.validateField(['description'])
      } else {
        this.$refs.issueForm.clearValidate(['description'])
      }
    },
    changeFlow() {
      $emit(this, 'success')
      this.onClose()
    },
    setting() {
      this.issueParam = {
        demoDiolog: true,
        title: '编辑配置',
      }
    },
    onClose() {
      this.splitFlag = false
      $emit(this, 'update:visible', false)

      this.saveSplit = false
      this.saveNext = false
    },
    // 保存
    async saveInfo() {
      const textLength = this.$refs.editor.$el.innerText.replace(
        /[|]*\n/,
        ''
      ).length
      if (textLength >= 1000) {
        this.$refs.issueForm.validate((valid) => {
          if (valid) {
            this.$refs.infoForm.proving()
          } else {
            return
          }
        })
      } else {
        const { name } = this.issueForm
        if (name) {
          this.$refs.infoForm.proving()
          // 执行校验成功的相关操作
        } else {
          this.$refs['issueForm'].validateField(['name'])
        }
      }
    },
    async save() {
      this.issueForm['files'] = this.$refs['bugUploadFile'].uploadFiles
      const params = {
        ...this.issueForm,
        ...this.$refs.infoForm.infoForm,
      }
      const res = await apiAlmIdeaAddOrEdit(params)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('保存成功')
      $emit(this, 'success')
      this.onClose()
    },
    nextChange() {},
  },
  emits: ['update:visible', 'success'],
}
</script>

<style lang="scss" scoped>
.dialogBox {
  height: calc(100vh - 100px);
  overflow-y: overlay;
  overflow-x: hidden;
  .leftForm {
    padding: 12px 20px;
  }
  .rightForm {
    border-left: 1px solid var(--disabled-bg-color, #ebeef5);
    padding: 12px 20px;
  }
  .form_group {
    display: flex;
    height: 32px;
    line-height: 32px;
    &::before {
      content: '';
      display: inline-block;
      position: absolute;
      left: 5px;
      width: 3px;
      height: 32px;
      background: #3e7bfa;
      margin-right: 5px;
    }
  }
  .footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .sender {
      display: flex;
      align-items: center;
      > span {
        margin-right: 10px;
        &:nth-child(1) {
          color: #c1c8d6;
        }
      }
    }
  }
}
</style>
