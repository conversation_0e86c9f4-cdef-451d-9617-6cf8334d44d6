<!-- 数据源-->
<template>
  <div class="pageContent" style="margin:-16px;">

    <div class="source_wrap">
      <aside>
        <header class="testTitle">
          <span class="strong">
            数据表
          </span>

          <el-tooltip effect="dark" content="新增" placement="top">
            <i plain class="iconfont el-icon-tips-plus-circle-fill addIcon" size="mini" :disabled="!$permission('project_iteration_add')" @click="addPlan" />
          </el-tooltip>
        </header>

        <nav v-loading="listLoading">
          <vone-empty v-if="planList.length==0 " />
          <div v-else class="list-container">
            <div
              v-for="(item, index) in planList"
              :key="index"
              :class="{
                selected: currentNodekey === item.id,
              }"
              class="list_item"
              @click="selectSource(item)"
            >
              <el-row type="flex" justify="space-between" align="middle">
                <span class="title text-over">{{ item.name }}</span>
                <el-dropdown trigger="click">
                  <i class="el-icon-more more" />
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item icon="iconfont el-icon-application-rename" @click.native="editPlan(item)">重命名</el-dropdown-item>
                    <el-dropdown-item icon="iconfont el-icon-application-delete" @click.native="removeDel(item)">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </el-row>
              <el-row v-if="item.desc">
                <div class="text-over description">{{ item.desc }}</div>
              </el-row>
            </div>

          </div>
        </nav>
      </aside>

      <!-- 表格 -->
      <planTable ref="sourceTable" class="source_table" :current-nodekey="currentNodekey" />

    </div>

    <el-dialog :title="dialogParams.title" width="30%" v-model="dialogParams.visible" :close-on-click-modal="false" :before-close="onClose">
      <el-form ref="planForm" :model="planForm" :rules="rules">
        <el-form-item label="名称" prop="name">
          <el-input v-model="planForm.name" style="width: 100%" placeholder="请输入名称" />
        </el-form-item>

        <el-form-item label="描述" prop="desc">
          <el-input v-model="planForm.desc" placeholder="请输入描述" type="textarea" />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="confirmDialog">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { addSourceData, deleteSourceById, editSourceData, getSourceById, getSourceList } from '@/api/vone/base/source'

import planTable from './form-config/table.vue'

export default {
  components: {
    planTable
  },

  data() {
    return {
      rules: {
        name: [{ required: true, message: '请输入名称' }, { pattern: '^.{1,30}$', message: '请输入不超过30个字符组成的名称' }]
      },
      saveLoading: false,
      planForm: {},
      dialogParams: { visible: false },
      planList: [],
      listLoading: false,
      currentNodekey: '',
      sourceData: {}
    }
  },
  mounted() {
    this.getSourceData()
  },
  methods: {
    // 左侧列表
    async getSourceData() {
      const res = await getSourceList()
      if (res.isSuccess) {
        this.planList = res.data
        this.currentNodekey = res.data[0]?.id
        this.$refs.sourceTable.refreshTable(this.currentNodekey)
      }
    },
    onClose() {
      this.planForm = {
        name: '',
        desc: ''
      }
      this.dialogParams = {
        visible: false
      }
    },
    selectSource(item) {
      this.currentNodekey = item.id
      this.$refs.sourceTable.refreshTable(item.id)
    },
    // 新增数据表
    addPlan() {
      this.dialogParams = {
        title: '新建数据源',
        visible: true
      }
    },
    // 重命名
    editPlan(val) {
      this.dialogParams = {
        title: '编辑数据源',
        visible: true
      }
      this.getTableConfig(val.id)
    },
    async getTableConfig(id) {
      const res = await getSourceById(id)
      if (res.isSuccess) {
        this.sourceData = res.data
        this.planForm = res.data
      }
    },
    //  删除
    async removeDel(item) {
      try {
        await this.$confirm(`确定删除${item.name}吗?`, '删除', {
          type: 'warning',
          customClass: 'delConfirm',
          showClose: false
        })
      } catch (e) {
        return
      }

      const res = await deleteSourceById(item.id)

      if (!res.isSuccess) {
        this.$message.error(res.msg)
        return
      }
      this.$message.success('删除成功')
      this.getSourceData()
    },
    async confirmDialog() {
      try {
        await this.$refs.planForm.validate()
      } catch (e) {
        return
      }
      const checkNameLen = this.planList.filter(v => this.planForm.id ? v.name === this.planForm.name && this.planForm.id != v.id : v.name === this.planForm.name).length
      if (checkNameLen >= 1) {
        this.$message.error('存在重复数据表，请修改')
        return
      }
      this.planForm.id ? this.sureEdit() : this.sureAdd()
    },
    async sureEdit() {
      const params = {
        ...this.planForm,
        fields: this.sourceData.fields
      }
      const res = await editSourceData(params)
      if (res.isSuccess) {
        this.$message.success('修改成功')
        this.onClose()
        this.getSourceData()
      } else {
        this.$message.error('修改失败')
      }
    },
    async sureAdd() {
      const obj = {
        label: '多行文本',
        type: 'text',
        icon: 'iconfont el-icon-application-duohangwenben'
      }
      const params = {
        ...this.planForm,
        fields: [{
          field: 'name',
          name: '名称',
          primary: true,
          config: JSON.stringify(obj)
        }]
      }
      const res = await addSourceData(params)
      if (res.isSuccess) {
        this.$message.success('新建成功')
        this.onClose()
        this.getSourceData()
      } else {
        this.$message.error('新建失败')
      }
    }
  }
}
</script>

<style lang='scss' scoped>
@use "@/styles/variables.scss";
.source_wrap {
  display: flex;
  width: 100%;
}
.source_table {
  width: calc(100% - 210px);
}
.pageContent {
  background-color: var(--main-bg-color);
  border: 4px;

  aside {
    width: 210px;
    border-right: 1px solid var(--el-divider);
    height: calc(100vh - #{$nav-top-height} - #{$main-margin} - #{$main-margin});
    header.testTitle {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 56px;
			font-size: 14px;
      padding: 0 16px;
      color: #1d2129;
      font-weight: 500;
      border-bottom: 1px solid var(--el-divider);

      .addIcon {
        display: inline-block;
        color: #3e7bfa;
        cursor: pointer;
        font-size: 20px;
      }
      .operation {
        display: flex;
        gap: 12px;
        i {
          cursor: pointer;
        }
      }
    }
    .more {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 24px;
      height: 24px;
			color: #6B7385;
      border-radius: 4px;
      &:hover {
        background: #f2f3f5;
      }
    }

    nav {
      height: calc(100vh - #{$hasHeader} - 65px);
      overflow-y: auto;
      padding: 12px 16px;

      .list-container {
        .el-row + .el-row {
          margin-top: 8px;
        }
        .list_item {
          border:1px solid #eaecf0;
        }
        > div {
          padding: 12px;
          transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
          border-radius: 4px;
          &:hover {
            border: 1px solid var(--main-theme-color, #3e7bfa);
            cursor: pointer;
          }
          & + div {
            margin-top: 12px;
          }
          .title {
            font-weight: 400;
            color: #1d2129;
          }
          .description {
            color: #838a99;
            font-weight: 400;
          }

          &.selected {
            border: 1px solid var(--main-theme-color, #3e7bfa);
            background-color: #fff;
          }
        }
      }
    }
  }
}

:deep(.page-wrapper) {
  padding: 11px 16px;
}
:deep(.table-operation-view) {
  padding: 0 16px 12px;
}
.strong{
  font-size: 16px;
  font-weight: 500;
  color: #2C2E36;
}
</style>

