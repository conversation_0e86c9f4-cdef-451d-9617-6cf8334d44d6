<template>
  <div>
    <div class="pageContentNoH mainheight">
      <el-tabs
        v-model="tabActive"
        class="vone-tab-line"
        style="margin: -16px -16px 16px -16px"
        @tab-click="getProjectList"
      >
        <el-tab-pane label="研发类" name="DEVELOP" />
        <el-tab-pane label="交付类" name="DELIVERY" />
      </el-tabs>
      <vone-search-wrapper>
        <template v-slot:search>
          <vone-search-dynamic
            ref="searchForm"
            :show-card="true"
            :card-options="cardOptions"
            table-search-key="product-project-table"
            :model="formData"
            :extra="extraData"
            :default-fileds="defaultFileds"
            show-basic
            :default-filter-collection="true"
            @getTableData="getProjectList"
          />
        </template>
        <template v-slot:actions>
          <el-button
            :icon="elIconIconGuanlian"
            type="primary"
            :disabled="!$permission('product_project_association')"
            @click="addNewProject"
            >关联</el-button
          >
        </template>
        <template v-slot:fliter>
          <vone-search-filter
            :extra="extraData"
            :model="formData"
            :default-fileds="defaultFileds"
            @getTableData="getProjectList"
          />
        </template>
      </vone-search-wrapper>
      <div v-loading="tableLoading">
        <div
          v-if="extraData.cardView == 'table'"
          style="height: calc(100vh - 230px)"
        >
          <vxe-table
            ref="projectTable"
            class="vone-vxe-table"
            border
            height="auto"
            show-overflow="tooltip"
            :loading="tableLoading"
            :empty-render="{ name: 'empty' }"
            :data="tableData.records"
            :column-config="{ resizable: true, minWidth: 120 }"
            :checkbox-config="{ reserve: true }"
            row-id="id"
          >
            <vxe-column title="名称">
              <template #default="{ row }">
                <div class="flexRow" :class="row.top ? 'vCard' : ''">
                  <a
                    @click="
                      itemProject(row.code, row.typeCode, row.id, row.name, row)
                    "
                    >{{ row.name }}</a
                  >

                  <!-- <div @click.stop="focus(row)">
                        <i v-if="row.collection" style="color: #FFC642;  font-size:18px" class="iconfont el-icon-icon-shoucang-on" />
                        <i v-else class="el-icon-star-off" style=" font-size:18px" />
                      </div> -->
                </div>
              </template>
            </vxe-column>
            <vxe-column title="标识" field="code" width="100" />
            <vxe-column title="项目经理" field="leadingBy" width="120">
              <template #default="{ row }">
                <vone-user-avatar
                  :avatar-path="
                    getUserInfo(row) ? getUserInfo(row).avatarPath : ''
                  "
                  :name="getUserInfo(row) ? getUserInfo(row).name : ''"
                />
              </template>
            </vxe-column>
            <vxe-column title="状态" field="isClosure" width="120">
              <template #default="{ row }">
                <span v-if="row.projectStage">
                  <el-tag>
                    {{ row.projectStage.desc }}
                  </el-tag>
                </span>
              </template>
            </vxe-column>
            <vxe-column title="类型" field="typeCode" width="80">
              <template #default="{ row }">
                <el-tag
                  v-if="row.typeCode && row.echoMap && row.echoMap.typeCode"
                  effect="dark"
                  color="success"
                  size="mini"
                  >{{ row.echoMap.typeCode.name }}</el-tag
                >
                <span v-else>--</span>
              </template>
            </vxe-column>
            <vxe-column title="操作" fixed="right" align="left" width="80">
              <template #default="{ row }">
                <el-tooltip class="item" content="取消关联" placement="top">
                  <el-button
                    type="text"
                    :disabled="!$permission('product_paoject_cancel')"
                    :icon="elIconIconQuxiaoguanlian"
                    @click="cancelProject(row)"
                  />
                </el-tooltip>
              </template>
            </vxe-column>
          </vxe-table>
          <vone-pagination
            ref="pagination"
            :total="tableData.total"
            @update="getProjectList"
          />
        </div>
        <vone-cards
          v-else
          ref="project-card"
          :data="tableData"
          :row-count="4"
          height="calc(100vh - 232px)"
          @updateData="getProjectList"
        >
          <template v-slot="{ row }">
            <a
              @click="
                itemProject(row.code, row.typeCode, row.id, row.name, row)
              "
            >
              <vone-card
                :title="row.name"
                :actions="rowActions"
                :class="row.top ? 'vCard' : ''"
                :actions-num="4"
              >
                <template v-slot:icon>
                  <svg
                    v-if="row.typeCode == 'AGILE'"
                    class="icon"
                    aria-hidden="true"
                  >
                    <use xlink:href="#el-icon-icon-minjie1" />
                  </svg>
                  <svg
                    v-else-if="row.typeCode == 'WALL'"
                    class="icon"
                    aria-hidden="true"
                  >
                    <use xlink:href="#el-icon-icon-pubu1" />
                  </svg>
                  <svg
                    v-else-if="row.typeCode == 'TEST'"
                    class="icon"
                    aria-hidden="true"
                  >
                    <use xlink:href="#el-icon-ceshixiangmu" />
                  </svg>
                  <svg
                    v-else-if="row.typeCode == 'DEVOPS'"
                    class="icon"
                    aria-hidden="true"
                  >
                    <use xlink:href="#el-icon-project-devops" />
                  </svg>
                  <svg
                    v-else-if="row.typeCode == 'CAR'"
                    class="icon"
                    aria-hidden="true"
                  >
                    <use xlink:href="#el-icon-project-car-model" />
                  </svg>
                  <svg
                    v-else-if="row.typeCode == 'PLATFORM'"
                    class="icon"
                    aria-hidden="true"
                  >
                    <use xlink:href="#el-icon-project-platform" />
                  </svg>
                </template>

                <template v-slot:title>
                  <el-row type="flex" justify="space-between">
                    <div class="title-box">
                      <el-tooltip
                        v-if="row.isShow"
                        class="item"
                        :content="row.name"
                        placement="top"
                      >
                        <div class="title">{{ row.name }}</div>
                      </el-tooltip>
                      <div
                        v-else
                        class="title"
                        @mouseenter="(e) => isShowToltip(e, row)"
                        @mouseout="hideTip(row)"
                      >
                        {{ row.name }}
                      </div>
                      <span>{{ row.key }}</span>
                    </div>
                    <!-- <div @click.stop="focus(row)">
                          <i v-if="row.collection" style="color: #FFC642;  font-size:18px" class="iconfont el-icon-icon-shoucang-on" />
                          <i v-else class="el-icon-star-off" style=" font-size:18px" />
                        </div> -->
                  </el-row>
                </template>
                <template v-slot:tagboxs>
                  <el-tag
                    v-if="
                      row.echoMap.projectProcess &&
                      row.echoMap.projectProcess.status
                    "
                    type="success"
                    >{{ row.echoMap.projectProcess.status }}</el-tag
                  >
                </template>
                <template v-slot:desc>
                  <div>
                    <vone-user-avatar
                      :avatar-path="
                        getUserInfo(row) ? getUserInfo(row).avatarPath : ''
                      "
                      :name="getUserInfo(row) ? getUserInfo(row).name : ''"
                    />
                  </div>
                </template>
                <el-row class="descbox" flex>
                  <vone-toolitip :content="row.code" :label="'标识'" />
                  <vone-toolitip :content="row.description" :label="'描述'" />
                </el-row>
              </vone-card>
            </a>
          </template>
        </vone-cards>
      </div>
    </div>
    <template />
    <connectDialog
      v-bind="addParam"
      v-if="addParam.visible"
      :project-type="tabActive"
      v-model:value="addParam.visible"
      @success="getProjectList"
    />
  </div>
</template>

<script>
import { cloneDeep } from "lodash";

import {
  getAllProject,
  apiProjectAuth,
  collectProject,
  topProject,
} from "@/api/vone/project/index";
import { getPermission, setPermission, getRouter } from "@/utils/auth";
import { textRange } from "@/utils";
import connectDialog from "./connectDialog.vue";
import { batchDelete } from "@/api/vone/product/index";

export default {
  components: {
    connectDialog,
  },
  data() {
    return {
      defaultFileds: [
        {
          key: "name",
          name: "名称",
          type: {
            code: "INPUT",
          },
          placeholder: "请输入名称",
        },
        // {
        //   key: 'collection',
        //   name: '我收藏的',
        //   type: {
        //     code: 'SELECT'
        //   },
        //   placeholder: '请选择',
        //   optionList: [
        //     {
        //       code: true,
        //       name: '是',
        //       id: '1'
        //     },
        //     {
        //       code: false,
        //       name: '否',
        //       id: '2'
        //     }
        //   ]

        // }
      ],
      cardOptions: [
        {
          name: "卡片",
          value: "card",
          icon: "el-icon-application-cengjishitu",
        },
        {
          name: "表格",
          value: "table",
          icon: "el-icon-application-biaogeshitu",
        },
      ],
      rowActions: [
        {
          type: "text",
          text: "取消关联",
          icon: "iconfont el-icon-icon-quxiaoguanlian",
          onClick: ({ row }) => this.cancelProject(row),
          disabled: !this.$permission("product_project_association"),
        },
      ],
      tableData: {},
      tableLoading: false,
      formData: {},
      extraData: {
        // cardView: 'card'
      },

      addParam: {
        visible: false,
      },
      tabActive: "DEVELOP",
    };
  },
  computed: {
    getUserInfo() {
      return function (row) {
        return row?.leadingBy && row?.echoMap?.leadingBy;
      };
    },
  },
  watch: {
    $route: function () {
      this.getProjectList();
    },
  },
  mounted() {
    // this.getProjectList()
  },
  methods: {
    addNewProject() {
      this.addParam = { visible: true, title: "关联项目" };
    },
    async cancelProject(row) {
      await this.$confirm(`确定取消关联该项目吗?`, "取消关联", {
        type: "warning",
        customClass: "delConfirm",
        showClose: false,
      })
        .then(async () => {
          const res = await batchDelete([
            {
              isHost: false,
              productId: this.$route.params.productId,
              projectId: row.id,
            },
          ]);
          if (!res.isSuccess) {
            return this.$message.error(res.msg);
          }
          this.getProjectList();
          this.$message.success("取消关联成功");
        })
        .catch(() => {});
    },
    handleClick() {},
    isShowToltip(e, node) {
      const bool = textRange(e.target);
      node["isShow"] = bool;
    },
    hideTip(node) {
      node["isShow"] = false;
    },
    // 查询项目列表
    async getProjectList() {
      this.tableLoading = true;
      let params = {};
      let pageObj = {};

      if (this.extraData.cardView == "table") {
        pageObj = this.$refs.pagination?.pageObj;
      } else {
        pageObj = this.$refs["project-card"].exportTableQueryData();
      }

      params = {
        ...pageObj,
        sort: "createTime",
        order: "descending",
        extra: {
          ...this.extraData,
        },
        model: {
          productId: this.$route.params.productId,
          classify: this.tabActive,
          ...this.formData,
        },
      };
      const { isSuccess, data, msg } = await getAllProject(params);
      this.tableLoading = false;
      if (!isSuccess) {
        this.$message.error(msg);
        return;
      }

      data.records.forEach((element) => {
        element.pStage = element.projectStage?.code || "RUNNING";
      });

      this.tableData = data;
    },

    async itemProject(code, typeCode, id, name, row) {
      const arry = ["APPROVAL", "CLOSED"];
      if (row.projectStage && arry.indexOf(row.projectStage.code) > -1) {
        return this.$message.warning(
          ` 【${row.projectStage.desc}】流程审批中... `
        );
      }
      const { data, isSuccess, msg } = await apiProjectAuth(id);
      const jumpRouter = cloneDeep(data);
      if (!isSuccess) {
        this.$message.error(msg);
        return;
      }
      if (!isSuccess) {
        return this.$message("获取用户权限失败，请重新登录");
      }
      if (!data.length) {
        this.$message.warning(
          "当前登录用户【项目角色】查看当前项目信息权限不足,请联系项目经理授权"
        );
        return;
      }
      // -------------------------------------------------------------------------------
      // 处理按钮权限
      const hasPermissionList = []; // 接口返回的用来接收新的按钮权限数组
      var findPermission = function (V) {
        V.forEach((item) => {
          // 把传入的数组循环遍历
          if (item.meta.isButton === true) {
            hasPermissionList.push(item.meta.code); // item.meta.isButton 为true 把id添加到新数组
          }
          if (item.children) {
            findPermission(item.children); // 递归调用自身
          }
        });
      };
      findPermission(data); // 调用函数

      const permission = getPermission(); // 从登录接口获取的权限按钮数据

      const allPermision = [...new Set([...permission, ...hasPermissionList])]; // 去重

      setPermission(allPermision);

      // -------------------------------------------------------------------------------

      const routerMenu = cloneDeep(getRouter());
      const projectSettingMenu = cloneDeep(
        routerMenu
          .find((ele) => ele.name == "project")
          .children.find((ele) => ele.name == "project_view")
      );

      routerMenu.map((item) => {
        if (item.name == "project") {
          item.children = [];
          item.children.push(projectSettingMenu);
          item.children = [...item.children, ...data];
        }
      });

      this.$store.commit("user/set_router", routerMenu);

      // 保存路由信息
      this.$store.dispatch("project/itemProject", typeCode);

      const firstMenuName =
        jumpRouter[0]?.children[0]?.name || jumpRouter[0]?.name;
      this.$router.push({
        name: firstMenuName,
        params: {
          projectKey: code,
          projectTypeCode: typeCode,
          id: id,
          name: name,
        },
      });
    },

    async focus(row) {
      const res = await collectProject(row.id, !row.collection);
      if (!res.isSuccess) {
        this.$message.error(res.msg);
        return;
      }
      this.$message.success("操作成功");
      this.getProjectList();
    },
    async setProjectTop(row) {
      const res = await topProject(row.id, !row.top);
      if (!res.isSuccess) {
        this.$message.error(res.msg);
        return;
      }
      this.$message.success("操作成功");
      this.getProjectList();
    },
  },
};
</script>

<style lang="scss" scoped>
.pageContentNoH{.title-header {
    border-left: 4px solid var(--main-theme-color);
    padding-left: 10px;
    color: var(--font-main-color);
    font-weight: 500;
    margin-bottom: 12px;
    line-height: 18px;
  }
  padding: 16px;}.mainheight{height:calc(100vh - 70px);position:relative}.AGILE{background:linear-gradient(to right,#E3F0FF,#91BBFF);background-size:100% 100%}.WALL{background:linear-gradient(to right,#FCF5E4,#FFCA7A);background-size:100% 100%}.TEST{background:linear-gradient(to right,#E6F7FF,#6BC1FF);background-size:100% 100%}.DEVOPS{background:linear-gradient(to right,#E3FCF8,#4FD6CD);background-size:100% 100%}.CAR{background:linear-gradient(to right,#E6F2FF,#6BA6FF);background-size:100% 100%}.PLATFORM{background:linear-gradient(to right,#F5F0FF,#B59EFF);background-size:100% 100%;/*// &:hover{*/
  //   background: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0,0.5)), linear-gradient(to right, #F5F0FF, #B59EFF);
  // }}.userText{height:35px;line-height:35px}.descbox{font-size:14px;color:var(--font-second-color)}.vCard{position:relative;overflow:hidden}.vCard::before{content:"";position:absolute;top:0;left:0;width:0;height:0;border-bottom:20px solid transparent;border-left:20px solid #09BDBD}.flexRow{display:flex;justify-content:space-between;align-items:center;margin-left:-10px;line-height:36px;a{
    padding-left: 20px;
  }}.list-view{margin-top:-8px}
</style>
