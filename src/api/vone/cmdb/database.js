// import request from '@/utils/request'
import request from '@/utils/axios-api'

// 查询数据库列表
export function apiCmdbDatabaseList() {
  return request({
    url: '/api/table/list',
    method: 'get'
  })
}

/**
 * 获取数据库组件列表
 * @param params
 */
export async function apiCmdbDbSeletByParams(data) {
  return request({
    url: `/api/cmdb/cmdb/dbComponents/page`,
    method: 'post',
    data
  })
}

/**
 * 根据数据库组件ID删除数据库组件
 * @param id
 */
export async function apiCmdbDbDeleteById(data) {
  return request({
    url: `/api/cmdb/cmdb/dbComponents`,
    method: 'delete',
    data
  })
}

/**
 * 根据数据库组件ID查询数据库机构组件信息
 * @param dbCompentsId
 */
export async function apiCmdbDbSeletOrgByDbCompentsId(dbCompentsId) {
  return request({
    url: `/api/cmdb/cmdb/dbComponentsOrg/` + dbCompentsId,
    method: 'get'
  })
}

/**
 * 根据数据库组件ID分配数据库机构组件信息
 * @param dbCompentsId
 */
export async function apiCmdbDbGrant(data) {
  return request({
    url: `/api/cmdb/cmdb/dbComponentsOrg`,
    method: 'put',
    data
  })
}

/**
 * 添加数据库组件信息
 * @param data
 */
export async function apiCmdbDbSave(data) {
  return request({
    url: `/api/cmdb/cmdb/dbComponents`,
    method: data.id ? 'put' : 'post',
    data
  })
}

/**
 * 根据数据库组件ID查询数据库组件信息
 * @param id
 */
export async function apiCmdbDbSeletById(id) {
  return request({
    url: `/api/cmdb/cmdb/dbComponents/` + id,
    method: 'get'
  })
}

/**
 * 根据组件类型查询扩展属性
 * @param {*} params
 */
export function apiCmdbDbExtendTemplete(dbComponentsType) {
  return request({
    url: `/api/cmdb/cmdb/dbComponentsExtendProperties/${dbComponentsType}`,
    method: 'get'
  })
}

/**
 * 不分页查询数据库组件列表
 * @param params
 */
export async function apiCmdbDataBaseNoPage(data) {
  return request({
    url: `/api/cmdb/cmdb/dbComponents/query`,
    method: 'post',
    data
  })
}

/**
 * 分页列表查询DB执行历史
 * @param params
 */
export async function apiCmdbDbHistoryPage(data) {
  return request({
    url: `/api/cmdb/cmdb/dbComponents/sqlExecution/page`,
    method: 'post',
    data
  })
}

