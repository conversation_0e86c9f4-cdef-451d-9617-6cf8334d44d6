<template>
  <vone-edit-wrapper show-footer>
    <!-- 基本配置 -->
    <vone-card-wrapper title="基本信息">
      <el-form
        ref="basicForm"
        :auto-layout="false"
        :model="basicForm"
        label-position="top"
        :rules="basicFormRules"
      >
        <el-row :gutter="24">
          <el-col v-if="id" :span="8">
            <el-form-item prop="serverIp" label="组件IP">
              <el-input
                v-model="basicForm.serverIp"
                :disabled="!!id"
                maxlength="32"
                placeholder="请输入长度不超过32个字符的名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="name" label="名称">
              <el-input
                v-model="basicForm.name"
                maxlength="32"
                placeholder="请输入长度不超过32个字符的名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="appComponentsTypeKey" label="类型">
              <el-select
                v-model="basicForm.appComponentsTypeKey"
                :disabled="!!id"
                clearable
                placeholder="请选择应用组件类型"
                style="width: 100%"
                @change="typeChange"
              >
                <el-option
                  v-for="(item, index) in dictList"
                  :key="index"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="serverPort" label="组件端口">
              <el-input
                v-model="basicForm.serverPort"
                :disabled="!!id"
                placeholder="请输入应用组件组件端口"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="consolePort" label="控制台端口">
              <el-input
                v-model="basicForm.consolePort"
                placeholder="请输入控制台端口号"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="publishDir" label="应用目录">
              <el-input
                v-model="basicForm.publishDir"
                placeholder="请输入应用组件所在目录的应用目录"
                maxlength="250"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="deployDir" label="部署目录">
              <el-input
                v-model="basicForm.deployDir"
                placeholder="请输入部署目录所在服务器的部署目录"
                maxlength="250"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="scriptDir" label="脚本目录">
              <el-input
                v-model="basicForm.scriptDir"
                placeholder="请输入脚本目录所在服务器的脚本目录"
                maxlength="250"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="permissionUser" label="权限用户名">
              <el-input
                v-model="basicForm.permissionUser"
                placeholder="请输入具有脚本目录操作权限的用户名"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="pythonDir" label="Python目录">
              <el-input
                v-model="basicForm.pythonDir"
                placeholder="请输入Python安装目录所在服务器绝对路径"
                maxlength="250"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="description" label="描述">
              <el-input
                v-model="basicForm.description"
                placeholder="请输入应用组件描述信息"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </vone-card-wrapper>
    <!-- 高级属性配置 -->
    <vone-card-wrapper v-if="advanceTemplete.length !== 0" title="高级属性配置">
      <el-form
        ref="advanceForm"
        label-position="top"
        :rules="advanceFormRules"
        :model="advanceForm"
      >
        <el-row :gutter="24">
          <template>
            <el-col
              v-for="(item, index) in advanceTemplete"
              :key="index"
              :span="8"
            >
              <el-form-item :prop="item.key" :label="item.name">
                <el-input
                  v-model="advanceForm[item.key]"
                  :type="
                    item.inputType.code === 'PASSWORD' ? 'password' : 'text'
                  "
                  :placeholder="`请输入${item.name}`"
                />
              </el-form-item>
            </el-col>
          </template>
        </el-row>
      </el-form>
    </vone-card-wrapper>

    <!-- 服务器 -->
    <vone-card-wrapper title="服务器">
      <vxe-table
        class="vone-vxe-table"
        border
        resizable
        show-overflow="tooltip"
        :empty-render="{ name: 'empty' }"
        :data="applicationDatas"
        :column-config="{ minWidth: '120px' }"
        row-id="id"
      >
        <vxe-column title="服务器" field="ip" />
        <vxe-column field="applicationId" title="服务应用">
          <template #default="{ row }">
            <el-select
              v-model="row.applicationId"
              clearable
              placeholder="请选择服务应用"
              @change="systemIdChange(row)"
            >
              <el-option
                v-for="(item, index) in applicationList"
                :key="index"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </template>
        </vxe-column>
        <vxe-column field="envKey" title="环境">
          <template #default="{ row }">
            <el-select
              v-model="row.envKey"
              clearable
              placeholder="请选择环境"
              :disabled="!row.envList"
            >
              <el-option
                v-for="(item, index) in row.envList"
                :key="index"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </template>
        </vxe-column>
        <vxe-column field="moduleId" title="工程包名称" width="250">
          <template #default="{ row }">
            <el-select
              v-model="row.moduleId"
              clearable
              placeholder="请选择工程包"
              :disabled="!row.moduleId"
            >
              <el-option
                v-for="item in row.engineList"
                :key="item.id"
                :label="item.productName"
                :value="item.id"
              />
            </el-select>
          </template>
        </vxe-column>
        <vxe-column field="checkUrl" title="检查路径">
          <template #default="{ row }">
            <el-input
              v-model="row.checkUrl"
              placeholder="请输入检查路径(如：http://***********:8080/checkUrl.html),多个路径以空格隔开"
            />
          </template>
        </vxe-column>
      </vxe-table>
    </vone-card-wrapper>

    <template v-slot:footer>
      <el-row>
        <el-button @click="cancle">取消</el-button>

        <el-button type="primary" :loading="saveLoading" @click="saveInfo"
          >确定</el-button
        >
      </el-row>
    </template>
  </vone-edit-wrapper>
</template>

<script>
import {
  apiBaseGetselectByAppOrDb,
  apiBaseGetAdvanced,
  apiBaseDataFullInfoById,
  apiCmdbUpdateCmAppCompents,
  apiCmdbSelectModuleBySystemId,
} from '@/api/vone/cmdb/application'
import {
  apiCmdbServerNoPage,
  apiBaseSelectBaseDatagById,
} from '@/api/vone/cmdb/server'

import { pick } from 'lodash'
import { apiBaseDictNoPage } from '@/api/vone/base/dict'
export default {
  components: {
    // exampleItem
  },
  data() {
    return {
      saveLoading: false,
      id: this.$route.params.id,
      activeNames: ['1', '2', '3', '4', '5'],
      hostLists: [],
      basicForm: {
        hostData: [],
      },
      advanceForm: {},
      advanceTemplete: [],
      applicationList: [], // 服务应用
      envList: [],
      dictList: [], // 应用组件类型列表
      businessSystemDatas: [],
      advancedDatas: [],
      basicFormRules: {
        name: [
          {
            required: true,
            message: '请输入长度不超过32个字符的名称',
            max: 32,
          },
        ],
        appComponentsTypeKey: [
          {
            required: true,
            message: '请选择应用组件类型',
          },
        ],
        serverPort: [
          {
            required: true,
            message: '请输入组件端口号',
            trigger: 'change',
          },
          // {
          //   pattern: '^([0-9]|[1-9]\\d{1,3}|[1-5]\\d{4}|6[0-5]{2}[0-3][0-5])$',
          //   message: '请输入合法的应用组件端口号'
          // }
        ],
        consolePort: [
          {
            required: true,
            message: '请输入控制台端口号',
            trigger: 'change',
          },
          // {
          //   pattern: '^([0-9]|[1-9]\\d{1,3}|[1-5]\\d{4}|6[0-5]{2}[0-3][0-5])$',
          //   message: '请输入合法的控制台端口号'
          // }
        ],
        publishDir: [
          {
            required: true,
            message: '请输入应用目录',
            trigger: 'blur',
          },
          {
            pattern:
              "^(([a-zA-Z]:(((\\\\(?! )[^\\/:*?<>\\''|\\\\]+)+\\\\?)|(\\\\)?)\\s*[^\\\\\\/])|[\\/][\\w-.]*[^\\\\\\/]){0,250}$",
            message:
              '数据库安装路径不合法,不能以“\\”或“/”结尾，不能为磁盘根目录且长度不能超过250',
          },
        ],
        deployDir: [
          {
            required: true,
            message: '请输入部署目录',
            trigger: 'blur',
          },
          {
            pattern:
              "^(([a-zA-Z]:(((\\\\(?! )[^\\/:*?<>\\''|\\\\]+)+\\\\?)|(\\\\)?)\\s*[^\\\\\\/])|[\\/][\\w-.]*[^\\\\\\/]){0,250}$",
            message:
              '数据库安装路径不合法,不能以“\\”或“/”结尾，不能为磁盘根目录且长度不能超过250',
          },
        ],
        scriptDir: [
          {
            required: true,
            message: '请输入脚本目录',
            trigger: 'blur',
          },
          {
            pattern:
              "^(([a-zA-Z]:(((\\\\(?! )[^\\/:*?<>\\''|\\\\]+)+\\\\?)|(\\\\)?)\\s*[^\\\\\\/])|[\\/][\\w-.]*[^\\\\\\/]){0,250}$",
            message:
              '数据库安装路径不合法,不能以“\\”或“/”结尾，不能为磁盘根目录且长度不能超过250',
          },
        ],
        permissionUser: [
          {
            required: true,
            message: '请输入长度不超过32个字符的名称',

            max: 32,
          },
        ],
        pythonDir: [
          {
            required: false,
            message: '请输入Python安装目录',
            trigger: 'blur',
          },
          {
            pattern:
              "^(([a-zA-Z]:(((\\\\(?! )[^\\/:*?<>\\''|\\\\]+)+\\\\?)|(\\\\)?)\\s*[^\\\\\\/])|[\\/][\\w-.]*[^\\\\\\/]){0,250}$",
            message:
              'Python安装目录不合法,不能以“\\”或“/”结尾，不能为磁盘根目录且长度不能超过250',
          },
        ],
        description: [
          {
            required: false,
            max: 250,
            message: '应用组件描述信息长度不能超过250个字符',
          },
        ],
      },
      advanceFormRules: {}, // 高级属性校验参数
      applicationDatas: [], // 应用列表
      applicationDeploy: {},
    }
  },
  mounted() {
    this.getAppComponents()
    this.getSelectByAppOrDb()
    this.getApplication()
    // 编辑时查询应用信息
    this.getAppInfo()
  },
  methods: {
    async getApplication() {
      const { data, isSuccess, msg } = await apiCmdbServerNoPage()
      if (!isSuccess) {
        return
      }
      this.applicationList = data
    },

    // 获取应用组件类型
    async getAppComponents() {
      const params = {
        state: true,
        type: 'APP_COMPONENTS_TYPE', // 应用组件类型
      }
      // 字典批量查询应用组件类型
      const { data, isSuccess } = await apiBaseDictNoPage(params)
      if (!isSuccess) {
        return
      }
      this.dictList = data
    },
    async getAppInfo() {
      // 根据id查询应用信息
      const { data, isSuccess } = await apiBaseDataFullInfoById(
        this.$route.params.id
      )
      if (!isSuccess) {
        return
      }
      this.basicForm = data

      if (data.appComponentsTypeKey) {
        this.typeChange(data.appComponentsTypeKey)
      }

      if (data.applicationDeploy) {
        const applicationeList = [data.applicationDeploy]

        applicationeList.forEach((element) => {
          element.ip = data.serverIp
          // element.systemId = data.applicationId
        })
        this.applicationDatas = applicationeList

        if (this.applicationDatas) {
          this.applicationDatas.forEach((d) => {
            this.systemIdChange(d, true)
          })
        }
      } else {
        this.applicationDatas = [{}]
      }

      const appComponentsExtends = {}
      data.appComponentsExtends.forEach((a) => {
        appComponentsExtends[a.key] = a.value
      })

      this.advanceForm = appComponentsExtends
    },
    // 获取服务器配置
    async getSelectByAppOrDb() {
      const { data, isSuccess } = await apiBaseGetselectByAppOrDb(
        'appComponents'
      )
      if (isSuccess) {
        this.hostLists = data
      }
    },
    // 应用组件类型下拉框change事件
    async typeChange(val) {
      // 查询高级属性
      const { data, isSuccess } = await apiBaseGetAdvanced(val)
      if (isSuccess) {
        this.advanceTemplete = data
      }

      const advanceRoule = data.map((r) => ({
        required: r.notEmpty,
        message: r.message,
        max: r.maxlength,

        pattern: r.regexp,
        key: r.key,
      }))

      advanceRoule.forEach((item) => {
        if (!this.advanceFormRules[item.key]) {
          this.advanceFormRules[item.key] = [item]
        } else {
          this.advanceFormRules[item.key].push(item)
        }
      })
    },
    // 保存
    async saveInfo() {
      try {
        await Promise.all([
          this.$refs.basicForm.validate(),
          this.advanceTemplete.length != 0
            ? this.$refs.advanceForm.validate()
            : null,
          this.id ? null : this.$refs.hostForm.validate(),
        ])
      } catch (error) {
        return
      }

      try {
        // 应用组件扩展信息
        const appComponentsExtends = this.advanceTemplete.map((r, i) => ({
          appComponentsId: r.id,
          key: r.key,
          type: r.inputType.code, // PASSWORD,INPUT,TEXTAREA,OTHER
          value: this.advanceForm[r.key],
        }))

        if (
          this.applicationDatas.length &&
          this.applicationDatas.filter((r) => r.applicationId != undefined)
            .length
        ) {
          this.applicationDeploy = {
            appComponentsId: this.applicationDatas[0].appComponentsId,
            applicationId: this.applicationDatas[0].applicationId,
            checkUrl: this.applicationDatas[0].checkUrl,
            envKey: this.applicationDatas[0].envKey,
            moduleId: this.applicationDatas[0].moduleId,
          }
        } else {
          this.applicationDeploy = null
        }

        // .map(r => ({
        //   appComponentsId: this.hostLists.find(item => item.ip == r.ip).id,
        //   applicationId: r.applicationId,
        //   checkUrl: r.checkUrl,
        //   envKey: r.envKey,
        //   moduleId: r.moduleId
        // }))

        const hostList = this.basicForm.hostData
        const params = {
          ...pick(this.basicForm, [
            'id',
            'ip',
            'serverPort',
            'appComponentsTypeKey',
            'name',
            'consolePort',
            'publishDir',
            'deployDir',
            'scriptDir',
            'permissionUser',
            'pythonDir',
            'description',
          ]),
          appComponentsExtends: appComponentsExtends, // 应用组件扩展信息
          applicationDeploy: this.applicationDeploy, // 服务应用部署配置信息
          hostIds: hostList,
        }
        this.saveLoading = true
        const { isSuccess, msg } = await apiCmdbUpdateCmAppCompents(params)
        this.saveLoading = false
        if (!isSuccess) {
          this.saveLoading = false
          return this.$message.error(msg)
        }
        this.$message.success('操作成功')
        this.$router.go(-1)
      } catch (error) {
        this.saveLoading = false
        return
      }
    },

    // 应用配置---服务应用下拉框change事件
    async systemIdChange(row, isInit) {
      if (!isInit) {
        row['envKey'] = ''
        row['moduleId'] = ''
        // row.envKey = null
        // row.moduleId = null
      }
      if (!row.applicationId) {
        return
      }
      // 查询环境
      const resEnv = await apiBaseSelectBaseDatagById(row.applicationId)
      if (!resEnv.isSuccess) {
        this.$message.warning(resEnv.msg)
        return
      }

      const list = resEnv.data.applicationEnvs.length
        ? resEnv.data.applicationEnvs.map((r) => ({
            code: r.echoMap.envKey.code,
            name: r.echoMap.envKey.name,
          }))
        : []
      // row.envList = list
      row['envList'] = list
      // 查询工程包
      var resProject = await apiCmdbSelectModuleBySystemId(row.applicationId)
      if (!resProject.isSuccess) {
        this.$message.warning(resProject.msg)
        return
      }
      row['echoMap'] = resProject.data.length
        ? resProject.data[0].echoMap
        : null
      row['moduleId'] = resProject.data.length ? resProject.data[0].id : ''

      row['engineList'] = resProject.data.length ? resProject.data : []
    },
    // 选择服务器
    onHostChange() {
      const { hostData } = this.basicForm
      // const oldIPs = this.applicationDatas.map(({ ip }) => ip)
      // this.applicationDatas = hostData.map((ip) => {
      //   const idx = oldIPs.indexOf(ip)
      //   return idx === -1 ? { ip } : this.applicationDatas[idx]
      // })
      this.applicationDatas = this.hostLists
        .filter((v) => hostData.indexOf(v.id) > -1)
        .map((v) => {
          v['checkUrl'] = ''
          return v
        })
    },
    cancle() {
      this.$router.go(-1)
    },
  },
}
</script>

<style lang="scss" scoped>
:deep() {
  .vone-vxe-table .vxe-table--body-wrapper {
    height: initial;
  }
}
</style>
