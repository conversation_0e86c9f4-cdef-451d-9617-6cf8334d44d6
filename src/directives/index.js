function getEleAttr(el) {
  let elDom = null
  let level = 1
  for (const i of el.childNodes) {
    if (i.className == 'label') {
      elDom = i
      level = i.getAttribute('data-node') || 1
    }
  }
  return { elDom, level }
}
export const showTooltips = {
  inserted: function(el, binding) {
    const { elDom, level } = getEleAttr(el)
    if (elDom.offsetWidth + (level * 10) <= el.offsetWidth) {
      // 隐藏  tooltip
      const openDelay = el.__vue__ && el.__vue__.openDelay || 0
      el.addEventListener('mouseenter', () => {
        const ariaDescribedby = el.attributes['aria-describedby'].value
        setTimeout(res => {
          if (!document.getElementById(ariaDescribedby)) { return }
          document.getElementById(ariaDescribedby).style.display = 'none'
        }, openDelay + 10)
      })
    }
  },
  update: function(el, binding) {
    const { elDom, level } = getEleAttr(el)
    const openDelay = el.__vue__ && el.__vue__.openDelay || 0
    const ariaDescribedby = el.attributes['aria-describedby'].value
    if (elDom.offsetWidth + (level * 10) <= el.offsetWidth) {
      // 隐藏  tooltip
      el.addEventListener('mouseenter', () => {
        setTimeout(res => {
          if (!document.getElementById(ariaDescribedby)) { return }
          document.getElementById(ariaDescribedby).style.display = 'none'
        }, openDelay + 10)
      })
    } else {
      el.addEventListener('mouseenter', () => {
        setTimeout(res => {
          if (!document.getElementById(ariaDescribedby)) { return }
          document.getElementById(ariaDescribedby).style.display = 'block'
        }, openDelay + 10)
      })
    }
  }
}

export const showWorkItemTooltips = {
  inserted: function(el, binding) {
    if (el.offsetWidth + 21 < el.parentNode.offsetWidth) {
      // 隐藏  tooltip
      el.addEventListener('mouseenter', () => {
        const ariaDescribedby = el.attributes['aria-describedby'].value
        setTimeout(res => {
          if (!document.getElementById(ariaDescribedby)) { return }
          document.getElementById(ariaDescribedby).style.display = 'none'
        }, 10)
      })
    }
  },
  update: function(el, binding) {
    const ariaDescribedby = el.attributes['aria-describedby'].value
    const ariaDescribedbyEle = document.getElementById(ariaDescribedby)
    if (el.offsetWidth + 21 < el.parentNode.offsetWidth) {
      // 隐藏  tooltip
      el.addEventListener('mouseenter', () => {
        setTimeout(res => {
          if (!ariaDescribedbyEle) { return }
          ariaDescribedbyEle.style.display = 'none'
        }, 10)
      })
    } else {
      el.addEventListener('mouseenter', () => {
        setTimeout(res => {
          if (!ariaDescribedbyEle) { return }
          ariaDescribedbyEle.style.display = 'block'
        }, 10)
      })
    }
  }
}
