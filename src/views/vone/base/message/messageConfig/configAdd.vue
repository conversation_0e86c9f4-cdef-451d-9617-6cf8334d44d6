<template>
  <el-dialog size="md" :title="title" v-model="visible" :before-close="onClose" :wrapper-closable="false" width="50%" :close-on-click-modal="false">

    <el-form ref="addForm" v-loading="loading" :model="addForm" :rules="rules" label-position="top">

      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="方案名称" prop="name">
            <el-input v-model="addForm.name" placeholder="请输入方案名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="引擎类型" prop="msgEngineType">
            <el-select v-model="addForm.msgEngineType" placeholder="请选择引擎类型" filterable :disabled="id?true :false">
              <el-option v-for="item in engineTypes" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="通知事件" prop="eventId">
            <el-select v-model="addForm.eventId" placeholder="请选择通知事件" filterable :disabled="id?true :false">
              <el-option v-for="item in eventList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="通知模版" prop="templateId">
            <el-select v-model="addForm.templateId" placeholder="请选择通知模版" filterable :disabled="id?true :false">
              <el-option v-for="item in templeteList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="消息接收人规则" prop="receiveId">
            <el-select v-model="addForm.receiveId" placeholder="请选择消息接收人" filterable :disabled="id?true :false">
              <el-option v-for="item in reciveList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="通知方式" prop="scheduled">
            <el-select v-model="addForm.scheduled" placeholder="请选择通知方式" filterable :disabled="id?true :false">
              <el-option label="即时" :value="false" />
              <!-- <el-option label="定时" :value="true" /> -->
            </el-select>
          </el-form-item>
        </el-col>
        <el-col v-if="addForm.scheduled == true" :span="12">
          <el-form-item label="定时规则" prop="cron">
            <el-input v-model="addForm.cron" placeholder="请输入定时规则" />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="方案描述" prop="description">
            <el-input
              v-model.trim="addForm.description"
              type="textarea"
              :rows="2"
              placeholder="请输入方案描述"
              maxlength="500"
              show-word-limit
              :autosize="{ minRows: 2}"
            />
          </el-form-item>
        </el-col>

      </el-row>

    </el-form>
    <el-row slot="footer">
      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="saveUser(0)">确定</el-button>

    </el-row>

  </el-dialog>
</template>

<script>

import {
  apiBaseMsgEvent,
  apiMsgSchemeHandle,
  apiMsgSchemeInfo
} from '@/api/vone/base/message'

import { apiBaseDictEnumList } from '@/api/vone/ouath/ouath'

import { avatarList } from '@/assets/avatar/avatar'

export default {
  props: {
    id: {
      type: String,
      default: null
    },
    title: {
      type: String,
      default: null
    },
    visible: {
      type: Boolean,
      default: false
    },
    templeteList: {
      type: Array,
      default: () => []
    },
    reciveList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      avatarList,
      loading: false,
      saveLoading: false,
      save: '保存并添加下一个',
      rules: {
        name: [
          {
            required: true,
            message: '请输入长度不超过50个字符的名称',
            trigger: 'change'
          },
          {
            max: 100,
            message: '请输入长度不超过100个字符的名称',
            trigger: 'change'
          }
        ],
        type: [
          {
            required: true,
            message: '请选择适用引擎类型',
            trigger: 'change'
          }
        ],
        event: [
          {
            required: true,
            message: '请选择适用事件',
            trigger: 'change',
            type: 'array'
          }
        ],
        title: [
          {
            required: true,
            message: '请输入模版标题',
            trigger: 'change'
          },
          {
            max: 500,
            message: '请输入长度不超过500个字符的模版标题',
            trigger: 'change'
          }
        ],
        body: [
          {
            required: true,
            message: '请输入模版标题',
            trigger: 'change'
          },
          {
            max: 5000,
            message: '请输入长度不超过5000个字符的模版标题',
            trigger: 'change'
          }
        ],
        description: [
          {
            required: false,
            max: 500,
            message: '请输入长度不超过500个字符的描述',
            trigger: 'change'
          }
        ]
      },
      addForm: {
        name: '',
        description: '',
        type: 'FEI_SHU',
        readonly: false,
        scheduled: false,
        state: true
      },
      engineTypes: [],
      eventList: []
    }
  },
  mounted() {
    this.getEngineTypes()
    this.getEventList()
    // this.getTemplateData()
    // this.getReciveData()
    if (this.id) {
      this.getUserDetail()
    }
    this.$nextTick(() => {
      this.$refs.addForm.clearValidate()
    })
  },
  methods: {
    async getEngineTypes() {
      const res = await apiBaseDictEnumList(
        ['MsgEngineType']
      )
      if (!res.isSuccess) {
        return
      }
      this.engineTypes = res.data.MsgEngineType
    },
    async getEventList() {
      const res = await apiBaseMsgEvent()
      if (!res.isSuccess) {
        return
      }
      this.eventList = res.data
    },

    onClose() {
      this.$emit('update:visible', false)
      this.$refs.addForm.resetFields()
    },

    async getUserDetail() {
      this.loading = true
      const res = await apiMsgSchemeInfo(this.id)
      this.loading = false

      if (!res.isSuccess) {
        return
      }
      this.addForm = res.data
      this.$set(this.addForm, 'msgEngineType', res.data?.msgEngineType?.code)
    },

    async saveUser(type) {
      try {
        await this.$refs.addForm.validate()
      } catch (e) {
        return
      }
      try {
        this.saveLoading = true

        const res = await apiMsgSchemeHandle(
          this.addForm
          , this.id ? 'put' : 'post')

        if (!res.isSuccess) {
          this.$message.error(res.msg)
          return
        }
        this.saveLoading = false
        this.$message.success(this.id ? '修改成功' : '保存成功')
        this.$emit('success')

        if (type == 0) {
          this.onClose()
        } else {
          this.$refs.addForm.resetFields()
        }
      } catch (e) {
        this.saveLoading = false
        return
      }
    }
  }
}
</script>

<style  lang="scss" scoped >
.urlBox {
  :deep(.el-input-group__prepend) {
    background-color: #49cc90;
    color: var(--main-bg-color);
  }
}
.mb-10 {
  margin-bottom: 10px;
}
:deep(.el-dialog__body) {
  max-height: 520px;
  overflow-y: auto;
  overflow-x: hidden;
}
</style>

