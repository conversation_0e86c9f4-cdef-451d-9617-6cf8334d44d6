<template>
  <div>
    <page-wrapper class="box">
      <div class="header">
        <div class="title">
          {{ dateType === "week" ? "每周" : "每天" }}工时日志填报
        </div>
        <div class="data-switching">
          <el-button :icon="ElIconArrowLeft" plain @click="previous" />
          <span v-if="dateType === 'week'" class="data-title"
            >{{ startDate }} 到 {{ endDate }}</span
          >
          <span v-else class="data-title">{{ dayDate }}</span>
          <el-button :icon="ElIconArrowRight" plain @click="next" />
        </div>
        <div class="date-tabs">
          <span :active="dateType === 'week'" @click="changeTab('week')"
            >周</span
          >
          <span :active="dateType === 'day'" @click="changeTab('day')">天</span>
        </div>
      </div>
      <vone-search-wrapper>
        <template v-slot:actions>
          <el-button type="primary" :icon="ElIconPlus" @click="addChild"
            >添加行</el-button
          >
        </template>
      </vone-search-wrapper>
      <el-form ref="timeForm" :model="timeForm" :inline-message="false">
        <main style="height: calc(100vh - 260rem)">
          <vxe-table
            ref="timeTable"
            class="vone-vxe-table"
            border
            resizable
            height="auto"
            show-overflow="tooltip"
            :empty-render="{ name: 'empty' }"
            :data="timeForm.timelist"
            :column-config="{ minWidth: '120px' }"
            :checkbox-config="{ reserve: true }"
            row-id="id"
          >
            <vxe-column v-if="dateType === 'week'" title="用户" width="130">
              <template #default="{ row, rowIndex }">
                <el-form-item
                  :key="'timelist.' + rowIndex + '.filledBy'"
                  label-width="0"
                  :prop="'timelist.' + rowIndex + '.filledBy'"
                  :rules="{
                    required: true,
                    message: '请选择用户',
                    trigger: 'change',
                  }"
                >
                  <vone-remote-user
                    v-model:value="row.filledBy"
                    placeholder="请选择"
                  />
                </el-form-item>
              </template>
            </vxe-column>
            <vxe-column
              v-if="dateType === 'week'"
              title="工作项"
              min-width="240"
            >
              <template #default="{ row, rowIndex }">
                <el-row class="typeselect">
                  <el-col :span="8">
                    <el-form-item
                      label-width="0"
                      :prop="'timelist.' + rowIndex + '.type'"
                      :rules="{
                        required: true,
                        message: '请选择类型',
                        trigger: 'change',
                      }"
                    >
                      <el-select
                        v-model="row.type"
                        class="select-tyle"
                        placeholder="请选择"
                        @change="(e) => workTypeChange(e, row)"
                      >
                        <el-option label="需求" value="ISSUE">
                          <el-icon
                            class="iconfont"
                            style="color: rgb(135, 145, 250)"
                            ><el-icon-icon-xuqiu
                          /></el-icon>
                          <span>需求</span>
                        </el-option>
                        <el-option label="缺陷" value="BUG">
                          <el-icon
                            class="iconfont"
                            style="color: rgb(250, 107, 87)"
                            ><el-icon-icon-quexian
                          /></el-icon>
                          <span>缺陷</span>
                        </el-option>
                        <el-option label="任务" value="TASK">
                          <el-icon
                            class="iconfont"
                            style="color: rgb(62, 123, 250)"
                            ><el-icon-icon-renwu
                          /></el-icon>
                          <span>任务</span>
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="16">
                    <el-form-item
                      label-width="0"
                      :prop="'timelist.' + rowIndex + '.bizId'"
                      :rules="{
                        required: true,
                        message: '请选择工作项',
                        trigger: 'change',
                      }"
                    >
                      <el-select
                        v-model="row.bizId"
                        :remote-method="(query) => remoteMethod(query, row)"
                        :loading="loading"
                        no-match-text="暂未查询到匹配数据,请重新输入"
                        filterable
                        remote
                        class="select-option"
                        placeholder="请选择"
                        clearable
                      >
                        <el-option
                          v-for="i in row.workItem"
                          :key="i.id"
                          :label="i.name"
                          :value="i.id"
                        >
                          <i
                            :class="`iconfont ${i.echoMap.typeCode.icon}`"
                            :style="{
                              color: `${
                                i.echoMap.typeCode
                                  ? i.echoMap.typeCode.color
                                  : '#ccc'
                              }`,
                            }"
                          />
                          <span class="fontstyle"
                            >{{ i.code }} {{ i.name }}</span
                          >
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
              </template>
            </vxe-column>
            <div v-for="(item, index) in dateList" :key="item.date + '_'">
              <vxe-column
                v-if="dateType === 'week'"
                width="110"
                :title="item.date"
              >
                <template #header>
                  <div v-if="dateType === 'week'">
                    <div>{{ item.name }}</div>
                    <div>{{ item.date }}</div>
                  </div>
                  <div v-else>工时h</div>
                </template>
                <template #default="{ row, rowIndex }">
                  <el-form-item
                    :key="'timelist.' + rowIndex + '_' + item.date"
                    label-width="0"
                    :prop="'timelist.' + rowIndex + '.' + item.date"
                    :rules="[
                      {
                        required: false,
                        message: '请输入工时',
                        trigger: 'blur',
                      },
                      { validator: checkTime },
                    ]"
                  >
                    <el-input
                      v-model.number="row[item.date]"
                      type="number"
                      :disabled="isdisabled(item)"
                      class="number-input"
                      placeholder="请输入"
                      @change="numberChange($event, row, item.date)"
                    />
                  </el-form-item>
                </template>
              </vxe-column>
            </div>
            <div v-for="(item, index) in dateList" :key="item.date">
              <vxe-column v-if="dateType === 'day'" title="用户" width="150">
                <template #default="{ row, rowIndex }">
                  <el-form-item
                    :key="
                      'timelist.' + rowIndex + '.' + item.date + '_filledBy'
                    "
                    label-width="0"
                    :prop="
                      'timelist.' + rowIndex + '.' + item.date + '_filledBy'
                    "
                    :rules="{
                      required: true,
                      message: '请选择用户',
                      trigger: 'change',
                    }"
                  >
                    <vone-remote-user
                      v-model:value="row[item.date + '_filledBy']"
                      placeholder="请选择"
                    />
                  </el-form-item>
                </template>
              </vxe-column>
              <vxe-column
                v-if="dateType === 'day'"
                min-width="240"
                title="工作项"
              >
                <template #default="{ row, rowIndex }">
                  <el-row class="typeselect">
                    <el-col :span="8">
                      <el-form-item
                        :key="
                          'timelist.' + rowIndex + '.' + item.date + '_type'
                        "
                        label-width="0"
                        :prop="
                          'timelist.' + rowIndex + '.' + item.date + '_type'
                        "
                        :rules="{
                          required: true,
                          message: '请选择类型',
                          trigger: 'change',
                        }"
                      >
                        <el-select
                          v-model="row[item.date + '_type']"
                          class="select-tyle"
                          placeholder="请选择"
                          @change="(e) => workTypeChange(e, row, item.date)"
                        >
                          <el-option label="需求" value="ISSUE">
                            <el-icon
                              class="iconfont"
                              style="color: rgb(135, 145, 250)"
                              ><el-icon-icon-xuqiu
                            /></el-icon>
                            <span>需求</span>
                          </el-option>
                          <el-option label="缺陷" value="BUG">
                            <el-icon
                              class="iconfont"
                              style="color: rgb(250, 107, 87)"
                              ><el-icon-icon-quexian
                            /></el-icon>
                            <span>缺陷</span>
                          </el-option>
                          <el-option label="任务" value="TASK">
                            <el-icon
                              class="iconfont"
                              style="color: rgb(62, 123, 250)"
                              ><el-icon-icon-renwu
                            /></el-icon>
                            <span>任务</span>
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="16">
                      <el-form-item
                        label-width="0"
                        :prop="
                          'timelist.' + rowIndex + '.' + item.date + '_bizId'
                        "
                        :rules="{
                          required: true,
                          message: '请选择工作项',
                          trigger: 'change',
                        }"
                      >
                        <el-select
                          v-model="row[item.date + '_bizId']"
                          :remote-method="
                            (query) => remoteMethod(query, row, item.date)
                          "
                          :loading="loading"
                          no-match-text="暂未查询到匹配数据,请重新输入"
                          filterable
                          remote
                          class="select-option"
                          placeholder="请选择"
                          clearable
                        >
                          <el-option
                            v-for="i in row[item.date + '_workItem']"
                            :key="i.id"
                            :label="i.name"
                            :value="i.id"
                          >
                            <i
                              :class="`iconfont ${i.echoMap.typeCode.icon}`"
                              :style="{
                                color: `${
                                  i.echoMap.typeCode
                                    ? i.echoMap.typeCode.color
                                    : '#ccc'
                                }`,
                              }"
                            />
                            <span class="fontstyle"
                              >{{ i.code }} {{ i.name }}</span
                            >
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </template>
              </vxe-column>
              <vxe-column
                v-if="dateType === 'day'"
                width="100"
                :title="item.date"
              >
                <template v-slot:header>
                  <div v-if="dateType === 'week'">
                    <div>{{ item.name }}</div>
                    <div>{{ item.date }}</div>
                  </div>
                  <div v-else>工时h</div>
                </template>
                <template #default="{ row, rowIndex }">
                  <el-form-item
                    :key="'timelist.' + rowIndex + '_' + item.date"
                    label-width="0"
                    :prop="'timelist.' + rowIndex + '.' + item.date"
                    :rules="[
                      {
                        required: false,
                        message: '请输入工时',
                        trigger: 'blur',
                      },
                      { validator: checkTime, trigger: ['blur', 'change'] },
                    ]"
                  >
                    <el-input
                      v-model.number="row[item.date]"
                      type="number"
                      :disabled="isdisabled(item)"
                      class="number-input"
                      placeholder="请输入"
                      @change="numberChange($event, row, item.date)"
                    />
                  </el-form-item>
                </template>
              </vxe-column>
              <vxe-column
                v-if="dateType === 'day'"
                min-width="250"
                label="描述"
              >
                <template #default="{ row, rowIndex }">
                  <el-form-item
                    label-width="0"
                    :prop="
                      'timelist.' + rowIndex + '.' + item.date + '_description'
                    "
                    :rules="[
                      {
                        required: false,
                        message: '请输入描述',
                        trigger: 'blur',
                      },
                    ]"
                  >
                    <el-input
                      v-model="row[item.date + '_description']"
                      placeholder="请输入"
                    />
                  </el-form-item>
                </template>
              </vxe-column>
            </div>
            <template v-if="dateType === 'week'">
              <vxe-column
                :key="Math.random()"
                width="80"
                field="total"
                title="全部工时"
                style="font-size: 14px"
              >
                <template #default="{ row }"> {{ getTotal(row) }} h </template>
              </vxe-column>
            </template>
            <vxe-column title="操作" fixed="right" align="left" width="70">
              <template #default="{ rowIndex }">
                <el-button
                  v-if="timeForm.timelist && timeForm.timelist.length > 1"
                  :icon="elIconApplicationDelete"
                  type="text"
                  size="small"
                  @click.prevent="deleteRow(rowIndex, timeForm.timelist)"
                />
              </template>
            </vxe-column>
          </vxe-table>
        </main>
      </el-form>
    </page-wrapper>
    <div class="foot">
      <el-button @click="resetForm('timeForm')">取消</el-button>
      <el-button type="primary" @click="addTime">提交</el-button>
    </div>
  </div>
</template>

<script>
import dayjs from "dayjs";
import {
  workItemSearch,
  addWorkingHoursInfoBatch,
  getWorkingHoursConfig,
} from "@/api/vone/manhour/index";

import _ from "lodash";

const calendar = require("dayjs/plugin/calendar");
dayjs.extend(calendar);
export default {
  data() {
    return {
      dateList: [],
      workItem: [],
      startDate: "",
      endDate: "",
      dayDate: "",
      weekday: [
        "星期日",
        "星期一",
        "星期二",
        "星期三",
        "星期四",
        "星期五",
        "星期六",
      ],
      currentDay: new Date(),
      currentToday: new Date(),
      timeForm: {
        timelist: [],
      },
      loading: false,
      params: {},
      dateType: "week",
      config: {},
      pickerType: {},
      column: [
        {
          label: "filledBy",
          width: "150px",
          value: "filledBy",
        },
      ],
    };
  },
  mounted() {
    this.pickerType = {
      week: dayjs().add(-6, "day").startOf("day").format("YYYY-MM-DD"),
      month: dayjs().add(-30, "day").startOf("day").format("YYYY-MM-DD"),
      quarter: dayjs().add(-90, "day").startOf("day").format("YYYY-MM-DD"),
    };
    this.addChild();
  },
  created() {
    this.getWorkingHoursConfFn();
  },
  methods: {
    isdisabled(item) {
      if (this.config["FILLABLE_PERIOD"] === "nolimit") {
        if (
          (item.week === 6 || item.week === 0) &&
          this.config["ENABLE_FILL_WEEKEND"] === "0"
        ) {
          return true;
        } else {
          return false;
        }
      } else {
        if (
          dayjs(item.date).isBefore(
            dayjs(this.pickerType[this.config["FILLABLE_PERIOD"]])
          ) ||
          dayjs(item.date).isAfter(dayjs())
        ) {
          return true;
        } else {
          if (
            (item.week === 6 || item.week === 0) &&
            this.config["ENABLE_FILL_WEEKEND"] === "0"
          ) {
            return true;
          } else {
            return false;
          }
        }
      }
    },
    async getWorkingHoursConfFn() {
      try {
        const res = await getWorkingHoursConfig();
        res.data.forEach((e) => {
          this.config[e.key] = e.value;
        });
        this.getDateList();
      } catch (e) {
        return;
      }
    },
    changeTab(e) {
      this.dateType = e;
      this.dayDate = dayjs(new Date()).format("YYYY-MM-DD");
      this.timeForm.timelist.forEach((e, index) => {
        if (e.dateContainer.length > 0) {
          e.dateContainer.forEach((item) => {
            e[item] = null;
          });
        }
        this.timeForm.timelist[index] = e;
      });
      this.getDateList();
    },
    getTotal(row) {
      var total = 0;
      row.datelist.forEach((item) => {
        if (row[item.date] && row[item.date] > 0) {
          total += row[item.date];
        }
      });
      row.total = total;
      return total;
    },
    checkTime(rule, value, callback) {
      if (value == null || value === "" || typeof value == "undefined") {
        if (this.dateType === "week") {
          return callback();
        } else {
          return callback(new Error("请输入工时"));
        }
      }
      if (typeof value !== "number") {
        callback(new Error("工时为数字"));
      } else if (value <= 0) {
        callback(new Error("工时应大于0"));
      } else if (value > Number(this.config["MAX_HOURS_OF_EVERY_DAY"])) {
        callback(
          new Error(`工时不大于${this.config["MAX_HOURS_OF_EVERY_DAY"]}`)
        );
      } else {
        callback();
      }
    },
    remoteMethod: _.debounce(function (query, row, date) {
      this.loading = true;
      if (query != "") {
        this.params.search = query;
        this.getworkItemSearch(row, date);
      }
    }, 1000),
    resetForm(formName) {
      this.timeForm.timelist.forEach((e) => {
        e.total = 0;
      });
      this.$refs[formName].resetFields();
    },
    numberChange(e, row, item) {
      if (e == "") {
        row[item] = null;
        if (row.dateContainer.includes(item)) {
          row.dateContainer.splice(
            row.dateContainer.findIndex((e) => e === item),
            1
          );
        }
      } else {
        if (!row.dateContainer.includes(item)) {
          row.dateContainer.push(item);
        }
      }
    },
    workTypeChange(e, row, date) {
      this.params.sourceTypes = [e];
      this.getworkItemSearch(row, date);
    },
    async getworkItemSearch(row, date) {
      this.loading = true;
      const res = await workItemSearch(this.params);
      if (res.isSuccess) {
        row[date + "_workItem"] = res.data;
        row.workItem = res.data;
      }
      this.loading = false;
    },
    previous() {
      const date = dayjs(this.currentDay).subtract(7, "day").toDate();
      this.currentDay = date;
      if (this.dateType === "day") {
        const todate = dayjs(this.currentToday).subtract(1, "day").toDate();
        this.currentToday = todate;
        this.dayDate = dayjs(this.currentToday).format("YYYY-MM-DD");
      }
      this.getDateList();
    },
    next() {
      const date = dayjs(this.currentDay).add(7, "day").toDate();
      this.currentDay = date;
      if (this.dateType === "day") {
        const todate = dayjs(this.currentToday).add(1, "day").toDate();
        this.currentToday = todate;
        this.dayDate = dayjs(this.currentToday).format("YYYY-MM-DD");
      }
      this.getDateList();
    },
    async addTime() {
      try {
        await this.$refs.timeForm.validate();
        var arry = [];
        this.timeForm.timelist.forEach((e) => {
          e.dateContainer.forEach((item) => {
            if (e[item]) {
              var obj = {
                filledBy:
                  this.dateType === "week" ? e.filledBy : e[item + "_filledBy"],
                type: this.dateType === "week" ? e.type : e[item + "_type"],
                bizId: this.dateType === "week" ? e.bizId : e[item + "_bizId"],
                fillingTime: item,
                duration: e[item],
                description: e[item + "_description"],
              };
              arry.push(obj);
            }
          });
        });
        if (arry.length > 0) {
          this.saveLoading = true;
          const res = await addWorkingHoursInfoBatch(arry);
          this.saveLoading = false;
          if (!res.isSuccess) {
            this.$message.warning(res.msg);
            return;
          }
          this.$message.success("添加成功");
          this.resetForm("timeForm");
        } else {
          this.$message.warning("请填写工时");
        }
      } catch (e) {
        this.saveLoading = false;
      }
    },
    addChild() {
      const parameter = {
        filledBy: "",
        type: "",
        name: "",
        datelist: this.dateList,
        total: 0,
        dateContainer: [],
        description: "",
        duration: null,
      };
      this.dateList.forEach((e) => {
        parameter[e.date] = null;
      });
      this.params = {};
      this.timeForm.timelist.push(parameter);
    },
    deleteRow(e) {
      this.timeForm.timelist.splice(e, 1);
    },
    getMonDate() {
      var d = _.cloneDeep(this.currentDay);
      var day = d.getDay();
      var date = d.getDate();
      if (day == 1) {
        return d;
      }
      if (day == 0) {
        d.setDate(date - 6);
      } else {
        d.setDate(date - day + 1);
      }
      return d;
    },
    getDateList() {
      var d = this.getMonDate();
      var arry = [];
      if (this.dateType === "week") {
        for (var i = 0; i < 7; i++) {
          var obj = {
            name: this.weekday[dayjs(d).day()],
            week: dayjs(d).day(),
            date: dayjs(d).format("YYYY-MM-DD"),
          };
          arry.push(obj);
          d.setDate(d.getDate() + 1);
        }
        this.startDate = arry[0].date;
        this.endDate = arry[6].date;
      } else {
        var objs = {
          name: this.weekday[dayjs(this.dayDate).day()],
          week: dayjs(this.dayDate).day(),
          date: this.dayDate,
        };
        arry.push(objs);
      }
      this.dateList = arry;
      this.dateCalculation();
    },
    dateCalculation() {
      this.timeForm.timelist.forEach((e, index) => {
        e.datelist = this.dateList;
        this.timeForm.timelist[index] = e;
      });
      this.$nextTick(() => {
        // this.$refs['timeTable'].doLayout()
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.box{position:relative;min-height:calc(100vh - 150px)}.header{padding:0 16px;margin:-16px -16px 16px;border-bottom:1px solid var(--disabled-bg-color);height:56px;font-size:16px;display:flex;justify-content:space-between;align-items:center;.title {
		font-weight: 500;
    color: var(--main-font-color);
	}
	.data-switching {
		width: 400px;
		text-align: center;
		.data-title {
			font-weight: 600;
			margin: 0 24px;
			color: var(--main-font-color);
		}
    .el-button {
      width: 32px;
    }
	}
  .date-tabs {
    background: #F2F3F5;
    border-radius: 2px;
    padding: 4px;
    height: 38px;
    >span {
      color: var(--auxiliary-font-color);
      font-size: 14px;
      cursor: pointer;
      height: 30px;
      width: 38px;
      text-align: center;
      line-height: 30px;
      display: inline-block;
      border-radius: 2px;
      &[active] {
        background: #fff;
        color: #3E7BFA;
        font-weight: 500;
      }
    }
  }}:deep(.el-button){min-width:32px;&--default {
    background-color: var(--main-bg-color);
    border-color: #CED1D9;
    color: #6B7385;
    &:focus{
      background-color: var(--main-bg-color);
      border-color: #CED1D9;
      color: #6B7385;
    }
    &:hover {
      background-color: #F2F3F5;
      border-color:#CED1D9;
      color: #6B7385;
    }
  }}:deep(th.el-table__cell.is-leaf){height:48px}:deep(.el-table__cell:nth-child(-n+2)){font-size:14px}:deep(.el-table__cell:nth-child(n+2)){font-size:14px}:deep(.vxe-table .el-form-item){/*// margin-bottom: 16px;*/margin:16px 0;.el-form-item {
    margin: 0;
  }}.select-tyle{min-width:90px;height:30px;:deep(.el-input__inner) {
		border: none;
	}
	&::after {
		content: '';
		width: 1px;
		height: 20px;
		background: #EAECF0;
		position: absolute;
		right: 0px;
    top: calc(50% - 10px);
	}}.select-option{height:30px;:deep(.el-input__inner) {
		border: none;
	}}.foot{width:calc(100% - 72px);height:64px;line-height:64px;text-align:right;position:absolute;bottom:0;right:0;padding-right:16px;background-color:var(--main-bg-color);box-shadow:0px -3px 12px rgba(29,33,41,0.06);z-index:10
  // background-color: red}.pushbut{width:100%;margin-bottom:30px}.typeselect{height:32px;line-height:32px;overflow:hidden;border:1px solid var(--input-border-color);border-radius:2px;:deep(.el-form-item) {
    margin: 0;
  }
  &:focus {
    border-color: #3E7BFA;
  }
  :deep(.el-input__inner + .typeselect) {
    &:focus {
      border-color: #3E7BFA;
      color: red;
    }
  }}.iconfont{line-height:22px;margin-right:9px}.fontstyle{overflow:hidden;white-space:nowrap;text-overflow:ellipsis}:deep(.vone-vxe-table .vxe-body--column){height:64px;line-height:64px;&>.vxe-cell {
     max-height: 64px;
  }}:deep(.el-button--text){padding:0;min-width:auto}:deep(.vxe-table::before){display:none}:deep(.el-table__fixed-right::before, .el-table__fixed::before){display:none}.number-input{:deep(input::-webkit-inner-spin-button) {
    appearance: none !important;
  }

  :deep(input::-webkit-outer-spin-button) {
    appearance: none !important;
  }

  input[type='number'] {
    appearance: textfield;
  }}
</style>
