import request from '@/utils/axios-api'
import requestUtils from '@/utils/requestUtils'

// 获取用户头像
export function getUserList(data) {
  return request({
    url: '/api/base/base/user/findByAccountOrName',
    method: 'get',
    data
  })
}

// 获取过滤器
export function getTableFilter(data) {
  return request({
    url: '/api/base/base/personalizedTableSearchFilter/findByQuery',
    method: 'post',
    data
  })
}
// 操作过滤器
export function operationFilter(data, method) {
  return request({
    url: '/api/base/base/personalizedTableSearchFilter',
    method: method,
    data
  })
}
// 设置为默认过滤器
export function setDefaultFilter(data) {
  return request({
    url: '/api/base/base/personalizedTableSearchFilter/setDefault',
    method: 'put',
    data
  })
}
// 获取table展示的列
export function getColumn(data) {
  return request({
    url: '/api/base/base/personalizedTableColumn/findByQuery',
    method: 'post',
    data
  })
}
// 保存table列
export function operationColumn(data, method) {
  return request({
    url: '/api/base/base/personalizedTableColumn',
    method: method,
    data
  })
}
// 上传文件
export function uploadFile(data) {
  return request({
    url: '/api/base/base/file/anyone/upload',
    method: 'post',
    data
  })
}
// 删除文件
export function delFile(data) {
  return request({
    url: '/api/base/base/file',
    method: 'delete',
    data
  })
}

// 文件下载--根据id
export function downloadFile(data) {
  return request({
    url: '/api/base/base/file/download',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

// 获取版本更新说明日期
export function apiCodeGetReleseTree(data) {
  return request({
    url: '',
    method: 'get',
    data
  })
}

// 获取版本对应日期文件
export function apiCodeGetReleseBlobs(data) {
  return request({
    url: '',
    method: 'get',
    data
  })
}

// 下载导入模板
export function downloadTemplate(url, data) {
  return request({
    url: url,
    method: 'get',
    data,
    headers: {
      'Content-Type': 'application/json; charset=UTF-8'
    },
    responseType: 'blob' // 在请求中加上这一行，特别重要
  })
}

// 导入
export function apiImport(url, data) {
  return request({
    url: url,
    method: 'post',
    data: requestUtils.fileFormData(data),
    timeout: 10 * 60 * 1000
  })
}

// 根据条件查询工作项自定义的字段
export function queryFieldList(data) {
  return request({
    url: '/api/alm/alm/projectCustomForm/queryFieldList',
    method: 'post',
    data
  })
}

// 获取 onlyoffice 配置
export function onlyofficeConfig(query) {
  return request({
    url: '/api/base/base/file/onlyoffice/config',
    method: 'get',
    params: query
  })
}
