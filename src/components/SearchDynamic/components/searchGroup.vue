<template>
  <div>
    <el-dropdown
      v-if="groupingOptions.length != 0"
      :class="[
        'grouping-style',
        groupingItem.value !== 'none' ? 'item-active' : '',
      ]"
      trigger="click"
      @command="groupingCommand"
    >
      <span class="label">
        <span class="weak-title">分组</span>
        {{ groupingItem.value == "none" ? "不分组" : groupingItem.name }}
        <el-icon class="iconfont el-icon--right"
          ><el-icon-direction-down
        /></el-icon>
      </span>
      <template v-slot:dropdown>
        <el-dropdown-menu>
          <el-dropdown-item
            :class="[
              'grouping-item',
              groupingItem.value == 'none' ? 'is-active' : '',
            ]"
            command="none"
          >
            全部
            <el-icon class="grouping-icon iconfont"
              ><el-icon-tips-done
            /></el-icon>
          </el-dropdown-item>
          <el-dropdown-item
            v-for="e in groupingOptions"
            :key="e.value"
            :class="[
              'grouping-item',
              groupingItem.value == e.value ? 'is-active' : '',
            ]"
            :command="e.value"
          >
            {{ e.name }}
            <el-icon class="grouping-icon iconfont"
              ><el-icon-tips-done
            /></el-icon>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script>
import {
  DirectionDown as ElIconDirectionDown,
  TipsDone as ElIconTipsDone,
} from "@element-plus/icons-vue";
import { $on, $off, $once, $emit } from "../../../utils/gogocodeTransfer";
export default {
  components: {
    ElIconDirectionDown,
    ElIconTipsDone,
  },
  props: {
    groupingOptions: {
      type: Array,
      default: () => [],
    },
    valueObj: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      groupingItem: {
        name: "不分组",
        value: "none",
      },
    };
  },
  watch: {
    valueObj: {
      deep: true,

      handler(val) {
        const obj = {
          name: "不分组",
          value: "none",
        };

        if (val && val.extra && val.extra.groupView) {
          this.groupingItem =
            val.extra.groupView == "none"
              ? obj
              : this.groupingOptions.find(
                  (e) => e.value == val.extra.groupView
                );
        } else {
          this.groupingItem = obj;
        }
      },

      immediate: true,
    },
  },
  methods: {
    // 分组
    groupingCommand(id) {
      const obj = {
        name: "不分组",
        value: "none",
      };
      this.groupingItem =
        id == "none" ? obj : this.groupingOptions.find((e) => e.value == id);
      $emit(this, "change", id);
    },
  },
  emits: ["change"],
};
</script>

<style lang="scss" scoped>
@use "../index.scss" as *;
</style>

<style lang="scss">
@use "../index-global.scss" as *;
</style>
