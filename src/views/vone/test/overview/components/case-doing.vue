<template>
  <div style="position:relative;">
    <!-- 测试计划--进行中 -->
    <vone-echarts-card :title="title">
      <!-- <vone-empty v-if="fetchData" style="height:400px;" /> -->
      <vone-echarts v-loading="loading" :options="options" />
    </vone-echarts-card>
    <span class="filterBox">
      <!-- <el-select
        v-model="selectedPlans"
        multiple
        filterable
        clearable
        collapse-tags
        remote
        :loading="planLoading"
        :remote-method="getTreeData"
        placeholder="请搜索测试计划"
        class="filterSelect"
        @change="changeSelectedPlans"
      >
        <el-option v-for="item in treeData" :key="item.id" :label="item.name" :value="item.id" />
      </el-select> -->
      <vone-tree-select
        v-model="selectedPlans"
        search-nested
        :tree-data="treeData"
        placeholder="请选择测试计划分组"
        multiple
        :check-strictly="true"
        :limit="1"
        :z-index="99"
        :limit-text="count => `+${count}`"
        :value-consists-of="'ALL_WITH_INDETERMINATE'"
        class="filterSelect"
        append-to-body
        @input="changeSelectedPlans"
      />
    </span>
  </div>

</template>

<script>
import { queryTestPlanRecords } from '@/api/vone/testmanage/index'
import { TestPlanTree, getTestPlanCase } from '@/api/vone/testplan'
import { list2Tree } from '@/utils/list2Tree'
import { gainTreeList, catchErr } from '@/utils'
import { debounce } from 'lodash'

export default {
  props: {
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    const vm = this
    const serieMap = ['systemNum', 'blockingNum', 'smokeNum', 'skipNum', 'undoNum']

    const theme = document.body.className.indexOf('dark') > -1
    return {
      loading: false,
      fetchData: false,
      planLoading: false,
      selectedPlans: [],
      treeData: [],
      echartData: [],
      options: {
        color: ['#3cb540', '#ffbf47', '#fa6b57', '#bd7ffa', '#adb0b8'],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          // formatter: '{b0}<br/>' + now + '<br/>{a}: {c0}%'
          formatter(params) {
            let title = params[0].name + '<br/>'
            for (const item of params) {
              const numData = vm.echartData[item.dataIndex] || {}
              title += item.marker + item.seriesName + `: ${numData[serieMap[item.seriesIndex]]} (${item.value}%)<br/>`
            }
            return title
          }
        },
        legend: {
          top: '6px',
          right: '10px',
          itemWidth: 12,
          itemHeight: 8
        },
        grid: {
          left: 72,
          right: '16px',
          top: '32px',
          bottom: '8%'
        },
        xAxis: {
          type: 'value',
          min: 0,
          max: 100,
          splitLine: {
            show: true,
            lineStyle: {
              color: theme ? '#333947' : '#EBEEF5',
              type: 'dashed'
            }
          },
          axisLabel: {
            color: '#8A8F99'
          }
        },
        yAxis: {
          type: 'category',
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            width: 68,
            color: '#8A8F99',
            overflow: 'truncate'
          },
          data: []
        },
        series: [
          {
            name: 'process',
            type: 'bar',
            barMaxWidth: 25,
            stack: 'total',
            emphasis: {
              focus: 'series'
            },
            data: []
          }
        ]
      }
    }
  },
  mounted() {
    this.$bus.$on('refreshChart', () => {
      this.changeTheme()
    })
    // 查询数据
    this.getData([])
    this.getPlanTree()
  },
  methods: {
    changeSelectedPlans(val) {
      this.getData(val)
    },
    // 查询测试计划树
    async getPlanTree() {
      const res = await TestPlanTree()

      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      const tree = list2Tree(res.data, { parentKey: 'parentId' })
      this.treeData = gainTreeList(tree)
    },
    // 查询测试计划数据
    getTreeData: debounce(async function(query) {
      if (query != '') {
        const params = {
          current: 1,
          extra: {},
          model: { name: query },
          order: 'descending',
          size: 99999,
          sort: 'createTime'
        }
        const [res, err] = await catchErr(getTestPlanCase(params))
        if (err) return
        if (res.isSuccess) {
          this.treeData = res.data.records
        }
      }
    }, 1000),
    setData(l, e) {
      return l.map(r => {
        const obj = {
          value: r[e],
          speed: r.echoMap.rateOfCoverage === 0 || r.echoMap.rateOfCoverage === 1 ? r.echoMap.rateOfCoverage * 100 + '%' : (r.echoMap.rateOfCoverage * 100).toFixed(2) + '%'
        }
        return obj
      })
    },
    // 测试计划--进行中
    async getData(val) {
      this.loading = true
      this.fetchData = false
      const [res, err] = await catchErr(queryTestPlanRecords(val))
      this.fetchData = true
      this.loading = false
      if (err) return
      if (!res.isSuccess) {
        this.$message.success(res.msg)
      }
      const names = []
      const progress = []

      const serieMap = {
        systemNum: { name: '通过', data: [] },
        blockingNum: { name: '阻塞', data: [] },
        smokeNum: { name: '失败', data: [] },
        skipNum: { name: '跳过', data: [] },
        undoNum: { name: '未测', data: [] }
      }
      this.echartData = res.data
      this.options.series = []
      if (res.data?.length > 0) {
        res.data.map(item => {
          names.push(item.echoMap?.plan.name || '')
          const { blockingNum, skipNum, smokeNum, systemNum, undoNum } = item
          const total = (+blockingNum) + (+skipNum) + (+smokeNum) + (+systemNum) + (+undoNum)
          // 计划下用例执行状况求值
          for (const key in serieMap) {
            const rate = ((total > 0 ? item[key] / total : 0) * 100).toFixed(2)
            serieMap[key].data.push(rate)
          }
          const rateOfCoverage = item.echoMap?.rateOfCoverage
          progress.push(rateOfCoverage === 0 || rateOfCoverage === 1 ? rateOfCoverage * 100 : (rateOfCoverage * 100).toFixed(2))
        })
      }
      this.options.yAxis.data = names
      // 设置series
      for (const key in serieMap) {
        const serie = {
          name: serieMap[key].name,
          type: 'bar',
          barMaxWidth: 12,
          barCategoryGap: '24px',
          stack: 'total',
          emphasis: {
            focus: 'series'
          },
          data: serieMap[key].data
        }
        this.options.series.push(serie)
      }
    },
    changeTheme() {
      // 是否是暗色主题
      const theme = document.body.className.indexOf('dark') > -1
      this.options.xAxis.splitLine.lineStyle.color = theme ? '#333947' : '#EBEEF5'
    }
  }

}
</script>

<style lang="scss" scoped>
.filterBox {
  position: absolute;
  top: 9px;
  right: 16px;

  .filterSelect {
    width: 240px;
    ::v-deep {
      .el-input__inner {
        background: var(--hover-bg-color);
        border-color: var(--hover-bg-color);
      }
      .el-select__tags {
        width: 160px;
        .el-tag {
          max-width: 150px;
        }
      }
    }
  }
}

::v-deep {
  .vone-echarts-card {
    .el-card__body {
      padding-right: 0;
    }
  }
}
::v-deep  {
  .vue-treeselect__control{
  line-height: 20px !important;
}
  .vue-treeselect__list{
    padding: 0;
  }
  .vue-treeselect__multi-value-item{
    padding: 0;
  }
  .filterSelect .vue-trees.vue-treeselect__multi-value-itemelect__limit-tip{
    padding: 0 6px;
  }
  .vue-treeselect__limit-tip {
    padding: 0px 6px;
    line-height: 16px;
  }
}

</style>
