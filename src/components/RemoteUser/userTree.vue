<template>
  <div class="userbox">
    <div class="center">
      <div class="f-left">
        <header>
          <el-input v-model="searchText" :placeholder="`请输入${activeName=='user'? '成员': '机构或成员'}名称`" style="width: 100%;" @input="searchTree" />
        </header>
        <el-tabs v-model="activeName" @tab-click="tabChange">
          <el-tab-pane label="成员" name="user">
            <div class="treeContent">
              <template v-if="!multiple">
                <div v-for="item in selectData" :key="item.id" class="user-list_item" @click="onSave(item)">
                  <span class="user-name">
                    <vone-user-avatar :avatar-path="item.avatarPath" height="16px" width="16px" show-name />
                    <span>{{ item.name }}</span>
                  </span>
                </div>
              </template>
              <el-checkbox-group v-if="activeName == 'user'&&multiple" v-model="userlist" @change="checkChange">
                <el-checkbox v-for="item in selectData" :key="item.id" :disabled="item.disabled" class="checkbox-list" :label="item">
                  <span class="user-name">
                    <vone-user-avatar :avatar-path="item.avatarPath" height="16px" width="16px" show-name />
                    <span>{{ item.name }}</span>
                  </span>
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </el-tab-pane>
          <el-tab-pane label="机构" name="org">
            <div class="treeContent">
              <el-tree
                v-if="activeName == 'org'"
                ref="orgTree"
                v-loading="treeLoading"
                class="tree"
                node-key="id"
                :show-checkbox="multiple"
                :data="treeData"
                :props="treeProps"
                :expand-on-click-node="false"
                :filter-node-method="filterNode"
                @check="treecheck"
              >
                <span slot-scope="{ node, data }" class="custom-tree-node">
                  <span v-if="data.type == 'USER'" class="user-name" @click="changeUser(data)">
                    <vone-user-avatar :avatar-path="data.avatarPath" height="16px" width="16px" show-name />
                    <span>{{ data.name }}</span>
                  </span>
                  <span v-else>{{ data.name }}</span>
                </span>
              </el-tree>
            </div>
          </el-tab-pane>
        </el-tabs>

      </div>
      <div class="f-right">
        <span class="user-num">已选择： {{ userlist.length }}个</span>
        <div class="user-list">
          <div v-for="(item,index) in userlist" :key="item.id" class="user-list_item">
            <span class="user-name">
              <vone-user-avatar :avatar-path="item.avatarPath" height="16px" width="16px" show-name />
              <span>{{ item.name }}</span>
            </span>
            <span class="user-del">
              <i class="el-icon-close" @click="delitem(index,item)" />
            </span>
          </div>
        </div>
      </div>
    </div>
    <footer>
      <el-button @click="close">取消</el-button>
      <el-button v-if="multiple" type="primary" @click="onSave">确定</el-button>
    </footer>
  </div>
</template>

<script>
import { debounce, cloneDeep, findIndex } from 'lodash'
import { getUserList } from '@/api/common'
import { getOrgUser } from '@/api/vone/base/user'
import { orgList } from '@/api/vone/base/org'
export default {
  name: 'RemoteUser',
  props: {
    type: {
      type: String,
      default: 'organization'
    },
    defaultData: {
      type: Array,
      default: () => []
    },
    multiple: {
      type: Boolean,
      default: false
    },
    selected: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      treeLoading: false,
      treeData: [],
      searchText: '',
      activeName: 'user',
      userlist: [],
      treeProps: {
        label: 'name',
        isLeaf: 'isLeaf'
      },
      selectData: []
    }
  },
  watch: {
    'selected': {
      handler: function(val, old) {
        if (val) {
          this.selectData = this.defaultData.map((e) => {
            if (this.selected.findIndex((item) => item.name == e.name) == -1) {
              e.disabled = false
            } else {
              e.disabled = true
            }
            return e
          })
        }
        if (this.treeData.length > 0) {
          this.gainTreeList(this.treeData)
        }
      },
      deep: true
    }
  },
  mounted() {
    this.getTreeData()
  },
  methods: {
    getTreeData() {
      getOrgUser().then((res) => {
        this.treeData = this.gainTreeList(res.data)
      })
    },
    gainTreeList(data, obj = []) {
      data.map(e => {
        if (e.type == 'USER' && this.selected.findIndex((item) => item.id == e.userId) !== -1) {
          e.disabled = true
        } else {
          e.disabled = false
        }
        if (e.children && e.children.length > 0) {
          this.gainTreeList(e.children)
        }
      })
      return data
    },
    tabChange() {
      this.searchText = ''
    },
    checkChange(e) {
      this.userlist = this.removeSomeData(e)
    },
    removeSomeData(data) {
      let resultData = []
      var obj = {}
      resultData = data.reduce((item, next) => {
        obj[next.id] ? '' : obj[next.id] = true && item.push(next)
        return item
      }, [])
      return resultData
    },
    remoteMethod: debounce(function(query) {
      if (query != '') {
        getUserList({ 'accountOrName': query }).then(res => {
          if (res.isSuccess) {
            this.selectData = this.removeSomeData(res.data).map((e) => {
              if (this.selected.findIndex((item) => item.id == e.id) == -1) {
                e.disabled = false
              } else {
                e.disabled = true
              }
              return e
            })
            // this.$emit('getUser', res.data)
          } else {
            this.$message.warning(res.msg)
          }
        })
      }
    }, 1000),
    treecheck(node, data) {
      const array = this.$refs.orgTree.getCheckedNodes().filter((e) => e.type == 'USER')
      this.userlist = this.removeSomeData(array)
    },
    close() {
      this.searchText = ''
      this.$emit('close')
    },
    onSave(e) {
      if (this.multiple) {
        const userArray = cloneDeep(this.userlist).map((e) => {
          return {
            id: e.userId || e.id,
            name: e.name
          }
        })
        this.$emit('checked', userArray)
      } else {
        this.$emit('checked', e)
      }
      this.searchText = ''
    },
    delitem(index, e) {
      this.userlist.splice(index, 1)
      if (this.activeName === 'org') {
        this.$refs.orgTree.setChecked(e.id)
      }
    },
    changeUser(data, node, el) {
      this.onSave(data)
    },
    filterNode(value, data) {
      if (!value) return true
      return data.name != null && data.name.indexOf(value) !== -1
    },
    searchTree() {
      if (this.activeName == 'user') {
        this.remoteMethod(this.searchText)
      } else {
        this.$refs.orgTree.filter(this.searchText)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.userbox {
  width: 100%;
  height: auto;
  .center {
    width: 100%;
    display: flex;
    .f-left {
      width: 50%;
      border-right: 1px solid #F0F0F0;
      header {
        padding: 15px 20px 0;
      }
      :deep(.el-tabs__header) {
        padding: 0 20px;
      }
      .checkbox-list {
        height: 36px;
        line-height: 36px;
        display: flex;
        align-items: center;
      }
    }
    .f-right {
      width: 50%;
      .user-num {
        height: 49px;
        line-height: 49px;
        padding: 0 20px;
      }
      .user-list {
        height: 300px;
        overflow-y: auto;
        padding: 0 20px;
      }
    }
    .user-list_item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 36px;
      line-height: 36px;
      cursor:pointer;
    }
  }
  .user-name {
    display: flex;
  }
  .user-del {
    cursor:pointer;
  }
  .treeContent {
    height: 250px;
    overflow-y: auto;
    padding: 0 20px;
  }
  footer {
    border-top: 1px solid #F0F0F0;
    width: 100%;
    padding: 16px 20px;
    text-align: right;
  }
}
:deep(.el-tabs__nav-wrap::after) {
  display: none;
}
</style>
