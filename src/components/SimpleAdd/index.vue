<template>
  <el-form ref="form" :class="['v-simple-add', { 'v-simple-add-no-shift': disabledShift }]" :rules="rules" :model="model" :auto-layout="false" label-width="0" @submit="save">
    <el-form-item prop="name" @keydown.native="lisEsc">
      <div class="input-layout">
        <div :class="{'v-simple-add-input': true, 'unfillBorder': showBorder}" :style="{ width: typeof inputWidth === 'number' ? `${inputWidth}px` : inputWidth }">
          <el-input ref="input" :value="value" :placeholder="placeholder" @input="v => $emit('input', v)" @keydown.native="inputKeyUp" />
        </div>
        <!-- 传入组件 -->
        <slot />

        <div class="con-btn">

          <el-button class="miniBtn " style="margin-left:8px" size="mini" @click="$emit('cancel')">取消</el-button>
          <el-button class="miniBtn" style="margin-left:8px" type="primary" size="mini" :loading="loading" @click.stop="save">
            确定
          </el-button>
        </div>

      </div>
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  props: {
    // 绑定表单数据
    model: {
      type: Object,
      default: () => {}
    },
    label: {
      type: String,
      default: ''
    },
    noFile: Boolean,
    hideFull: Boolean,
    disabledShift: Boolean,
    loading: Boolean,
    // 输入框绑定值
    value: {
      type: String,
      default: ''
    },
    fileList: {
      type: Array,
      default: () => []
    },
    inputWidth: {
      type: [Number, String],
      default: 240
    },
    rules: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      fileAni: false,
      // 创建标题未填时的边框样式
      showBorder: false
    }
  },
  computed: {
    placeholder() {
      return !this.disabledShift ? `${this.label || ''}（按回车快速创建）` : `${this.label || ''}`
    }
  },
  watch: {
    value(value) {
      if (value) {
        this.showBorder = false
      }
    }
  },
  mounted() {
    this.$refs.input.focus()
  },
  methods: {
    reset() {
      // this.$refs.form.reset()
    },
    open() {
      this.$emit('open')
      this.$emit('cancel')
    },
    inputKeyUp(e) {
      if (!this.disabledShift) {
        if (e.keyCode === 13 && e.shiftKey) {
          e.preventDefault()
          this.open()
        }
        if (e.keyCode === 13) {
          e.preventDefault()
          this.save()
        }
      }
    },
    lisEsc(e) {
      if (e.keyCode === 27) {
        this.$emit('cancel')
      }
    },
    async save() {
      try {
        await this.$refs.form.validate()
      } catch (error) {
        return
      }
      if (!this.value) {
        this.showBorder = true
        return
      } else {
        this.showBorder = false
      }
      this.$emit('submit')
    }
  }
}
</script>

<style lang="scss" scoped>
.v-simple-add {
  display: inline-block;
  height: 32px;
}
.v-simple-add-input {
  max-width: 280px;
}
.el-form-item {
  transition: all 0.3s cubic-bezier(0.645,0.045,0.355,1);
  &:not(.is-error) {
    margin-bottom: 0;
  }
}
.image-item {
  position: relative;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  transition: all 0.3s cubic-bezier(0.645,0.045,0.355,1);
  &:hover {
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
  }
}
:deep() {
  .el-input__inner {
    border: 0;
    background: none;
  }
}
.input-layout {
  background-color: #fafafa;
  border-radius: 4px;
  display: flex;
  align-items: center;
  // padding: 2px 8px;
}
.el-image {
  height: 100px;
}

.unfillBorder {
  border: 1px solid #e44135;
  border-radius: 4px;
}

.con-btn{
	height:32px;
	display: flex;
	background-color: #fff;
	align-items: center;
	.miniBtn {
  height: 28px;
  line-height: 28px;
  font-size: 12px;
  min-width: 56px;

}
}
</style>
