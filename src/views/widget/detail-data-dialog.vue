<template>
  <el-dialog title="数据详情" :visible.sync="visible" width="80%" :close-on-click-modal="false" :before-close="onClose">
    <vxe-table
      ref="filterCustomChart"
      class="vone-vxe-table"
      border
      auto-resize
      max-height="500px"
      :loading="loading"
      :empty-render="{ name: 'empty' }"
      :data="tableData"
      :column-config="{ minWidth:'150px' }"
      row-id="id"
    >
      <vxe-column title="编号" field="code" min-width="150" fixed="left">
        <template #default="{ row }">
          <a @click="jumpTask(row)">{{ row.code }}</a>
        </template>
      </vxe-column>
      <vxe-column title="标题" field="name" min-width="150" fixed="left">
        <template #default="{ row }">
          <a @click="jumpTask(row)">{{ row.name }}</a>
        </template>
      </vxe-column>
      <vxe-column title="工作项类型" field="classify" min-width="150">
        <template #default="{ row }">
          {{ typeNameObj(row).classifyType }}
        </template>
      </vxe-column>
      <vxe-column title="类型" field="type" min-width="150">
        <template #default="{ row }">
          {{ typeNameObj(row).typeName }}
        </template>
      </vxe-column>
      <vxe-column title="负责人" field="leadingBy" min-width="100">
        <template #default="{ row }">
          <vone-user-avatar :avatar-path="userInfo(row).avatarPath" :avatar-type="userInfo(row).avatarType" :name="userInfo(row).name" />
        </template>
      </vxe-column>
      <vxe-column title="状态" field="state" width="100">
        <template #default="{ row }">
          {{ stateName(row) }}
        </template>
      </vxe-column>
      <vxe-column field="projectId" show-overflow="tooltip" title="所属项目" min-width="180">
        <template #default="{ row }">
          {{ projectName(row) }}
        </template>
      </vxe-column>
    </vxe-table>
    <div slot="footer" style="height:25px">
      <vone-pagination ref="pagination" :total="tableData.total" @update="getInitTableData" />
    </div>
  </el-dialog>
</template>

<script>
import { apiInsightDetailItemInfo, apiProjectRoleDetail } from '@/api/vone/weidget/index'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    form: {
      type: Object,
      default: () => {}
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      loading: false,
      tableData: []

    }
  },
  computed: {
    typeNameObj() {
      return function(row) {
        const classifyType = row.echoMap?.typeCode?.classify?.desc || ''
        const typeName = row.echoMap?.typeCode?.name || ''
        return { classifyType, typeName }
      }
    },
    userInfo() {
      return function(row) {
        const avatarPath = row.echoMap?.leadingBy?.avatarPath || ''
        const avatarType = row.echoMap?.leadingBy?.avatarType || ''
        const name = row.echoMap?.leadingBy?.name || ''
        return { avatarPath, avatarType, name }
      }
    },
    stateName() {
      return function(row) {
        return row.echoMap?.stateCode?.name || ''
      }
    },
    projectName() {
      return function(row) {
        return row.echoMap?.projectId?.name || ''
      }
    }
  },
  mounted() {
    if (this.data.isRoleTask) {
      this.getData()
      return
    }
    this.getInfo()
  },
  methods: {
    async getInfo() {
      this.tableData = []
      this.loading = true
      const { data, isSuccess, msg } = await apiInsightDetailItemInfo({
        ...this.form,
        ...this.data
      })
      this.loading = false
      if (!isSuccess) {
        this.$message.error(msg)
        return
      }
      this.tableData = data
    },
    async getData() {
      this.tableData = []
      this.loading = true
      const { data, isSuccess, msg } = await apiProjectRoleDetail(
        this.form.projectId,
        this.data.xdata
      )
      this.loading = false
      if (!isSuccess) {
        this.$message.error(msg)
        return
      }
      this.tableData = data
    },
    async jumpTask(row) {
      const workItemType = row.echoMap?.typeCode?.classify?.code
      const projectKey = row.echoMap?.projectId?.code
      const projectTypeCode = row.echoMap?.projectId?.typeCode
      const projectId = row.echoMap?.projectId?.id
      const pathStr = workItemType == 'ISSUE' ? '/project/issue' : workItemType == 'TASK' ? '/project/task' : workItemType == 'BUG' ? '/project/defect' : '/project/issue'
      const newpage = await this.$router.resolve({ path: `${pathStr}/${projectKey}/${projectTypeCode}/${projectId}`, query: {
        showDialog: true,
        queryId: row?.id,
        rowTypeCode: row?.typeCode,
        stateCode: row?.stateCode,
        projectId: projectId
      }})
      const winOpen = window.open(newpage.href, '_blank')
      winOpen.opener = null
    },
    onClose() {
      this.$emit('update:visible', false)
      this.tableData = []
    }
  }

}
</script>

<style>

</style>
