<template>
  <div>
    <vone-empty v-if="storyList.length == 0" />
    <el-collapse v-model="activeNames" class="collapse" accordion>
      <el-collapse-item
        v-for="(item, index) in storyList"
        :key="item.id"
        :name="item.id"
        @click="handleChange(index, item)"
      >
        <template v-slot:title>
          <div class="collapse-title">
            <el-icon class="iconfont" style="margin-right: 19px"
              ><el-icon-icon-wendangkushuxiala
            /></el-icon>
            <span class="name">{{ item.name }}</span>
            <el-button
              v-if="item.id !== '0'"
              type="text"
              class="collapse-status"
              :class="{
                purple: item.stateId == '1',
                blue: item.stateId == '2',
                green: item.stateId == '3',
                orange: item.stateId == '4',
              }"
              ><span v-if="item.stateId == 1">规划中</span>
              <span v-if="item.stateId == 2">进行中</span>
              <span v-if="item.stateId == 3">发布成功</span>
              <span v-if="item.stateId == 4">发布失败</span>
            </el-button>
            <span v-if="item.planStime && item.planRtime">
              <el-button
                v-if="item.id !== '0'"
                type="text"
                class="collapse-time"
                >{{
                  item.planStime ? item.planStime.substring(0, 11) : "" || "-"
                }}
                至
                {{
                  item.planRtime ? item.planRtime.substring(0, 11) : ""
                }}</el-button
              >
            </span>

            <span @click.stop>
              <el-dropdown
                v-if="item.id !== '0'"
                trigger="click"
                placement="bottom"
                @visible-change="(v) => clickDrop(v, item, index)"
                @command="(cmd) => commandChange(cmd, item.id, index, item)"
              >
                <span
                  class="filter-group"
                  :class="{ active: activeFilter && activeFilter == index }"
                >
                  <el-icon class="iconfont" style="margin-right: 6px"
                    ><el-icon-icon-line-fenzu /></el-icon
                  >{{ collapseMap[item.id] || "分组" }}</span
                >
                <template v-slot:dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      v-for="item in dropList"
                      :key="item.key"
                      :command="item.id"
                      >{{ item.name }}</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </span>
          </div>
        </template>
        <CollapseItem ref="collapseItem" />
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
import {
  IconWendangkushuxiala as ElIconIconWendangkushuxiala,
  IconLineFenzu as ElIconIconLineFenzu,
} from "@element-plus/icons-vue";
import { getProductVersionList } from "@/api/vone/product";

import CollapseItem from "./collapseItem.vue";
export default {
  components: {
    CollapseItem,
    ElIconIconWendangkushuxiala,
    ElIconIconLineFenzu,
  },
  data() {
    return {
      dropValue: "",
      activeNames: null,
      priorityList: [],
      activeFilter: null,
      storyList: [],
      pageData: {
        // 分页配置
        model: {
          productId: this.$route.params.productId,
        },
      },
      dropList: [
        { id: "1", name: "全部", key: 1 },
        { id: "MODULE", name: "按模块", key: 2 },
        { id: "LEADING_BY", name: "按负责人", key: 3 },
        { id: "PRIORITY_CODE", name: "优先级", key: 4 },
        { id: "STATE_CODE", name: "状态", key: 5 },
      ],
      collapseMap: {},
    };
  },
  mounted() {
    this.getTableData();
    const filters = document.querySelectorAll(".filter-group");
    filters.forEach((ele) => {
      ele.onclick = function (e) {
        e.cancelBubble = true;
      };
    });
  },
  methods: {
    async getTableData() {
      this.pageLoading = true;
      const { data, isSuccess, msg } = await getProductVersionList({
        productId: this.$route.params.productId,
      });
      if (!isSuccess) {
        this.$message.warning(msg);
        return;
      }
      data.push({ name: "未规划", id: "0" });
      this.storyList = data;

      this.$nextTick(() => {
        this.handleChange(
          this.storyList.length - 1,
          this.storyList[this.storyList.length - 1]
        );
      });
    },
    handleChange(val, item) {
      this.$refs.collapseItem[val].getProductList(item);
    },
    // 下拉框出现/隐藏时触发
    clickDrop(visible, activeIndex, index) {
      this.activeFilter = visible ? "" : index;
    },
    // 点击菜单项触发的事件回调
    commandChange(cmd, id, index, item) {
      const params = this.dropList.filter((v) => v.id == cmd);
      this.collapseMap[id] = params[0].name;
      item["code"] = cmd == "1" ? "" : cmd;
      this.handleChange(index, item);
    },
  },
};
</script>

<style lang="scss" scoped>
.blue {
  color: #64befa;
  background: rgba(100, 190, 250, 0.12);
}
.p-item-purple {
  color: #bd7ffa;
  background: rgba(189, 127, 250, 0.12);
}
.p-item-green {
  color: #3cb540;
  background: rgba(60, 181, 64, 0.12);
}
.p-item-orange {
  color: #fc9772;
  background: rgba(252, 151, 114, 0.12);
}
.collapse {
  :deep(.el-collapse-item) {
    margin-bottom: 16px;
  }
  :deep(.el-collapse-item__header.is-active) {
    border-bottom: 1px solid #f2f3f5;
    border-bottom-color: #f2f3f5;
  }
}
.collapse-status {
  font-weight: 500;
  font-size: 14px;
  background: #ffeecf;
  color: #fa962b;
  height: 28px;
  padding: 0 8px;
  margin: 0 16px 0 16px;
  &.blue {
    color: #64befa;
    background: rgba(100, 190, 250, 0.12);
  }
  &.purple {
    color: #bd7ffa;
    background: rgba(189, 127, 250, 0.12);
  }
  &.green {
    color: #3cb540;
    background: rgba(60, 181, 64, 0.12);
  }
  &.orange {
    color: #fc9772;
    background: rgba(252, 151, 114, 0.12);
  }
}
:deep(.el-collapse-item__header) {
  border-radius: 4px;
  border: none;
}
.collapse-time {
  font-size: 14px;
  height: 28px;
  color: #838a99;
  margin-right: 16px;
  border-radius: 2px;
  background: #f2f3f5;
}
:deep(.collapse-title) {
  flex: 1 0 90%;
  order: 1;
  .name {
    font-size: 14px;
  }
  .el-collapse-item__header {
    flex: 1 0 auto;
    order: -1;
  }
}
:deep(.el-collapse-item__arrow) {
  display: none;
}
:deep(.el-collapse-item__wrap) {
  overflow-y: auto;
}
:deep(.el-collapse-item__content) {
  padding: 0px 16px;
}
.active {
  transform: rotate(90deg);
}
.filter-group {
  font-weight: 400;
  padding: 7px 12px;
  border-radius: 2px;
  &:hover {
    background-color: #f2f3f5;
  }
  &.active {
    border: 1px solid #3e7bfa;
    background-color: #fff;
  }
}
</style>

<style lang="scss">
.el-collapse-item__header:hover {
  background-color: #fff;
}
.custom-theme-dark {
  .story-card {
    border: 1px solid #333947;
  }
  .collapse-time {
    background: #252933;
  }

  :deep(.el-collapse-item__header:hover) {
    background-color: #333947;
  }
  :deep(.collapse > .el-collapse-item__header.is-active) {
    border-bottom-color: 1px solid #333947;
  }
  .filter-group:hover {
    background-color: #333947;
  }
}
</style>
