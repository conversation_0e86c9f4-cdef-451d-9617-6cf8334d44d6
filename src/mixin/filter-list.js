import { getFilterList } from '@/api/vone/dashboard'
// 查询所有筛选器

const filterListMixin = {
  data() {
    return {
      filterList: []
    }
  },

  mounted() {
    this.getList()
  },
  methods: {
    async getList() {
      const res = await getFilterList({
        table: 'work_item_entity'
      })

      if (!res.isSuccess) {
        console.warn(res.msg)
        return
      }
      this.filterList = res.data
    }
  }
}

export default filterListMixin
