import request from '@/utils/axios-api'

// 租户查询
export function apiBaseTenantList(data) {
  return request({
    url: '/api/base/base/function/page',
    method: 'post',
    data
  })
}

// 租户删除
export function apiBaseTenantDel(data) {
  return request({
    url: '/api/base/base/tenant',
    method: 'DELETE',
    data
  })
}

// 租户信息-详情
export function apiBaseTenantInfo(data) {
  return request({
    url: '/api/base/base/tenant',
    method: 'post',
    data
  })
}

/**
 * 保存租户信息
 * @param {*} data
 */
export function apiBaseTenantSave(data) {
  return request({
    url: `/api/base/base/tenant`,
    method: data.id ? 'put' : 'post',
    data
  })
}
