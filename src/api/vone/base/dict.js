import request from '@/utils/axios-api'

// 字典删除
export function apiBaseDictDelDic(data) {
  return request({
    url: '/api/base/base/dictionary',
    method: 'DELETE',
    data
  })
}

// 查询app
export function apiBaseDictPage(data) {
  return request({
    url: '/api/base/base/dictionary/page',
    method: 'post',
    data
  })
}

// 字典新增
export function apiBaseDictAddDict(data) {
  return request({
    url: '/api/base/base/dictionary',
    method: 'post',
    data
  })
}

// 字典--根据字典ID回显详情
export function apiBaseDictGetInfoById(id) {
  return request({
    url: `/api/base/base/dictionary/${id}`,
    method: 'GET'
  })
}

// 字典修改
export function apiBaseDictEdit(data) {
  return request({
    url: '/api/base/base/dictionary',
    method: 'PUT',
    data
  })
}

// 字典无分页查询
export function apiBaseDictNoPage(data) {
  return request({
    url: '/api/base/base/dictionary/query',
    method: 'POST',
    data
  })
}

// 字典--根据字典ID回显详情
export function apiBaseDictExtend(dictionaryType) {
  return request({
    url: `/api/base/cmdb/dictionaryExtendProperties/${dictionaryType}`,
    method: 'GET'
  })
}
// 字典--选择代码库引擎，出现流程配置下拉选择框
export function actReProcdef(data) {
  return request({
    url: `/api/camunda/actReProcdef/query`,
    method: 'post',
    data
  })
}
// 查询所有字典类型
export function apiBaseAllDict() {
  return request({
    url: `/api/base/base/dictionary/findAllDictionaryType`,
    method: 'GET'
  })
}

// 修改字典状态
export function apiBaseDictUpdateStatus(id, state) {
  return request({
    url: `/api/base/base/dictionary/updateStateById/${id}/${state}`,
    method: 'PUT'
  })
}

