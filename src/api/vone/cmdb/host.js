// import request from '@/utils/request'
import request from '@/utils/axios-api'
import requestUtils from '@/utils/requestUtils'

// 查询服务器连接信息
export function getSelectAccessHostInfo() {
  return request({
    url: '/api-cmdb/api/cmdb/host/selectAccessHostInfo',
    method: 'get'
  })
}

// 按操作系统类型查询虚拟机分组数量
export function getVmHostOsAmountGroup() {
  return request({
    url: '/api-cmdb/api/cmdb/host/vm/selectVmHostOsAmountGroup',
    method: 'get'
  })
}

// 按环境分组查询虚拟机数量
export function getVmHostEnvAmountGroup() {
  return request({
    url: '/api-cmdb/api/cmdb/host/vm/selectVmHostEnvAmountGroup',
    method: 'get'
  })
}

// 按状态分查询虚拟机组数量
export function getVmHostHealthAmountGroup() {
  return request({
    url: '/api-cmdb/api/cmdb/host/vm/selectVmHostHealthAmountGroup',
    method: 'get'
  })
}

// 按系统版本查询虚机数量
export function getVmHostOsreleaseAmountGroup() {
  return request({
    url: '/api-cmdb/api/cmdb/host/vm/selectVmHostOsreleaseAmountGroup',
    method: 'get'
  })
}

// 查询我的虚机数量
export function getHostCountByAccendantId() {
  return request({
    url: '/api-cmdb/api/cmdb/host/vm/selectHostCountByAccendantId',
    method: 'get'
  })
}

// 查询部门虚拟机数量
export function getHostCountByOrgId() {
  return request({
    url: '/api-cmdb/api/cmdb/host/vm/selectHostCountByOrgId',
    method: 'get'
  })
}

/**
 * 根据id查询服务器信息
 */
export function apiCmdbGetHostById(id) {
  return request({
    url: `/api/cmdb/cmdb/host/${id}`,
    method: 'get'
  })
}

export function apiCmdbApplicationSelectByHostId(hostId) {
  return request({
    url: '/api-cmdb/api/cmdb/applicationBase/selectByHostId/' + hostId,
    method: 'get'
  })
}

export function apiCmdbScriptSelectByHostId(hostId) {
  return request({
    url: '/api-cmdb/api/cmdb/script/selectByHostId/' + hostId,
    method: 'get'
  })
}

export async function apiCmdbAppCompentsSelectByHostId(hostId) {
  return request({
    url: '/api-cmdb/api/cmdb/app/selectByHostId/' + hostId,
    method: 'get'
  })
}

export async function apiCmdbDbCompentsSelectByHostId(hostId) {
  return request({
    url: '/api-cmdb/api/cmdb/dbcompents/selectByHostId/' + hostId,
    method: 'get'
  })
}

// 根据ID获取高级属性---服务器
export function apiBaseGetLevel(params) {
  return request({
    url: '/api-cmdb/api/cmdb/host/selectControlByModuleAndDictName',
    method: 'get',
    params
  })
}

/**
 * 根据条件查询服务器列表信息
 * @param {*} params
 */
export function apiCmdbHostSeletByParams(data) {
  return request({
    url: `/api/cmdb/cmdb/host/page`,
    method: 'POST',
    data
  })
}

// 根据ID获取数据库授权信息
export function apiBaseGetHostOrg(hostId) {
  return request({
    url: `/api/cmdb/cmdb/hostOrg/${hostId}`,
    method: 'get'
  })
}

/**
 * 服务器--权限分配---保存
 */
export async function apiBaseHostGrant(data) {
  return request({
    url: `/api/cmdb/cmdb/hostOrg`,
    method: 'put',
    data
  })
}

/**
 * 删除服务器信息
 * @param {*} ids
 */
export function apiCmdbHostDeleteByIds(data) {
  return request({
    url: `/api/cmdb/cmdb/host`,
    method: 'delete',
    data
  })
}

/**
 * 获取服务器os
 */
export function apiCmdbHostSelectOs() {
  return request({
    url: `/api-cmdb/api/cmdb/host/getHostOs`,
    method: 'get'
  })
}

/**
 * 手动刷新服务器状态
 * @param {*} ids
 */
export function apiCmdbHostRefreshByIds(data) {
  return request({
    url: `/api/cmdb/cmdb/host/refresh`,
    method: 'put',
    data
  })
}

/**
 * 纳管服务器
 * @param {*} ids
 */
export function apiCmdbHostAcceptByIds(data) {
  return request({
    url: `/api/cmdb/cmdb/host/accept`,
    method: 'put',
    data
  })
}

/**
 * 下线服务器
 * @param {*} ids
 */
export function apiCmdbHostOffLineByIds(data) {
  return request({
    url: `/api/cmdb/cmdb/host/offline`,
    method: 'put',
    data
  })
}

export async function apiBaseDictSelectByParentKey(parentKey) {
  return request(`/api-base/api/base/system/dict/parentKey/${parentKey}`)
}

/**
 * 查询服务器高级属性模版信息
 * @param {*} params
 */
export function apiCmdbHostSelectAdvanceTemplate(params) {
  return request({
    url: '/api/cmdb/cmdb/hostExtendProperties',
    method: 'get',
    params
  })
}

/**
 * 手动添加服务器信息
 * @param {*} data
 */
export function apiCmdbHostAdd(data) {
  return request({
    url: `/api/cmdb/cmdb/host`,
    method: data.id ? 'put' : 'post',
    data
  })
}

/**
 * 修改服务器信息
 * @param {*} data
 */
export function apiCmdbHostUpdate(data) {
  return request({
    url: `/api-cmdb/api/cmdb/host`,
    method: 'put',
    data
  })
}

/**
 * 查询连接终端账号
 */
export async function apiCmdbGetHostUserJudgeAuth(hostId) {
  return request(`/api-cmdb/api/cmdb/hostUser/judgeAuth/${hostId}`)
}

/**
 * 保存轮巡策略
 * @param {*} data
 */
export function apiCmdbHostMonitorSave(data) {
  return request({
    url: `/api/cmdb/cmdb/host/updatePatrolStrategy`,
    method: 'PUT',
    data
  })
}

/**
 * 导出服务
 * @param {*} data
 */
export async function apiCmdbHostExport(data) {
  return request({
    url: `/api-cmdb/api/cmdb/host/exportHostExcel`,
    method: 'post',
    data: data,
    transformResponse: [requestUtils.download({ fileName: '服务器信息.xls' })],
    responseType: 'blob'
  })
}

/**
 * 查询导出服务器字段
 * @param {*} params
 */
export function apiCmdbHostColumns(params) {
  return request({
    url: '/api-cmdb/api/cmdb/host/getHostColumns',
    method: 'get',
    params
  })
}

/**
 * 获取要同步的服务器信息
 * @param {*} params
 */
export function apiCmdbHostSelectAllSyncHostKey(data) {
  return request({
    url: '/api/cmdb/cmdb/host/findHostMinionKey',
    method: 'post',
    data
  })
}

/**
 * 同步服务器信息
 * @param {*} engineId
 * @param {*} id
 */
export function apiCmdbHostSyncHostById(engineId, minionKey) {
  return request({
    url: `/api/cmdb/cmdb/host/sync/${engineId}/${minionKey}`,
    method: 'put'
  })
}

/**
 * 根据服务器ID查询轮巡策略
 * @param {*} id
 */
export function apiCmdbHostMonitorSelectByHostId(hostId) {
  return request({
    url: `/api-cmdb/api/cmdb/hostMonitor/${hostId}`,
    method: 'get'
  })
}

/**
 * 查询所有服务器，不分页
 * @param {*} id
 */
export function apiCmdbHostNoPage() {
  return request({
    url: `/api/cmdb/cmdb/host/query`,
    method: 'POST'
  })
}

/**
 * CMDB-服务器详情,根据服务器IP查询该主机上下发的脚本
 */
export async function apiPiplineHostIpScript(data) {
  return request({
    url: `/api/pipeline/pipeline/pipelineScriptSend/selectByHostIp/page`,
    method: 'post',
    data: data
  })
}
