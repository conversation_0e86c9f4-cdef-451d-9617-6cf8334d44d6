// import { is, getBusinessObject } from "bpmn-js/lib/util/ModelUtil";
import { omit } from 'lodash'
// import { getAllNodes } from "./ModelUtil";

export function jsonToXml({ nodes, edges }) {
  const process = []
  const diagram = []
  nodes.forEach(node => {
    // 保留需要的属性，排除布局相关属性
    const excludeKeys = ['label', 'x', 'y', 'width', 'height', 'color']
    const attrs = omit(node, excludeKeys)
    const attrList = []
    Object.keys(attrs).forEach(key => {
      if (attrs[key] !== null && attrs[key] !== undefined) {
        attrList.push(`${key}="${attrs[key]}"`)
      }
    })
    process.push(`<j:Base ${attrList.join(' ')} key="${node.stateCode}" >  `)
    const stroke = node.color ? `bioc:stroke="${node.color}"` : ''
    diagram.push(`<bpmndi:BPMNShape id="${node.id}_di" bpmnElement="${node.id}" ${stroke}>`)
    diagram.push(`<dc:Bounds x="${node.x}" y="${node.y}" width="${node.width || 100}" height="${node.height || 36}" />`)
    diagram.push(`</bpmndi:BPMNShape>`)
    edges.forEach(edge => {
      if (edge.source === node.id) {
        process.push(`<bpmn:outgoing>${edge.id}</bpmn:outgoing>`)
      } else if (edge.target === node.id) {
        process.push(`<bpmn:incoming>${edge.id}</bpmn:incoming>`)
      }
    })
    process.push(`</j:Base>`)
  })
  edges.forEach(edge => {
    process.push(`<bpmn:sequenceFlow id="${edge.id}" onlyId="${edge.onlyId}" permission="${edge.permission}" ${edge.name ? `name="${edge.name}"` : ''} sourceRef="${edge.source}" targetRef="${edge.target}" />`)
    diagram.push(`<bpmndi:BPMNEdge id="${edge.id}_di" bpmnElement="${edge.id}">`)
    if (edge.waypoints) {
      const waypoints = JSON.parse(edge.waypoints)
      waypoints.forEach(w => {
        diagram.push(`<di:waypoint x="${w.x}" y="${w.y}" />`)
      })
    }
    diagram.push(`</bpmndi:BPMNEdge>`)
  })
  return `
    <?xml version="1.0" encoding="UTF-8"?>
    <bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:activiti="http://activiti.org/bpmn" xmlns:A1Category="http://a1bpm.category/schema/bpmn/ExtCategory/1.0">
      <bpmn:process id="Process_1ecksy2">
      ${process.join('\n')}
      </bpmn:process>
      <bpmndi:BPMNDiagram id="sid-74620812-92c4-44e5-949c-aa47393d3830">
        <bpmndi:BPMNPlane id="sid-cdcae759-2af7-4a6d-bd02-53f3352a731d" bpmnElement="Process_1ecksy2">
        ${diagram.join('\n')}
        </bpmndi:BPMNPlane>
      </bpmndi:BPMNDiagram>
    </bpmn:definitions>
    `
}
