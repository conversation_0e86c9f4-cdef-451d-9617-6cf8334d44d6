<template>
  <el-dialog class="dialogContainer" title="分享筛选器" v-model="visible" width="456px" :close-on-click-modal="false" :close-on-press-escape="false" @close="close">
    <!-- 表单部分 -->
    <el-form ref="sharedForm" :model="sharedForm" :rules="rules" label-position="top">
      <el-form-item label="用户" prop="userIds">
        <vone-remote-user v-model="sharedForm.userIds" multiple />
      </el-form-item>
    </el-form>
    <!-- 表单结束，按钮操作部分 -->
    <span slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="loading" @click="sharedFilterUser">确定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { sharedFilter } from '@/api/vone/dashboard/filter'
export default {
  props: {
    visible: <PERSON><PERSON><PERSON>,
    rowData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      sharedForm: {
        userIds: []
      },
      rules: {
        userIds: [{ required: true, message: '请选择要分享的用户', trigger: 'change' }]
      },
      loading: false
    }
  },
  mounted() {
  },
  methods: {
    close() {
      this.$emit('update:visible', false)
      this.$emit('update:rowData', null)
    },
    async sharedFilterUser() {
      try {
        await this.$refs.sharedForm.validate()
      } catch (e) {
        return
      }
      this.sharedForm.viewIds = [this.rowData.id]
      this.loading = true
      const res = await sharedFilter(this.sharedForm)
      this.loading = false
      if (res.isSuccess) {
        this.$message.success('分享成功')
        this.close()
      } else {
        this.$message.warning(res.msg)
      }
    }
  }
}
</script>
