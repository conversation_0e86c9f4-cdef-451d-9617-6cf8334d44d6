<template>
  <div ref="lane" class="handlebox">
    <el-card v-if="$slots.search || $slots.actions" class="toolbar">
      <el-row class="toolbar" type="flex" align="middle">
        <el-col :span="14">
          <slot name="search" />
        </el-col>
        <el-col :span="10" style="text-align: right">
          <slot name="actions" />
          <el-button icon="el-icon-full-screen" plain @click="full">{{ fullText }}</el-button>
        </el-col>
      </el-row>
    </el-card>
    <div class="vone-lane-scroll">
      <slot />
    </div>
  </div>
</template>
<script>
export default {
  props: {
    sort: {
      type: Boolean,
      default: true
    },
    dragstart: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      group: new Date().valueOf(),
      disablePut: true,
      dragging: false,
      dragCtx: undefined,
      colDragStates: [],
      activeCard: {},
      activeId: '',
      activeType: '',
      activeDragKey: '',
      fullText: '全屏'
    }
  },
  provide() {
    return {
      lane: this
    }
  },
  mounted() {
    if (document.fullscreenElement) {
      this.fullText = '退出'
    }
    window.onresize = () => {
      if (document.fullscreen) {
        this.fullText = '退出'
      } else {
        this.fullText = '全屏'
      }
    }
  },
  destroyed() {
    window.onresize = null
  },
  methods: {
    async onDragStart(context) {
      if (!this.dragstart) {
        this.disablePut = false
        this.colDragStates = {}
        return
      }
      this.disablePut = true
      this.colDragStates = await this.dragstart(context)
      this.disablePut = false
    },
    full(full) {
      if (!full || document.fullscreen) {
        if (document.fullscreen) {
          document.exitFullscreen()
          this.fullText = '全屏'
        }
      } else {
        const ele = document.querySelector('.handlebox')
        ele.requestFullscreen()
        this.fullText = '退出'
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@use "@/styles/variables.scss" as *;
.handlebox {
  background: var(--main-bg-color,#fff);
  border-radius: 6px;
  :deep(.el-card.is-always-shadow) {
    box-shadow: none;
    border-radius: 6px 0;
  }
  :deep(.el-card__body) {
    padding: 0px 16px;
    border-radius: 0;
  }
  .toolbar {
    height: 48px;
  }
}
.vone-lane-scroll {
  display: flex;
  border-radius: 0 6px;
}
.toolbar {
  :deep() {
    .el-button--text {
      padding-left: 15px;
      padding-right: 15px;
    }
    .el-input--mini .el-input__icon {
      color: var(--font-second-color);
    }
    .el-date-editor {
      width: 110px;
      cursor: pointer;
      .el-input__inner {
        padding-right: 0;
        cursor: pointer;
        border: 0;
        font-size: 14px;
        color: var(--font-second-color);
        font-weight: 500;
        &::-webkit-input-placeholder {
          color: var(--color-text-regular);
          transition: var(--all-transition);
        }
        &::-moz-placeholder {
          transition: var(--all-transition);
          color: var(--color-text-regular);
        }
      }
    }
  }
}
</style>
