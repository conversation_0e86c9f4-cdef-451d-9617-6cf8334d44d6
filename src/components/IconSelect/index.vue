<template>
  <el-select
    ref="vone-icon-select"
    v-bind="$attrs"
    class="vone-icon-select"
    popper-class="vone-icon-select"
    :disabled="noPermission && selectType == 'priority'"
   
    @change="(val) => watchInput(val)"
  >
    <!-- noName 回显时不显示头像，用于工作项编辑时 -->
    <div v-if="selectIconVal && !noName" slot="prefix" class="select-header">
      <i
        v-if="selectType == 'priority'"
        :class="`iconfont ${selectIconVal.icon}`"
        :style="{color: selectIconVal.color, fontSize: '16px'}"
      />
      <vone-user-avatar
        v-else-if="selectIconVal.avatarPath"
        :avatar-path="selectIconVal.avatarPath"
        :avatar-type="selectIconVal.avatarType"
        :show-name="false"
        height="22px"
        width="22px"
      />
      <vone-user-avatar
        v-else
        :avatar-path="selectIconVal.echoMap.userId.avatarPath"
        :avatar-type="selectIconVal.echoMap.userId.avatarType"
        :show-name="false"
        height="22px"
        width="22px"
      />
    </div>
    <slot />
  </el-select>
</template>
<script>
export default {
  name: 'VoneIconSelect',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    selectType: {
      type: String,
      default: 'priority'
    },
    noPermission: {
      type: Boolean,
      default: false
    },
    noName: {
      type: Boolean,
      default: false
    }
    // multiple: {
    //   type: Boolean,
    //   default: false
    // }
  },
  data() {
    return {
      selectIconVal: null
    }
  },
  watch: {
    '$attrs.value': {
      handler: function(val, old) {
        if (val != old) {
          this.watchInput(val)
        }
      },
      deep: true,
      immediate: true
    },
    'data': {
      handler: function(val, old) {
        if (val.length > 0 && val != old) {
          this.watchInput(this.$attrs.value)
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
  },
  methods: {
    watchInput(val) {
      if (val && this.data.length > 0) {
        if (this.selectType == 'priority') {
          this.selectIconVal = this.data.find(item => item.code == val)
        } else {
          this.selectIconVal = this.data.find(item => item.userId == val || item.id == val)
        }
      } else {
        this.selectIconVal = null
      }
    }
  }
}
</script>
<style lang="scss" scoped>
	.vone-select {
		padding-right: 10px;
	}
	:deep(.select-header) {
		min-width: 28px;
		height: 100%;
		display: flex;
		align-items: center;
		text-align: center;
		font-size: 14px;
		color: #4983F3;
	}
</style>
<style lang="scss">
	.vone-icon-select {
		.el-select-dropdown__wrap{
			.el-select-dropdown__item{
				display: flex;
        align-items: center;
				.avatar{
					padding-right: 6px;
				}
			}
		}
	}
</style>
