<template>
  <el-dialog
    :title="title"
    :model-value="spaceVisible"
    :close-on-click-modal="false"
    :before-close="close"
  >
    <el-form
      ref="userForm"
      :model="userForm"
      :rules="rules"
      label-width="100px"
      class="demo-ruleForm"
    >
      <el-form-item label="项目角色" prop="roleIds">
        <el-select
          v-model="userForm.roleIds"
          placeholder="请选择项目角色"
          multiple
          filterable
        >
          <el-option
            v-for="item in groupData"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="(title = '新增用户')" label="用户" prop="userIds">
        <vone-remote-user v-model:value="userForm.userIds" multiple />
      </el-form-item>
      <el-form-item
        v-else-if="(title = '编辑用户')"
        label="用户"
        prop="userIds"
      >
        <vone-remote-user v-model:value="userForm.userIds" />
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <div class="dialog-footer">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="save"
          >确定</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import _ from 'lodash'
export default {
  props: {
    spaceVisible: {
      type: Boolean,
      default: false,
    },
    currentSpaceInfo: {
      type: Object,
      default: () => {},
    },
    title: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      spaceForm: {
        name: '',
        typeId: '',
        description: '',
        icon: '',
      },
    }
  },
  mounted() {
    this.searchSpaceType()
    if (Object.keys(this.currentSpaceInfo).length > 0) {
      this.spaceForm = _.cloneDeep(this.currentSpaceInfo)
    }
  },
  methods: {
    save() {},
    close() {
      $emit(this, 'update:spaceVisible', false)
      this.$refs['spaceForm'].resetFields()
    },
  },
  emits: ['update:spaceVisible'],
}
</script>
