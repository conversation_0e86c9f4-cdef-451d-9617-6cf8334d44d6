<template>
  <div>
    <el-card class="milepost_card">
      <template v-slot:header>
        <div class="card">
          <el-row type="flex" class="header" justify="space-between">
            <div class="title">里程碑</div>
            <div class="iteration_btn">
              <div class="btn-group-style">
                <el-button
                  type="primary"
                  :icon="elIconTipsPlusCircle"
                  :disabled="!$permission('project_milestone_add')"
                  @click="addMilestone"
                  >里程碑</el-button
                >
              </div>
            </div>
          </el-row>
        </div>
      </template>
      <milepost :list="milestoneList" @editMilepostChange="editPlan" />

      <div class="subtitle">里程碑时间轴</div>
      <el-table
        size="medium"
        :data="milestoneList"
        height="calc(100vh - 425px)"
      >
        <el-table-column prop="name" label="里程碑名称" />
        <el-table-column prop="status" label="状态">
          <template v-slot="scope">
            <div
              class="status"
              :style="`color: ${
                stateColorMap[scope.row.stateCode].color
              };background:${stateColorMap[scope.row.stateCode].bgcolor}`"
            >
              {{ stateColorMap[scope.row.stateCode].lable }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="planEtime" label="完成日期">
          <template v-slot="scope">
            {{ format_filter(scope.row.planEtime) }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="交付物">
          <template v-slot="scope">
            {{
              scope.row.echoMap.deliverables
                ? scope.row.echoMap.deliverables.delivered +
                  "/" +
                  scope.row.echoMap.deliverables.total
                : "--"
            }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="120">
          <template v-slot="scope">
            <el-button
              :icon="elIconApplicationEdit"
              type="text"
              size="small"
              :disabled="!$permission('project_milestone_edit')"
              @click="editPlan(scope.row.id)"
            />
            <el-button
              :icon="elIconApplicationDelete"
              type="text"
              size="small"
              :disabled="!$permission('project_milestone_del')"
              @click="removeDel(scope.row)"
            />
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!-- 新增里程碑 -->
    <add-milepost
      v-bind="addPlanParam"
      v-if="addPlanParam.visible"
      v-model:value="addPlanParam.visible"
      @success="getTestPlanList"
    />
    <edit-milepost
      v-bind="editPlanParam"
      v-if="editPlanParam.visible"
      v-model:value="editPlanParam.visible"
      @success="getTestPlanList"
    />
  </div>
</template>

<script>
const stateColorMap = {
  1: { color: "#8F90A6", bgcolor: "#E4E4EB", lable: "未开始" }, // 未开始
  2: { color: "#2D6EF7", bgcolor: "#F0F7FF", lable: "进行中" }, // 进行中
  3: { color: "#2CC750", bgcolor: "#F0FFF1", lable: "已完成" }, // 已完成
  4: { color: "#f56c6c", bgcolor: "#fab7b7", lable: "已归档" },
};
import * as dayjs from "dayjs";

import {
  apiAlmProjectPlanNoPage,
  apiAlmProjectPlanDel,
  apiAlmProjectPlanAdd,
} from "@/api/vone/project/iteration";

import addMilepost from "./components/add-milepost.vue";
import editMilepost from "./components/edit-milepost.vue";
import milepost from "./components/milepost.vue";
export default {
  components: {
    milepost,
    addMilepost,
    editMilepost,
  },
  data() {
    return {
      stateColorMap,
      saveLoading: false,
      addPlanParam: { visible: false },
      editPlanParam: { visible: false },
      treeData: [],
      listLoading: false,
      currentNodekey: "",
      milestoneList: [],
    };
  },
  provide() {
    return {
      treeData: this.treeData,
    };
  },
  created() {
    this.getTestPlanList();
  },
  methods: {
    format_filter(val) {
      if (!val) return "";
      return dayjs(val).format("YYYY-MM-DD");
    },
    editMilepostChange(e) {
      this.editPlanParam.visible = true;
    },
    // 查询里程碑计划
    async getTestPlanList() {
      this.listLoading = true;
      const res = await apiAlmProjectPlanNoPage({
        projectId: this.$route.params.id,
        type: "MILESTONE",
      });
      this.listLoading = false;
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      this.milestoneList = res.data;
    },
    // 新增里程碑
    addMilestone() {
      this.addPlanParam = { visible: true, hasParent: false };
    },
    // 编辑里程碑
    editPlan(id) {
      this.editPlanParam = { visible: true, id: id };
    },
    // 删除里程碑
    async removeDel(item) {
      await this.$confirm(`确定删除【${item.name}】吗?`, "删除", {
        type: "warning",
        closeOnClickModal: false,
      })
        .then(async (actions) => {
          this.loading = true;
          const { isSuccess, msg } = await apiAlmProjectPlanDel([item.id]);
          if (!isSuccess) {
            this.loading = false;
            this.$message.error(msg);
            return;
          }
          this.$message.success("删除成功");
          this.getTestPlanList();
        })
        .catch(() => {});
    },
    // 修改计划状态
    async changeStatus(item, key) {
      item["stateCode"] = key;
      const { isSuccess, msg } = await apiAlmProjectPlanAdd({
        ...item,
      });
      if (!isSuccess) {
        this.$message.warning(msg);
        this.saveLoading = false;
        return;
      }
      this.saveLoading = false;
      this.$message.success("修改里程碑状态成功");
    },
    // 刷新列表
    getTableData() {
      this.$refs.planTable.getPlanCaseList(this.currentNodekey);
    },
  },
};
</script>

<style lang="scss" scoped>
.header {
  line-height: 32px;
  .title {
    font-weight: 600;
    font-size: 16px;
  }
}
.subtitle {
  border-top: 1px solid var(--disabled-bg-color);
  font-weight: 600;
  font-size: 14px;
  padding: 16px 20px;
  margin: 0 -20px;
}
:deep(th.el-table__cell.is-leaf) {
  height: 48px;
  background-color: var(--bottom-bg-color);
}
.status {
  width: 66px;
  padding: 0px 12px;
  height: 22px;
  line-height: 22px;
  border-radius: 2px;
}
.el-table {
  .el-button {
    min-width: 0;
    padding: 0;
  }
}
.milepost_card {
  :deep(.el-card__body) {
    height: calc(100vh - 148px);
    padding: 16px;
  }
}
</style>
