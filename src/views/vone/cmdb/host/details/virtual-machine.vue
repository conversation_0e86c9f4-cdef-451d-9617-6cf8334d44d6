<template>
  <el-col :span="8">
    <el-card class="virtual_machine" style="min-height: 300px">
      <template v-slot:header>
        <div>
          <span>虚拟机占比</span>
          <el-select
            v-model="value"
            placeholder="请选择"
            class="header_sele"
            @change="change"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </template>
      <el-row>
        <vone-empty v-if="noData" :style="{ height: '200px' }" />
        <vone-echarts :height="'200px'" :options="option" />
      </el-row>
    </el-card>
  </el-col>
</template>

<script>
import {
  getVmHostOsAmountGroup,
  getVmHostEnvAmountGroup,
  getVmHostHealthAmountGroup,
  getVmHostOsreleaseAmountGroup,
} from '@/api/vone/cmdb/host'
export default {
  data() {
    return {
      value: '状态',
      options: [
        { value: '状态', label: '状态' },
        { value: '系统版本', label: '系统版本' },
        { value: '类型', label: '类型' },
        { value: '环境', label: '环境' },
      ],
      option: {},
      noData: false,
      data: [],
    }
  },
  created() {
    this.getList()
  },
  methods: {
    change(val) {
      this.value = val
      this.getList()
    },
    async getList() {
      this.option = {}
      let legendData = []
      let seriesData = []
      if (this.value == '类型') {
        const { data, success, message } = await getVmHostOsAmountGroup()
        if (!success) {
          this.$message.warning(message)
          return
        }
        if (!data || data.length == 0) {
          this.noData = true
          return
        }
        for (let i = 0; i < data.length; i++) {
          legendData.push(data[i].os)
          seriesData.push({ value: data[i].amount, name: data[i].os })
        }
      } else if (this.value == '环境') {
        const { data, success, message } = await getVmHostEnvAmountGroup()
        if (!success) {
          this.$message.warning(message)
          return
        }
        if (!data || data.length == 0) {
          this.noData = true
          return
        }
        for (let i = 0; i < data.length; i++) {
          legendData.push(data[i].envName)
          seriesData.push({ value: data[i].amount, name: data[i].envName })
        }
      } else if (this.value == '状态') {
        const { data, success, message } = await getVmHostHealthAmountGroup({
          projectGroupKey: this.projectGroupKey,
        })
        if (!success) {
          this.$message.warning(message)
          return
        }
        if (!data || data.length == 0) {
          this.noData = true
          return
        }
        const healthName = {
          1: '异常',
          0: '正常',
          '-1': '未知',
        }
        legendData = ['异常', '正常']
        seriesData = [
          { value: 0, name: '异常', color: 'red' },
          { value: 0, name: '正常', color: 'green' },
        ]
        for (let i = 0; i < data.length; i++) {
          for (let j = 0; j < seriesData.length; j++) {
            if (healthName[data[i].health] == seriesData[j].name) {
              seriesData[j].value = data[i].amount
              break
            }
          }
        }
      } else {
        const { data, success, message } = await getVmHostOsreleaseAmountGroup({
          projectGroupKey: this.projectGroupKey,
        })
        if (!data || data.length == 0) {
          this.noData = true
          return
        }
        if (!success) {
          this.$message.warning(message)
          return
        }
        for (let i = 0; i < data.length; i++) {
          legendData.push(data[i].osrelease)
          seriesData.push({ value: data[i].amount, name: data[i].osrelease })
        }
      }

      const colorlist = ['#4daaff', '#67C23A', '#E6A23C', '#FA5555']
      this.option = {
        color: colorlist,
        tooltip: {
          trigger: 'item',
          axisPointer: {
            type: 'shadow',
          },
          backgroundColor: '#fff',
          borderColor: 'none',
          extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);', // 附加阴影样式
          textStyle: {
            color: '#53565C',
          },
        },
        grid: [
          {
            top: 0,
            left: 0,
            bottom: 0,
          },
        ],
        legend: {
          type: 'scroll',
          orient: 'vertical',
          right: 0,
          top: 'center',
          itemWidth: 5,
          itemHeight: 5,
          data: legendData,
          textStyle: {
            fontWeight: 'normal',
            fontSize: 12,
            color: '#8A8F99',
          },
        },
        series: [
          {
            name: '虚机数量',
            type: 'pie',
            radius: ['20%', '60%'],
            center: ['40%', '50%'],
            avoidLabelOverlap: false,
            data: seriesData,
            label: {
              normal: {
                textStyle: {
                  fontWeight: 'normal',
                  fontSize: 10,
                },
              },
            },
          },
        ],
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.virtual_machine {
  .el-select {
    width: 100px;
  }
  .el-card {
    padding: 12px 16px;
    text-align: center;
  }
}
</style>
