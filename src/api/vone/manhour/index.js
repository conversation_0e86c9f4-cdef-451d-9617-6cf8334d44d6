import request from '@/utils/axios-api'
// 查询工时记录列表分页
export function getWorkingHoursInfopage(data) {
  return request({
    url: '/api/alm/alm/workingHoursInfo/page',
    method: 'post',
    data
  })
}
// 查询工作项下的工时记录
export function getWorkingHoursInfo(bizId, type) {
  return request({
    url: `/api/alm/alm/workingHoursInfo/${type}/${bizId}`,
    method: 'get'
  })
}
// 工作项下新增工时记录
export function addWorkingHoursInfo(data) {
  return request({
    url: '/api/alm/alm/workingHoursInfo',
    method: 'post',
    data
  })
}
// 删除工时记录
export function deleteWorkingHoursInfo(data) {
  return request({
    url: '/api/alm/alm/workingHoursInfo',
    method: 'DELETE',
    data
  })
}
// 工作项搜索
export function workItemSearch(data) {
  return request({
    url: '/api/alm/alm/detection/work_item/search',
    method: 'post',
    data
  })
}
// 批量添加工时
export function addWorkingHoursInfoBatch(data) {
  return request({
    url: '/api/alm/alm/workingHoursInfo/batch',
    method: 'post',
    data
  })
}
// 批量查询工时
export function geWorkingHoursInfoQuery(data) {
  return request({
    url: '/api/alm/alm/workingHoursInfo/query',
    method: 'post',
    data
  })
}
// 工时审批
export function workingHoursConfirm(data) {
  return request({
    url: '/api/alm/alm/workingHoursConfirm',
    method: 'post',
    data
  })
}
// 批量审批
export function bathConfirm(data) {
  return request({
    url: '/api/alm/alm/workingHoursConfirm/bathConfirm',
    method: 'post',
    data
  })
}
// 查询配置
export function getWorkingHoursConfig(data) {
  return request({
    url: '/api/alm/alm/workingHoursConf/query',
    method: 'post',
    data
  })
}
// 修改配置
export function setWorkingHoursConfig(data) {
  return request({
    url: '/api/alm/alm/workingHoursConf',
    method: 'put',
    data
  })
}
// 批量修改配置
export function batchSetWorkingHoursConfig(data) {
  return request({
    url: '/api/alm/alm/workingHoursConf/batchUpdate',
    method: 'put',
    data
  })
}
// 批量修改是否计费
export function batchSetbilled(data) {
  return request({
    url: '/api/alm/alm/workingHoursInfo/billed',
    method: 'put',
    data
  })
}
// 统计视图-填报人
export function getStatisticsOfFilledBy(data) {
  return request({
    url: '/api/alm/alm/workingHoursInfo/statistics/filledBy',
    method: 'get',
    data
  })
}
// 统计视图-项目
export function getStatisticsOfProject(data) {
  return request({
    url: '/api/alm/alm/workingHoursInfo/statistics/project',
    method: 'get',
    data
  })
}
// 统计视图-项目
export function getStatisticsOfType(data) {
  return request({
    url: '/api/alm/alm/workingHoursInfo/statistics/type',
    method: 'get',
    data
  })
}
// 查询分组
export async function getGroup(data) {
  return request({
    url: `/api/alm/alm/workingHoursInfo/group`,
    method: 'post',
    data
  })
}
// 分组查询工时
export function getGroupWorkingHoursInfo(groupBy, data) {
  return request({
    url: '/api/alm/alm/workingHoursInfo/' + groupBy,
    method: 'post',
    data
  })
}

// 查询工时分摊信息
export function getAllocationSum(data) {
  return request({
    url: `/api/alm/workingHoursAllocation/query`,
    method: 'POST',
    data
  })
}

// 分摊工时
export function getWorkingHoursBatchSave(data) {
  return request({
    url: '/api/alm/workingHoursAllocation/batchSave',
    method: 'post',
    data
  })
}

// 查询分摊工时
export function getWHoursAllocation(data) {
  return request({
    url: '/api/alm/workingHoursAllocation/page',
    method: 'post',
    data
  })
}

// 审批分摊工时
export function getWHoursAllocationConfirm(data) {
  return request({
    url: '/api/alm/alm/workingHoursConfirm/bathConfirmAllocation',
    method: 'post',
    data
  })
}

// 项目下查询每天填写了多少工时
export function getWorkingHoursDay(startDate, endDate, userIds, projectId) {
  return request({
    url: `/api/alm/alm/workingHoursInfo/history?startDate=${startDate}&endDate=${endDate}&userIds=${userIds}&projectId=${projectId}`,
    method: 'GET'
  })
}

// 快捷入口查询全部
export function getWorkingHoursDayAll(startDate, endDate, userIds) {
  return request({
    url: `/api/alm/alm/workingHoursInfo/history?startDate=${startDate}&endDate=${endDate}&userIds=${userIds}`,
    method: 'GET'
  })
}
// 日志
export function getWHoursNewPage(data) {
  return request({
    url: '/api/alm/alm/workingHoursInfo/newPage',
    method: 'post',
    data
  })
}

// 项目下一键确认
export function batchConfirm(data) {
  return request({
    url: '/api/alm/alm/workingHoursInfo/allConfirm',
    method: 'post',
    data
  })
}

// 工时管理一键确认
export function manHourConfirm(data) {
  return request({
    url: '/api/alm/workingHoursAllocation/allConfirm',
    method: 'post',
    data
  })
}

