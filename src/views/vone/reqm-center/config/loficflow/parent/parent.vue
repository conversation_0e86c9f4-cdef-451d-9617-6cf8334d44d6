<template>
  <transition>
    <div :class="['parent-node', form.originNode ? 'requirement' : '']">
      <div class="lane-card-title">
        <i
          :style="{
            color: form.echoMap.typeCode && form.echoMap.typeCode.color,
          }"
          :class="[
            'iconfont',
            `${form.echoMap.typeCode && form.echoMap.typeCode.icon}`,
          ]"
        />
        <el-tooltip
          :show-after="800"
          class="item"
          :content="form.name"
          placement="top-start"
        >
          <div class="lane-card-title-text" @click.stop.prevent="click(form)">
            {{ form.name }}
          </div>
        </el-tooltip>
      </div>
      <div class="lane-card-code">
        <div v-if="form.delay" class="delay">延期</div>
        <div class="lane-card-code-text">
          计划完成时间：{{ dayjs(form.planEtime) || '--' }}
        </div>
      </div>
      <div class="lane-card-code">
        <div class="lane-card-code-text">
          {{ form.code }}
        </div>
        <template>
          <div class="lane-card-code-types">
            规模:{{ form.estimatePoint ? form.estimatePoint : '-' }}
          </div>
        </template>
        <div class="lane-card-title-priorityCode">
          <i
            :style="{
              color:
                form.echoMap.priorityCode && form.echoMap.priorityCode.color,
            }"
            :class="[
              'iconfont',
              `${form.echoMap.priorityCode && form.echoMap.priorityCode.icon}`,
            ]"
          />
        </div>
      </div>

      <div class="lane-card-user">
        <div class="lane-card-user-main">
          <vone-user-avatar
            height="20px"
            width="20px"
            :avatar-path="
              form.echoMap.handleBy && form.echoMap.handleBy.avatarPath
            "
            :name="form.echoMap.handleBy && form.echoMap.handleBy.name"
          />
        </div>

        <div
          class="lane-card-code-tag"
          :style="{
            color: form.echoMap.stateCode.color,
            borderColor: form.echoMap.stateCode.color,
          }"
        >
          {{ form.echoMap.stateCode.name }}
        </div>

        <!-- </div> -->
      </div>
    </div></transition
  >
</template>

<script>
import dayjs from 'dayjs'
export default {
  props: {
    form: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {}
  },
  mounted() {},

  methods: {
    // click() {

    // },

    dayjs(date) {
      if (date != null) {
        return dayjs(date).format('YYYY-MM-DD')
      } else {
        return null
      }
    },
    async click(row) {
      const projectKey = row.echoMap?.projectId?.code
      const projectTypeCode = row.echoMap?.projectId?.typeCode
      const projectId = row.echoMap?.projectId?.id
      const pathStr = '/project/issue'

      const newpage = await this.$router.resolve({
        path: `${pathStr}/${projectKey}/${projectTypeCode}/${projectId}`,
        query: {
          showDialog: true,
          queryId: row?.id,
          rowTypeCode: row?.typeCode,
          stateCode: row?.stateCode,
          projectId: projectId,
        },
      })

      const winOpen = window.open(newpage.href, '_blank')
      winOpen.opener = null
    },
  },
}
</script>

<style lang="scss" scoped>
.parent-node {
  box-sizing: border-box;
  width: 220px;
  background-color: #fff;
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.08);
  border-radius: 4px;
  padding: 12px;
  border: 1px solid #f0f0f0;
  display: flex;
  flex-direction: column;
}
.lane-card-title {
  line-height: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  i {
    font-size: 20px;
    margin-right: 8px;
  }
  &- {
    &text {
      font-size: 14px;
      width: 180px;
      cursor: pointer;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    // &priorityCode {}
  }
}
.lane-card-code,
.lane-card-user {
  display: flex;
  font-size: 12px;
  align-items: center;
  height: 20px;
  margin: 3px 0;
  gap: 0 8px;
  &- {
    &text {
      display: flex;
      align-items: center;
      padding: 0 4px;
      color: #838a99;
      border-radius: 2px;
      background-color: #f2f3f5;
    }
    &types {
      padding: 0 4px;
      color: #838a99;
      border-radius: 2px;
      background-color: #f2f3f5;
    }
    &main {
      width: 120px;
    }
    &time {
      width: calc(100% - 120px);
      text-align: right;
    }
    &tag {
      height: 18px;
      line-height: 16px;
      padding: 0 6px;
      border-radius: 2px;
      border: 1px solid;
    }
  }
}
.delay {
  display: flex;
  align-items: center;
  padding: 0px 6px;
  width: 36px;
  height: 18px;
  color: #ed3e3e;
  background: #ffe9e6;
  border-radius: 2px;
}
.lane-card-user {
  justify-content: space-between;
  .lane-card-user-main {
    display: flex;
    align-items: center;
  }
  .flag {
    display: none;
    color: #bcc2cb;
    margin-left: 9px;
    cursor: pointer;
  }
  .flag-del {
    color: #f27900;
    margin-left: 9px;
    cursor: pointer;
  }
}
.tooltip-text {
  max-width: 250px;
}
.requirement {
  border: 2px solid #3e7bfa;
}
</style>
