<template>
  <!-- 需求关联用户需求- -->
  <vone-div-wrapper :title="tabName">
    <template v-slot:actions>
      <div v-if="$route.name != 'reqm-center-require-list'">
        <el-button
          type="text"
          class="action-btn con-idea"
          :icon="elIconEditRelate"
          @click="addRelation = true"
          >关联</el-button
        >
      </div>
    </template>
    <div class="boxContent">
      <div v-if="addRelation">
        <div class="row_host">
          <el-row
            :gutter="12"
            type="flex"
            justify="space-between"
            style="align-items: center"
          >
            <el-col>
              <el-select
                v-model.trim="issueTab.relationIssue"
                placeholder="请输入用户需求名称"
                clearable
                filterable
                remote
                :remote-method="getIdeaList"
                :loading="requireLoading"
                class="requireSelect"
                multiple
                style="margin-right: 12px"
                @focus="setOptionWidth"
              >
                <el-option
                  v-for="ele in ideaList"
                  :key="ele.id"
                  :value="ele.id"
                  :label="ele.name"
                  :disabled="ele.disabled"
                  :style="{ width: selectOptionWidth }"
                  :title="ele.name"
                >
                  {{ `${ele.code}   ${ele.name}` }}
                </el-option>
              </el-select>
            </el-col>
          </el-row>
          <div style="text-align: right">
            <el-button class="miniBtn" @click="addRelation = !addRelation"
              >取消</el-button
            >
            <el-button type="primary" class="miniBtn" @click="addRelationShip"
              >确定</el-button
            >
          </div>
        </div>
      </div>

      <el-collapse
        v-if="tableData.length != 0"
        v-model="activeNames"
        v-loading="pageLoading"
      >
        <!-- 关联/------------------- -->
        <el-collapse-item name="RELATION">
          <template v-slot:title>
            <el-icon class="iconfont colorOrange"
              ><el-icon-yibiaoban-shuxingyanse
            /></el-icon>
            <span class="centerText"> 关联 </span>
            <span class="centerNum">
              {{ tableData.length }}
            </span>
          </template>

          <el-table
            v-show="tableData.length != 0"
            ref="tableData"
            class="vone-table voneNobg-table"
            :data="tableData"
          >
            <template>
              <el-table-column
                prop="code"
                label="用户需求"
                show-overflow-tooltip
                min-width="250"
                class-name="name_col"
              >
                <template v-slot="scope">
                  <a
                    v-if="
                      scope.row.ideaId &&
                      scope.row.echoMap &&
                      scope.row.echoMap.ideaId
                    "
                    class="table_title"
                    target="_blank"
                    @click="toRouter(scope.row)"
                  >
                    <svg class="icon svg-icon" aria-hidden="true">
                      <use xlink:href="#el-icon-icon-yixiang" />
                    </svg>

                    {{ scope.row.echoMap.ideaId.code }}
                    {{ scope.row.echoMap.ideaId.name }}
                  </a>
                </template>
              </el-table-column>

              <!-- <el-table-column prop="projectId" label="项目" show-overflow-tooltip min-width="70">
                    <template slot-scope="scope">
                      <el-tag v-if="projectId != scope.row.projectId" effect="plain" type="info">
                        外部
                      </el-tag>
                    </template>
                  </el-table-column> -->

              <el-table-column
                prop="handleBy"
                label="处理人"
                show-overflow-tooltip
                min-width="110"
              >
                <template v-slot="scope">
                  <span>
                    <vone-user-avatar
                      :avatar-path="
                        getUserInfo(scope.row)
                          ? getUserInfo(scope.row).avatarPath
                          : ''
                      "
                      :name="
                        getUserInfo(scope.row)
                          ? getUserInfo(scope.row).name
                          : ''
                      "
                    />
                  </span>
                </template>
              </el-table-column>
              <el-table-column
                prop="stateCode"
                label="状态"
                show-overflow-tooltip
                min-width="110"
              >
                <template v-slot="scope">
                  <span
                    v-if="
                      scope.row.echoMap.ideaId.stateCode &&
                      scope.row.echoMap.ideaId.echoMap &&
                      scope.row.echoMap.ideaId.echoMap.stateCode
                    "
                  >
                    <el-tag type="success">
                      {{ scope.row.echoMap.ideaId.echoMap.stateCode.name }}
                    </el-tag>
                  </span>
                </template>
              </el-table-column>
              <el-table-column
                prop="expectedTime"
                label="计划完成时间"
                show-overflow-tooltip
                min-width="165"
              >
                <template v-slot="scope">
                  {{ scope.row.echoMap.ideaId.expectedTime || "未设置" }}
                </template>
              </el-table-column>
              <el-table-column
                v-if="$route.name != 'reqm-center-require-list'"
                label="操作"
                width="50"
              >
                <template v-slot="scope">
                  <el-icon class="iconfont i-text"
                    ><el-icon-icon-line-jiechuguanlian
                  /></el-icon>
                </template>
              </el-table-column>
            </template>
          </el-table>
          <vone-empty v-show="tableData.length == 0" />
        </el-collapse-item>
      </el-collapse>
      <div v-else class="empty-container">
        <svg-icon icon-class="zanwu" class-name="empty-svg" />
        暂无关联内容
      </div>
    </div>
  </vone-div-wrapper>
</template>

<script>
import {
  ideaListByCondition,
  issueIdeaProductRequirement,
  issueIdeaProductRequirementDel,
} from "@/api/vone/reqmcenter/idea";

import { issueToIdeaQuery } from "@/api/vone/reqmcenter/require";

import { debounce } from "lodash";

export default {
  name: "IssueToIdea",
  components: {},

  props: {
    issueId: {
      type: String,
      default: undefined,
    },
    assistantSystem: {
      type: String,
      default: null,
    },
    doneTime: {
      type: Number,
      default: null,
    },
    noEdit: {
      type: Boolean,
      default: false,
    },
    isNotLink: {
      type: Boolean,
      default: false,
    },
    projectKey: {
      type: Array,
      default: () => [],
    },
    projectId: {
      type: String,
      default: null,
    },
    tabName: {
      type: String,
      default: null,
    },
    typeCode: {
      type: String,
      default: null,
    },
    issueInfo: {
      // 需求详情,看是否有关联的意向
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      pageLoading: false,
      tableData: [],
      activeNames: "RELATION",
      issueTab: {
        relationIssue: [],
      },
      addRelation: false,
      tableLoading: false,

      loading: false,
      selectOptionWidth: "",
      requireLoading: false,
      ideaList: [],
      form: {},
    };
  },
  computed: {
    getUserInfo() {
      return function (row) {
        return row?.handleBy && row?.echoMap?.handleBy;
      };
    },
  },
  watch: {
    issueId() {
      this.getRelationTable();
    },
    immediate: true,
  },

  mounted() {
    this.getRelationTable();
  },
  methods: {
    toRouter(val) {
      if (!this.$permission("reqm_center_idea_view")) {
        this.$message.warning(
          "当前登录账户没有查看用户需求的权限,请联系管理员授权"
        );
        return;
      }
      // this.$router.push({
      //   name: 'reqm_center_idea_view',
      //   query: {
      //     queryId: val?.echoMap?.ideaId?.id
      //   }
      // })
      const newpage = this.$router.resolve({
        path: `/reqmcenter/idea`,
        query: {
          showDialog: true,
          queryId: val?.echoMap?.ideaId?.id,
          rowTypeCode: val?.echoMap?.ideaId?.typeCode,
          stateCode: val?.echoMap?.ideaId?.stateCode,
          projectId: val?.echoMap?.ideaId?.projectId,
        },
      });
      window.open(newpage.href, "_blank");
    },

    // 下拉框弹出时，设置弹框的宽度
    setOptionWidth(event) {
      this.$nextTick(() => {
        this.selectOptionWidth =
          event.target.parentNode.parentNode.clientWidth + "px";
      });
    },

    // 查意向列表
    getIdeaList: debounce(async function (query, projectId) {
      try {
        this.requireLoading = true;
        const res = await ideaListByCondition({ name: query });
        this.requireLoading = false;
        if (!res.isSuccess) {
          this.$message.warning(res.msg);
          return;
        }

        res.data.forEach((element) => {
          element.disabled = element.id == this.issueId;
        });

        this.ideaList = res.data;
      } catch (e) {
        this.requireLoading = false;
      }
    }, 1000),

    // 查询关系
    async getRelationTable() {
      if (!this.issueId) {
        return;
      }
      this.pageLoading = true;
      const res = await issueToIdeaQuery({ requirementId: this.issueId });
      this.pageLoading = false;
      if (!res.isSuccess) {
        return;
      }

      this.tableData = res.data;
    },

    // 添加关系
    async addRelationShip() {
      if (!this.issueTab.relationIssue.length) {
        this.$message.warning("请选择需求");
        return;
      }

      const { isSuccess, msg } = await issueIdeaProductRequirement({
        ideaIds: this.issueTab.relationIssue,
        requirementId: this.issueId,
      });
      if (!isSuccess) {
        this.$message.warning(msg);
        return;
      }
      this.$message.success(msg);
      this.getRelationTable();
      this.issueTab["relationIssue"] = [];
      this.addRelation = false;
    },

    // 需求解除与用户需求的关系
    async deleteRelation(row) {
      this.$confirm(
        `你确定要取消关联 【${row.echoMap.ideaId.name}】的关联关系吗?`,
        "取消关联",
        {
          confirmButtonText: "确认",
          type: "warning",
          closeOnClickModal: false,
        }
      )
        .then(async () => {
          this.issueInfo["ideaId"] = null;
          const { isSuccess, msg } = await issueIdeaProductRequirementDel({
            ideaId: row.ideaId,
            requirementId: this.issueId,
          });
          if (!isSuccess) {
            this.$message.error(msg);
            return;
          }
          this.$message.success(msg);
          this.getRelationTable();
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
:deep(.el-dialog .el-dialog__body .el-form-item--small[class~="is-required"]) {
  margin-bottom: 0px !important;
}
:deep(.el-dialog .el-dialog__body .el-form-item) {
  margin-bottom: 0px !important;
}
.i-text {
  color: var(--main-theme-color);
  cursor: pointer;
}
:deep(.el-button + .el-button) {
  margin: 12px 0px 12px 12px;
}
.con-idea {
  font-weight: 400;
  font-size: 14px;
}
.boxContent {
  :deep(.el-collapse-item__content) {
    padding: 0;
  }
  .headerTotal {
    background: var(--disabled-bg-color);
    border-radius: 50%;
    padding: 0 5px;
    color: var(--main-font-color);
  }
  .centerText {
    font-size: 14px;
    font-weight: 400;
    margin: 0 5px;
  }
  .centerNum {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 20px;
    height: 15px;
    background: #ebeef5;
    border-radius: 14px;
    color: var(--main-font-color);
    font-size: 14px;
    font-weight: 400;
  }
  .boxTitle {
    font-weight: 600;
    color: var(--auxiliary-font-color);
  }
}
.mt-10 {
  margin-top: 10px;
}
.colorOrange {
  color: #ffd591;
}
.colorGreen {
  color: #b7eb8f;
}
.colorPerple {
  color: #adc6ff;
}
.formBox {
  padding: 10px; /*// height: 300px;*/ /*// overflow-y: auto;*/ /*// overflow-x: hidden;*/
  :deep(.el-form-item__label) {
    color: var(--auxiliary-font-color);
  }
  :deep(.el-input--small) {
    font-size: 14px;
  }

  :deep(.el-input.is-disabled .el-input__inner) {
    color: #000 !important;
    font-size: 14px;
    margin-left: 10px;
  }
  .contentText {
    font-weight: bold;
    font-size: 16px;
  }
}
.shadow_bottom {
  margin: 0 -38px 16px; /*// padding: 0 26px 16px;*/
  padding-bottom: 16px;
  box-shadow: 0px 15px 10px -15px #ccc;
  .row_host {
    padding: 0px 32px 0px 32px;
    .el-row {
      .el-col {
        padding-right: 12px !important;
      }
    }
  }
}
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 0 8px; /*// height: 100%;*/
  height: calc(60vh - 158px);
  .empty-svg {
    width: 50px;
    height: 40px;
  }
}
:deep(.name_col) {
  .cell {
    display: flex;
    div.name_icon {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: normal;
      word-break: break-all;
      flex: 1;
      display: flex;
      align-items: center;
    }
  }
}
.table_title {
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
:deep(.el-button) {
  line-height: 24px;
  height: 24px;
}
:deep(.el-collapse-item__header) {
  height: 40px;
  padding: 0px 10px;
}
:deep(.el-collapse-item) {
  border: 1rem solid var(--solid-border-color);
  box-shadow: var(--main-bg-shadow);
  margin-bottom: 10px;
}
</style>
