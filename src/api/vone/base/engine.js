import request from '@/utils/axios-api'

/**
 * @desc 查询
 */
export function apiBaseEngineList(data) {
  return request({
    url: '/api/tm-base/base/engine/page',
    method: 'POST',
    data
  })
}

// 引擎新增保存
export function apiBaseEngineAdd(data) {
  return request({
    url: '/api/tm-base/base/engine',
    method: data.id ? 'put' : 'post',
    data
  })
}

// 引擎删除
export function apiBaseEngineDelById(data) {
  return request({
    url: '/api/tm-base/base/engine',
    method: 'DELETE',
    data
  })
}

// // 引擎修改
// export function apiBaseEngineEdit(data) {
//   return request({
//     url: '/api/engine/engine',
//     method: 'PUT',
//     data
//   })
// }

// 根据ID查询引擎详情
export function apiBaseEngineInfoGetById(id) {
  return request({
    url: `/api/tm-base/base/engine/${id}`,
    method: 'GET'
  })
}

// 引擎扩展属性
export function apiBaseEngineExtendInstance(engineInstance) {
  return request({
    url: `/api/tm-base/cmdb/engineExtendProperties/${engineInstance}`,
    method: 'GET'
  })
}

// 字典修改
export function apiBaseEngineTestConnect(data) {
  return request({
    url: '/api/tm-base/base/engine/testConnect',
    method: 'PUT',
    data
  })
}

/**
 * @desc 查询引擎
 */
export function apiBaseEngineNoPage(data) {
  return request({
    url: '/api/tm-base/base/engine/query',
    method: 'POST',
    data
  })
}
// 批量设置巡检策略
export function updatePatrolStrategy(data) {
  return request({
    url: '/api/tm-base/base/engine/updatePatrolStrategy',
    method: 'put',
    data
  })
}

// 引擎授权信息
export function apiBaseEngineAuth(engineId) {
  return request({
    url: `/api/tm-base/base/engineOrg/${engineId}`,
    method: 'GET'
  })
}

// 修改引擎授权信息
export function updateBaseEngineOrg(data) {
  return request({
    url: '/api/tm-base/base/engineOrg',
    method: 'put',
    data
  })
}
// 代码库设置
export function getCodeUserTokenByLoginUser() {
  return request({
    url: `/api/codem/codeUserToken/getCodeUserTokenByLoginUser`,
    method: 'GET'
  })
}

// 动态节点列表
export function getEngineJenkinsCloudList(data) {
  return request({
    url: '/api/tm-base/engineJenkinsCloud/page',
    method: 'POST',
    data
  })
}
// 批量
export function getEngineJenkinsCloudListNoPage(data) {
  return request({
    url: '/api/tm-base/engineJenkinsCloud/query',
    method: 'POST',
    data
  })
}

// 新增动态节点
export function addEngineJenkinsCloudList(data) {
  return request({
    url: '/api/tm-base/engineJenkinsCloud',
    method: 'POST',
    data
  })
}
// 删除动态节点
export function delEngineJenkinsCloudList(data) {
  return request({
    url: '/api/tm-base/engineJenkinsCloud',
    method: 'DELETE',
    data
  })
}
//  修改动态节点
export function editEngineJenkinsCloudList(data) {
  return request({
    url: '/api/tm-base/engineJenkinsCloud',
    method: 'PUT',
    data
  })
}
