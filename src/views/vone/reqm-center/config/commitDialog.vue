<template>
  <div>
    <el-dialog
      :title="title"
      :append-to-body="true"
      width="35%"
      v-model:value="visible"
      :before-close="onClose"
      :close-on-click-modal="false"
    >
      1111111
    </el-dialog>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../utils/gogocodeTransfer'
export default {
  name: 'CommitDialog',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '',
    },
  },
  data() {
    return {}
  },
  mounted() {},
  methods: {
    onClose() {
      $emit(this, 'update:visible', false)
      // this.$refs.worktimeForm.resetFields()
    },
  },
  emits: ['update:visible'],
}
</script>
