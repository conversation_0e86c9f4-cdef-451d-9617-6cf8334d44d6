<template>
  <el-dialog
    title="复制用例"
    :model-value="visible"
    :close-on-click-modal="false"
    width="600px"
    :close-on-press-escape="false"
    @close="close"
  >
    <!-- 表单部分 -->
    <el-form ref="caseForm" :rules="rules" :model="caseForm">
      <el-form-item label="复制到哪个用例树下" prop="treeId">
        <vone-tree-select
          v-model:value="caseForm.treeId"
          append-to-body
          :z-index="9999"
          search-nested
          :tree-data="caseTree"
          placeholder="请选择用例树"
          :value-consists-of="'ALL_WITH_INDETERMINATE'"
        />
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <div>
        <el-button @click="close">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="onSave"
          >确定</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script>
import {
  $on,
  $off,
  $once,
  $emit,
} from '../../../../../../utils/gogocodeTransfer'
import { list2Tree } from '@/utils/list2Tree'
import { gainTreeList } from '@/utils'
import { productTestCaseCopy } from '@/api/vone/testmanage/case'
import { getPlanRelaseCaseTree } from '@/api/vone/testmanage/plan'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    caseId: {
      type: String,
      default: '',
    },
    libraryId: {
      type: String,
      default: '',
    },
    caseList: {
      type: Array,
      default: () => [],
    },
    menuNode: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      rules: {
        treeId: [
          {
            required: true,
            message: '请选择用例树节点',
            trigger: 'blur',
          },
        ],
      },
      caseForm: {},
      caseTree: [],
      saveLoading: false,
    }
  },
  created() {
    this.getAllCaseTree()
  },
  methods: {
    async getAllCaseTree() {
      const res = await getPlanRelaseCaseTree(
        this.$route.params.caseId || this.libraryId
      )
      if (!res.isSuccess) {
        this.$message.success(res.msg)
      }
      const data = []
      Array.isArray(res.data) &&
        res.data.forEach((item) => {
          if (item.parentId !== '0') {
            data.push(item)
          }
        })
      const tree = list2Tree(data, { parentKey: 'parentId' })
      this.caseTree = gainTreeList(tree)
    },
    async onSave() {
      try {
        await this.$refs.caseForm.validate()
      } catch (e) {
        return
      }
      try {
        this.saveLoading = true
        const res = await productTestCaseCopy({
          caseId: this.caseList.length ? this.caseList : [this.caseId],
          treeId: this.caseForm.treeId,
        })
        this.saveLoading = false

        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.$message.success('复制用例成功')
        $emit(this, 'success')
        this.close()
      } catch (e) {
        this.saveLoading = false
      }
    },
    close() {
      $emit(this, 'update:visible', false)
    },
  },
  emits: ['update:visible', 'success'],
}
</script>

<style lang="scss" scoped>
:deep(
    .vue-treeselect--open-below:not(.vue-treeselect--append-to-body)
      .vue-treeselect__menu-container
  ) {
  top: 66px;
}
</style>
