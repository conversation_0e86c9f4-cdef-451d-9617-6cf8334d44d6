<template>
  <div>
    <RiskCommon
      :placeholder="'请输入需求搜索'"
      :classify="'ISSUE'"
      :tab-name="tabName"
      :issue-id="issueId"
      @toRouter="toRouter"
    />
  </div>
</template>

<script>
import RiskCommon from './risk-common.vue'
export default {
  name: 'RiskToIssue',
  components: {
    RiskCommon,
  },
  props: {
    tabName: {
      type: String,
      default: null,
    },
    issueId: {
      type: String,
      default: null,
    },
  },
  data() {
    return {}
  },
  methods: {
    toRouter(val) {
      const { projectKey, id, projectTypeCode } = this.$route.params
      const newpage = this.$router.resolve({
        path: `/project/issue/${projectKey}/${projectTypeCode}/${id}`,
        query: {
          showDialog: true,
          queryId: val?.echoMap?.bizId?.id,
          rowTypeCode: val?.echoMap?.bizId?.typeCode,
          stateCode: val?.echoMap?.bizId?.stateCode,
          projectId: val?.echoMap?.bizId?.projectId,
        },
      })

      window.open(newpage.href, '_blank')
    },
  },
}
</script>
