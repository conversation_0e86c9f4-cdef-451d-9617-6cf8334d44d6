<template>
  <div>
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic
          ref="searchForm"
          table-search-key="common_filter_table"
          :model="formData"
          :table-ref="tableRef"
          :hide-filter-form="true"
          :disable-init-request="true"
          :show-columns-data="showColumnsData"
          :show-column-sort="true"
          @getTableData="getTableData"
        />
      </template>

    </vone-search-wrapper>
    <vxe-table
      ref="common_filter_table"
      class="vone-vxe-table"
      border
      auto-resize
      max-height="500px"
      :loading="loading"
      :empty-render="{ name: 'empty' }"
      :data="tableData.records"
      :column-config="{ minWidth:'150px',resizable: true }"
      show-overflow="tooltip"
      row-id="id"
    >
      <vxe-column v-for="(item) in tableProps" :key="item.id" :title="item.name" :field="item.key">
        <template #default="{ row }">
          <a v-if="item.key === 'code' || item.key === 'name'" @click="jumpTask(row,item)">{{ row[item.key] }}</a>
          <span v-else-if="item.key === 'leadingBy' || item.key === 'handleBy'">
            <vone-user-avatar :avatar-path="userInfo(row,item.key).avatarPath" :avatar-type="userInfo(row,item.key).avatarType" :name="userInfo(row,item.key).name" />
          </span>

          <!-- 标签 -->
          <span v-else-if="item.key == 'tagId'">
            <!-- {{ getTag(row).tags }} -->
            <el-tooltip v-if="getTag(row).tags.length > 1" :content="getTag(row).tags.join(',')" placement="top">
              <span v-for="(i,index) in getTag(row).tags.slice(0,1)" :key="index">
                <span>
                  <el-tag type="success" class="ml-10">{{ i }}</el-tag>
                </span>

                &nbsp; &nbsp;
                <el-tag v-if="getTag(row).tags.length > 1" type="success">{{ ` + ${ getTag(row).tags.length - 1}` }}</el-tag>
              </span>
            </el-tooltip>

            <span v-else>
              <span v-for="(j,index) in getTag(row).tags" :key="index">
                <span>
                  <el-tag type="success" class="ml-10">{{ j }}</el-tag>
                </span>
              </span>
            </span>
          </span>
          <span v-else-if="codeMapList.includes(item.key)">
            <span v-if="row.echoMap && row.echoMap[item.key]">
              {{ row.echoMap[item.key].name }}
            </span>

          </span>
          <span v-else-if="timeMap.includes(item.key) && row[item.key]">

            {{ dayjs(row[item.key]).format("YYYY-MM-DD") }}
          </span>

          <!-- <span v-else-if="item.key === 'tagId' ">
            <span v-for="(item,index) in row.tag" :key="index">
              <el-tag style="margin-right:6px" type="success">
                222
              </el-tag>
            </span>
          </span> -->
          <span v-else>
            {{ row[item.key] }}
          </span>
        </template>
      </vxe-column>

    </vxe-table>
    <vone-pagination ref="pagination" :total="tableData.total" @update="getTableData" />
  </div>
</template>

<script>
import { queryFilterConditions } from '@/api/vone/dashboard/filter'

const codeMapList = ['typeCode', 'stateCode', 'projectId', 'sourceCode', 'priorityCode', 'planId', 'productVersionId', 'productModuleFunctionId', 'productRepairVersionId', 'productId']
const timeMap = ['createTime', 'planEtime', 'planStime']

export default {
  props: {
    tableData: {
      type: Object,
      default: () => {}
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tableProps: [],
      formData: {},
      tableRef: null,
      codeMapList,
      timeMap,
      showColumnsData: []
    }
  },
  computed: {
    userInfo() {
      return function(row, key) {
        const avatarPath = row.echoMap[key]?.avatarPath || ''
        const avatarType = row.echoMap[key]?.avatarType || ''
        const name = row.echoMap[key]?.name || ''
        return { avatarPath, avatarType, name }
      }
    },
    getTag() {
      return function(row) {
        const tags = row.echoMap?.tags?.map(r => r.name) || []
        return { tags }
      }
    }
  },
  created() {
    this.getFilterConditions('WORK_ITEM')
  },
  methods: {
    getFilterConditions(type) {
      queryFilterConditions(type).then(res => {
        if (res.isSuccess) {
          const data = res.data.filter(item => item.echoMap?.tableField?.tableName && item?.type?.code != 'FILE' && item?.type?.code != 'EDITOR')
          // 把code,name字段放到前面

          data.forEach(element => {
            element.changeSort = element.key == 'code' ? 1 : element.key == 'name' ? 2 : 3
          })
          this.showColumnsData = ['code', 'name', 'createTime', 'typeCode', 'handleBy', 'stateCode', 'planEtime', 'priorityCode', 'projectId']

          this.tableProps = data.sort(function(a, b) {
            return a.changeSort - b.changeSort
          })
          this.$nextTick(() => {
            this.tableRef = this.$refs.common_filter_table
          })
        }
      })
    },
    // 获取表格数据
    getTablePage() {
      const pageObj = this.$refs.pagination?.pageObj
      return pageObj
    },
    getSort() {
      const sortObj = this.$refs.searchForm?.sortObj
      return sortObj
    },
    getTableData() {
      this.$emit('preview')
    },
    async jumpTask(row) {
      const workItemType = row.echoMap?.typeCode?.classify?.code
      const projectKey = row.echoMap?.projectId?.code
      const projectTypeCode = row.echoMap?.projectId?.typeCode
      const projectId = row.echoMap?.projectId?.id
      const pathStr = workItemType == 'ISSUE' ? '/project/issue' : workItemType == 'TASK' ? '/project/task' : workItemType == 'BUG' ? '/project/defect' : '/project/issue'
      const newpage = await this.$router.resolve({ path: `${pathStr}/${projectKey}/${projectTypeCode}/${projectId}`, query: {
        showDialog: true,
        queryId: row?.id,
        rowTypeCode: row?.typeCode,
        stateCode: row?.stateCode,
        projectId: projectId
      }})
      const winOpen = window.open(newpage.href, '_blank')
      winOpen.opener = null
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.vone-vxe-table .vxe-table--body-wrapper) {
	height: 100%;
}
</style>

