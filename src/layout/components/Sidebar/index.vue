<template>
  <div
    ref="scrollbar"
    class="scrollbar_wrap"
    :class="
      currentAppMenu.length > 11 && showLongTab ? 'long_scrollbar' : 'scrollbar'
    "
  >
    <!-- <el-scrollbar wrap-class="scrollbar-wrapper"> -->

    <el-row ref="outContain" type="flex">
      <span v-show="showDrop" ref="dropWidth" class="change">
        <!-- 项目切换 -->
        <projectMenu v-if="routeParams.projectKey" />
        <!-- 产品切换 -->
        <productMenu v-if="routeParams.productCode" />
        <!-- 产品集切换 -->
        <productfitMenu v-if="routeParams.productfitId" />
        <!-- 资源中心切换 -->
        <sourceMenu v-if="routeParams.systemId" />
      </span>
      <!-- <el-menu ref="elMenu" :default-active="activeMenu" mode="horizontal">
            <div class="left">

              <i v-if="currentTabIndex && showIcon && scroll" class="iconfont el-icon-direction-left" @click="menuleft" />

            </div>

            <sidebar-item v-for="(route,index) in currentAppMenu" v-show="index >= currentTabIndex " :key="route.path" ref="menu" :item="route" :base-path="route.path" />

          </el-menu>
          <div class="right">
            <i v-show="showIconRight && scroll" class="iconfont el-icon-direction-right" @click="menuright" />
          </div> -->
      <el-menu
        ref="elMenu"
        class="sidemenu"
        :default-active="activeMenu"
        mode="horizontal"
      >
        <sidebar-item
          v-for="route in showApp"
          :key="route.path"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
      <div v-if="moreApp.length > 0" class="more-dropdown">
        <span
          class="el-dropdown-link"
          @click="showMoreDropdown = !showMoreDropdown"
        >
          更多<el-icon class="iconfont el-icon--right"
            ><el-icon-direction-down
          /></el-icon>
        </span>
        <div v-if="showMoreDropdown" class="dropdown-menu">
          <div
            v-for="(route, index) in moreApp"
            :key="index"
            class="dropdown-item"
            :class="{ activePath: $route.meta.activeMenu == route.path }"
            @click="handleCommand(route.path)"
          >
            {{ route.meta.title }}
          </div>
        </div>
      </div>
    </el-row>
    <!-- </el-scrollbar> -->
  </div>
</template>

<script>
import { DirectionDown as ElIconDirectionDown } from "@element-plus/icons-vue";
import { debounce } from "lodash";
import { mapGetters } from "vuex";
import SidebarItem from "./SidebarItem.vue";
import variables from "@/styles/variables.scss?inline";

import projectMenu from "./projectMenu.vue";
import productMenu from "./productMenu.vue";
import productfitMenu from "./productfitMenu.vue";
import sourceMenu from "./sourceMenu.vue";

export default {
  components: {
    SidebarItem,
    projectMenu,
    productMenu,
    productfitMenu,
    sourceMenu,
    ElIconDirectionDown,
  },
  name: "Sidebar",
  data() {
    return {
      showDrop: false,
      currentTabIndex: 0,
      showIcon: false,
      showIconRight: false,
      box: 0,
      content_width: 0,
      dropWidth_width: 0,
      all_width: 0,
      scroll: false,
      showApp: [],
      moreApp: [],
      showMoreDropdown: false,
    };
  },
  computed: {
    ...mapGetters(["currentAppMenu"]),
    activeMenu() {
      const route = this.$route;
      const { meta, path } = route;
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    },
    variables() {
      return variables;
    },
    showLongTab() {
      if (
        this.$route.params.projectKey ||
        this.$route.params.projectmCode ||
        this.$route.params.productCode ||
        this.$route.params.productfitId ||
        this.$route.params.systemId
      ) {
        return true;
      }
      return false;
    },
    routeParams() {
      return this.$route.params;
    },
  },
  watch: {
    currentAppMenu: {
      handler(v) {
        this.getWidth();
      },
      deep: true,
      immediate: true,
    },
  },
  beforeUnmount() {
    window.onresize = null;
  },
  mounted() {
    if (
      this.$route.params.projectKey ||
      this.$route.params.projectmCode ||
      this.$route.params.productCode ||
      this.$route.params.productfitId ||
      this.$route.params.systemId
    ) {
      this.showDrop = true;
    } else {
      this.showDrop = false;
    }
    this.computeWidth();
    this.resize(); // 浏览器大小变化时
    this.getWidth();
  },
  methods: {
    getWidth() {
      this.$nextTick(() => {
        this.calcMenus();
      });
    },
    resize() {
      window.onresize = () => {
        this.calcMenus();
      };
    },
    // 动态修改菜单显示个数
    calcMenus: debounce(function () {
      const windowWidth = window.innerWidth;
      const menuLen = document.querySelector(".change")?.offsetWidth || 0;
      const sumLen = [0]; // 保存菜单累计宽度
      // 获取菜单长度
      const total = this.currentAppMenu.reduce((acc, cur, i) => {
        acc += cur.meta.title.length * 15 + 32;
        sumLen.push(acc);
        return acc;
      }, 0);
      // 头部导航栏宽度+左侧下拉框宽度+左侧导航栏宽度
      const menuWidth = this.showLongTab
        ? windowWidth + menuLen + 72 + 75
        : total + 72;
      const maxLen = this.showLongTab
        ? windowWidth - (menuLen + 72 + 75)
        : Math.min(windowWidth, total);
      const count = sumLen.findIndex((v) => v >= maxLen);

      const len = this.currentAppMenu.length;
      // 屏幕宽度大于导航栏最大宽度
      if (windowWidth >= menuWidth || count == -1) {
        this.showApp = this.currentAppMenu.slice(0, len);
        this.moreApp = [];
      } else {
        this.showApp = this.currentAppMenu.slice(0, count - 1);
        this.moreApp = this.currentAppMenu.slice(count - 1, len);
      }
    }, 200),

    handleCommand(routePath) {
      this.showMoreDropdown = false;
      const params = this.$route.params;
      let basePath = routePath || "";
      for (const k in params) {
        basePath = basePath.replace(":" + k, params[k]);
      }
      this.$router.push({
        path: basePath,
      });
    },
    menuleft() {
      this.currentTabIndex = this.currentTabIndex - 1;
      this.computeWidth();
    },
    menuright(index) {
      this.currentTabIndex = this.currentTabIndex + 1;
      this.computeWidth();
    },
    computeWidth() {
      this.box = this.$refs.outContain.$el.clientWidth;
      this.content_width = this.$refs.elMenu.$el.clientWidth;
      this.dropWidth_width = this.$refs.dropWidth.clientWidth;

      this.all_width = this.content_width + this.dropWidth_width + 100;

      if (this.box <= this.all_width) {
        this.showIconRight = true;
      } else if (this.box > this.all_width) {
        this.showIconRight = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@use "@/styles/variables.scss" as *;
:deep(.el-scrollbar__view) {
  position: relative;
}
:deep(.el-menu.el-menu--horizontal) {
  border: none;
}
.scrollbar_wrap {
  position: relative;
  display: flex;
  background-color: var(--main-bg-color, #fff);
  box-shadow: var(--nav-top-shadow);
  .change {
    display: inline-block;
  }
}
.scrollbar {
  justify-content: center;
  height: 48px;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  .change {
    position: fixed;
    left: 72px;
  }
}
.long_scrollbar {
  box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.08);
}
.el-dropdown {
  height: 38px;
  line-height: 38px;
  margin: 6px 0px 0px 5px;
  color: var(--main-font-color);
}
.el-dropdown-link {
  width: 75px;
  height: 38px;
  line-height: 38px;
  border-radius: 4px;
  padding: 0px 8px;
  display: inline-block;
  cursor: pointer;
}
.el-dropdown-link:focus {
  background: #f5f6f7;
  color: #1966ff;
}
.activePath {
  background: #f5f6f7;
  color: var(--main-theme-color, #1966ff) !important;
}
</style>

<style lang="scss">
.icon-img {
  width: 20px;
  height: auto;
  margin-right: 4px;
  vertical-align: middle;
}
</style>
