<template>
  <div>
    <!-- <el-card v-if="tabActive == 'flow' && hasRouter">
          <router-view />
        </el-card> -->

    <div class="sectionPageContent">
      <section class="leftSection">
        <!-- 左侧菜单 -->
        <el-menu :default-active="currentMenu" @select="select">
          <!-- <el-menu-item index="formManagement">
                <i class="iconfont el-icon-gongzuoxiang" style="color:var(--main-theme-color,#3e7bfa)" />
                <span class="ml-3">表单</span>
              </el-menu-item> -->
          <el-sub-menu
            index="formManagement"
            popper-append-to-body
            class="rowLine"
          >
            <template v-slot:title>
              <el-icon class="iconfont" style="color: #699dff"
                ><el-icon-application-form-fill
              /></el-icon>
              <span class="ml-3">表单</span>
            </template>
            <el-menu-item index="formManagement">表单配置</el-menu-item>
            <el-menu-item index="formTable">数据表</el-menu-item>
          </el-sub-menu>
          <el-menu-item index="priority">
            <el-icon class="iconfont" style="color: #699dff"
              ><el-icon-application-priority-fill
            /></el-icon>
            <span class="ml-3">优先级</span>
          </el-menu-item>

          <el-sub-menu index="workFlow" popper-append-to-body class="rowLine">
            <template v-slot:title>
              <el-icon class="iconfont" style="color: #699dff"
                ><el-icon-application-workflow-fill
              /></el-icon>
              <span class="ml-3">工作流</span>
            </template>
            <el-menu-item index="flow">配置</el-menu-item>
            <el-menu-item index="step">阶段</el-menu-item>
            <el-menu-item index="status">状态</el-menu-item>
          </el-sub-menu>

          <el-sub-menu
            v-for="item in menuList"
            :key="item.code"
            :index="item.code"
            popper-append-to-body
          >
            <template v-slot:title>
              <i :class="item.icon" :style="{ color: item.color }" />

              <span class="ml-3">{{ item.name }}</span>
            </template>
            <el-menu-item
              v-for="child in item.children"
              :key="child.id"
              :index="`${child.code}-${item.code}`"
              :is-nest="true"
            >
              {{ child.name }}
            </el-menu-item>
          </el-sub-menu>
        </el-menu>
      </section>
      <section class="rightSection">
        <form-management v-if="tabActive == 'formManagement'" :id="tabActive" />
        <tag-management v-if="tabActive == 'tagManagement'" :id="tabActive" />
        <formTable v-if="tabActive == 'formTable'" :id="tabActive" />
        <priority v-if="tabActive == 'priority'" :id="tabActive" />
        <issueStep v-if="tabActive == 'step'" />
        <issueStatus v-if="tabActive == 'status'" />
        <workFlowConfig v-if="tabActive == 'flow'" />

        <type-table
          v-if="tabActive == 'type'"
          :type-classify="typeClassify"
          :list-data="menuList"
        />
        <source-table
          v-if="tabActive == 'source'"
          :type-classify="typeClassify"
        />
      </section>
    </div>
  </div>
</template>

<script>
import {
  ApplicationFormFill as ElIconApplicationFormFill,
  ApplicationPriorityFill as ElIconApplicationPriorityFill,
  ApplicationWorkflowFill as ElIconApplicationWorkflowFill,
} from "@element-plus/icons-vue";
import priority from "./tab/priority";
import formManagement from "./tab/form-management";
import formTable from "./tab/form-table.vue";
import tagManagement from "./tab/tag-management.vue";
import issueStep from "./work/issueStep.vue";
import issueStatus from "./work/issuesStatus.vue";
import workFlowConfig from "./work/taskflow.vue";

import TypeTable from "./tab/common/type-table.vue";
import sourceTable from "./tab/common/source-table.vue";

import { apiVaBaseClassify } from "@/api/vone/base/customForm";

export default {
  components: {
    formManagement,
    tagManagement,
    priority,
    issueStep,
    formTable,
    TypeTable,
    sourceTable,
    issueStatus,
    // formPage
    // workItem
    workFlowConfig,
    ElIconApplicationFormFill,
    ElIconApplicationPriorityFill,
    ElIconApplicationWorkflowFill,
  },
  data() {
    return {
      tabActive: "formManagement",
      pageLoading: false,
      currentMenu: "formManagement",
      menuList: [],
      typeClassify: undefined,
      hasRouter: false,
      menuLoading: false,
    };
  },
  watch: {
    $route: {
      // $route可以用引号，也可以不用引号
      handler(val) {
        if (val.query.activeMenu == "flow") {
          this.currentMenu = "flow";
          this.tabActive = "flow";
        }
      },
      deep: true, // 深度监听
      immediate: true, // 第一次初始化渲染就可以监听到
    },
  },
  mounted() {
    this.getTypeList();
  },
  methods: {
    async getTypeList() {
      this.menuLoading = true;
      const res = await apiVaBaseClassify();

      if (!res.isSuccess) {
        return;
      }

      const childLiist = [
        {
          name: "类型",
          code: "type",
        },
        {
          name: "来源",
          code: "source",
        },
      ];

      const iconList = [
        {
          code: "IDEA",
          icon: "iconfont el-icon-icon-yixiang",
          color: "#BD7FFA",
        },
        {
          code: "ISSUE",
          icon: "iconfont el-icon-icon-xuqiu",
          color: "#8791FA",
        },
        {
          code: "BUG",
          icon: "iconfont el-icon-icon-quexian",
          color: "#FA6B57",
        },
        {
          code: "TASK",
          icon: "iconfont el-icon-icon-renwu",
          color: "var(--main-theme-color,#3e7bfa)",
        },
        {
          code: "RISK",
          icon: "iconfont el-icon-icon-fengxian",
          color: "#FFBF47",
        },
        {
          code: "WORK_ITEM",
          icon: "iconfont el-icon-icon-shixiang",
          color: "var(--main-theme-color,#3e7bfa)",
        },
      ];

      const iconMap = iconList.reduce((r, v) => (r[v.code] = v) && r, {});

      if (res.data && res.data.length) {
        res.data.forEach((element) => {
          element.children = childLiist;
          element.icon = iconMap[element.code].icon;
          element.color = iconMap[element.code].color;
        });
      }
      this.menuList = res.data;
      this.menuLoading = false;
    },
    configRow() {
      this.hasRouter = true;
    },
    select(val) {
      const tabActive = val.split("-")[0];
      const classify = val.split("-")[1];
      this.tabActive = tabActive;
      this.typeClassify = classify;
    },
  },
};
</script>

<style lang="scss" scoped>
.leftSection {
  width: 220px;
  :deep(.el-menu-item.is-active) {
    // border-radius: 4px 4px 4px 4px;
    opacity: 1;
    border: none;
    color: var(--main-theme-color, #3e7bfa);
    background-color: #f0f7ff;
  }

  :deep(.el-menu-item) {
    width: 100%;
    margin-left: 0;
    height: 36px;
    line-height: 36px;
    padding: 0 16px !important;
  }
  :deep(.el-submenu__title) {
    height: 36px;
    line-height: 36px;
    padding: 0 16px !important;
  }
  :deep(.el-menu) {
    border-right: none;
  }
  :deep(.el-menu-item:hover),
  :deep(.el-submenu__title:hover) {
    background: #f5f6f7 !important;
  }
  :deep(.el-submenu .el-menu-item) {
    padding-left: 40px !important;
  }
  .ml-3 {
    margin-left: 10px;
  }
}
.rightSection {
}
</style>
