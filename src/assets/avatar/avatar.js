// const { reduce } = require("core-js/fn/array");
import avatar1 from '@/assets/avatar/avatar1.png'
import avatar2 from '@/assets/avatar/avatar2.png'
import avatar3 from '@/assets/avatar/avatar3.png'
import avatar4 from '@/assets/avatar/avatar4.png'
import avatar5 from '@/assets/avatar/avatar5.png'
import avatar6 from '@/assets/avatar/avatar6.png'
import avatar7 from '@/assets/avatar/avatar7.png'
import avatar8 from '@/assets/avatar/avatar8.png'
import avatar9 from '@/assets/avatar/avatar9.png'
import avatar10 from '@/assets/avatar/avatar10.png'
import avatar11 from '@/assets/avatar/avatar11.png'
import avatar12 from '@/assets/avatar/avatar12.png'
import avatar13 from '@/assets/avatar/avatar13.png'
import avatar14 from '@/assets/avatar/avatar14.png'
import avatar15 from '@/assets/avatar/avatar15.png'
import avatar16 from '@/assets/avatar/avatar16.png'
import avatar17 from '@/assets/avatar/avatar17.png'
import avatar18 from '@/assets/avatar/avatar18.png'
import avatar19 from '@/assets/avatar/avatar19.png'
import avatar20 from '@/assets/avatar/avatar20.png'
const avatarList = [
  {
    name: "avatar1",
    src: avatar1
  },
  {
    name: "avatar2",
    src: avatar2
  },
  {
    name: "avatar3",
    src: avatar3
  },
  {
    name: "avatar4",
    src: avatar4
  },
  {
    name: "avatar5",
    src: avatar5
  },
  {
    name: "avatar6",
    src: avatar6
  },
  {
    name: "avatar7",
    src: avatar7
  },
  {
    name: "avatar8",
    src: avatar8
  },
  {
    name: "avatar9",
    src: avatar9
  },
  {
    name: "avatar10",
    src: avatar10
  },
  {
    name: "avatar11",
    src: avatar11
  },
  {
    name: "avatar12",
    src: avatar12
  },
  {
    name: "avatar13",
    src: avatar13
  },
  {
    name: "avatar14",
    src: avatar14
  },
  {
    name: "avatar15",
    src: avatar15
  },
  {
    name: "avatar16",
    src: avatar16
  },
  {
    name: "avatar17",
    src: avatar17
  },
  {
    name: "avatar18",
    src: avatar18
  },
  {
    name: "avatar19",
    src: avatar19
  },
  {
    name: "avatar20",
    src: avatar20
  },
];

const avatarMap = {}
avatarList.map((item)=>{
  avatarMap[item.name] = item
})
export { avatarList, avatarMap};

export default avatarList;
