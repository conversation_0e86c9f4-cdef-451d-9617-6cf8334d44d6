<template>
  <div class="flow_wrap">
    <header class="header">
      <span class="header_title">基本信息</span>
    </header>
    <el-form ref="form" :model="basicData" label-position="top">
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item label="项目编号" prop="name">
            <el-input v-model="basicData.code" placeholder="请输入" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="项目名称" prop="procInstId">
            <el-input v-model="basicData.name" placeholder="请输入" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="项目类型">
            <el-select
              v-model="basicData.classify"
              placeholder="请选择项目类型"
              filterable
              disabled
            >
              <el-option
                v-for="item in typeList"
                :key="item.value"
                :label="item.name"
                :value="item.code"
                :title="item.name"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="项目经理">
            <vone-remote-user v-model:value="basicData.leadingBy" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="组织机构">
            <vone-tree-select
              v-model:value="basicData.orgId"
              style="height: 32px"
              search-nested
              :tree-data="orgDatas"
              placeholder="请选择机构"
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="关联项目">
            <el-select
              v-model="basicData.projectIds"
              placeholder="请选择关联项目"
              filterable
              multiple
              disabled
            >
              <el-option
                v-for="item in allProjectList"
                :key="item.value"
                :label="item.name"
                :value="item.id"
                :title="item.name"
              >
                <span>
                  {{ item.name }}
                </span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="主办产品">
            <el-select
              v-model="basicData.hostProductId"
              placeholder="请选择产品"
              style="width: 100%"
              filterable
              clearable
              disabled
            >
              <el-option
                v-for="item in productList"
                :key="item.value"
                :label="item.name"
                :value="item.id"
                :title="item.name"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="辅办产品">
            <el-select
              v-model="basicData.assistProductIds"
              placeholder="请选择产品"
              style="width: 100%"
              filterable
              clearable
              multiple
              disabled
            >
              <el-option
                v-for="item in productList.filter(
                  (v) => v.id !== basicData.hostProductId
                )"
                :key="item.value"
                :label="item.name"
                :value="item.id"
                :title="item.name"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="项目集">
            <el-select
              v-model="basicData.programId"
              placeholder="请选择项目集"
              style="width: 100%"
              clearable
              disabled
            >
              <el-option
                v-for="item in programDatas"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { orgList } from '@/api/vone/base/org'
import { gainTreeList } from '@/utils'
import { apiBaseDictNoPage } from '@/api/vone/base/dict'
import {
  productListByCondition,
  apiAlmProjectNoPage,
} from '@/api/vone/project/index'
export default {
  props: {
    variables: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      basicData: {},
      achievementsTemplate: [],
      orgDatas: null,
      typeList: [],
      allProjectList: [],
      hostProductId: [],
      productList: [],
      programDatas: [],
    }
  },

  mounted() {
    this.basicData = this.variables.saveProject || this.variables.closureProject
    this.getOrgList()
    this.getTypeList()
    this.getAllProjectList(this.basicData.classify)
    this.getAllProductList()
    if (this.basicData.projects) {
      this.basicData.projectIds = this.basicData.projects.map((r) => r.id)
    }
  },
  methods: {
    // 查询所有产品
    async getAllProductList() {
      const res = await productListByCondition()
      if (!res.isSuccess) {
        return
      }
      this.productList = res.data
    },
    // 关联项目
    async getAllProjectList(val) {
      const res = await apiAlmProjectNoPage({
        classify: val == 'DEVELOP' ? 'DELIVERY' : 'DEVELOP',
      })
      if (!res.isSuccess) {
        return
      }
      this.allProjectList = res.data
    },
    // 项目类型
    async getTypeList() {
      const res = await apiBaseDictNoPage({
        type: 'PROJECT_CLASSIFY',
      })
      if (!res.isSuccess) {
        return
      }
      this.typeList = res.data
    },
    // 查询所有机构
    getOrgList() {
      this.pageLoading = true
      orgList().then((res) => {
        const orgTree = gainTreeList(res.data)
        this.orgDatas = orgTree
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.flow_wrap {
  padding: 0 10px;
}
.header {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 56px;
}
.header_title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 500;
  color: var(--main-font-color);
  &::before {
    display: block;
    content: '';
    background: var(--main-theme-color, #4287ff);
    width: 4px;
    height: 18px;
    border-radius: 1px;
  }
}
</style>
