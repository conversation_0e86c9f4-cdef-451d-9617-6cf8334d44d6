<template>
  <page-wrapper>
    <div style="height: calc(100vh - 30px)">
      <vone-search-wrapper>
        <template v-slot:search>
          <vone-search-dynamic
            ref="searchForm"
            table-search-key="wiki-cards"
            :model="formData"
            :default-fileds="defaultFileds"
            show-basic
            :extra="extraData"
            @getTableData="getWikiList"
          />
        </template>
        <template v-slot:actions>
          <el-button
            :icon="ElIconPlus"
            :disabled="!$permission('wiki_view_add')"
            type="primary"
            @click="addSpace"
            >新增</el-button
          >
        </template>
        <template v-slot:fliter>
          <vone-search-filter
            :extra="extraData"
            :model="formData"
            :default-fileds="defaultFileds"
            @getTableData="getWikiList"
          />
        </template>
      </vone-search-wrapper>
      <div v-loading="tableLoading" class="wiki">
        <div class="wiki-card">
          <vone-cards
            ref="wiki-card"
            :data="wikiList"
            :row-count="4"
            height="calc(100vh - 135px)"
            @updateData="getWikiList"
          >
            <template v-slot="{ row }">
              <div
                :class="[
                  !$permission('wiki_document') ? 'disabledLinkCls' : 'linkCls',
                ]"
              >
                <vone-card
                  :title="row.name"
                  :actions="rowActions"
                  @click="jumpDetail(row)"
                >
                  <template v-slot:icon>
                    <svg-icon
                      v-if="row.type == 'CONFLUENCE'"
                      icon-class="wiki-confluence"
                    />
                    <svg-icon
                      v-else-if="row.type == 'FEISHU'"
                      icon-class="wiki-feishu"
                    />
                    <svg-icon v-else icon-class="wiki-vone" />
                  </template>
                  <template v-slot:title>
                    <el-row>
                      <div class="title-box">
                        <div class="title" :title="row.name">
                          {{ row.name }}
                        </div>
                      </div>
                    </el-row>
                  </template>

                  <template v-slot:desc>
                    <div>
                      <span
                        v-if="
                          row.leadingBy && row.echoMap && row.echoMap.leadingBy
                        "
                      >
                        <vone-user-avatar
                          :avatar-path="row.echoMap.leadingBy.avatarPath"
                          :name="row.echoMap.leadingBy.name"
                        />
                      </span>
                      <span v-else>
                        <vone-user-avatar :avatar-path="''" :name="''" />
                      </span>
                    </div>
                  </template>

                  <el-row style="font-size: 12px; color: #909399">
                    <vone-toolitip
                      :content="row.updateTime"
                      :label="'更新时间 '"
                    />

                    <vone-toolitip
                      :content="row.description"
                      :label="'描述 '"
                    />
                  </el-row>
                </vone-card>
              </div>
            </template>
          </vone-cards>
        </div>
        <wiki-space-dialog
          v-if="spaceVisible"
          :current-space-info="currentSpaceInfo"
          v-model:space-visible="spaceVisible"
          :title="title"
          @getSpaceList="getWikiList"
        />
      </div>
    </div>
  </page-wrapper>
</template>

<script>
import { Plus as ElIconPlus } from "@element-plus/icons-vue";
import {
  searchSpacePage,
  searchDoucment,
  operationSpace,
} from "@/api/vone/wiki";
import WikiSpaceDialog from "./wiki-space-dialog.vue";
export default {
  data() {
    return {
      extraData: {},
      defaultFileds: [
        {
          key: "name",
          name: "产品名称",
          type: {
            code: "INPUT",
          },
          placeholder: "请输入产品名称",
        },
        {
          key: "leadingBy",
          name: "负责人",
          type: {
            code: "USER",
          },
          placeholder: "请选择负责人",
          multiple: false,
        },
      ],
      wikiList: {},
      rowActions: [
        {
          type: "text",
          text: "修改",
          icon: "iconfont el-icon-application-edit",
          onClick: ({ row }) => this.operationSpace("edit", row),
          disabled: !this.$permission("wiki_view_edit"),
        },
        {
          type: "text",
          text: "删除",
          icon: "iconfont el-icon-application-delete",
          onClick: ({ row }) => this.operationSpace("del", row),
          disabled: !this.$permission("wiki_view_del"),
        },
      ],
      formData: {},
      spaceVisible: false,
      currentSpaceInfo: {},
      title: "",
      tableLoading: false,
      ElIconPlus,
    };
  },
  components: {
    WikiSpaceDialog,
  },
  mounted() {
    this.getWikiList();
  },
  methods: {
    getWikiList() {
      this.tableLoading = true;
      let params = {};
      const cardAttr = this.$refs["wiki-card"].exportTableQueryData();
      cardAttr["sort"] = "updateTime";
      cardAttr["order"] = "descending";
      params = {
        ...cardAttr,
        extra: {
          tableId: !this.$refs.searchForm.isBtnSure
            ? this.$refs.searchForm.viewId
            : null, // 比填,区分是查询还是切换筛选器
          ...this.extraData,
        },
        model: { ...this.formData },
      };
      searchSpacePage(params).then((res) => {
        if (res.isSuccess) {
          res.data.records.forEach((element) => {
            element.type = element.type?.code;
          });
          this.wikiList = res.data;
        }
      });
      this.tableLoading = false;
    },
    async jumpDetail(row) {
      if (!this.$permission("wiki_document")) return;
      // 根据空间id 查询文档
      const { data, isSuccess } = await searchDoucment(row.id);
      if (isSuccess) {
        this.jumpRoute(row.id, data.length > 0 ? data[0].id : "mulu");
      }
    },
    // 通过router控制页面内容
    jumpRoute(spaceId, docId) {
      this.$router.push({
        name: "wiki_document",
        params: {
          spaceId: spaceId,
          docId: docId,
        },
      });
    },
    // 新增空间
    addSpace() {
      this.title = "新增空间";
      this.spaceVisible = true;
      this.currentSpaceInfo = {};
    },
    // 操作空间
    operationSpace(type, row) {
      if (type == "edit") {
        this.title = "编辑空间";
        this.currentSpaceInfo = row;
        this.spaceVisible = true;
      } else {
        this.$confirm(`确定删除【${row.name}】吗？`, "删除", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          customClass: "delConfirm",
          showClose: false,
          type: "warning",
        }).then(() => {
          operationSpace([row.id], "delete").then((res) => {
            if (res.isSuccess) {
              this.getWikiList();
            } else {
              this.$message.warning(res.msg);
            }
          });
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.wiki {
  .title-box {
    cursor: pointer;
    .title {
      height: 28px;
      line-height: 28px;
      i {
        padding-left: 3px;
      }
      &- {
        &text {
          font-size: 14px;
          padding-left: 14px;
          font-weight: bold;
        }
      }
    }
    .desc {
      height: 18px;
      line-height: 18px;
      margin: 10px 0;
    }
  }
}
.disabledLinkCls {
  cursor: not-allowed !important;
}
.linkCls {
  cursor: pointer;
}
.svg-icon {
  font-size: 28px; /*// vertical-align: bottom;*/
}
.flexRow {
  display: flex;
  align-items: center;
}
.list-view {
  margin-top: -8px;
}
</style>
