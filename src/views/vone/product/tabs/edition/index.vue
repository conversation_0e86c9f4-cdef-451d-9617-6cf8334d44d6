<template>
  <div v-loading="pageLoading" class="page-content">
    <header>
      <span class="back" @click="backPage">
        <el-icon class="iconfont"><el-icon-direction-back /></el-icon>
      </span>
      <span class="map-title">版本路线图</span>
    </header>

    <section class="section">
      <div class="step-box">
        <div v-if="stepData.length" class="vone-version-step">
          <el-steps>
            <el-step
              v-for="(item, index) in stepData"
              :key="index"
              :class="
                index === 0
                  ? 'step-start'
                  : index === stepData.length - 1
                  ? 'step-end'
                  : ''
              "
            >
              <template v-slot:title>
                <el-tooltip :content="item.productVersionName" placement="top">
                  <div class="text-over tooltip-title">
                    {{ item.productVersionName }}
                  </div>
                </el-tooltip>
                <div style="margin: 36px 0 8px">
                  <el-button
                    type="text"
                    class="collapse-status"
                    :class="{
                      purple: item.stateId == '1',
                      blue: item.stateId == '2',
                      green: item.stateId == '3',
                      orange: item.stateId == '4',
                    }"
                    ><span v-if="item.stateId == 1">规划中</span>
                    <span v-if="item.stateId == 2">进行中</span>
                    <span v-if="item.stateId == 3">发布成功</span>
                    <span v-if="item.stateId == 4">发布失败</span>
                  </el-button>
                </div>
                <div class="versionTime">
                  <span v-if="item.planRtime">
                    {{ dayjs(item.planRtime).format("YYYY-MM-DD") }}
                  </span>
                  <span v-else class="noTime"> 未设置 </span>
                </div>
              </template>
              <template v-slot:icon>
                <div>
                  <!-- 规划中 -->
                  <div v-if="item.stateId == '1'" class="unstart" />
                  <!-- //进行中 -->
                  <div v-if="item.stateId == '2'" class="planning" />
                  <!-- //发布成功 -->
                  <div v-if="item.stateId == '3'" class="success" />
                  <!-- //发布失败 -->
                  <div v-if="item.stateId == '4'" class="error" />
                </div>
              </template>
              <template v-slot:description>
                <StepCard :form="item" :index="index" />
              </template>
            </el-step>
          </el-steps>
        </div>
        <vone-empty v-else />
      </div>
    </section>
  </div>
</template>

<script>
import { ArrowLeft as ElIconDirectionBack } from "@element-plus/icons-vue";
import { productVersionCircuit } from "@/api/vone/product/index";
import StepCard from "./step-card.vue";
export default {
  components: {
    StepCard,
    ElIconDirectionBack,
  },
  data() {
    return {
      stepData: [],
      pageLoading: false,
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    backPage() {
      this.$router.back();
    },
    async getData() {
      this.pageLoading = true;
      const { data, isSuccess, msg } = await productVersionCircuit(
        this.$route.params.productId
      );
      this.pageLoading = false;
      if (!isSuccess) {
        this.$message.warning(msg);
        return;
      }

      data.forEach((element) => {
        element.productVersionName = element.productVersion?.name;
        element.stateId = element.productVersion?.stateId;
        element.planRtime = element.productVersion?.planRtime;
      });
      this.stepData = data;
    },
  },
};
</script>

<style lang="scss" scoped>
.map-title {
  font-size: 16px;
  font-weight: 600;
  color: #1d2129;
}
.page-content {
  background-color: var(--main-bg-color);
  border-radius: 2px;
  header {
    height: 54px;
    line-height: 54px;
    padding: 0 16px;
    border-bottom: 1px solid var(--el-divider);
  }
}
.section {
  width: 100%;
  min-height: calc(100vh - 140px);
  overflow-y: hidden;
  display: flex;
  align-items: center;
  padding: 0 30px;
}
.step-box {
  width: 100%;
  height: 116px;
  margin-top: 60px;
  background-color: #fff;
}
.collapse-status {
  background: #ffeecf;
  color: #fa962b;
  height: 28px;
  &.blue {
    color: #64befa;
    background: rgba(100, 190, 250, 0.12);
  }
  &.purple {
    color: #bd7ffa;
    background: rgba(189, 127, 250, 0.12);
  }
  &.green {
    color: #3cb540;
    background: rgba(60, 181, 64, 0.12);
  }
  &.orange {
    color: #fc9772;
    background: rgba(252, 151, 114, 0.12);
  }
}
.versionTime {
  color: #1d2129;
}
.vone-version-step {
  padding-left: 30px;
  width: 100%;
  :deep(.el-tooltip) {
    color: #202123;
  }

  .el-step {
    position: relative;
    &:not(.step-end) {
      min-width: 270px;
    }
  }
  .today {
    position: absolute;
    text-align: center;
    top: 0px;
    left: 50%;
    a {
      display: inline-block;
      width: 15px;
      height: 15px;
      border: 2px solid;
      border-radius: 50%;
      margin-top: 10px;
      background: var(--main-bg-color, #fff);
    }
  }
}
.iconImg {
  width: 20px;
  height: 20px;
}
:deep() {
  .el-step__icon {
    border: 0;
    width: 10px;
    height: 10px;
    padding: 0 6px;
    bottom: -4px;
  }
  .el-step.is-horizontal .el-step__line {
    background-color: #eaecf0;
    height: 4px;
    left: 18px;
    right: 6px;
  }
  .el-step__title {
    line-height: initial;
    max-width: 150px;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: -46px 0 0 -75px;
  }
  .tooltip-title {
    width: 100%;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
  }
  .step-start {
    .el-step__title {
      align-items: flex-start;
      margin-left: 0;
    }
    .tooltip-title {
      font-size: 14px;
      font-weight: 500;
      text-align: start;
    }
  }
}
.unstart {
  width: 10px;
  height: 10px;
  line-height: 10px;
  border-radius: 50%;
  background: #bd7ffa;
}
.planning {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #64befa;
}
.success {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #3cb540;
}
.error {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #fc9772;
}
.custom-theme-dark {
  .vone-version-step:deep(.el-tooltip) {
    color: #e6e9f0;
  }
}
.noTime {
  color: var(--placeholder-color);
}
</style>
