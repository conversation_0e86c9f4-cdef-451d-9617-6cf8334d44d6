<template>
  <el-dialog
    :title="editTitle"
    :model-value="dialogVisible"
    width="600px"
    top="5vh"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="close"
  >
    <!-- 表单部分 -->
    <el-form
      ref="form"
      v-loading="formLoading"
      :rules="rules"
      :model="org"
      label-position="top"
      label-width="100px"
    >
      <el-row :gutter="24">
        <el-col v-if="org.parentId" :span="24">
          <el-form-item label="上级团队" prop="parentName">
            <el-input
              v-model="org.parentName"
              placeholder="请输入名称"
              :disabled="editTitle == '编辑团队' || org.parentId !== ''"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="团队名称" prop="name">
            <el-input v-model="org.name" placeholder="请输入名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="类型" prop="type">
            <el-select v-model="org.type" disabled>
              <el-option
                v-for="item in typeList"
                :key="item.id"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="负责人" prop="leaderBy">
            <vone-remote-user v-model:value="org.leaderBy" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="隶属机构" prop="orgId">
            <vone-tree-select
              v-model:value="org.orgId"
              search-nested
              :tree-data="orgData"
              placeholder="请选择机构"
            />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="描述" prop="description">
            <el-input
              v-model="org.description"
              type="textarea"
              placeholder="描述"
              maxlength="100"
              show-word-limit
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template v-slot:footer>
      <div>
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="onSave">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../utils/gogocodeTransfer'
import { orgList } from '@/api/vone/base/org'
import { addTeam, editTeam } from '@/api/vone/base/team'
import { gainTreeList } from '@/utils'
import { apiBaseDictNoPage } from '@/api/vone/base/dict'
export default {
  props: {
    clickNode: {
      type: Object,
      default: () => {},
    },
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    editTitle: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      srcList: [],
      rules: {
        type: [
          {
            required: true,
            message: '请选择类型',
            trigger: 'change',
          },
        ],
        leaderBy: [
          {
            required: true,
            message: '请选择负责人',
            trigger: 'blur',
          },
        ],
        name: [
          {
            required: true,
            message: '请输入长度不超过50个字符的名称',
            trigger: 'change',
          },
          {
            max: 50,
            message: '请输入长度不超过50个字符的名称',
            trigger: 'change',
          },
        ],
      },
      org: {
        name: '',
        parentId: '',
        parentName: '',
        leaderBy: '',
        type: 'OrgTeam',
        description: '',
        orgId: null,
      },
      orgData: [],
      formLoading: false,
      typeList: [],
    }
  },
  computed: {},
  mounted() {
    this.getAllOrg()
    this.getType()
    if (this.clickNode.id) {
      if (this.editTitle == '新增团队') {
        this.org.id = this.clickNode.id
        this.org.parentId = this.clickNode.id
        this.org.parentName = this.clickNode.name
      } else {
        // 编辑
        this.org = this.clickNode
        this.org.parentName =
          this.clickNode.echoMap.parentId &&
          this.clickNode.echoMap.parentId.name
      }
    }
  },
  methods: {
    getType() {
      apiBaseDictNoPage({ type: 'TEAM_TYPE' }).then((res) => {
        if (res.isSuccess) {
          this.typeList = res.data
        }
      })
    },
    async getAllOrg() {
      orgList().then((res) => {
        const orgTree = gainTreeList(res.data)
        this.orgData = orgTree
      })
    },
    async onSave() {
      try {
        await this.$refs.form.validate()
      } catch (e) {
        return
      }
      if (this.editTitle == '新增团队') {
        addTeam(this.org).then(async (res) => {
          if (res.isSuccess) {
            this.$message.success('保存成功')
            $emit(this, 'success')
            $emit(this, 'update:dialogVisible', false)
          } else {
            this.$message.warning(res.msg)
          }
        })
      } else {
        editTeam(this.org).then(async (res) => {
          if (res.isSuccess) {
            this.$message.success('修改成功')
            $emit(this, 'success')
            $emit(this, 'update:dialogVisible', false)
          } else {
            this.$message.warning(res.msg)
          }
        })
      }
    },
    close() {
      $emit(this, 'update:dialogVisible', false)
      this.$refs.form.resetFields()
    },
  },
  emits: ['update:dialogVisible', 'success'],
}
</script>

<style lang="scss" scoped>
.el-image {
  height: 100px;
  width: 100px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
</style>
