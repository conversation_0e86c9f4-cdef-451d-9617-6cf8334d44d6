import G6 from '@antv/g6'
// const colors = {
//   B: '#5B8FF9',
//   R: '#F46649',
//   Y: '#EEBC20',
//   G: '#5BD8A6',
//   DI: '#A7A7A7'
// }
import cmdb from '@/assets/graph/icon-cmdb.png'
import pipeLineCustom from '@/assets/graph/pipeline-CUSTOM.svg'
import pipeLineSchedule from '@/assets/graph/pipeline-SCHEDULE.svg'
import story from '@/assets/graph/icon-story.svg'
import collapseImg from '@/assets/graph/icon-collapse.png'
import collapsedImg from '@/assets/graph/icon-collapsed.png'

let rootId = ''
let rootx = ''
const customNode = {
  init() {
    G6.registerNode(
      'flow-rect',
      {
        shapeType: 'flow-rect',
        draw(cfg, group) {
          const {
            name = '',
            id,
            parentId,
            topologicalType,
            code = '',
            projectnameOrApplicationName = '',
            version = '',
            dateTime = '',
            description = '',
            echoMap = { userId: {}, applicationId: {}},
            collapsed
          } = cfg
          // 是否是二级节点和环境标签节点
          const specialNode = rootId === parentId || topologicalType === 'PACKSTATUS'
          // 逻辑不应该在这里判断
          const rectConfig = {
            width: topologicalType === 'PACK' ? 240 : specialNode ? 90 : 240,
            height: topologicalType === 'PACK' ? 100 : specialNode ? 45 : topologicalType === 'REQUIREMENT' ? 96 : 75,
            lineWidth: 1,
            fontSize: 12,
            fill: '#fff',
            radius: 4,
            stroke: '#CED4D9',
            opacity: 1
          }

          const textConfig = {
            textAlign: 'left',
            textBaseline: 'bottom'
          }
          // 节点容器
          const rect = group.addShape('rect', {
            attrs: {
              ...rectConfig,
              x: -rectConfig.width / 2,
              y: -rectConfig.height / 2,
              shadowColor: 'rgba(41, 82, 166, 0.16)',
              shadowBlur: 16,
              lineWidth: 0
            }
          })
          // 获取最外层容器坐标宽高
          const rectBBox = rect.getBBox()
          // 保存根节点id
          if (topologicalType === 'PACK') {
            rootId = id
            rootx = cfg.x
          }
          if (!specialNode) {
            // 图标
            group.addShape('image', {
              attrs: {
                x: 12 + rectBBox.x,
                y: 6 + rectBBox.y,
                width: 20,
                height: 20,
                img:
                  topologicalType === 'PIPELINE'
                    ? cfg?.issueOrPipelineType === 'CUSTOM'
                      ? pipeLineCustom
                      : pipeLineSchedule
                    : topologicalType === 'DEPLOYRECORD'
                      ? cmdb
                      : story
              },
              visible: topologicalType !== 'PACK',
              name: 'image-shape'
            })
          }
          // 二级菜单和环境标签 标题
          if (specialNode) {
            // 标题
            group.addShape('text', {
              attrs: {
                x: rectBBox.x + rectBBox.width / 2,
                y: rectBBox.y + rectBBox.height / 2 + 6,
                text: name.length > 18 ? name.substr(0, 18) + '...' : name,
                textAlign: 'center',
                textBaseline: 'bottom',
                fontSize: 14,
                fontWeight: 700,
                fontFamily: 'PingFang SC',
                fill: '#000',
                cursor: 'pointer'
              },
              name: 'name-shape'
            })
          } else {
            if (topologicalType !== 'PACK') {
              // 三级节点标题部分
              group.addShape('text', {
                attrs: {
                  x: 36 + rectBBox.x,
                  y: 24 + rectBBox.y,
                  text: encodeURI(name).length > 25 ? name.substr(0, 25) + '...' : name,
                  fontSize: 14,
                  fontWeight: 700,
                  fill: '#000',
                  cursor: 'pointer'
                },
                name: 'name-shape'
              })
              const { userId = {}} = echoMap
              // 头像地址
              const avatarPath = userId.avatarPath ? userId.avatarPath : 'avatar1'
              const userAvatar = require(`@/assets/avatar/${avatarPath}.png`)

              const userName = userId.name || cfg.userName
              // 头像图标
              group.addShape('image', {
                attrs: {
                  x: 12 + rectBBox.x,
                  y: -24 + rectBBox.maxY,
                  width: 20,
                  height: 20,
                  img: userAvatar
                },
                name: 'avatar-shape'
              })
              // 用户名称
              group.addShape('text', {
                attrs: {
                  ...textConfig,
                  x: 36 + rectBBox.x,
                  y: -8 + rectBBox.maxY,
                  text: userName,
                  fontSize: 12,
                  fill: '#202124'
                },
                name: 'username-shape'
              })
            }
            // 部署信息子节点设置
            if (topologicalType === 'DEPLOYRECORD') {
              group.addShape('rect', {
                attrs: {
                  x: rectBBox.maxX - 40,
                  y: 8 + rectBBox.y,
                  width: 30,
                  height: 20,
                  lineWidth: 1,
                  stroke: cfg.version ? cfg.version === '成功' ? '#3CB540' : '#FA6B57' : '#fff',
                  fontSize: 12,
                  radius: 4,
                  opacity: 1,
                  fill: '#fff'
                },
                visible: !!cfg.version,
                name: 'status-rect'
              })
              // 状态
              group.addShape('text', {
                attrs: {
                  ...textConfig,
                  x: rectBBox.maxX - 38,
                  y: 24 + rectBBox.y,
                  text: cfg.version || '',
                  fontSize: 12,
                  fill: cfg.version ? cfg.version === '成功' ? '#3CB540' : '#FA6B57' : '#fff'
                },
                name: 'desc-shape'
              })
              // 描述
              group.addShape('text', {
                attrs: {
                  ...textConfig,
                  x: 12 + rectBBox.x,
                  y: 44 + rectBBox.y,
                  text: '服务器:' + (projectnameOrApplicationName.length > 18 ? projectnameOrApplicationName.slice(0, 17) + '...' : projectnameOrApplicationName),
                  fontSize: 12,
                  fill: '#8A8F99',
                  opacity: 0.85
                },
                name: 'desc-shape'
              })
              // 时间
              group.addShape('text', {
                attrs: {
                  x: rectBBox.maxX - 12,
                  y: rectBBox.maxY - 8,
                  text: dateTime,
                  textAlign: 'right',
                  textBaseline: 'bottom',
                  fontSize: 12,
                  fill: '#000',
                  opacity: 0.85
                },
                name: 'date-shape'
              })
            }
            // 流水线子节点设置
            if (topologicalType === 'PIPELINE') {
              // 描述
              group.addShape('text', {
                attrs: {
                  ...textConfig,
                  x: 12 + rectBBox.x,
                  y: 44 + rectBBox.y,
                  text: description.length > 18 ? description.slice(0, 16) + '...' : description,
                  fontSize: 12,
                  fill: '#8A8F99',
                  opacity: 0.85
                },
                name: 'desc-shape'
              })
            }
            // 需求子节点设置
            if (topologicalType === 'REQUIREMENT') {
              // 编号
              group.addShape('text', {
                attrs: {
                  ...textConfig,
                  x: 12 + rectBBox.x,
                  y: 44 + rectBBox.y,
                  text:
                    '编号:' +
                    (encodeURI(code).length > 144 ? code.slice(0, 17) + '...' : code),
                  fontSize: 12,
                  fill: '#8A8F99',
                  opacity: 0.85
                },
                name: 'num-shape'
              })
              // 项目
              group.addShape('text', {
                attrs: {
                  ...textConfig,
                  x: 12 + rectBBox.x,
                  y: 64 + rectBBox.y,
                  text:
                    '项目:' +
                    (projectnameOrApplicationName ? encodeURI(projectnameOrApplicationName).length > 162
                      ? projectnameOrApplicationName.slice(0, 17) + '...'
                      : projectnameOrApplicationName : ''),
                  fontSize: 12,
                  fill: '#8A8F99',
                  opacity: 0.85
                },
                name: 'num-shape'
              })
              // 时间
              group.addShape('text', {
                attrs: {
                  x: rectBBox.maxX - 12,
                  y: rectBBox.maxY - 6,
                  text: dateTime,
                  textAlign: 'right',
                  textBaseline: 'bottom',
                  fontSize: 12,
                  fill: '#8A8F99',
                  opacity: 0.85
                },
                name: 'date-shape'
              })
            }
          }
          // 制品库根节点 文本
          if (topologicalType == 'PACK') {
            // 标题
            group.addShape('text', {
              attrs: {
                x: 12 + rectBBox.x,
                y: 24 + rectBBox.y,
                text: name.length > 18 ? name.substr(0, 16) + '...' : name,
                fontSize: 14,
                fontWeight: 700,
                fontFamily: 'PingFang SC',
                fill: '#000',
                cursor: 'pointer'
              },
              name: 'name-shape'
            })
            // 版本
            group.addShape('text', {
              attrs: {
                ...textConfig,
                x: 12 + rectBBox.x,
                y: 44 + rectBBox.y,
                text: '版本: ' + version.length > 18 ? version.slice(0, 17) + '...' : version,
                fontSize: 12,
                fill: '#8A8F99',
                opacity: 0.85
              },
              name: 'version-shape'
            })
            // 服务应用
            group.addShape('text', {
              attrs: {
                ...textConfig,
                x: 12 + rectBBox.x,
                y: 64 + rectBBox.y,
                text: '服务应用: ' + (projectnameOrApplicationName ? projectnameOrApplicationName.length > 18 ? projectnameOrApplicationName.substr(0, 16) + '...' : projectnameOrApplicationName : ''),
                fontSize: 12,
                fill: '#8A8F99',
                opacity: 0.85
              },
              name: 'app-shape'
            })
            // 更新时间
            group.addShape('text', {
              attrs: {
                ...textConfig,
                x: 12 + rectBBox.x,
                y: 84 + rectBBox.y,
                text: '更新时间: ' + dateTime,
                fontSize: 12,
                fill: '#8A8F99',
                opacity: 0.85
              },
              name: 'time-shaoe'
            })
          }

          // collapse
          if (cfg.children && cfg.children.length) {
            // 左侧存在子节点 rootx为根节点x坐标
            const leftPos = cfg.x ? cfg.x < rootx : false
            // 右侧是否存在节点
            const rightPos = cfg.x ? cfg.x > rootx : true
            const centerPos = cfg.x === rootx
            if (centerPos) {
              group.addShape('image', {
                attrs: {
                  x: rectBBox.width / 2 + rectBBox.x - 14,
                  y: rectBBox.maxY,
                  width: 28,
                  height: 28,
                  img: collapsed ? collapsedImg : collapseImg
                },
                name: 'collapse-back',
                modelId: cfg.id
              })
            }
            // 左侧
            if (leftPos) {
              group.addShape('image', {
                attrs: {
                  x: rectBBox.x - 14,
                  y: rectBBox.maxY / 2 - 24,
                  width: 28,
                  height: 28,
                  img: collapsed ? collapsedImg : collapseImg
                },
                name: 'collapse-back',
                modelId: cfg.id
              })
            }
            if (rightPos) {
              group.addShape('image', {
                attrs: {
                  x: rectBBox.maxX - 14,
                  y: rectBBox.maxY / 2 - 24,
                  width: 28,
                  height: 28,
                  img: collapsed ? collapsedImg : collapseImg
                },
                name: 'collapse-back',
                modelId: cfg.id
              })
            }
          }

          this.drawLinkPoints(cfg, group)
          return rect
        },
        update(cfg, item) {
          const group = item.getContainer()
          this.updateLinkPoints(cfg, group)
        },
        setState(name, value, item) {
          if (name === 'collapse') {
            const group = item.getContainer()
            const collapseShape = group.find(
              e => e.get('name') === 'collapse-back'
            )
            if (collapseShape) {
              collapseShape.attr({
                img: value ? collapsedImg : collapseImg
              })
            }
          }
        },
        getAnchorPoints() {
          return [
            [0, 0.5],
            [1, 0.5]
          ]
        }
      },
      'rect'
    )
  }
}

export default customNode
