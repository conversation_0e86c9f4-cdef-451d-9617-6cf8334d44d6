<template>
  <el-pagination
    class="pagination"
    :total="+$attrs.total || 0"
    :current-page="current"
    :page-size="page"
    :page-sizes="[20, 40, 60, 80, 100]"
    :layout="layout"
    v-bind="$attrs"
    @current-change="handleCurrentChange"
    @size-change="handleSizeChange"
   
  >
    <!-- <span v-if="showCustomSizes" class="custom-sizes">
      <el-input ref="custom-page" v-model="customPage" size="mini" placeholder="自定义条数" @blur="customPageChange" @keyup.enter.native="customPageEnter" />
    </span> -->
  </el-pagination>
</template>
<script>
export default {
  name: 'VonePagination',
  props: {
    showCustomSizes: {
      type: Boolean,
      default: true
    },
    layout: {
      type: String,
      default: '->,total,  prev, pager, next, sizes,slot, jumper'
    }
  },
  data() {
    return {
      page: 20,
      current: 1,
      customPage: null,
      pageObj: {
        size: 20,
        current: 1
      }
    }
  },
  methods: {
    // 对外暴露page、current
    exportPages() {
      return { size: this.page, current: this.current }
    },
    handleCurrentChange(val) {
      this.pageObj.current = val
      this.current = val
      this.$emit('update')
    },
    handleSizeChange(val) {
      this.pageObj.current = 1
      this.pageObj.size = val
      this.current = 1
      this.page = val
      this.$emit('update')
      this.customPage = val
    },
    customPageChange() {
      if (!this.customPage) {
        return
      }
      this.$nextTick(() => {
        const reg = /^\+?[1-9][0-9]*$/
        if (!reg.test(this.customPage)) {
          this.$message.warning('请输入非0正整数')
          return
        }
        this.pageObj.current = 1
        this.pageObj.size = Number(this.customPage)
        this.current = 1
        this.page = Number(this.customPage)
        this.$emit('update')
      })
    },
    customPageEnter() {
      this.$refs['custom-page'] && this.$refs['custom-page'].blur()
    }
  }
}
</script>
<style lang='scss' scoped>
.pagination {
  margin-top: 10px;
  position: absolute;
  bottom: 10px;
  right: 16px;
  background: #ffffff;
  min-width: 50%;
}
.custom-sizes {
  :deep(.el-input__inner) {
    border-color: #C1C8D6;
    background: #fff;
    color: #202124;
    border-radius: 2px;
    font-size: 14px;
    width: 105px;
  }
}
</style>
