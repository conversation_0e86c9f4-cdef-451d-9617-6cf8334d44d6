<template>
  <!-- 需求状态流转 -->
  <div>
    <el-dropdown
      ref="dropdown"
      trigger="click"
      placement="bottom"
      :disabled="isDone || noPermission || infoDisabled"
      @command="handleCommand"
    >
      <a @click="findNextNode">
        <span
          class="tagCustom"
          :style="{
            border: `1px solid ${
              workitem.stateCode &&
              workitem.echoMap &&
              workitem.echoMap.stateCode
                ? workitem.echoMap.stateCode.color
                : '#ccc'
            }`,
            color: `${
              workitem.stateCode &&
              workitem.echoMap &&
              workitem.echoMap.stateCode
                ? workitem.echoMap.stateCode.color
                : '#ccc'
            }`,
          }"
        >
          <span>
            <span
              v-if="
                workitem.stateCode &&
                workitem.echoMap &&
                workitem.echoMap.stateCode
              "
              >{{ workitem.echoMap.stateCode.name }}</span
            >
            <span v-else>
              {{ workitem.stateCode }}
            </span>
          </span>

          <i v-if="onSearch" class="el-icon-loading" />
          <i v-else class="iconfont el-icon-direction-down el-icon--right" />
        </span>
      </a>
      <el-dropdown-menu slot="dropdown" class="change-status">
        <el-dropdown-item
          v-for="item in nextNode"
          :key="item.id"
          :command="item.stateCode"
        >
          <span
            class="tagItem"
            :style="{
              border: `1px solid ${
                item.stateCode && item.echoMap && item.echoMap.stateCode
                  ? item.echoMap.stateCode.color
                  : '#ccc'
              }`,
              color: `${
                item.stateCode && item.echoMap && item.echoMap.stateCode
                  ? item.echoMap.stateCode.color
                  : '#ccc'
              }`,
            }"
          >
            {{ item.name }}
          </span>
        </el-dropdown-item>
        <el-dropdown-item v-if="nextNode.length > 0" command="workFlow" divided>
          <span style="width: 100%; text-align: center">
            <!-- <i class="iconfont el-icon-gongzuoliu" style="color:#3E7BFA;margin-right:4px;" /> -->
            查看工作流
          </span>
        </el-dropdown-item>
        <el-dropdown-item command="progress" divided>
          <span style="width: 100%; text-align: center">
            <!-- <i class="iconfont el-icon-gongzuoliu" style="color:#3E7BFA;margin-right:4px;" /> -->
            进度
          </span>
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <el-dialog
      title="工作流"
      :visible.sync="flowVisible"
      top="7vh"
      append-to-body
      @before-close="closeFlow"
    >
      <vone-work-flow
        ref="vone-g"
        :xml="xml"
        :show-bar="false"
        hide-text-annotation
        single
        :node-style="nodeStyle"
        :show-progress="showProgress"
        :properties-props="{ width: 250 }"
      />
      <div slot="footer">
        <el-button type="primary" @click="closeFlow">确认</el-button>
      </div>
    </el-dialog>

    <requiredFields
      v-if="requireParam.visible"
      v-bind="requireParam"
      :visible.sync="requireParam.visible"
      @success="$emit('changeFlow')"
    />
  </div>
</template>

<script>
import {
  apiAlmFindNextNode,
  apiAlmStateChange,
} from "@/api/vone/project/issue";
import { jsonToXml } from "@/views/vone/base/project-config/tab/common/xmlUtils";
import {
  getFlow,
  findTransitionHistoryByIdType,
} from "@/api/vone/base/work-flow";
import requiredFields from "@/components/CustomEdit/components/require-fields.vue";

export default {
  components: {
    requiredFields,
  },
  props: {
    workitem: {
      type: Object,
      default: () => {},
    },
    infoDisabled: {
      type: Boolean,
      default: false,
    },
    noPermission: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      nextNode: [],
      onSearch: false,
      isDone: false,
      flowVisible: false,
      showProgress: true,
      requireParam: { visible: false },
      workItemInfo: {},
      xml: null,
      nodeStyle: {
        width: 100,
        height: 36,
      },
      flowId: null,
      currentNode: {},
      spendTimes: [],
    };
  },
  watch: {
    workitem: {
      handler: function (val) {
        this.workItemInfo = JSON.parse(JSON.stringify(this.workitem));
      },
      immediate: true,
    },
  },
  mounted() {
    this.isDone = this.workItemInfo && this.workItemInfo.stateCode == "DONE";
  },
  methods: {
    closeFlow() {
      this.flowVisible = false;
    },
    async getTransitionHistory() {
      const params = {
        bizType: this.workItemInfo?.echoMap?.typeCode?.classify?.code,
      };
      const res = await findTransitionHistoryByIdType(
        this.workItemInfo?.id,
        params
      );
      if (res.isSuccess) {
        this.currentNode = res.data.currentNode;
        this.spendTimes = res.data.spendTimes;
      } else {
        // 测试数据 - 如果API失败，使用模拟数据
        this.currentNode = { nodeType: "DEVELOP" };
        this.spendTimes = [
          { nodeType: "ANALYSIS", spendTime: 120 },
          { nodeType: "DESIGN", spendTime: 300 },
          { nodeType: "DEVELOP", spendTime: 1800 },
          { nodeType: "TEST", spendTime: 600 },
        ];
      }
    },
    async getFlowInfo() {
      let nodes = [];
      let edges = [];
      // 查询任务工作流
      const res = await getFlow(this.flowId);
      if (res.isSuccess) {
        if (
          res.data.workflowNodes.length > 0 &&
          res.data.workflowTransitions.length > 0
        ) {
          this.workflowId = res.data.workflowNodes[0].workflowId;
          nodes = res.data.workflowNodes;
          edges = res.data.workflowTransitions;
          nodes.map((item) => {
            item.nodeType = item.nodeType.code;
            item.color = item.echoMap?.stateCode?.color;
            item.onlyId = item.id;
            item.id = item.code;
            if (this.showProgress) {
              item.showProgress = true;
              const spendTimeData = this.spendTimes.find(
                (r) => r.nodeType == item.stateCode
              );
              item.spendTime = spendTimeData ? spendTimeData.spendTime : 0;
              item.currentNode = this.currentNode.nodeType == item.stateCode;
            }
            delete item.shape;
            delete item.size;
            delete item.echoMap;
          });
          edges.map((item) => {
            item.onlyId = item.id;
            item.id = item.code;
            item.source = item.sourceAnchor;
            item.target = item.targetAnchor;
          });
          this.xml = jsonToXml({ nodes, edges });
        }
      }
    },
    async handleCommand(val) {
      if (val === "workFlow") {
        this.flowVisible = true;
        this.getFlowInfo();
        return;
      }
      if (val === "progress") {
        this.flowVisible = true;
        this.showProgress = true;
        await this.getTransitionHistory();
        await this.getFlowInfo();
        return;
      }
      if (this.nextNode.find((r) => r.stateCode == val).echoMap.check.length) {
        const check = this.nextNode.find((r) => r.stateCode == val).echoMap
          .check;
        const list = [];
        check.forEach((element) => {
          list.push(element.echoMap.field);
        });

        const targetName = this.nextNode.find((r) => r.stateCode == val).name;
        this.requireParam = {
          visible: true,
          infoData: this.workItemInfo,
          fields: list,
          sourceNode: this.workItemInfo.stateCode,
          sourceName: this.workItemInfo?.echoMap?.stateCode?.name,
          targetName: targetName,
          targetNode: val,
          typeClassfiy: this.workItemInfo?.echoMap?.typeCode?.classify?.code,
          dataId: this.workItemInfo.id || this.workItemInfo.bizId,
          key: Date.now(),
        };
      } else {
        const id = this.workItemInfo.id || this.workItemInfo.bizId;

        // 修改状态
        const flowUrlMap = {
          ISSUE: `/api/alm/alm/requirement/transitionState/${id}/${this.workItemInfo.stateCode}/${val}`,
          BUG: `/api/alm/alm/bug/transitionState/${id}/${this.workItemInfo.stateCode}/${val}`,
          TASK: `/api/alm/alm/task/transitionState/${id}/${this.workItemInfo.stateCode}/${val}`,
          RISK: `/api/alm/alm/risk/transitionState/${id}/${
            this.workItemInfo.stateCode
          }/${val}/${this.$route.params.projectKey ? "project" : "program"}`,
          IDEA: `/api/alm/alm/idea/transitionState/${id}/${this.workItemInfo.stateCode}/${val}`,
        };

        const classify = this.workItemInfo?.echoMap?.typeCode?.classify?.code;
        const res = await apiAlmStateChange(flowUrlMap[classify]);
        if (!res.isSuccess) {
          this.$message.warning(res.msg);
          return;
        }
        this.$message.success("状态流转成功");
        this.nextNode = [];
        this.$emit("changeFlow");
      }
    },
    async findNextNode() {
      try {
        if (this.noPermission || this.isDone || this.infoDisabled) {
          return;
        }
        this.onSearch = true;

        // 因为在迭代列表中需求id的字段名为bizId
        const id = this.workItemInfo.id || this.workItemInfo.bizId;
        const urlMap = {
          ISSUE: `/api/alm/alm/requirement/findNextNode/${id}`,
          BUG: `/api/alm/alm/bug/findNextNode/${id}`,
          TASK: `/api/alm/alm/task/findNextNode/${id}`,
          RISK: `/api/alm/alm/risk/findNextNode/${id}/${
            this.$route.params.projectKey ? "project" : "program"
          }`,
          IDEA: `/api/alm/alm/idea/findNextNode/${id}`,
        };
        const classify = this.workItemInfo?.echoMap?.typeCode?.classify?.code;
        const res = await apiAlmFindNextNode(urlMap[classify]);
        this.onSearch = false;
        if (!res.isSuccess) {
          this.$message.warning(res.msg);
          return;
        }

        if (!res.data.length) {
          this.$refs.dropdown.hide();
          this.$message.warning("暂无当前节点流转权限");
          this.isDone = true;
          return;
        }

        this.nextNode = res.data;

        this.flowId = res.data[0].workflowId;
      } catch (error) {
        this.onSearch = false;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.change-status {
  .el-dropdown-menu__item.el-dropdown-menu__item--divided:before {
    margin: 0;
  }
}
::v-deep .bpmn {
  height: calc(100vh - 270px);
}

// 自定义的tag样式，主要用在平台配置，配置项，状态
.tagItem {
  text-align: center;
  border-radius: 3px;
  padding: 4px 10px;
  // min-width: 80px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
