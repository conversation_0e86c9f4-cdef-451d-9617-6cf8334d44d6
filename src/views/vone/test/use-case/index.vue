<template>
  <page-wrapper>
    <!-- 卡片视图 -->
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic
          ref="searchForm"
          table-search-key="useCase-card"
          :model.sync="formData"
          :default-fileds.sync="defaultFileds"
          show-basic
          :extra.sync="extraData"
          @getTableData="getUseCaseList"
        />
      </template>
      <template slot="actions">
        <el-button type="primary" icon="iconfont el-icon-tips-plus-circle" :disabled="!$permission('testm_test_use_case_add')" @click="addUseCase">新增</el-button>
      </template>
      <template slot="fliter">
        <vone-search-filter
          :extra.sync="extraData"
          :model.sync="formData"
          :default-fileds.sync="defaultFileds"
          @getTableData="getUseCaseList"
        />
      </template>
    </vone-search-wrapper>
    <vone-cards ref="useCase-card" v-loading="loading" :data="tableData" :row-count="4" @updateData="getUseCaseList">
      <template slot-scope="{ row }">
        <a :style="{cursor: $permission('testm_case_tree_view') ? 'pointer':'not-allowed'}" @click="editById(row)">
          <vone-card :actions-num="2" :actions="rowActions">
            <template slot="title">
              <el-row type="flex" justify="space-between">
                <div class="title-box">
                  <div class="title" :title="row.name">{{ row.name }}</div>
                  <span>{{ row.key }}</span>
                </div>
                <el-tag class="statusTag">{{ row.num > 10000? parseInt((row.num) / 10000) + 'W+':row.num }}用例</el-tag>
              </el-row>
            </template>

            <svg v-if="row.type == 'scenario'" slot="icon" class="icon" aria-hidden="true">
              <use xlink:href="#el-icon-icon-ceshiguanli-copy" />
            </svg>
            <svg v-else slot="icon" class="icon" aria-hidden="true">
              <use xlink:href="#el-icon-vone-ceshiguanli" />
            </svg>

            <div slot="desc">
              <span>
                <vone-user-avatar :avatar-path="row.userData ? row.userData.avatarPath :''" :name="row.userData ?row.userData.name : ''" />
              </span>

            </div>
            <el-row>

              <div class="cardText">
                <vone-toolitip v-if="row.echoMap && row.echoMap.productInfos.length" :content="row.productInfos" label="关联产品" />
                <span v-else style="margin-top:10px;display: inline-block;">关联产品:</span>
              </div>

              <div class="cardText">
                <vone-toolitip :content="row.description" :label="'描述'" />
              </div>

            </el-row>

          </vone-card>

        </a>
      </template>
    </vone-cards>

    <!-- 新增编辑 -->
    <edit-dialog v-if="useCaseParam.visible" ref="forminfo" v-bind="useCaseParam" :visible.sync="useCaseParam.visible" @success="getUseCaseList" />

    <!-- 权限分配抽屉 -->
    <division v-if="divisionDrawerParam.visible" v-bind="divisionDrawerParam" :visible.sync="divisionDrawerParam.visible" />

  </page-wrapper>
</template>
<script>
import { apiTestUseCasePage, operationUseCase, getLibraryAllProductCase } from '@/api/vone/testmanage/case'
import EditDialog from './components/edit-dialog.vue'
import division from './division.vue'
import { getUserById } from '@/api/vone/base/user'
import { catchErr } from '@/utils'

export default {
  components: {
    EditDialog,
    division
  },
  data() {
    return {
      extraData: {},
      defaultFileds: [
        {
          key: 'name',
          name: '用例库名称',
          type: {
            code: 'INPUT'
          },
          placeholder: '请输入用例库名称'
        },
        {
          key: 'leadingBy',
          name: '负责人',
          type: {
            code: 'USER'
          },
          placeholder: '请选择负责人'
        }

      ],
      divisionDrawerParam: { visible: false },
      formData: {
        cardView: 'card'
      },
      loading: false,
      useCaseParam: {
        visible: false
      },
      tableData: {},
      userMap: {},
      rowActions: [
        {
          disabled: !this.$permission('testm_tese_useCase_edit'),
          text: '编辑基本信息',
          type: 'text',
          icon: 'iconfont el-icon-application-edit',
          onClick: ({ row }) => this.editBaseInfo(row)
        },
        {
          type: 'text',
          // disabled: !this.$permission('productift_org'),
          text: '权限分配',
          icon: 'iconfont el-icon-application-user-permission',
          onClick: ({ row }) => this.resDivision(row),
          disabled: !this.$permission('testm_auth_put')
        },
        {
          disabled: !this.$permission('testm_case_chart'),
          type: 'icon',
          text: '用例报表',
          icon: 'iconfont el-icon-baobiaofenxi',
          onClick: ({ row }) => this.toChart(row)
        },
        {
          disabled: !this.$permission('testm_useCase_tag_copy'),
          type: 'icon',
          text: '复制用例库',
          icon: 'iconfont el-icon-application-copy',
          onClick: ({ row }) => this.copyCaseLibrary(row)
        },
        {
          disabled: !this.$permission('testm_test_useCase_del'),
          text: '删除',
          type: 'text',
          icon: 'iconfont el-icon-application-delete',
          onClick: ({ row }) => this.deleteById(row)
        }
      ]
    }
  },

  methods: {
    // 用例报表
    toChart(row) {
      this.$router.push({
        name: 'testm_case_chart',
        params: {
          caseId: row.id
        },
        query: {
          caseName: row.name,
          caseType: row.type
        }
      })
    },
    // 查询用例数量
    async getCaseNum(tableMap) {
      const ids = Object.keys(tableMap)
      const [res, err] = await catchErr(getLibraryAllProductCase(ids))
      if (err) return
      if (res.isSuccess) {
        res.data.forEach(val => {
          const index = tableMap[val.id]
          if (index >= 0) {
            this.$set(this.tableData?.records[index], 'num', val.casesNumber)
          }
        })
      }
    },
    // 查询所有人
    async getUserList(userId, tableIdxs = []) {
      if (this.userMap[userId]) {
        tableIdxs.forEach(idx => {
          this.$set(this.tableData?.records[idx], 'userData', this.userMap[userId])
        })
        return
      }
      const [res, err] = await catchErr(getUserById(userId))
      if (err) return
      if (res.isSuccess) {
        this.userMap[userId] = res.data
        tableIdxs.forEach(idx => {
          this.$set(this.tableData?.records[idx], 'userData', this.userMap[userId])
        })
      }
    },
    // 编辑基本信息
    editBaseInfo(row) {
      this.useCaseParam = {
        visible: true,
        title: '编辑用例库',
        id: row.id,
        data: row
      }
    },
    // 复制用例库
    copyCaseLibrary(row) {
      this.useCaseParam = {
        visible: true,
        title: '复制用例库',
        id: row.id,
        tagCopy: true,
        data: row
      }
    },
    // 列表查询
    async getUseCaseList() {
      this.loading = true
      let params = {}
      const tableAttr = this.$refs['useCase-card'].exportTableQueryData()
      this.$set(tableAttr, 'sort', 'createTime')
      this.$set(tableAttr, 'order', 'descending')
      params = {
        ...tableAttr,
        extra: {
          ...this.extraData
        },
        model: { ...this.formData }
      }
      const [res, err] = await catchErr(apiTestUseCasePage(params))
      this.loading = false
      if (err) return
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      this.tableData = res.data

      // 保存表格相同用户index
      const tableUserMap = {}
      const tableNumMap = {}
      res.data.records.forEach((ele, i) => {
        // 设置管理产品
        ele.productInfos = ele.echoMap?.productInfos?.map(r => r.name).join(',') || ''
        // 查询人员数据
        if (ele.leadingBy) {
          const userIds = tableUserMap[ele.leadingBy] ?? []
          tableUserMap[ele.leadingBy] = [...userIds, i]
        }
        tableNumMap[ele.id] = i
      })
      for (const key in tableUserMap) {
        // 请求用户信息并更新表格
        this.getUserList(key, tableUserMap[key])
      }
      if (res.data.records.length === 0) return
      // 查询用例数
      this.getCaseNum(tableNumMap)
    },
    // 新增
    addUseCase() {
      this.useCaseParam = { visible: true, title: '新增用例库' }
    },
    // 修改
    editById(row) {
      // 当前角色无权限
      if (!this.$permission('testm_case_tree_view')) return

      const productInfoIds = row.echoMap?.productInfos?.map(r => r.id).join(',') || ''
      this.$router.push({
        name: 'testm_case_tree_view',
        params: {
          caseId: row.id,
          depth: row.depth || 'depth',
          productInfoIds: productInfoIds
        }
      })
    },
    // 删除
    async deleteById(row) {
      this.$confirm(`确定删除【${row.name}】吗?`, '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: 'delConfirm',
        showClose: false,
        type: 'warning'
      })
        .then(async(actions) => {
          const res = await operationUseCase(
            [row.id], 'delete'
          )
          if (!res.isSuccess) {
            return this.$message.error(res.msg)
          }
          this.getUseCaseList()
          this.$message.success('删除成功')
        })
        .catch(() => { })
    },
    // 权限分配
    resDivision(row) {
      this.divisionDrawerParam = { visible: true, id: row.id }
    }

  }
}
</script>

<style lang="scss" scoped>
.cardText {
  color: var(--auxiliary-font-color);
  font-size: 12px;
}
.iconImg {
  width: 32px;
  height: 32px;
}
.userText {
  height: 35px;
  line-height: 35px;
}
.cards-actions {
  display: flex;
  justify-content: flex-end;
  width: 100%;
}
// 状态tag样式
.statusTag {
  min-width: 66px;
  height: 26px;
  line-height: 26px;
  z-index: 100;
  margin-right: -16px;
  text-align: center;
  border-radius: 4px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  font-size: 12px;
  color: var(--sub-btn-hover-color);
  background: var(--hover-bg-color);
  border-color: var(--app-active-color);
}

</style>
