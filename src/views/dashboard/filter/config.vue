<template>
  <page-wrapper>
    <vone-back :title="filterTitle" @back="back">
      <template v-slot:toolbar>
        <div v-if="showBtn">
          <el-button :loading="loading" @click="() => preview()"
            >预览</el-button
          >
          <el-button type="primary" :loading="saveLoading" @click="saveFilter"
            >保存</el-button
          >
        </div>
      </template>
    </vone-back>
    <div class="filter">
      <el-collapse v-model="activeNames" class="filter-collapse">
        <el-collapse-item
          v-for="item in workItemConditions"
          :key="item.value"
          :name="item.value"
        >
          <template v-slot:title>
            <div class="collapse-title">
              <el-icon class="iconfont" style="margin-right: 8px"
                ><el-icon-direction-right
              /></el-icon>
              <span class="name">{{ item.title }}</span>

              <el-tooltip
                class="tooltipIcon"
                effect="dark"
                content="选择版本和功能模块数据时，请确保已选择关联产品"
                placement="top-start"
              >
                <el-icon><el-icon-warning-outline /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <div>
            <common-conditions
              v-if="item.value == 'WORK_ITEM'"
              ref="WORK_ITEM"
              :detail="detail"
              @addItem="addItem"
              @changeProject="changeProject"
            />

            <item-conditions
              v-else
              :ref="item.value"
              :type="item.type"
              :detail="detail"
              :choose-project="chooseProject"
              :filter-data="item.filterData && item.filterData.conditions"
            />
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
    <el-divider />
    <preview-table
      ref="previewTable"
      :table-data="tableData"
      :loading="loading"
      @preview="preview"
    />
    <!-- </div> -->
  </page-wrapper>
</template>

<script>
import {
  DirectionRight as ElIconDirectionRight,
  WarningOutline as ElIconWarningOutline,
} from "@element-plus/icons-vue";
import CommonConditions from "./common-conditions.vue";
import ItemConditions from "./item-conditions.vue";
import PreviewTable from "./preview-table.vue";
import {
  previewData,
  opeartionFilter,
  queryDetail,
} from "@/api/vone/dashboard/filter";
import { cloneDeep } from "lodash";
export default {
  components: {
    CommonConditions,
    ItemConditions,
    PreviewTable,
    ElIconDirectionRight,
    ElIconWarningOutline,
  },
  data() {
    return {
      activeNames: ["WORK_ITEM"],
      workItemConditions: [{ value: "WORK_ITEM", title: "公共属性" }],
      tableData: {},
      loading: false,
      detail: {},
      saveLoading: false,
      showBtn: this.$route.query?.edit,
      chooseProject: [],
      filterTitle: "",
    };
  },
  mounted() {
    this.getFilterDetail();
  },
  methods: {
    back() {
      const type = this.$route.query?.type || null;
      const path = type ? `/filter/view?type=${type}` : `/filter/view$`;
      this.$router.push(path);
    },
    getFilterDetail() {
      queryDetail(this.$route.params.id).then((res) => {
        this.detail = res.data;

        this.filterTitle = res.data.name;
        const searchParams = JSON.parse(res.data.search);
        const list = searchParams?.WORK_ITEM?.conditions[0].conditions || [];
        this.chooseProject = list.find((r) => r.field == "projectId")?.value;

        if (searchParams) {
          Object.keys(searchParams).length && this.preview(searchParams);
        }
      });
    },
    getConditions() {
      const commonConditions = this.$refs.WORK_ITEM[0].getCommonAttrValue();
      const conditionsParams = {
        WORK_ITEM: {
          conditions: [
            {
              conditions: commonConditions,
            },
          ],
        },
      };
      const unCommonData = this.workItemConditions.filter(
        (item) => item.value != "WORK_ITEM"
      );
      unCommonData.map((item) => {
        conditionsParams[item.value] = cloneDeep(
          this.$refs[item.value][0].conditionGroup
        );
      });
      // 处理自定义工作项的选项值
      for (const i in conditionsParams) {
        if (i != "WORK_ITEM") {
          conditionsParams[i]?.conditions?.forEach((condItem) => {
            // 将选中的值添加到selectData中
            condItem?.conditions?.forEach((item) => {
              if (item?.selectData && item?.selectKey && item?.tableName) {
                item.selectData = item.selectData?.filter((itm) =>
                  item?.value?.includes(itm[item.selectKey])
                );
              }
            });
          });
        }
      }
      return conditionsParams;
    },
    preview(data) {
      const conditionsParams = this.getConditions();
      this.loading = true;
      const pageObj = this.$refs.previewTable.getTablePage();
      const paramsData = data || conditionsParams;
      const sortObj = this.$refs.previewTable.getSort();
      const params = {
        ...pageObj,
        ...sortObj,
        extra: {},
        model: {
          ...paramsData,
        },
      };
      previewData(params)
        .then((res) => {
          this.loading = false;
          if (res.isSuccess) {
            this.tableData = res.data;
          } else {
            this.$message.warning(res.msg);
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    addItem(arr) {
      const initWorkItem = [{ value: "WORK_ITEM", title: "公共属性" }];
      initWorkItem.push.apply(initWorkItem, arr);
      this.workItemConditions = initWorkItem;
    },
    async saveFilter() {
      const conditionsParams = await this.getConditions();
      this.detail.search = JSON.stringify(conditionsParams);
      this.saveLoading = true;
      opeartionFilter(this.detail, "put")
        .then((res) => {
          this.saveLoading = false;
          if (res.isSuccess) {
            this.$message.success("保存成功");
          } else {
            this.$message.warning(res.msg);
          }
        })
        .catch(() => {
          this.saveLoading = false;
        });
    },
    changeProject(val) {
      this.chooseProject = val;
    },
  },
};
</script>

<style lang="scss" scoped>
.filter {
  margin-top: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 0 16px;
  .filter-collapse {
    margin-bottom: 0;
    :deep(.el-collapse-item:last-child) {
      margin-bottom: 0;
      .el-collapse-item__header,
      .el-collapse-item__wrap {
        border-bottom: none;
      }
    }
    :deep(.el-collapse-item__) {
      &header {
        padding: 0;
        &:hover {
          color: var(--font-main-color);
          background-color: #fff !important;
        }
      }
      &content {
        padding: 0 0;
      }
    }
    .collapse-title {
      user-select: none;
      .iconfont {
        color: var(--font-second-color);
      }
      .active {
        transform: rotate(90deg);
      }
      .name {
        font-weight: 500;
        font-size: 14px;
        color: var(--font-main-color);
      }
    }
    :deep(.el-collapse-item__arrow) {
      display: none;
    }
  }
}
.el-divider--horizontal {
  margin: 16px 0;
}
.tooltipIcon {
  margin-left: 6px;
}
</style>
