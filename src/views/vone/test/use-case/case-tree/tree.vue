<template>
  <div>
    <div v-loading="leftLoading" class="custom-tree">
      <!-- 默认用例树 -->
      <el-tree
        v-show="data.length == 0"
        ref="treeGroupCase"
        node-key="id"
        draggable
        :load="loadNode"
        lazy
        :expand-on-click-node="false"
        :current-node-key="currentCasekey"
        :highlight-current="true"
        :filter-node-method="filterNode"
        :allow-drop="collapse"
        :props="defaultProps"
        @node-click="treeCaseClick"
        @node-drop="handleDrop"
        @node-expand="treeExpand"
        @node-collapse="treeCaseClick"
      >
        <template v-slot="{ node, data }">
          <span class="custom-tree-node draggable-node" style="width: 100%">
            <span>
              <svg v-if="data.name == '未分组'" class="icon" aria-hidden="true">
                <use xlink:href="#el-icon-icon-wenjianjia-weifenzu" />
              </svg>

              <svg v-else-if="node.level !== 1" class="icon" aria-hidden="true">
                <use xlink:href="#el-icon-wenjianjia2" />
              </svg>
              <span :class="{ rootNode: node.level == 1 }" class="treeNode">{{
                node.label
              }}</span>

              <span v-if="data.name !== '未分组'">
                <span v-if="node.level == 1"
                  >({{ num ? num : data.num || 0 }})</span
                >
                <span v-else>({{ data.num || 0 }})</span>
              </span>
            </span>

            <template v-if="data.name !== '未分组'">
              <span class="operation-icon">
                <el-tooltip content="新增" placement="top">
                  <el-button
                    type="text"
                    size="mini"
                    :disabled="
                      !$permission('testm_project_group_add') ||
                      node.level >= depth
                    "
                    :icon="elIconTipsPlusCircle"
                    @click="addNode(data)"
                  />
                </el-tooltip>

                <el-tooltip
                  v-if="node.level == 1"
                  content="编辑"
                  placement="top"
                >
                  <el-button
                    type="text"
                    size="mini"
                    :disabled="!$permission('testm_project_test_group_edit')"
                    :icon="elIconApplicationEdit"
                    @click="allCase(data)"
                  />
                </el-tooltip>

                <el-tooltip
                  v-if="node.level != 1"
                  content="重命名"
                  placement="top"
                >
                  <el-button
                    type="text"
                    size="mini"
                    :disabled="!$permission('testm_project_test_group_edit')"
                    :icon="elIconApplicationRename"
                    @click="editNode(data, node)"
                  />
                </el-tooltip>

                <el-tooltip
                  v-if="node.level != 1 && type !== 'graftTree'"
                  content="归档"
                  placement="top"
                >
                  <el-button
                    type="text"
                    size="mini"
                    :disabled="!$permission('project_case_manage_file')"
                    :icon="elIconApplicationArchive"
                    @click="caseArchive(data, node)"
                  />
                </el-tooltip>

                <el-tooltip
                  v-if="node.level != 1 && type !== 'graftTree'"
                  content="导出"
                  placement="top"
                >
                  <el-button
                    type="text"
                    size="mini"
                    :disabled="!$permission('testm_project_test_tree_export')"
                    :icon="elIconEditExport"
                    @click="exportTypeFile(data, node)"
                  />
                </el-tooltip>

                <el-tooltip
                  v-if="node.level != 1"
                  content="删除"
                  placement="top"
                >
                  <el-button
                    type="text"
                    size="mini"
                    :disabled="!$permission('testm_project_group_test_del')"
                    :icon="elIconApplicationDelete"
                    @click="removeNode(data, node)"
                  />
                </el-tooltip>
              </span>
            </template>
          </span>
        </template>
      </el-tree>
      <el-tree
        v-show="data.length > 0"
        ref="treeGroupCases"
        class="draggable-tree"
        node-key="id"
        draggable
        default-expand-all
        :data="data"
        :expand-on-click-node="false"
        :current-node-key="currentCasekey"
        :highlight-current="true"
        :filter-node-method="filterNode"
        :allow-drop="collapse"
        :props="defaultProps"
        @node-click="treeCaseClick"
        @node-drop="handleDrop"
        @node-expand="treeExpand"
        @node-collapse="treeCaseClick"
      >
        <template v-slot="{ node, data }">
          <span class="custom-tree-node draggable-node" style="width: 100%">
            <span class="item">
              <svg v-if="data.name == '未分组'" class="icon" aria-hidden="true">
                <use xlink:href="#el-icon-icon-wenjianjia-weifenzu" />
              </svg>

              <svg v-else-if="node.level !== 1" class="icon" aria-hidden="true">
                <use xlink:href="#el-icon-wenjianjia2" />
              </svg>

              <span :class="{ rootNode: node.level == 1 }" class="treeNode">{{
                node.label
              }}</span>

              <span v-if="data.name !== '未分组'">({{ data.sum || 0 }})</span>
            </span>
            <span v-if="data.name !== '未分组'" class="operation-icon">
              <el-button
                type="text"
                size="mini"
                :icon="elIconTipsPlusCircle"
                :disabled="
                  !$permission('testm_case_tree_add') || node.level >= depth
                "
                @click.stop="() => addNode(data)"
              />
              <el-button
                v-if="node.level == 1"
                type="text"
                size="mini"
                :icon="elIconApplicationEdit"
                :disabled="!$permission('testm_product_tree_case_edit_batch')"
                @click.stop="() => allCase(data)"
              />
              <el-button
                v-if="node.level != 1"
                type="text"
                size="mini"
                :icon="elIconApplicationRename"
                :disabled="!$permission('testm_case_tree_put')"
                @click.stop="() => editNode(data, node)"
              />
              <el-button
                v-if="node.level != 1"
                type="text"
                size="mini"
                :icon="elIconApplicationDelete"
                :disabled="!$permission('case_tree_del')"
                @click.stop="() => removeNode(data, node)"
              />

              <el-button
                v-if="node.level != 1"
                type="text"
                size="mini"
                :icon="elIconApplicationArchive"
                :disabled="!$permission('case_tree_del')"
                @click.stop="() => caseArchive(data, node)"
              />
            </span>
          </span>
        </template>
      </el-tree>
    </div>

    <!-- 新增编辑分组树弹窗 -->
    <treeAdd
      v-model:value="menuVisible"
      :flag="nameFlag"
      :data="menuNode"
      :library-id="libraryId"
      @success="submitR(menuNode)"
      @close="() => {}"
    />

    <!-- 删除用例树节点 -->
    <delNodeDialog
      v-bind="delNodeParam"
      v-if="delNodeParam.visible"
      v-model:value="delNodeParam.visible"
      @success="delSave(delNodeParam)"
    />

    <!-- 批量修改 -->
    <allEdit
      v-if="editVisible"
      v-model:value="editVisible"
      :dialog-type="dialogType"
      :node-data="menuNode"
      :ids="ids"
      :edit="editType"
      :library-id="libraryIds"
      @success="$emit('success')"
    />
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from "../../../../../utils/gogocodeTransfer";

import { debounce } from "lodash";
import { list2Tree } from "@/utils/list2Tree";
import { catchErr } from "@/utils";

import {
  productTestCaseTreeSwap,
  queryAllCaseNumberOfDraftTree,
  getTreeByLibrary,
  queryAllCaseNumberOfTree,
  productCaseTree,
} from "@/api/vone/testmanage/case";
import { treeArchive } from "@/api/vone/testmanage";

import treeAdd from "./components/tree-add.vue";
import allEdit from "./all-edit.vue";
import delNodeDialog from "./components/del-node-dialog.vue";

export default {
  components: {
    treeAdd,
    allEdit,
    delNodeDialog,
  },
  props: {
    libraryId: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      num: "",
      data: [],
      getCountList: {},
      libraryIds: "",
      dialogType: "",
      editVisible: false, // 批量修改
      editType: "",
      depth: "",
      menuVisible: false,
      leftLoading: false, // 左侧树加载
      filterText: "",
      currentCasekey: "",
      defaultProps: {
        children: "children",
        label: "name",
        isLeaf: "leaf",
      },
      dataTree: [],
      nameFlag: "",
      nodeItem: {},
      menuNode: {}, // 选中节点数据
      formData: {},
      ids: [],
      delNodeParam: {
        visible: false,
      },
    };
  },
  watch: {
    filterText(val) {
      if (this.type == "graftTree") {
        this.$refs["treeGroupCase"].filter(val);
        return;
      }
      if (val == "") {
        this.data = [];
      } else {
        this.debouncedGettree();
      }
    },
    getCountList(val) {
      if (val) {
        this.setNum(val);
      }
    },
  },
  created() {
    this.depth = Number(this.$route.params.depth);
    this.getCount();
  },
  methods: {
    exportTypeFile(val, node) {
      $emit(this, "exportFile", val);
    },
    debouncedGettree: debounce(async function (query) {
      if (!this.filterText) {
        this.data = [];
        return;
      }
      this.leftLoading = true;
      try {
        const res = await productCaseTree({
          libraryId: this.$route.params.caseId || this.libraryId,
          name: this.filterText,
        });

        if (!res.isSuccess) {
          this.$message.warning(res.msg);
          return;
        }
        this.leftLoading = false;
        const tree = res.data?.tree;
        const count = res.data?.count;
        tree?.forEach((item) => {
          item.sum = count[item.id]?.[0].count;
        });
        this.data = tree ? list2Tree(tree, { parentKey: "parentId" }) : [];
      } catch (e) {
        this.leftLoading = false;
      }
    }, 1000),
    // 用例树归档
    async caseArchive(val, node) {
      const confirm = await catchErr(
        this.$confirm(`是否进行归档`, "提示", {
          type: "warning",
          beforeClose: async (action, instance, done) => {
            if (action === "confirm") {
              instance.confirmButtonLoading = true;
              instance.confirmButtonText = "执行中...";
              const res = await treeArchive(val.id);
              instance.confirmButtonLoading = false;
              if (!res.isSuccess) {
                return this.$message.error(res.msg);
              }
              done();
              this.$message.success(res.msg);
            } else {
              done();
            }
          },
        })
      );
      if (confirm[1]) return;

      this.refreshNode(node.data.parentId);
      this.getCount(true);
      $emit(this, "delNode");
    },

    // 正常树和草稿箱树获取用例数量
    async getCount(update) {
      const api =
        this.type == "caseTree"
          ? queryAllCaseNumberOfTree
          : queryAllCaseNumberOfDraftTree;

      const res = await api(this.$route.params.caseId || this.libraryId);
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      this.getCountList = res.data;
      if (update) {
        this.setNum(res.data);
      }
    },

    setNum(data) {
      const nodesMap = this.$refs.treeGroupCase?.store?.nodesMap;
      if (!nodesMap) return;
      this.num = 0;
      for (const i in nodesMap) {
        const cur = data[i];

        nodesMap[i].data["num"] = cur ? cur[0].count : 0;

        if (nodesMap[i].level == 1) {
          this.num = +nodesMap[i].data.num + this.num;
        }
      }
    },
    allCase(row) {
      this.menuNode = row;
      this.editType = "edit";
      this.libraryIds = this.$route.params.caseId || this.libraryId;
      this.editVisible = true;
    },
    treeExpand(data, node) {
      this.menuNode = data;

      this.$nextTick(() => {
        this.$refs.treeGroupCase?.setCurrentKey(data.id); // 树当前选中
        this.$refs.treeGroupCase?.getNode(data.id)?.expand(); // 手动展开
        $emit(this, "submit", this.menuNode);
      });

      if (node.level === 1) {
        const wrap = document.querySelector(".custom-tree");
        // 滚动到指定位置
        wrap?.scrollTo({
          top: 0,
          behavior: "smooth",
        });
      }
    },
    // 删除节点
    delSave(node) {
      this.refreshNode(node.node.parentId);
      this.getCount(true);
      $emit(this, "delNode");
    },
    // 新增和编辑节点
    submitR(node) {
      if (this.nameFlag == "add") {
        this.refreshNode(node.id);
      } else {
        this.refreshNode(node.parentId);
      }
    },
    refreshNode(id) {
      const node = this.$refs.treeGroupCase.getNode(id);
      node.loaded = false;

      node.expand(); // 主动调用展开节点方法,重新查询该节点下的所有子节点
    },
    // 默认显示节点树懒加载方法
    async loadNode(node, resolve) {
      // 节点id
      const rootId = node.level == 0 ? 0 : node.data.id;

      const res = await getTreeByLibrary(
        this.$route.params.caseId || this.libraryId,
        rootId
      );

      if (node.level === 0) {
        // 设置默认节点
        this.menuNode = res.data?.[0];
        this.nodeItem = res.data?.[0];
        this.currentCasekey = res.data?.[0]?.id;

        this.$nextTick(() => {
          this.$refs.treeGroupCase?.setCurrentKey(this.currentCasekey); // 树当前选中
          this.$refs.treeGroupCase?.getNode(this.currentCasekey)?.expand(); // 手动展开
          $emit(this, "submit", this.menuNode);
        });
      }

      if (node.level === 1) {
        res.data.unshift({
          id: 0,
          name: "未分组",
          leaf: true,
          treeId: "",
        });
      }
      if (this.getCountList) {
        res.data.map((v, w) => {
          v.num = this.getCountList[v.id]?.[0].count;
        });
      }

      resolve(res.data);
      // 加载拖拽方法
      this.$nextTick(() => {
        // const className = document.querySelectorAll('.draggable-node')
        // this.$emit('treeDrop', className)
      });
    },
    getTreeNode() {
      return document.querySelectorAll(".draggable-node");
    },
    collapse(draggingNode, dropNode, type) {
      if (dropNode.level == 1) {
        return type == "inner";
      } else if (draggingNode.data.leaf) {
        return false;
      } else if (dropNode.data.leaf) {
        return false;
      } else {
        return true;
      }
    },
    async handleDrop(draggingNode, dropNode, dropType, ev) {
      var obj = {
        orgId: draggingNode.data.id,
        parentId:
          dropType == "before" ? dropNode.data.parentId : dropNode.data.id,
        preOrgId: this.getChange(
          dropNode.parent.childNodes,
          draggingNode.data.id
        ),
      };
      const res = await productTestCaseTreeSwap(obj).catch((e) => {
        if (dropType == "before") {
          this.refreshNode(dropNode.data.parentId);
          this.refreshNode(draggingNode.data.parentId);
        } else if (dropType == "inner") {
          this.refreshNode(dropNode.data.id);
          this.refreshNode(draggingNode.data.parentId);
        }
        // this.refreshNode()
      });
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
      } else {
        this.getCount(true);
        this.$message.success("移动成功");
      }
    },
    getChange(arr, id) {
      var proOrgId = null;
      arr.forEach((item, i) => {
        if (item.data.id == id && i > 0) {
          proOrgId = arr[i - 1].data.id;
        }
      });
      return proOrgId;
    },

    // 模糊搜索定位节点
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    // 新增子级节点
    addNode(data) {
      this.menuNode = data;
      this.menuVisible = true;
      this.nameFlag = "add";
    },
    // 编辑节点
    async editNode(data, node) {
      this.menuNode = data;
      this.menuVisible = true;
      this.nameFlag = "edit";
    },
    // 删除节点
    async removeNode(data) {
      this.delNodeParam = { visible: true, node: data };
    },

    // 点击节点查询数据
    treeCaseClick(data) {
      this.menuNode = data;
      this.$nextTick(() => {
        this.$refs.treeGroupCase?.setCurrentKey(data.id); // 树当前选中
        $emit(this, "submit", data);
      });
    },
    // 新增左侧分组
    addMenu() {
      this.menuVisible = true;
      this.nameFlag = "add";
    },
  },
  emits: ["success", "exportFile", "submit", "delNode"],
};
</script>

<style lang="scss" scoped>
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
:deep(.el-tree-node__content) {
  overflow: auto;
  overflow-x: auto;
}
.custom-tree {
  height: calc(100vh - 275px);
  overflow-y: auto;
  width: fit-content;
  min-width: 330px;
  overflow-x: auto;
  white-space: nowrap;
}
:deep(.operation-icon) {
  opacity: 0;
  padding-left: 10px;
  .is-disabled {
    color: var(--placeholder-color);
  }
}
:deep(.el-tree-node__content:hover) {
  .operation-icon {
    opacity: 1;
  }
}
.treeNode {
  margin-left: 3px;
}
</style>
