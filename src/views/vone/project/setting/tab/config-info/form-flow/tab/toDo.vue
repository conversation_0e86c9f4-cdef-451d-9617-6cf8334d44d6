<template>
  <div class="pagecontent">
    <section>
      <strong>
        表单修改
      </strong>
      <el-table v-loading="todoLoading" :data="todoData" class="vone-table mt10" :show-header="false" style="width: 100%">
        <el-table-column prop="name" label="字段" width="90" align="center" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>
              <el-dropdown v-model="scope.row.name" trigger="click" @command="changeProject($event, scope.row)">
                <el-row type="flex">
                  <a>
                    <span v-if="scope.row.name">
                      <span class="mainText">
                        {{ scope.row.name }}
                      </span>

                    </span>
                    <span v-else class="defaultColor">
                      <span> 请选择 </span>
                      <i class="iconfont el-icon-direction-down el-icon--right" />
                    </span>
                  </a>
                </el-row>
                <el-dropdown-menu slot="dropdown">
                  <template v-if="options.length">
                    <el-dropdown-item v-for="item in options" :key="item.id" :command="item.key" :disabled="item.disabled">
                      {{ item.name }}
                    </el-dropdown-item>
                  </template>
                  <vone-empty v-else desc="无可选字段" />
                </el-dropdown-menu>
              </el-dropdown>
            <!-- <el-select v-model="scope.row.name" placeholder="请选择">
              <el-option
                v-for="item in options"
                :key="item.id"
                :label="item.name"
                :value="item.key"
              />
            </el-select> -->
            </span>

          </template>

        </el-table-column>
        <el-table-column prop="value" label="值" align="left">

          <template slot-scope="scope">

            <!-- 项目人员组件 -->
            <projectRemoteUser v-if="scope.row.eleType == 'PROJECTUSER'" v-model="scope.row.value" :multiple="scope.row.multiple" />

            <!-- 人员组件 -->
            <vone-remote-user v-else-if="scope.row.eleType == 'USER'" v-model="scope.row.value" :multiple="scope.row.multiple" />

            <!-- 数 -->
            <el-input-number v-else-if="scope.row.eleType == 'INT'" v-model="scope.row.value" :min="0.1" :max="1000" :precision="scope.row.precision" controls-position="right" style="width:100%" :placeholder="scope.row.placeholder" />

            <!-- 日期组件 -->
            <el-date-picker v-else-if="scope.row.eleType == 'DATE'" v-model="scope.row.value" prefix-icon="el-icon-date" type="datetime" format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm:ss" :default-time="`${scope.row.defaultTime}:00`" :placeholder="scope.row.placeholder" style="width:100%" />
            <!-- 输入框 -->
            <el-input v-if="scope.row.eleType == 'INPUT'" v-model="scope.row.value" :placeholder="scope.row.placeholder" />
            <!-- 组织机构 -->
            <vone-tree-select v-else-if="scope.row.eleType == 'ORG'" v-model="scope.row.value" search-nested :tree-data="orgData" placeholder="请选择机构" :multiple="scope.row.multiple" />

            <!-- 输入文本框 -->
            <el-input v-else-if="scope.row.eleType == 'TEXTAREA'" v-model="scope.row.value" type="textarea" :placeholder="scope.row.placeholder" maxlength="200" show-word-limit />

            <span v-else-if="scope.row.eleType == 'SELECT'">
              <!-- 下拉单选框 -->
              <span v-if="!scope.row.multiple">
                <el-select v-model="scope.row.value" :placeholder="scope.row.placeholder" clearable filterable @focus="setOptionWidth">
                  <el-option v-for="i in scope.row.options" :key="i.id" :label="i.name" :value=" scope.row.key == 'priorityCode' ? i.code : i.id" :style="{ width: selectOptionWidth }">
                    <span v-if="scope.row.key == 'ideaId'">{{ `${i.code}  ${i.name}` }}</span>

                    <span v-if="scope.row.key == 'productId'">
                      {{ i.name }}
                      <!-- <span v-if="i.echoMap" style="float:right">
                        <el-tag v-if="i.echoMap.isHost" type="success">
                          主
                        </el-tag>
                        <el-tag v-if="i.echoMap.isHost == false" type="warning">
                          辅
                        </el-tag>
                      </span> -->

                    </span>
                  </el-option>
                </el-select>
              </span>
              <!-- 下拉多选框 -->
              <span v-else-if="scope.row.multiple">

                <!-- 标签 -->
                <tagSelect v-if="scope.row.key == 'tagId'" v-model="scope.row.value" multiple />

                <el-select v-else v-model="scope.row.value" :placeholder="scope.row.placeholder" multiple clearable filterable @focus="setOptionWidth">
                  <el-option v-for="i in scope.row.options" :key="i.id" :label="i.name" :value="i.name" :style="{ width: selectOptionWidth }" />
                </el-select>
              </span>
            </span>

            <dataSelect v-else-if="scope.row.eleType == 'LINKED'" text-info="edit" :model="scope.row.value" :config="scope.row" :placeholder="scope.row.placeholder" @change="dataSelectChange($event,scope.row.key)" />
            <div v-else-if="scope.row.eleType == 'QUOTE'">
              <vone-remote-user v-if="scope.row.quoteType === 'user'" v-model="scope.row.value" disabled />
              <el-input v-else v-model="scope.row.value" disabled placeholder="" />
            </div>

          </template>

        </el-table-column>
        <el-table-column label="操作" width="40">
          <template slot-scope="scope">
            <a>
              <i class="iconfont el-icon-application-delete" @click="del(scope.row)" />
            </a>
          </template>
        </el-table-column>

      </el-table>
      <el-row type="flex" class="rowAdd">
        <a @click="addRow">
          <i class="el-icon-plus" />
          添加条件
        </a>
      </el-row>
    </section>
    <el-divider v-if="typeClassify !='BUG' && typeClassify !='RISK' && isEndNode" />
    <section v-if="typeClassify !='BUG' && typeClassify !='RISK' && isEndNode">
      <strong>
        状态流转
      </strong>
      <el-checkbox-group v-model="checkboxState" class="mt10">
        <div class="checkboxItem">
          <el-checkbox label="PARENT">
            <span class="labelText">

              当{{ typeClassify == 'ISSUE'? '子需求' : '子任务' }}全部完成时，自动将{{ typeClassify == 'ISSUE'? '父需求' : '父任务' }}流转到完成状态
            </span>
          </el-checkbox>
        </div>
        <div class="checkboxItem">
          <el-checkbox label="CHILDREN">
            当{{ typeClassify == 'ISSUE'? '父需求' : '父任务' }}完成时，自动将所有{{ typeClassify == 'ISSUE'? '子需求' : '子任务' }}流转到完成状态
          </el-checkbox>
        </div>
      </el-checkbox-group>

    </section>
  </div>

</template>

<script>
import {
  apiAlmPriorityNoPage,
  getAllProductInfoList
} from '@/api/vone/alm/index'
import { requirementListByCondition } from '@/api/vone/project/issue'
import { bugListByCondition } from '@/api/vone/project/defect'
import {
  queryListByCondition
} from '@/api/vone/project/index'
import { planListByCondition } from '@/api/vone/project/iteration'
import { getProjectPlans } from '@/api/vone/testmanage/case'
import { getTodoFields } from '@/api/vone/alm'
import projectRemoteUser from '@/components/CustomEdit/components/project-user-remote.vue'

import tagSelect from '@/components/CustomEdit/components/tag-select'
export default {
  components: {
    projectRemoteUser,
    tagSelect
  },
  props: {
    formFields: {
      type: Array,
      default: () => []
    },
    lineId: {
      type: String,
      default: ''
    },
    typeClassify: {
      type: String,
      default: undefined
    },
    typeCode: {
      type: String,
      default: undefined
    },
    activeName: {
      type: String,
      default: undefined
    },
    isEndNode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      checkboxState: [],
      todoLoading: false,
      customForm: {},
      orgData: [],
      selectOptionWidth: '',
      todoData: [],
      options: []
      // checkIdMap: []
    }
  },
  watch: {
    lineId: {
      handler(val) {
        if (val) {
          this.getLineTodo(val)
        }
      },
      immediate: true
    },
    activeName: {
      handler(val) {
        if (val) {
          this.getLineTodo(this.lineId)
        }
      },
      immediate: true
    }

  },
  mounted() {
    // 只能是这五种类型
    const type = ['SELECT', 'PROJECTUSER', 'DATE', 'INT', 'USER']
    // 不包括这4个字段
    const code = ['sourceCode', 'stateCode', 'typeCode', 'tagId']

    const typeFilter = this.formFields.filter((item) => {
      return type.indexOf(item.type) != -1
    })

    const codeFilter = typeFilter.filter((item) => {
      return code.indexOf(item.key) == -1
    })
    this.options = codeFilter
  },
  methods: {
    getIsBuitOptions(e) {
      const options = JSON.parse(this.formFields.find(r => r.key == e).config).options

      this.todoData.forEach((i) => {
        if (i.key == e) {
          i.options = options
        }
      })
      // this.$forceUpdate()
    },

    // 下拉框弹出时，设置弹框的宽度
    setOptionWidth(event) {
      this.$nextTick(() => {
        this.selectOptionWidth =
          event.target.parentNode.parentNode.clientWidth + 'px'
      })
    },
    changeProject(e, row) {
      const rowData = this.options.find(r => r.key == e)

      row.value = ''
      row.name = rowData.name
      row.eleType = rowData.type
      row.key = e
      const isBuiltOption = JSON.parse(rowData.config)?.options
      row.options = rowData.isBuilt ? rowData.options : isBuiltOption
      row.defaultTime = JSON.parse(rowData.config)?.defaultTime
      row.placeholder = JSON.parse(rowData.config)?.placeholder
      row.multiple = JSON.parse(rowData.config)?.multiple
      row.message = JSON.parse(rowData.config)?.message
      row.validator = JSON.parse(rowData.config)?.validator
      row.precision = JSON.parse(rowData.config)?.precision

      this.getOptions(e, row)
    },
    getOptions(e, row) {
      if (e == 'priorityCode') {
        this.getPrioritList(row)
      } else if (e == 'projectId') {
        this.getProjectList(row)
      } else if (e == 'planId') {
        this.getPlanList(row)
      } else if (e == 'productId') {
        this.getProjectProductList(row)
      } else if (e == 'testPlanId') {
        this.getProjectTestPlan(row)
      } else if (e == 'requirementId') {
        this.getRequirementList(row)
      } else if (e == 'bugId') {
        this.getBugList(row)
      }
    },

    async getLineTodo(val) {
      this.todoLoading = true
      try {
        const { data, isSuccess, msg } = await getTodoFields({
          projectId: this.$route.params.id,
          transitionId: val,
          // type: 'UPDATE_FIELD',
          typeClassify: this.typeClassify,
          typeCode: this.typeCode
        })
        this.todoLoading = false
        if (!isSuccess) {
          this.$message.warning(msg)
          return
        }

        data.forEach(element => {
          element.name = JSON.parse(element.config).name
          element.value = JSON.parse(element.config).value
          element.eleType = JSON.parse(element.config).eleType
          element.key = JSON.parse(element.config).key
          element.target = JSON.parse(element.config).target
          element.type = element.type?.code
          if (!element.isBuilt && element.eleType == 'SELECT') {
            const options = JSON.parse(this.formFields.find(r => r.key == element.key).config).options

            element.options = options
          }

          this.getOptions(element.key, element)
        })

        const hasKey = data.map(r => r.key)
        this.options.forEach(element => {
          element.disabled = !!hasKey.includes(element.key)
        })

        this.todoData = data.filter(r => r.type == 'UPDATE_FIELD')
        this.checkboxState = data.filter(r => r.type == 'UPDATE_STATE').map(j => j.target)
      } catch (e) {
        this.todoLoading = false
      }
    },
    addRow() {
      this.todoData.push({
        options: []
      })
      const hasKey = this.todoData.map(r => r.key)
      this.options.forEach(element => {
        element.disabled = !!hasKey.includes(element.key)
      })
    },
    async del(row) {
      // if (!row.id) {
      this.todoData.splice(this.todoData.findIndex(i => i.key === row.key), 1)
      //   return
      // }
      // try {
      //   await this.$confirm(`是否删除当前数据?`, '提示', {
      //     type: 'warning'
      //   })
      //   this.todoLoading = true
      //   const res = await afterHandlerDel([row.id])
      //   this.todoLoading = false
      //   if (!res.isSuccess) {
      //     this.$message.error(res.msg)
      //     return
      //   }
      //   this.$message.success('删除成功')
      //   this.getLineTodo(this.lineId)
      // } catch (e) {
      //   this.todoLoading = false
      // }
    },

    // 查优先级
    async getPrioritList(row) {
      const res = await apiAlmPriorityNoPage()

      if (!res.isSuccess) {
        return
      }
      row.options = res.data
    },
    // 归属项目
    async getProjectList(row) {
      const res = await queryListByCondition()
      if (!res.isSuccess) {
        return
      }
      row.options = res.data
    },
    // 迭代计划
    async getPlanList(row) {
      // this.$set(this.form, 'planId', '')
      const res = await planListByCondition({
        projectId: this.$route.params.id
      })
      if (!res.isSuccess) {
        return
      }
      row.options = res.data
    },
    // 查询项目关联的产品，用于缺陷和任务表单关联产品【主办/辅办】
    async getProjectProductList(row) {
      const res = await getAllProductInfoList(this.$route.params.id || '0')
      if (!res.isSuccess) {
        return
      }
      row.options = res.data
    },
    // 测试计划
    async getProjectTestPlan(row) {
      const res = await getProjectPlans({
        projectId: this.$route.params.id || '0'
      })
      if (!res.isSuccess) {
        return
      }
      row.options = res.data
    },
    // 查项目下需求
    async getRequirementList(row) {
      const res = await requirementListByCondition({
        projectId: this.$route.params.id
      })
      if (!res.isSuccess) {
        return
      }
      row.options = res.data
    },
    // 查项目下缺陷
    async getBugList(row) {
      const res = await bugListByCondition({
        projectId: this.$route.params.id
      })
      if (!res.isSuccess) {
        return
      }

      row.options = res.data
    }
  }

}
</script>

<style lang="scss" scoped>
.rowAdd {
  padding: 16px;
  margin-bottom: 30px;
  a {
    color: var(--main-theme-color);
    font-weight: 500;
  }
}
.defaultColor {
  color: var(--placeholder-color);
}
.mainText {
  color: var(--main-font-color);
}
:deep(.el-table .cell) {
  padding: 0 8rem;
}
.checkboxItem{
  background-color: #F7F7F7;
  padding: 12px;
  margin-bottom: 10px;
  border-radius: 4px;
  border: 1px solid #D6DAE0;
  // word-wrap: break-word;

  :deep(.el-checkbox) {
  width: 320px;
}
 :deep(.el-checkbox .el-checkbox__label) {
  white-space: normal;
  font-weight: 400;
  // color: #2C2E36;
}
:deep(.el-checkbox) {
  display: flex;
  align-items: center;
}

}
.pagecontent{
  width: 380px;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 0 10px;
}

.mt10 {
  margin-top: 10px;

}

.vone-table a{
  color: #777F8E;
  &:hover{
  color: var(--main-theme-color);
  }
}

</style>
