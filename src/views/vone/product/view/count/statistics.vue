<template>
  <vone-echarts-card :title="'统计'" class="dashboard_card">
    <el-row style="width: 100%; height: 100%">
      <!-- <el-col :span="6" style="padding-right:5px">
            <div style="background:#BD7FFA; height:100%">
              <h1 class="title_l">{{ data.applicationCount }}</h1>
              <h3 class="title_m">服务应用</h3>
            </div>
          </el-col> -->
      <el-col :span="8" style="padding-right: 5px">
        <div style="background: #fc9772">
          <h1 class="title_l">{{ data.moduleCount }}</h1>
          <h3 class="title_m">模块</h3>
        </div>
      </el-col>
      <el-col :span="8" style="padding-right: 5px">
        <div style="background: #8791fa">
          <h1 class="title_l">{{ data.functionCount }}</h1>
          <h3 class="title_m">功能点</h3>
        </div>
      </el-col>
      <el-col :span="8" style="padding-right: 5px">
        <div style="background: #4bccbb">
          <h1 class="title_l">{{ num }}</h1>
          <h3 class="title_m">项目数</h3>
        </div>
      </el-col>
    </el-row>
  </vone-echarts-card>
</template>

<script>
import { getcount } from '@/api/vone/product/index'
export default {
  props: {
    count: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      data: {},
      num: 0,
    }
  },
  watch: {
    count: function (val) {
      if (val) {
        this.num = val
      }
    },
  },
  mounted() {
    this.getCount()
  },
  methods: {
    async getCount() {
      await getcount({
        productId: this.$route.params.productId,
      }).then((res) => {
        if (res.isSuccess) {
          this.data = res.data
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.dashboard_card {
  height: 100%;
  div {
    color: #fff;
    text-align: center;
    border-radius: 6px;
    position: relative;
  }
  .title_l {
    font-size: 28px;
    line-height: 52px;
    min-height: 102px;
  }
  .title_m {
    font-size: 15px;
    height: 40px;
    background: rgba(255, 255, 255, 0.14);
    line-height: 40px;
  }
}
</style>
