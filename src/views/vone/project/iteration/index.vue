<template>
  <div class="sectionPageContent">
    <div class="leftSection">
      <div class="header">
        <span>迭代</span>
        <el-button
          :loading="tableLoading"
          :disabled="!$permission('project_iteration_export')"
          @click="exportFlie"
          size="mini"
          style="margin-top: 8px"
          >导出</el-button
        >
        <div class="search">
          <span v-if="type == 'fileTree'" class="operation">
            <el-popover
              v-model="searchPopover"
              placement="bottom-start"
              width="400"
              popper-class="table-search-form"
              trigger="click"
            >
              <div class="search-main">
                <el-form
                  ref="searchForm"
                  class="search-form"
                  inline
                  :model="formData"
                  label-position="top"
                >
                  <el-row :gutter="16">
                    <el-col :span="12">
                      <el-form-item label="迭代名称" prop="name">
                        <el-input
                          v-model="formData.name"
                          placeholder="请输入"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="负责人" prop="leadingBy">
                        <vone-remote-user v-model:value="formData.leadingBy" />
                      </el-form-item>
                    </el-col>
                    <el-col>
                      <el-form-item label="迭代时间" prop="date">
                        <el-date-picker
                          v-model="formData.date"
                          :append-to-body="false"
                          type="daterange"
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                          value-format="yyyy-MM-dd"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
                <div class="footerSearch">
                  <el-button plain @click="cancel">重置</el-button>
                  <el-button type="primary" @click="searchData">确定</el-button>
                </div>
              </div>

              <template v-slot:reference>
                <a>
                  <el-icon class="iconfont"
                    ><el-icon-application-filter
                  /></el-icon>
                </a>
              </template>
            </el-popover>

            <el-icon class="iconfont"
              ><el-icon-application-step-setting
            /></el-icon>
          </span>
          <el-tooltip v-else effect="dark" content="新增迭代" placement="top">
            <el-icon class="iconfont addIcon"><el-icon-tips-plus /></el-icon>
          </el-tooltip>
        </div>
      </div>
      <div class="cardContent">
        <el-tabs
          v-model="type"
          class="vone-tab-line small-tab"
          @tab-click="cancel"
        >
          <el-tab-pane
            v-for="item in types"
            :key="item.key"
            :label="item.tab"
            :name="item.key"
            :disabled="item.disabled"
          />
        </el-tabs>
        <div
          v-for="(item, index) in type == 'fileTree'
            ? planList.filter((v) => v.id != -1)
            : planList"
          :key="index"
        >
          <el-card
            shadow="hover"
            :class="[
              'left-small-card',
              currentNodekey === item.id ? 'is-active' : '',
            ]"
            style="margin-top: 16px"
            @click="sendId(item)"
          >
            <!-- <div class="small-card-title">
                  <div class="small-card-title-left">
                    <div class="svg-icon">
                      <svg-icon icon-class="dict" />
                    </div>
                    <span>{{ item.label }}<span class="count" />{{ item.dictionaryNum }}</span>
                  </div>
                </div>
                <div class="small-card-desc">
                  {{ item.desc && item.desc.length > 15 ? item.desc.slice(0, 15) + "..." : item.desc }}
                </div> -->
            <el-row type="flex" justify="space-between" align="middle">
              <span class="text-over">{{ item.name }}</span>
              <span v-if="type == 'fileTree' && item.id != -1">
                <el-tooltip effect="dark" content="撤销归档" placement="top">
                  <el-icon class="iconfont" style="color: #838a99"
                    ><el-icon-yibiaopan-chexiao
                  /></el-icon>
                </el-tooltip>
              </span>
              <el-dropdown
                v-else-if="item.id != -1"
                trigger="click"
                style="cursor: pointer"
              >
                <el-icon class="iconfont more"
                  ><el-icon-icon-gengduo
                /></el-icon>
                <template v-slot:dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      :icon="elIconApplicationEdit"
                      :disabled="!$permission('project_iteration_edit')"
                      @click="editPlan(item)"
                      >编辑</el-dropdown-item
                    >
                    <el-dropdown-item
                      :icon="elIconApplicationArchive"
                      :disabled="!$permission('project_iteration_del')"
                      @click="fileDel(item)"
                      >归档</el-dropdown-item
                    >
                    <el-dropdown-item
                      :icon="elIconEditRelate"
                      @click="version(item)"
                      >关联版本</el-dropdown-item
                    >
                    <el-dropdown-item
                      :icon="elIconApplicationDelete"
                      :disabled="!$permission('project_iteration_del')"
                      @click="removeDel(item)"
                      >删除</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </el-row>
            <el-row v-if="item.id != -1" class="row-flex">
              <el-tooltip
                effect="dark"
                :content="item.description"
                placement="top"
              >
                <div class="text-over left-content">{{ item.description }}</div>
              </el-tooltip>
              <div v-if="type == 'fileTree' && item.id != -1" class="dropItem">
                <div
                  class="stateBlock"
                  :style="{
                    color: `${item.stateColor}`,
                    background: `${item.backGroundColor}`,
                  }"
                >
                  {{ stateMap[item.stateCode].name || "" }}
                </div>
              </div>
              <el-dropdown v-else-if="item.id != -1" trigger="click">
                <a class="el-dropdown-link dropItem">
                  <div
                    class="stateBlock"
                    :style="{
                      color: `${item.stateColor}`,
                      background: `${item.backGroundColor}`,
                    }"
                  >
                    {{ stateMap[item.stateCode].name || "" }}
                  </div>
                  <div
                    class="stateBlockIcon"
                    :style="{
                      color: `${item.stateColor}`,
                      background: `${item.backGroundColor}`,
                    }"
                  >
                    <el-icon class="iconfont"
                      ><el-icon-direction-down
                    /></el-icon>
                  </div>
                </a>
                <template v-slot:dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      :disabled="
                        item.stateCode == 1 ||
                        !$permission('project_iteration_edit')
                      "
                      @click="changeStatus(item, 1)"
                      >未开始</el-dropdown-item
                    >
                    <el-dropdown-item
                      :disabled="
                        item.stateCode == 2 ||
                        !$permission('project_iteration_edit')
                      "
                      @click="changeStatus(item, 2)"
                      >进行中</el-dropdown-item
                    >
                    <el-dropdown-item
                      :disabled="
                        item.stateCode == 3 ||
                        !$permission('project_iteration_edit')
                      "
                      @click="changeStatus(item, 3)"
                      >已完成
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </el-row>

            <el-row v-if="item.id != -1" class="row-flex">
              <vone-user-avatar
                v-if="item.echoMap && item.echoMap.leadingBy"
                :avatar-path="item.echoMap.leadingBy.avatarPath"
                :name="item.echoMap.leadingBy.name"
                class="left-content text-over"
              />
              <span v-if="item.planStime && item.planEtime">
                <small class="it_item_time">
                  {{ dayjs(item.planStime).format("MM-DD") }} ~
                  {{ dayjs(item.planEtime).format("MM-DD") }}
                </small>
              </span>
            </el-row>
          </el-card>
        </div>
      </div>
    </div>
    <div class="rightSection">
      <planTable
        ref="planTable"
        :key="componentKey"
        :current-nodekey="currentNodekey"
        :type="type"
        :iteration-detail="iterationDetail"
        @showInfo="showInfo"
        @showAdd="showAdd"
        @cancleConnect="cancleConnect"
        @connectPlan="connectPlan"
      />
    </div>
    <!-- 新增 -->
    <vone-custom-add
      v-bind="addParam"
      ref="vone-custom-add"
      v-if="addParam.visible"
      v-model:value="addParam.visible"
      @initList="getTableData"
      :type-code="globalTypeCode || addParam.typeCode"
      @success="saveChildSuccess"
    />

    <!-- 编辑 -->
    <vone-custom-edit
      v-bind="editParam"
      v-if="editParam.visible"
      ref="vone-custom-edit"
      v-model:value="editParam.visible"
      @success="getTableData"
      @initList="getTableData"
      @add-child="addChild"
    />

    <!-- 详情 -->
    <vone-custom-info
      v-bind="infoParam"
      v-if="infoParam.visible"
      ref="vone-custom-info"
      v-model:value="infoParam.visible"
      @success="getTableData"
    />

    <!-- 关联迭代 -->
    <el-dialog
      title="关联迭代"
      width="30%"
      v-model:value="dialogFormVisible"
      :close-on-click-modal="false"
      :before-close="onClose"
    >
      <el-form ref="planForm" :model="planForm">
        <el-form-item label="迭代" prop="id">
          <el-select
            v-model="planForm.id"
            style="width: 100%"
            placeholder="请选择迭代"
          >
            <el-option
              v-for="(item, index) in newPlanList"
              :key="index"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <div class="info">
          <el-icon class="iconfont"
            ><el-icon-tips-exclamation-circle
          /></el-icon>
          任务，缺陷不能创建子任务
        </div>

        <el-form-item prop="createType">
          <el-checkbox-group v-model="planForm.createType">
            <el-row>
              <el-col :span="24">
                <el-checkbox label="自动创建前端任务" />
              </el-col>
              <el-col :span="24">
                <el-checkbox label="自动创建后端任务" />
              </el-col>
              <!-- <el-col :span="24">
                    <el-checkbox label="自动创建测试任务" />
                  </el-col> -->
            </el-row>
          </el-checkbox-group>
        </el-form-item>
      </el-form>

      <template v-slot:footer>
        <div class="dialog-footer">
          <el-button @click="onClose">取消</el-button>
          <el-button type="primary" :loading="saveLoading" @click="sureAdd"
            >确定</el-button
          >
        </div>
      </template>
    </el-dialog>

    <!-- 新增编辑迭代对话框 -->
    <add-plan-dialog
      v-bind="addPlanParam"
      v-if="addPlanParam.visible"
      v-model:value="addPlanParam.visible"
      @success="getTestPlanList"
    />

    <fileDialog
      v-bind="file"
      v-if="file.visible"
      v-model:value="file.visible"
    />
    <versionDialog
      v-bind="versionParam"
      v-if="versionParam.visible"
      v-model:value="versionParam.visible"
    />
  </div>
</template>

<script>
import * as dayjs from "dayjs";
import {
  apiAlmPlanCancleSprint,
  apiAlmPlanLinkedSprint,
  apiAlmProjectPlanAdd,
  apiAlmProjectPlanDel,
  apiAlmProjectPlanNoPage,
  filed,
  unarchive,
} from "@/api/vone/project/iteration";

import planTable from "./components/planTable.vue";
import addPlanDialog from "./components/add-plan-dialog.vue";
import fileDialog from "./components/file-dialog.vue";
import versionDialog from "./components/version-dialog.vue";
import { download } from "@/utils";
import { apiBaseFileLoad } from "@/api/vone/base/file";
import { apiAlmGetInfo } from "@/api/vone/project/issue";
import { apiAlmTaskInfo } from "@/api/vone/project/task";

const stateList = [
  {
    state: 1,
    name: "未开始",
    color: "#ADB0B8",
    backGroundColor: "#f0f0f0",
  },
  {
    state: 2,
    name: "进行中",
    color: "#63BDF9",
    backGroundColor: "#e6f7ff",
  },
  {
    state: 3,
    name: "已完成",
    color: "#3CB540",
    backGroundColor: "#d7fada",
  },
  {
    state: 4,
    name: "已归档",
    color: "#F56C6C",
    backGroundColor: "#fab7b7",
  },
];

export default {
  components: {
    planTable,
    addPlanDialog,
    fileDialog,
    versionDialog,
  },
  data() {
    return {
      tableLoading: false,
      componentKey: null,
      stateList,
      stateMap: {},
      searchPopover: "",
      formData: {
        date: [],
      },
      type: "iteration",
      types: [
        {
          key: "iteration",
          tab: "迭代",
        },
        {
          key: "fileTree",
          tab: "已归档",
        },
      ],

      planForm: {
        createType: [],
      },
      saveLoading: false,
      dialogFormVisible: false,
      tableData: {},
      file: { visible: false }, // 配置归档
      addPlanParam: { visible: false },
      versionParam: { visible: false },
      typeCode: "",
      title: "",
      addParam: { visible: false }, // 新增
      editParam: { visible: false }, // 编辑
      infoParam: { visible: false }, // 详情
      pageName: "未规划事项",
      originalTree: [],
      treeData: [],
      listLoading: false,
      defaultList: [
        {
          id: "-1",
          name: "未规划事项",
        },
      ],
      planList: [], // 计划列表
      newPlanList: [],
      currentNodekey: "",
      isLarge: false,
      currentIteraId: "-1", // 当前迭代ID，传值给需求/任务/缺陷，用于默认回显当前迭代
      iterationDetail: {
        id: "-1",
      },
      leftTabsMap: {
        ISSUE: [
          {
            label: "需求",
            name: "IssueToIssue",
          },
          {
            label: "关联任务",
            name: "TaskTab",
          },
          {
            label: "关联缺陷",
            name: "BugTab",
          },
          {
            label: "关联代码",
            name: "DevelopTab",
          },
          {
            label: "测试用例",
            name: "TestCase",
          },
        ],
        BUG: [
          {
            label: "关联缺陷",
            name: "DefectToDefect",
          },
        ],
        TASK: [
          {
            label: "任务",
            name: "TaskTask",
          },
        ],
      },
      globalTypeCode: undefined,
    };
  },
  created() {
    this.getTestPlanList();
  },
  mounted() {
    this.stateMap = this.stateList.reduce((r, v) => (r[v.state] = v) && r, {});
  },
  methods: {
    format_filter(val) {
      if (!val) return "";
      return dayjs(val).format("MM月DD日");
    },
    saveChildSuccess() {
      this.getTableData();
      this.$refs["vone-custom-edit"]?.$refs[
        "IssueToIssue"
      ]?.[0]?.getChildrenTable();
      this.$refs["vone-custom-edit"]?.$refs[
        "TaskTask"
      ]?.[0]?.getChildrenTable();
      this.$refs["vone-custom-edit"]?.$refs["TaskTab"]?.[0]?.getTableData();
      this.$refs["vone-custom-edit"]?.$refs["BugTab"]?.[0]?.getTableData();
    },
    addChild(type) {
      console.log("this.editParam.typeCode", this.editParam.typeCode);

      // 需求下的拆分
      if (this.editParam.typeCode === "ISSUE") {
        if (type == "issue") {
          this.globalTypeCode = "ISSUE";
          this.addParam = {
            visible: true,
            key: Date.now(),
            infoDisabled: false,
            title: "新增需求",
          };
        } else if (type == "task") {
          this.globalTypeCode = "TASK";
          this.addParam = {
            visible: true,
            key: Date.now(),
            infoDisabled: false,
            title: "新增任务",
          };
        } else if (type == "bug") {
          this.globalTypeCode = "BUG";
          this.addParam = {
            visible: true,
            key: Date.now(),
            infoDisabled: false,
            title: "新增缺陷",
          };
        }
        setTimeout(async () => {
          // 获取需求详情
          const res = await apiAlmGetInfo(
            `/api/alm/alm/requirement/${
              this.infoParam.visible ? this.infoParam.id : this.editParam.id
            }`
          );

          // 基本信息赋值
          const fixdForm = this.$refs["vone-custom-add"]?.fixdForm;
          fixdForm["name"] = res.data.name;
          fixdForm["planEtime"] = res.data.planEtime;
          fixdForm["description"] = res.data.description;

          // 自定义组件集合
          const customList = this.$refs["vone-custom-add"].customList || [];
          // 基本属性赋值
          const form = this.$refs["vone-custom-add"]?.form;
          if (type === "issue") {
            form["parentId"] = res.data.id;
            form["sourceCode"] = res.data.sourceCode;
            form["planStime"] = res.data.planStime;
            form["planId"] = res.data.planId;
            form["productVersionId"] = res.data.productVersionId;
            form["productModuleFunctionId"] = res.data.productModuleFunctionId;
          } else if (type === "task") {
            form["sourceCode"] = customList.find(
              (item) => item.key === "sourceCode"
            )?.options?.[0]["code"];
            form["planStime"] = res.data.planStime;
            form["planId"] = res.data.planId;
            form["productVersionId"] = res.data.productVersionId;
            form["productRepairVersionId"] = res.data.productRepairVersionId;
            form["requirementId"] = res.data.id;
            form["c4"] = res.data.putBy;
          } else if (type === "bug") {
            form["requirementId"] = res.data.id;
            form["planStime"] = res.data.planStime;
            form["planId"] = res.data.planId;
            form["sourceCode"] = customList.find(
              (item) => item.key === "sourceCode"
            )?.options?.[0]["code"];
            form["envCode"] = customList.find(
              (item) => item.key === "envCode"
            )?.options?.[0]["id"];
          }
        }, 500);
      }
      // TASK下的拆分
      else if (this.editParam.typeCode === "TASK") {
        this.globalTypeCode = "TASK";
        this.addParam = {
          visible: true,
          key: Date.now(),
          infoDisabled: false,
          title: "新增任务",
        };

        setTimeout(async () => {
          // 获取任务详情
          const res = await apiAlmTaskInfo(
            this.infoParam.visible ? this.infoParam.id : this.editParam.id
          );

          // 基本信息赋值
          const fixdForm = this.$refs["vone-custom-add"]?.fixdForm;
          fixdForm["name"] = res.data.name;
          fixdForm["typeCode"] = res.data.typeCode;
          fixdForm["handleBy"] = res.data.handleBy;
          fixdForm["planEtime"] = res.data.planEtime;
          fixdForm["description"] = res.data.description;

          // 自定义组件集合
          const customList = this.$refs["vone-custom-add"].customList || [];
          // 基本属性赋值
          const form = this.$refs["vone-custom-add"]?.form;
          form["parentId"] = res.data.id;
          form["sourceCode"] = res.data.sourceCode;
          form["priorityCode"] = res.data.priorityCode;
          form["leadingBy"] = res.data.leadingBy;
          form["requirementId"] = res.data.requirementId;
          form["planStime"] = res.data.planStime;
          form["bugId"] = res.data.bugId;
          form["productId"] = res.data.productId;
          form["projectId"] = res.data.projectId;
          form["planId"] = res.data.planId;
          form["c4"] = res.data.putBy;
        }, 500);
      }
    },
    async fileDel(val) {
      if (val.stateCode == "1" || val.stateCode == "2") {
        this.$message.error("当前迭代还未完成，无法进行归档，请在完成后进行");
        return;
      }
      await this.$confirm(`是否进行归档`, "提示", {
        type: "warning",
      });

      const res = await filed(val.id);
      if (!res.isSuccess) {
        return this.$message.error(res.msg);
      }
      this.$message.success(res.msg);
      this.getTestPlanList();
    },
    // 筛选
    searchData() {
      this.getTestPlanList();
    },
    clickTab(type) {
      this.type = type;
      if (type == "fileTree") {
        this.cancel();
      } else {
        this.sendId({
          id: "-1",
          name: "未规划事项",
        });
      }
    },
    // 重置
    cancel() {
      this.formData = {};
      this.getTestPlanList();
      this.componentKey = JSON.stringify(new Date());
    },
    onClose() {
      this.dialogFormVisible = false;
      this.$refs.planForm.resetFields();
    },
    // 新增页判断
    showAdd(command) {
      const mapList = [
        {
          id: 1,
          typeCode: "ISSUE",
          title: "新增需求",
        },
        {
          id: 2,
          typeCode: "BUG",
          title: "新增缺陷",
        },
        {
          id: 3,
          typeCode: "TASK",
          title: "新增任务",
        },
      ];

      const map = mapList.reduce((r, v) => (r[v.id] = v) && r, {});
      this.addParam = {
        visible: true,
        sprintId: this.currentIteraId,
        key: Date.now(),
        typeCode: map[command].typeCode,
        title: map[command].title,
      };
    },
    async callFile(val) {
      try {
        await this.$confirm("确定撤销归档吗？", "提示", {
          type: "warning",
        });
      } catch (e) {
        return;
      }
      const res = await unarchive(val.id);
      if (res.isSuccess) {
        this.$message.success("撤销成功");

        this.getTestPlanList();
      }
    },
    // 列表标题点击弹出【编辑或者详情页】
    showInfo(row) {
      // 获取分类,判断当前数据属于需求/任务/缺陷
      const type = row.echoMap.typeCode.classify.code;

      const titleMap = {
        ISSUE: "编辑需求",
        BUG: "编辑缺陷",
        TASK: "编辑任务",
      };

      const infoMap = {
        ISSUE: "需求详情",
        BUG: "缺陷详情",
        TASK: "任务详情",
      };

      // 已归档tab页里的的数据只能查看详情
      if (this.type == "fileTree") {
        this.infoParam = {
          visible: true,
          title: infoMap[type],
          typeCode: type,
          id: row.bizId,
          key: Date.now(),
          infoDisabled: false,
          rowTypeCode: row.typeCode,
          stateCode: row.stateCode,
          leftTabs: this.leftTabsMap[row.classify?.code],
          rightTabs: [
            {
              active: true,
              label: "评论",
              name: "comment",
            },
            {
              label: "活动",
              name: "active",
            },
            {
              label: "变更记录",
              name: "activityRecord",
            },
          ],
        };
        return;
      }
      this.editParam = {
        visible: true,
        title: titleMap[type],
        typeCode: type,
        id: row.bizId,
        key: Date.now(),
        infoDisabled: false,
        rowTypeCode: row.typeCode,
        stateCode: row.stateCode,
        // 拆分子项是否弹框
        showPopupForSplit: true,
        leftTabs: this.leftTabsMap[row.classify?.code],
        rightTabs: [
          {
            active: true,
            label: "评论",
            name: "comment",
          },
          {
            label: "活动",
            name: "active",
          },
          {
            label: "变更记录",
            name: "activityRecord",
          },
          {
            label: "工时",
            name: "workTime",
          },
        ],
      };
    },
    // 查询迭代计划
    async getTestPlanList() {
      this.currentNodekey = "";
      this.componentKey = JSON.stringify(new Date());
      this.listLoading = true;
      const res = await apiAlmProjectPlanNoPage({
        projectId: this.$route.params.id,
        type: "SPRINT",
        isFiled: this.type == "fileTree",
        planEtime: this.formData.date ? this.formData.date[1] : "",
        planStime: this.formData?.date ? this.formData?.date[0] : "",
        name: this.formData.name,
        leadingBy: this.formData.leadingBy,
      });
      this.listLoading = false;
      if (!res.isSuccess) {
        return;
      }

      res.data.forEach((element) => {
        element.stateColor = this.stateMap[element.stateCode]?.color || "#ccc";
        element.backGroundColor =
          this.stateMap[element.stateCode]?.backGroundColor || "#fff";
      });
      this.planList = this.defaultList.concat(res.data);
      this.newPlanList = res.data;
      // 数据处理,如果当前迭代有进行中的,默认选中第一个进行中的数据,没有则选中未规划事项
      if (this.type == "iteration") {
        const hasIng = this.planList.filter((r) => r.stateCode == 2).length
          ? this.planList.filter((r) => r.stateCode == 2)[0]
          : null;
        if (hasIng) {
          this.currentNodekey = hasIng?.id;
          this.pageName = hasIng.name;
          this.iterationDetail = hasIng;
          this.currentIteraId = hasIng?.id;
          this.$refs.planTable?.getPlanCaseList(this.currentNodekey);
        } else {
          this.currentNodekey = this.planList[0]?.id;
          this.pageName = "未规划事项";
          this.iterationDetail = this.planList[0];
          this.$refs.planTable?.getPlanCaseList(this.currentNodekey);
        }
      }
      if (this.type == "fileTree") {
        if (this.newPlanList.length < 1) return;
        this.currentNodekey = this.newPlanList && this.newPlanList[0]?.id;
        this.iterationDetail = this.newPlanList[0];
        this.$nextTick(() => {
          this.$refs.planTable?.getPlanCaseList(this.currentNodekey);
        });
      }
    },
    // 配置归档
    confFile() {
      this.file = { visible: true };
    },
    // 新增迭代
    addPlan() {
      this.addPlanParam = { visible: true };
    },
    // 编辑迭代
    editPlan(row) {
      this.addPlanParam = { visible: true, id: row.id };
    },
    // 关联版本
    version(row) {
      this.versionParam = {
        visible: true,
        id: row.id,
        productVersionId: row.productVersionId,
      };
    },
    // 删除迭代
    async removeDel(item) {
      await this.$confirm(`确定删除【${item.name}】吗`, "删除", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        customClass: "delConfirm",
        showClose: false,
        type: "warning",
      });

      this.loading = true;
      const { isSuccess, msg } = await apiAlmProjectPlanDel([item.id]);
      if (!isSuccess) {
        this.loading = false;
        this.$message.error(msg);
        return;
      }
      this.$message.success("删除成功");
      this.getTestPlanList();
    },
    // 关联迭代
    connectPlan(val) {
      const tableData = val;
      if (!tableData.length) {
        this.$message.warning("请选择要关联迭代的需求/任务/缺陷");
        return;
      }
      this.dialogFormVisible = true;
    },
    // 查询计划下需求/任务/缺陷
    sendId(val) {
      if (val.id == this.currentNodekey) {
        return;
      }
      // if (val.id == '-1') {
      //   this.currentIteraId = ''
      // } else {
      this.currentIteraId = val.id;
      // }

      // if(val.id == '-1'){

      // }

      this.pageName = val.name;
      this.currentNodekey = val.id;
      this.iterationDetail = val;
      this.$refs.planTable.getPlanCaseList(val.id);
    },
    // 修改计划状态
    async changeStatus(item, key) {
      item["stateCode"] = key;
      // 是否锁定，未开始不锁定
      item["enable"] = key == 1;
      const { isSuccess, msg } = await apiAlmProjectPlanAdd({
        ...item,
      });
      if (!isSuccess) {
        this.$message.warning(msg);
        this.saveLoading = false;
        return;
      }
      this.saveLoading = false;
      this.$message.success("修改迭代状态成功");
      this.getTestPlanList();

      // await this.sendId(item)
    },
    // 刷新列表
    getTableData() {
      this.$refs.planTable.getPlanCaseList(this.currentNodekey);
    },
    // 关联迭代--保存
    async sureAdd() {
      const tableData = this.$refs.planTable.selecteTableData;

      const objMap = {
        自动创建前端任务: 1,
        自动创建后端任务: 2,
      };

      const params = tableData.map((r) => ({
        createTypes: this.planForm.createType.length
          ? this.planForm.createType.map((r) => objMap[r])
          : [],
        issueId: r.bizId,
        planId: this.planForm.id,
        typeClassify:
          r.typeCode && r.echoMap && r.echoMap.typeCode
            ? r.echoMap.typeCode.classify.code
            : "",
      }));

      try {
        this.saveLoading = true;
        const res = await apiAlmPlanLinkedSprint(params);
        this.saveLoading = false;
        if (!res.isSuccess) {
          this.$message.warning(res.msg);
          return;
        }
        this.onClose();
        this.$message.success("关联迭代成功");
        this.getTableData();
      } catch (e) {
        this.saveLoading = false;
      }
    },
    // 取消关联迭代
    async cancleConnect(val) {
      const tableData = val;
      if (!tableData.length) {
        this.$message.warning("请选择要取消关联迭代的需求/任务/缺陷");
        return;
      }

      await this.$confirm(`确定取消关联的迭代吗?`, "提示", {
        type: "warning",
        closeOnClickModal: false,
      });

      const params = tableData.map((r) => ({
        issueId: r.bizId,
        planId: this.currentNodekey,
        typeClassify:
          r.typeCode && r.echoMap && r.echoMap.typeCode
            ? r.echoMap.typeCode.classify.code
            : "",
      }));

      const res = await apiAlmPlanCancleSprint(params);
      this.saveLoading = false;
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      this.$message.success("取消关联迭代成功");
      this.getTestPlanList();
    },
    // 导出
    async exportFlie() {
      try {
        this.tableLoading = true;

        download(
          `迭代信息.xlsx`,
          await apiBaseFileLoad(
            `/api/alm/project/plan/excel/export/${this.$route.params.id}`
          )
        );

        this.tableLoading = false;
      } catch (e) {
        this.tableLoading = false;
        return;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@use "@/styles/variables.scss" as *;
.leftSection {
  width: 260px;
  .header {
    padding: 0px 16px;
    height: 48px;
    line-height: 48px;
    border-bottom: 1px solid var(--solid-border-color);
    display: flex;
    span {
      color: var(--font-main-color);
      font-size: 16px;
      font-weight: 500;
      flex: 1;
    }
    .search {
      display: inline-block;
      .iconfont {
        cursor: pointer;
        color: var(--font-second-color);
        margin-left: 8px;
      }
      .iconfont:hover {
        color: var(--main-theme-color);
      }
      .iconfont.active {
        color: var(--main-theme-color);
      }
    }
  }
  .cardContent {
    padding: 0px 16px;
    width: 100%;
    height: calc(
      100vh - #{$nav-top-height} - #{$main-margin} - #{$main-margin} - 57px
    );
    overflow-y: overlay;
    span {
      display: flex;
      align-items: center;
    }
    .count {
      height: 12px;
      width: 1px;
      background: var(--solid-border-color);
      margin: 0px 4px;
      display: inline-block;
    }
  }
}
.menubox {
  border-bottom: 1px solid var(--solid-border-color);
  padding: 0 16px;
  margin: 0px -16px;
  a {
    margin: 0px -16px;
    line-height: 36px;
    display: block;
    padding: 0px 28px;
    color: var(--font-main-color);
    &:hover {
      background: var(--content-bg-hover-color);
    }
  }
  .is-active {
    background-color: var(--main-hover-page-color);
    color: var(--main-theme-color);
  }
}
.left-small-card {
  height: unset;
  .el-row {
    margin-top: 8px;
  }
  .el-row:first-child {
    margin-top: 0px;
  }
}
.stateBlock {
  text-align: center;
  font-weight: 500;
  border-radius: 2px 0 0 2px;
  padding: 0px 6px;
}
.stateBlockIcon {
  text-align: center;
  font-weight: 500;
  border-radius: 2px 0 0 2px;
  padding: 0px 4px;
  border-left: 1px solid var(--main-bg-color);
  i {
    font-size: 12px;
  }
}
.dropItem {
  display: flex;
  font-size: 12px;
  line-height: 18px;
}
.it_item_time {
  color: var(--font-second-color);
}
.small-tab {
  margin-top: 8px;
  :deep(.el-tabs__item) {
    height: 36px;
    line-height: 36px;
  }
  :deep(.el-tabs__item.is-top:nth-child(2)) {
    margin-left: 0px;
  }
}
.more {
  color: var(--font-second-color);
  &:hover {
    color: var(--main-theme-color);
  }
}
.row-flex {
  display: flex;
  align-items: center;
  .left-content {
    flex: 1;
  }
}
.footerSearch {
  text-align: right;
  padding: 0 15px 15px;
}
</style>
