<!-- 信息卡片 -->
<template>
  <div class="infoCard">
    <div v-loading="loading" class="linkCls">
      <!-- 骨架屏加载 -->
      <el-skeleton class="skelenton" :loading="loading">
        <!-- 加载时遮挡 -->
        <template slot="template">
          <div style="display: flex; align-items: center; justify-items: space-between;">
            <el-skeleton-item variant="image" style="width: 60px; height: 60px;" />
            <div style="width:calc(100% - 60px); padding-left: 4px;">
              <el-skeleton-item variant="h3" style="width: 30%;" />
              <el-skeleton-item variant="text" style="width: 80%;" />
            </div>
          </div>
          <div style="margin: 6px 0;">
            <el-skeleton-item variant="h3" style="width: 50%;" />
            <el-skeleton-item variant="image" style="width: 100%; height: 80px;" />
            <div style="display: flex; align-items: center; justify-items: space-between; margin-top: 6px; height: 16px;">
              <el-skeleton-item variant="text" style="margin-right: 16px;" />
              <el-skeleton-item variant="text" style="width: 30%;" />
            </div>
          </div>
        </template>
        <!-- 单个卡片 -->
        <vone-card class="cardItem" :actions-num="3" :split-menu="true" :prop-row="infoData" :actions="actions">
          <template v-slot:icon>
            <div class="codeType">
              <svg v-if="infoData.typeCode == 'AGILE'" class="icon" aria-hidden="true">
                <use xlink:href="#el-icon-icon-minjie1" />
              </svg>
              <svg v-else-if="infoData.typeCode == 'WALL'" class="icon" aria-hidden="true">
                <use xlink:href="#el-icon-icon-pubu1" />
              </svg>
              <svg v-else-if="infoData.typeCode == 'TEST'" class="icon" aria-hidden="true">
                <use xlink:href="#el-icon-ceshixiangmu" />
              </svg>
              <svg v-else-if="infoData.typeCode == 'DEVOPS'" class="icon" aria-hidden="true">
                <use xlink:href="#el-icon-project-devops" />
              </svg>
            </div>
          </template>
          <template v-slot:title>
            <div class="titleBox">
              <div class="title">{{ infoData.name }}</div>
              <!-- <i class="iconfont el-icon-icon-shoucang-on star" /> -->
            </div>

          </template>
          <div class="descBox">
            <div class="description">{{ infoData.description }}</div>
            <div class="description">标识：{{ infoData.typeCode }}</div>
          </div>
          <template v-slot:desc>

            <!-- 用户信息 -->

            <vone-user-avatar
              :avatar-path="getUserInfo(infoData) ? getUserInfo(infoData).avatarPath : ''"
              :name="getUserInfo(infoData)
                ? getUserInfo(infoData).name :''"
            />
          </template>
        </vone-card>
      </el-skeleton>
    </div>
  </div>
</template>

<script>

import { apiProjectInfo } from '@/api/vone/project'
import { catchErr } from '@/utils'

export default {
  props: {
    formInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      actions: [
        {
          text: '迭代',
          icon: 'el-icon-application-iteration',
          onClick: ({ row }) => this.jumpToDetail(row, 'iteration')
        },
        {
          text: '需求',
          icon: 'el-icon-application-demand',
          onClick: ({ row }) => this.jumpToDetail(row, 'issue')
        },
        {
          text: '测试',
          icon: 'el-icon-application-test',
          onClick: ({ row }) => this.jumpToDetail(row, 'testtree')
        }
      ],
      infoData: {}
    }
  },
  computed: {
    getUserInfo() {
      return function(row) {
        return row?.leadingBy && row.echoMap && row.echoMap?.leadingBy
      }
    }
  },
  mounted() {
    if (this.formInfo.projectId) {
      this.getProjectInfo()
    }
  },
  methods: {
    // 查询项目数据
    async getProjectInfo() {
      this.loading = true
      const [{ data, msg, isSuccess }, err] = await catchErr(apiProjectInfo(
        this.formInfo.projectId
      ))
      this.loading = false
      if (err) return
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.infoData = data || {}
    },

    jumpToDetail(row, type) {
      if (this.loading) return
      const { code, typeCode, id } = this.infoData
      let path
      if (type) {
        path = `/project/${type}/${code}/${typeCode}/${id}`
      } else {
        path = `/project/overview/${code}/${typeCode}/${id}`
      }
      this.$router.push({
        path
      })
    }
  }
}
</script>
<style lang='scss' scoped>
.infoCard {
  position: relative;
  height: 100%;
  overflow: hidden;
  background-color: var(--main-bg-color);
  border: 1px solid var(--solid-border-color);
  box-shadow: var(--main-bg-shadow);
  border-radius: 4px;

  .skelenton {
    height: 100%;
  }
}
.cardItem {
  height: 100%;
  padding: 0;
  color: var(--auxiliary-font-color);
  border: none;

  ::v-deep.el-card__header {
    padding: 0;
  }
  ::v-deep .el-card__body {
    height: 100%;
  }
  // 图标样式
  ::v-deep .vone-card__icon {
    width: 32px;
    height: 32px;
    line-height: 32px;
    margin-right: 8px;
    justify-content: start;
    .codeType {
      position: relative;
      width: 32px;
      height: 32px;
      border-radius: 4px;
      background-color: var(--hover-bg-color);
      svg.icon {
        position: absolute;
        top: 4px;
        left: 4px;
        width: 24px;
        height: 24px;
      }
    }
  }
  // 卡片标题
  ::v-deep .vone-card__title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .titleBox {
      justify-content: space-between;
      align-items: center;
      height: 32px;
      line-height: 32px;
      color: var(--main-font-color);
      font-size: 14px;
      width: calc(100% - 42px);
      .title {
        margin-right: 8px;
        font-weight: 400;
        min-width: 140px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .star {
        font-size: 18px;
        color: #ffc642;
      }
    }
  }
  .descBox {
    height: calc(100% - 90px);
    overflow-y: auto;
  }
  .description {
    min-height: 18px;
    line-height: 18px;
    margin: 8px 0;
    color: var(--auxiliary-font-color);
    font-size: 12px;
    font-weight: 400;
  }
  // 取消卡片动画
  &:hover {
    ::v-deep.vone-card__title {
      transition: none;
      transform: none;
    }
  }
}
// 用户信息样式
.userbox {
  display: flex;
  justify-content: center;
  color: var(--auxiliary-font-color);
  .card-user {
    display: flex;
    align-items: center;
    color: var(--main-font-color);
    i {
      font-size: 18px;
      border-radius: 50%;
      margin-right: 8px;
    }
  }
  img {
    width: 20px;
    width: 20px;
    border-radius: 50%;
    margin-right: 8px;
  }
  span {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

.linkCls {
  width: 100%;
  height: 100%;
}

</style>
