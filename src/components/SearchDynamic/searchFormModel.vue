<template>
  <div class="padding16">
    <section
      v-for="(item, index) in fieldFormList"
      :key="index"
      class="flexRow"
    >
      <span class="flexLabel">
        <el-select
          v-model="item.key"
          placeholder="请选择"
          filterable
          @change="keyChange($event, item, index)"
        >
          <el-option-group
            v-for="group in fieldOptions"
            :key="group.label"
            :label="group.label"
          >
            <el-option
              v-for="(e, idx) in group.options"
              :key="idx"
              :label="e.name"
              :value="e.key"
              :disabled="e.disabled"
            />
          </el-option-group>
        </el-select>
      </span>
      <span class="flexValue">
        <!-- 人员 -->
        <vone-remote-user
          v-if="item.type.code == 'USER'"
          v-model:value="item.value"
          :collapse-tags="true"
          :no-name="false"
          :multiple="item.multiple"
        />

        <!-- 标签 -->
        <tagSelect
          v-else-if="
            (item.type.code == 'SELECT' && item.key == 'tagId') ||
            item.key == 'tabName'
          "
          v-model:value="item.value"
          multiple
          is-id
        />
        <!-- 下拉框 -->
        <el-select
          v-else-if="item.type.code == 'SELECT' || item.type.code == 'ICON'"
          v-model="item.value"
          collapse-tags
          :placeholder="item.placeholder || '请选择'"
          filterable
          :multiple="item.multiple"
          :multiple-limit="item.key == 'productId' ? 1 : null"
          @change="onTypeChange($event, item)"
        >
          <el-option
            v-for="o in item.optionList"
            :key="o.id"
            :label="o.name"
            :value="item.valueType == 'id' ? o.id : o.code"
          />
        </el-select>
        <!-- 数字框 -->
        <input-number-range
          v-else-if="item.type.code == 'INT'"
          v-model:value="item.value"
          :precision="1"
        />
        <!-- 输入框 -->
        <el-input
          v-else-if="item.type.code == 'INPUT' || item.type.code == 'EDITOR'"
          v-model.trim="item.value"
          :placeholder="item.placeholder || '请输入'"
          style="width: 100%"
          :disabled="!item.key"
        />
        <!-- 日期 -->
        <el-date-picker
          v-else-if="item.type.code == 'DATE'"
          v-model="item.value"
          style="width: 100%"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
        <!-- treeSelect -->
        <vone-tree-select
          v-else-if="item.type.code == 'TREE'"
          v-model:value="item.value"
          search-nested
          :tree-data="item.optionList"
          :placeholder="item.placeholder || '请选择'"
        />
      </span>
      <span>
        <a @click="changeFixed(item, index)">
          <svg v-if="item.isFixed" class="icon" aria-hidden="true">
            <use xlink:href="#el-icon-pin-selected" />
          </svg>
          <svg v-else class="icon" aria-hidden="true">
            <use xlink:href="#el-icon-pin-normal" />
          </svg>
        </a>
        <a @click="deleteRow(index, item)">
          <el-icon><ElIconDelete /></el-icon>
        </a>
      </span>
    </section>

    <a class="addRow" @click="addRow">
      <el-icon><ElIconPlus /></el-icon> 添加条件
    </a>
  </div>
</template>

<script>
import {
  Delete as ElIconDelete,
  Plus as ElIconPlus,
} from "@element-plus/icons-vue";
import dayjs from "dayjs";
import tagSelect from "@/components/CustomEdit/components/tag-select.vue";
import { cloneDeep } from "lodash";

export default {
  name: "SearchFormModel",
  components: {
    tagSelect,
    ElIconDelete,
    ElIconPlus,
  },
  props: {
    defaultFileds: {
      type: Array,
      default: () => [],
    },
    basicFieldFormList: {
      type: Array,
      default: () => [],
    },
  },
  emits: ["update:basicFieldFormList", "updateDefaultFileds", "onTypeChange"],
  data() {
    return {
      fieldFormList: [],
      fieldOptions: [],
    };
  },
  watch: {
    basicFieldFormList: {
      deep: true,
      immediate: true,
      handler(val) {
        this.fieldFormList = val ? cloneDeep(val) : [];
      },
    },
    fieldFormList: {
      deep: true,
      immediate: true,
      handler(val) {
        this.updateFieldOptions();
        this.$emit("update:basicFieldFormList", val);
      },
    },
    defaultFileds: {
      deep: true,
      immediate: true,
      handler(val) {
        this.updateFieldOptions();
      },
    },
  },
  mounted() {
    this.updateFieldOptions();
  },
  methods: {
    updateFieldOptions() {
      if (!this.defaultFileds) return;

      this.fieldOptions = [
        {
          label: "基础",
          options: this.defaultFileds
            .filter((r) => !r.isBasicFilter)
            .map((i) => ({
              ...i,
              disabled: this.isOptionDisabled(i.key),
            })),
        },
        {
          label: "自定义",
          options: this.defaultFileds
            .filter((r) => r.isBasicFilter)
            .map((i) => ({
              ...i,
              disabled: this.isOptionDisabled(i.key),
            })),
        },
      ];
    },

    isOptionDisabled(key) {
      const hasKey = this.fieldFormList.map((r) => r.key);
      if (key === "typeCodes") {
        return !!hasKey.includes(key) || !hasKey.includes("classify");
      }
      return !!hasKey.includes(key);
    },

    addRow() {
      this.fieldFormList.push({
        isFixed: true,
        type: { code: "INPUT" },
        placeholder: "请选择筛选条件",
      });
      this.updateFieldOptions();
    },

    keyChange(key, item) {
      const allData = this.defaultFileds;
      const obj = allData.find((r) => r.key === key);
      if (!obj) return;

      Object.assign(item, {
        type: obj.type,
        isFixed: true,
        placeholder: obj.placeholder,
        optionList: obj.optionList,
        value: null,
        valueType: obj.valueType,
        multiple: obj.multiple,
      });

      this.$emit("update:basicFieldFormList", this.fieldFormList);
      this.$emit("updateDefaultFileds", this.defaultFileds);
      this.updateFieldOptions();
    },

    deleteRow(index, item) {
      const key = item.key;
      this.fieldFormList = this.fieldFormList.filter((i) => i.key !== key);

      if (key === "classify") {
        this.fieldFormList = this.fieldFormList.filter(
          (i) => i.key !== "typeCodes"
        );
      }

      this.$emit("update:basicFieldFormList", this.fieldFormList);
      this.updateFieldOptions();
    },

    changeFixed(item) {
      item.isFixed = !item.isFixed;
      const defaultFileds = cloneDeep(this.defaultFileds);
      const selectFiled = defaultFileds.find((r) => r.key === item.key);
      if (selectFiled) {
        selectFiled.isFixed = item.isFixed;
        this.$emit("updateDefaultFileds", defaultFileds);
      }
    },

    onTypeChange(val, item) {
      if (["classify", "typeCode", "releasePlanId"].includes(item.key)) {
        this.$emit("onTypeChange", val, item);
      }

      if (item.key === "productId") {
        this.$emit("onTypeChange", item);
      }

      if (item.key === "classify") {
        const typeCodeField = this.fieldFormList.find(
          (e) => e.key === "typeCodes"
        );
        if (typeCodeField) {
          typeCodeField.value = null;
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.custom-search-dropdown {
  padding: 16px;
  .dropdown-box {
    max-height: 200px;
    overflow-y: auto;
  }
  .el-dropdown-menu__item {
    padding: 0;
  }
  .dropdown-item-grouping {
    display: inline-block;
    line-height: 32px;
    color: #838a99;
    // margin-bottom: 12px;
  }
}
.custom-search-form {
  /*// padding: 16px 0;*/ /*// border-top: 1px solid #f2f3f5;*/
  .el-dropdown {
    color: #3e7bfa;
  }
}
.flexRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  .flexLabel {
    flex: 1;
  }
  .flexValue {
    flex: 2;
    margin: 0 10px;
  }
  i,
  svg {
    cursor: pointer;
    margin-left: 10px;
    &:hover {
      color: #3e7bfa;
    }
  }
}
.flexRow + .flexRow {
  margin-top: 10px;
}
.el-dropdown-link {
  color: #3e7bfa; /*// margin: 10px;*/
}
.addRow {
  color: #3e7bfa;
}
.padding16 {
  padding-bottom: 16px;
}
:deep(.el-select-group__title) {
  color: #777f8e;
  font-weight: 600;
}
</style>
