<template>
  <el-dialog
    class="dialogContainer"
    :title="title"
    :model-value="spaceVisible"
    width="456px"
    :close-on-click-modal="false"
    :before-close="close"
  >
    <el-form ref="spaceForm" :model="spaceForm" :rules="rules">
      <el-form-item label="空间名" prop="name">
        <el-input v-model="spaceForm.name" placeholder="请输入空间名" />
      </el-form-item>
      <el-form-item label="分类" prop="type">
        <el-select
          v-model="spaceForm.type"
          :disabled="currentSpaceInfo.id ? true : false"
        >
          <el-option
            v-for="item in classifyList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="空间类型" prop="typeId">
        <el-select v-model="spaceForm.typeId">
          <el-option
            v-for="item in spaceType"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="空间描述" prop="description">
        <el-input
          v-model="spaceForm.description"
          type="textarea"
          placeholder="请输入空间描述"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <span class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="save">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../utils/gogocodeTransfer'
import { operationSpace, searchSpaceType } from '@/api/vone/wiki'
import { apiBaseDictEnumList } from '@/api/vone/ouath/ouath'
import storage from 'store'
import _ from 'lodash'
export default {
  props: {
    spaceVisible: {
      type: Boolean,
      default: false,
    },
    currentSpaceInfo: {
      type: Object,
      default: () => {},
    },
    title: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      spaceForm: {
        name: '',
        typeId: '',
        description: '',
        icon: '',
      },
      spaceType: [],
      classifyList: [],
      rules: {
        name: [{ required: true, message: '请输入空间名', trigger: 'change' }],
        type: [{ required: true, message: '请选择分类', trigger: 'change' }],
        typeId: [
          { required: true, message: '请选择空间类型', trigger: 'change' },
        ],
      },
    }
  },
  mounted() {
    this.searchSpaceType()
    this.getClassifyList()
    if (Object.keys(this.currentSpaceInfo).length > 0) {
      this.spaceForm = _.cloneDeep(this.currentSpaceInfo)
    }
  },
  methods: {
    async getClassifyList() {
      const res = await apiBaseDictEnumList(['SpaceType'])

      if (!res.isSuccess) {
        return
      }
      this.classifyList = res.data.SpaceType
    },
    async save() {
      try {
        await this.$refs.spaceForm.validate()
      } catch (e) {
        return
      }
      const userInfo = storage.get('user')
      const params = {
        ...this.spaceForm,
        leadingBy: userInfo.id,
      }
      if (Object.keys(this.currentSpaceInfo).length > 0) {
        params.id = this.currentSpaceInfo.id
      }
      operationSpace(
        params,
        Object.keys(this.currentSpaceInfo).length > 0 ? 'put' : 'post'
      ).then((res) => {
        if (res.isSuccess) {
          $emit(this, 'getSpaceList')
          this.$message.success('操作成功')
          this.close()
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    searchSpaceType() {
      searchSpaceType().then((res) => {
        if (res.isSuccess) {
          this.spaceType = res.data
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    close() {
      $emit(this, 'update:spaceVisible', false)
      this.$refs['spaceForm'].resetFields()
    },
  },
  emits: ['update:spaceVisible', 'getSpaceList'],
}
</script>

<style lang="scss" scoped>
.image:focus {
  border: none;
  outline: none;
}
</style>
