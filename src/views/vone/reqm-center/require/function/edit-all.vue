<template>
  <!-- 批量编辑 -->
  <el-dialog
    title="批量编辑"
    width="35%"
    v-model:value="visible"
    :before-close="onClose"
    class="dialogBox"
    :close-on-click-modal="false"
  >
    <el-form
      ref="projectForm"
      v-loading="pageLoading"
      :model="projectForm"
      label-position="right"
      label-width="100px"
      :rules="rules"
    >
      <el-form-item label="负责人" prop="leadingBy">
        <template>
          <vone-remote-user
            v-model:value="projectForm.leadingBy"
            :default-data="defaultData"
          />
        </template>
      </el-form-item>
      <el-form-item label="处理人" prop="handleBy">
        <template>
          <template>
            <vone-remote-user
              v-model:value="projectForm.handleBy"
              :default-data="defaultData"
            />
          </template> </template
      ></el-form-item>
      <el-form-item
        v-if="typeCode != 'TASK' && typeCode != 'IDEA' && !isReq"
        label="提出人"
        prop="putBy"
      >
        <vone-remote-user
          v-model:value="projectForm.putBy"
          :default-data="defaultData"
        />
      </el-form-item>
      <el-form-item label="优先级" prop="priorityCode">
        <vone-icon-select
          v-model:value="projectForm.priorityCode"
          filterable
          :data="prioritList"
          clearable
          style="width: 100%"
        >
          <el-option
            v-for="item in prioritList"
            :key="item.key"
            :label="item.name"
            :value="item.code"
          >
            <i
              :class="`iconfont ${item.icon}`"
              :style="{
                color: item.color,
                fontSize: '16px',
                paddingRight: '6px',
              }"
            />
            {{ item.name }}
          </el-option>
        </vone-icon-select>
      </el-form-item>

      <template v-if="typeCode == 'IDEA'">
        <el-form-item label="期望完成时间" prop="planEtime">
          <el-date-picker
            v-model="projectForm.planEtime"
            type="datetime"
            format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:mm:ss"
            default-time="9:00:00"
            placeholder="选择计划开始时间"
            style="width: 100%"
          />
        </el-form-item>
      </template>

      <template v-else>
        <el-form-item
          :label="typeCode != 'RISK' ? '计划开始时间' : '开始时间'"
          prop="planStime"
        >
          <el-date-picker
            v-model="projectForm.planStime"
            type="datetime"
            format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:mm:ss"
            default-time="9:00:00"
            placeholder="选择计划开始时间"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item
          :label="typeCode != 'RISK' ? '计划完成时间' : '关闭时间'"
          prop="planEtime"
        >
          <el-date-picker
            :shortcuts="pickerOptions && pickerOptions.shortcuts"
            :disabled-date="pickerOptions && pickerOptions.disabledDate"
            :cell-class-name="pickerOptions && pickerOptions.cellClassName"
            v-model="projectForm.planEtime"
            type="datetime"
            format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:mm:ss"
            default-time="9:00:00"
            placeholder="选择计划完成时间"
            style="width: 100%"
            @change="changeDate"
          ></el-date-picker>
        </el-form-item>
      </template>
    </el-form>
    <template v-slot:footer>
      <span class="dialog-footer">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="saveProject"
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'

import { apiAlmUpdateAllByIds } from '@/api/vone/project/index'
import { apiAlmPriorityNoPage } from '@/api/vone/alm/index'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    typeCode: {
      type: String,
      default: undefined,
    },
    tableSelect: {
      type: Array,
      default: () => [],
    },
    isReq: {
      // 用于区别需求中心的需求以及项目集的风险，这里处理人负责人查的是所有人，没有提出人
      type: Boolean,
      default: false,
    },
  },
  data() {
    var validatePass = (rule, value, callback) => {
      if (this.projectForm.planEtime && value > this.projectForm.planEtime) {
        callback(new Error('开始日期不能大于结束日期!'))
      } else {
        callback()
      }
    }
    return {
      pickerOptions: {
        disabledDate: (time) => {
          if (this.projectForm.planStime) {
            return (
              time.getTime() <
              new Date(this.projectForm.planStime).getTime() -
                1 * 24 * 60 * 60 * 1000 +
                1
            )
          }
        },
      },
      rules: {
        planStime: [{ validator: validatePass }],
      },
      defaultData: [],
      projectForm: {
        handleBy: '',
        ids: this.tableSelect.map((r) => r.id),
        leadingBy: '',
        planEtime: '',
        planStime: '',
        priorityCode: '',
        putBy: '',
      },
      productList: [],
      prioritList: [], // 优先级
      saveLoading: false,
      pageLoading: false,
    }
  },
  mounted() {
    this.getPrioritList() // 优先级
  },
  methods: {
    changeDate(v) {
      if (this.projectForm.planStime < v) {
        this.$refs['projectForm'].clearValidate('planStime')
      }
    },
    onClose() {
      $emit(this, 'update:visible', false)
      this.$refs.projectForm.resetFields()
    },
    // 查优先级
    async getPrioritList() {
      const res = await apiAlmPriorityNoPage()
      if (!res.isSuccess) {
        return
      }
      this.prioritList = res.data
    },
    // 保存
    async saveProject() {
      await this.$refs.projectForm.validate()
      try {
        this.saveLoading = true
        // const params = {
        //   handleBy: this.projectForm.handleBy ? this.projectForm.handleBy : null,
        //   ids: this.tableSelect.map(r => r.id),
        //   leadingBy: this.projectForm.leadingBy || null,
        //   planEtime: this.projectForm.planEtime || null,
        //   planStime: this.projectForm.planStime || null,
        //   priorityCode: this.projectForm.priorityCode || null,
        //   putBy: this.projectForm.putBy || null
        // }
        const res = await apiAlmUpdateAllByIds(this.typeCode, this.projectForm)
        this.saveLoading = false
        if (!res.isSuccess) {
          this.$message.error(res.msg)
        }
        this.$message.success('保存成功')
        this.onClose()
        $emit(this, 'success')
      } catch (e) {
        this.saveLoading = false
      }
    },
  },
  emits: ['update:visible', 'success'],
}
</script>

<style lang="scss" scoped>
.dialogBox {
  :deep(.el-form-item) {
    margin-bottom: 20px;
  }
}
</style>
