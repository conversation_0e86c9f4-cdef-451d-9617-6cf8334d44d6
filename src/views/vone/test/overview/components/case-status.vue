<template>
  <!-- 测试用例分配状态 -->
  <vone-echarts-card :title="title" header-box>
    <vone-empty v-if="showNodata" />
    <vone-echarts
      v-else
      v-loading="dataLoading"
      :options="options"
      :height="'300px'"
    />
    <template v-slot:headerBox>
      <span class="filterBox">
        <el-select
          v-model="selectedPlans"
          multiple
          filterable
          clearable
          collapse-tags
          remote
          :loading="planLoading"
          :remote-method="getTreeData"
          placeholder="请搜索测试计划"
          class="filterSelect"
          @change="changeSelectedPlans"
        >
          <el-option
            v-for="item in treeData"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </span>
    </template>

    <choosePlanDialog
      v-bind="productParams"
      v-if="productParams.visible"
      v-model:value="productParams.visible"
      is-all
      @success="getData"
    />
  </vone-echarts-card>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import choosePlanDialog from './choose-plan-dialog.vue'
import { testPlanCaseStatus } from '@/api/vone/testmanage/index'
import { getTestPlanCase } from '@/api/vone/testplan'
import { debounce } from 'lodash'
import { catchErr } from '@/utils'

export default {
  components: {
    choosePlanDialog,
  },
  props: {
    title: {
      type: String,
      default: '',
    },
  },
  data() {
    const theme = document.body.className.indexOf('dark') > -1
    return {
      productParams: {
        visible: false,
      },
      dataLoading: false,
      planLoading: false,
      showNodata: false,
      data: [],
      selectedPlans: [],
      treeData: [],
      options: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          formatter: '{b0}<br/>用例: {c0}个',
        },
        xAxis: {
          type: 'category',
          axisTick: {
            show: false,
          },
          axisLine: {
            lineStyle: {
              color: theme ? '#333947' : '#EBEEF5',
            },
          },
          axisLabel: {
            color: '#8A8F99',
          },
          data: [],
        },
        grid: {
          left: 0,
          right: 0,
          top: '16px',
          bottom: '3%',
          containLabel: true,
        },
        yAxis: {
          type: 'value',
          minInterval: 1,
          splitLine: {
            // 网格线
            lineStyle: {
              type: 'dashed', // 设置网格线类型 dotted：虚线   solid:实线
              width: 1,
              color: theme ? '#333947' : '#EBEEF5',
            },
          },
          axisLabel: {
            color: '#8A8F99',
          },
        },
        series: [
          {
            data: [],
            type: 'bar',
            color: ['#8791FA'],
          },
        ],
      },
    }
  },
  mounted() {
    $on(this.$bus, 'refreshChart', () => {
      this.changeTheme()
    })
    this.getData()
  },
  methods: {
    // 查询测试计划数据
    getTreeData: debounce(async function (query) {
      if (query != '') {
        const params = {
          current: 1,
          extra: {},
          model: { name: query },
          order: 'descending',
          size: 99999,
          sort: 'createTime',
        }
        this.planLoading = true
        const [res, err] = await catchErr(getTestPlanCase(params))
        this.planLoading = false
        if (err) return
        if (res.isSuccess) {
          this.treeData = res.data.records
        }
      }
    }, 1000),
    changeSelectedPlans() {
      this.getData(this.selectedPlans.join())
    },
    // 测试用例分布状态
    async getData(val) {
      this.dataLoading = true
      const [res, err] = await catchErr(testPlanCaseStatus(val || ''))

      this.dataLoading = false
      if (err) return
      if (!res.isSuccess) {
        this.$message.success(res.msg)
      }
      if (res.data?.planId) {
        this.selectedPlans = [res.data.planId]
        this.treeData = [
          {
            id: res.data.planId,
            name: res.data.planName,
          },
        ]
      }

      const ojbMap = {
        executed: '已执行',
        unexecuted: '待执行',
        unappointed: '未分配',
      }

      const xAxisData = Object.keys(res.data)
        .filter((v) => v !== 'planId' && v !== 'planName')
        .map((r) => ojbMap[r])
      const status = Object.entries(res.data)
        .filter(([name]) => name !== 'planId' && name !== 'planName')
        .map((r) => r[1])
      this.options.xAxis.data = xAxisData
      this.options.series[0].data = status
    },
    changeTheme() {
      // 是否是暗色主题
      const theme = document.body.className.indexOf('dark') > -1
      this.options.yAxis.splitLine.lineStyle.color = theme
        ? '#333947'
        : '#EBEEF5'
      this.options.xAxis.axisLine.lineStyle.color = theme
        ? '#333947'
        : '#EBEEF5'
    },
  },
}
</script>

<style lang="scss" scoped>
.filterBox {
  /*// position: absolute;*/ /*// top: 9px;*/ /*// right: 16px;*/
  :deep(.el-cascader__tags) {
    flex-wrap: nowrap;
  }
  :deep(.el-tag--info) {
    max-width: 85px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .filterSelect {
    width: 240px;
    :deep() {
      .el-input__inner {
        background: var(--hover-bg-color);
        border-color: var(--hover-bg-color);
      }
      .el-select__tags {
        width: 160px;
        .el-tag {
          max-width: 150px;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.filterSelect {
  .vue-treeselect__multi-value {
    display: flex;
    align-items: center;
    margin-bottom: 0;
  }
  .vue-treeselect__multi-value-item-container,
  .vue-treeselect__limit-tip {
    padding-top: 0;
    padding-right: 4px;
  }
  .vue-treeselect__multi-value-item {
    color: #8a8f99;
    background-color: #e4e4eb;
    border-color: #e4e4eb;
    height: 20px;
  }
  .vue-treeselect__multi-value-label {
    display: inline-block;
    max-width: 96px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .vue-treeselect__limit-tip {
    padding: 2px 6px;
    background-color: #e4e4eb;
    &-text {
      color: #8a8f99;
    }
  }
  .vue-treeselect__value-remove {
    color: #909399;
    border-left: none;
  }
  .vue-treeselect__control {
    height: 26px;
    border-radius: 2px;
    background: #f5f6fa;
    border-color: #f5f6fa;
    .vue-treeselect__x-container {
      right: 12px;
      visibility: hidden;
      opacity: 0;
    }
    .vue-treeselect__placeholder {
      line-height: 26px;
    }
    &:hover {
      .vue-treeselect__x-container {
        visibility: visible;
        opacity: 1;
      }
      .vue-treeselect__control-arrow-container {
        visibility: hidden;
        opacity: 0;
      }
    }
  }
}
.custom-theme-dark {
  .filterSelect {
    .vue-treeselect__control {
      background: #252933;
      border-color: #495265;
    }
    .vue-treeselect__multi-value-item {
      color: #b5b8bd;
      background-color: #373e4d;
      border-color: #373e4d;
    }
    .vue-treeselect__limit-tip {
      background-color: #373e4d;
      &-text {
        color: #b5b8bd;
      }
    }
  }
}
</style>
