<template>
  <div>
    <el-row>
      <el-button type="primary" :icon="ElIconPlus" @click="addServer"
        >新建服务应用</el-button
      >
    </el-row>

    <vone-cards v-loading="loading" :data="tableData" :row-count="4">
      <template v-slot="{ row }">
        <a @click="toConfigDetails(row)">
          <vone-card
            :title="row.name"
            :title-content="row.name"
            :actions="rowActions"
          >
            <template v-slot:icon>
              <el-icon class="iconfont" style="color: #409eff"
                ><el-icon-application-setting
              /></el-icon>
            </template>
            <el-row class="cardText">
              <div class="cardText">应用编号:&nbsp;&nbsp;{{ row.id }}</div>
              <div class="cardText">
                维护人:&nbsp;&nbsp;{{ row.personLiable }}
              </div>
              <div class="cardText">
                更新时间:&nbsp;&nbsp;{{ row.updateTime }}
              </div>
              <div class="cardText">
                描述 :&nbsp;&nbsp;{{ row.description }}
              </div>
            </el-row>
            <template v-slot:desc>
              {{ row.content }}
            </template>
          </vone-card>
        </a>
      </template>
    </vone-cards>
  </div>
</template>

<script>
import {
  ApplicationSetting as ElIconApplicationSetting,
  Plus as ElIconPlus,
} from "@element-plus/icons-vue";
import {
  apiBaseDeleteApplilcation,
  apiBaseGetapplication,
} from "@/api/vone/cmdb/server";

export default {
  data() {
    return {
      loading: false,
      str: "",
      bussinessDialogParam: { visible: false },
      form: {},
      tableData: [],
      dataList: [],
      IDS: "",
      rowActions: [
        {
          text: "编辑",
          icon: "iconfont el-icon-application-edit",
          onClick: ({ row }) => this.editById(row, "edit"),
        },
        {
          text: "删除",
          icon: "iconfont el-icon-application-delete",
          onClick: ({ row }) => this.deleteById(row),
        },
      ],
      ElIconPlus,
    };
  },
  components: {
    ElIconApplicationSetting,
  },
  props: {
    id: {
      type: String,
      default: null,
    },
  },
  mounted() {
    this.IDS = this.id;

    // this.getServerList()
  },
  methods: {
    async getServerList() {
      this.loading = true;
      const { data, success, message } = await apiBaseGetapplication({
        businessSystemId: this.IDS,
        id: "",
        name: this.str,
        type: "",
      });
      if (!success) {
        this.$message.warning(message);
        return;
      }
      this.loading = false;
      this.tableData = data;
      this.dataList = data;
    },
    // 新建服务应用
    addServer() {
      this.$router.push({
        name: "cmdb_business_addServer",
        params: { id: this.id },
      });
    },
    // 修改
    editById(row, type) {
      this.$router.push({
        name: "cmdb_business_editServer",
        params: { systemId: row.id, type: 2 },
      });
    },
    // 查看
    toConfigDetails(row) {
      // if (this.$auth('CMDB_business_applicationSearch')) {
      this.$router.push({
        name: "cmdb_business_editServer",
        params: { systemId: row.id, type: 1 },
      });
      // }
    },
    // 删除
    async deleteById(row) {
      await this.$confirm("确定删除该信息吗?", "删除", {
        type: "warning",
        closeOnClickModal: false,
        customClass: "delConfirm",
      })
        .then(async (actions) => {
          const { success, message } = await apiBaseDeleteApplilcation({
            id: row.id,
          });
          if (!success) {
            return this.$message.error(message);
          }
          this.getServerList();
          this.$message.success(message);
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.cardText {
  padding-left: 5px;
  padding-top: 5px;
  color: #909399;
  font-size: 12px;
}
</style>
