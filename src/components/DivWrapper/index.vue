<template>
  <div class="card-wrapper" :body-style="{padding: '12px 16px 16px'}">

    <div
      :class="[
        !hasLine
          ? 'card-wrapper-header'
          : 'card-wrapper-header rowLine',
      ]"
    >
      <div class="card-wrapper-header-title">{{ title }}
        <small class="text"> {{ text }}</small>
      </div>
      <!-- <div class="rowLine" /> -->
      <slot name="header" />
      <slot name="actions" />
    </div>
    <div class="card-wrapper-content">
      <slot />
    </div>

  </div>
</template>
<script>
export default {
  name: 'VoneDivWrapper',
  props: {
    title: {
      type: String,
      default: ''
    },
    text: {
      type: String,
      default: ''
    },
    hasLine: {
      type: Boolean,
      default: false
    }
  }
}
</script>
<style lang="scss" scoped>
.card-wrapper {
  margin-bottom: 12px;

  .text {
    font-weight: 400;
    color: var(--auxiliary-font-color);
  }
  &- {
    &header {
      margin-top: 11px;
      height: 22px;
      line-height: 22px;
      display: flex;
      &- {
        &title {
          position: relative;
          flex: 1;
          font-size: 14px;
          font-weight: 500;
					color:#202124;

          &:before {
            display: block;
            content: "";
            background: var(--main-theme-color, #3e7bfa);
            position: absolute;
            top: 10px;
            width: 4px;
            height: 18px;
            top: 9px;
            left: -20px;
            border-radius: 1px;
          }
        }
      }
    }
    &content {
      margin-top: 16px;
    }
  }
  &:last-child {
    margin-bottom: 0;
  }
}
.rowLine {
  border-bottom: 1px solid var(--el-divider);
  margin: 0 -16px 16px;
  //  padding: 0 16px 16px;
}
</style>
