<template>
  <el-card>
    <template v-slot:header>
      <div class="clearfix">
        <span>{{ title }}</span>
        <!-- <el-button style="float: right;" type="info">{{ formInfo.name }}</el-button> -->
      </div>
    </template>
    <vone-echarts v-if="data.length > 0" :options="options" :height="'300px'" />
    <vone-empty v-else />
  </el-card>
</template>

<script>
import {
  requierByPlanId,
  taskByPlanId,
  bugByPlanId,
} from '@/api/vone/project/overview.js'

export default {
  props: {
    title: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      data: [],
      backgroundColor: '#2c343c',
      options: {
        color: ['#ADB0B8', '#63BDF9', '#3CB540'],
        tooltip: {
          trigger: 'item',
          axisPointer: {
            type: 'shadow',
          },
          backgroundColor: '#fff',
          borderColor: 'none',
          extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);', // 附加阴影样式
          textStyle: {
            color: '#53565C',
          },
        },
        legend: {
          orient: 'vertical',
          left: '70%',
          bottom: '25%',
          textStyle: {
            // 图例文字的样式
            color: '#8A8F99',
          },
        },
        graphic: {
          type: 'text',
          left: '32%',
          top: 'center',
          style: {
            text: '',
            textAlign: 'center',
            fill: '#000',
            width: 20,
            height: 20,
            fontSize: 14,
          },
        },
        series: [
          {
            name: '需求概览',
            type: 'pie',
            center: ['35%', '50%'],
            radius: ['40%', '60%'],
            avoidLabelOverlap: false,
            itemStyle: {
              // borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2,
            },
            label: {
              show: false,
              position: 'center',
            },
            emphasis: {
              label: {
                show: false,
                fontSize: '20',
                // fontWeight: 'bold'/
              },
            },
            labelLine: {
              show: false,
            },
            data: [
              // { value: 120, name: '未开始' },
              // { value: 110, name: '进行中' },
              // { value: 100, name: '已完成' }
            ],
          },
        ],
      },
    }
  },
  watch: {
    title: function (val) {
      if (val == '需求概览') {
        this.getRequire()
      }
      if (val == '任务概览') {
        this.getTask()
      }
      if (val == '缺陷概览') {
        this.getBug()
      }
    },
  },
  mounted() {
    if (this.title == '需求概览') {
      this.getRequire()
    }
    if (this.title == '任务概览') {
      this.getTask()
    }
    if (this.title == '缺陷概览') {
      this.getBug()
    }
  },
  methods: {
    async getRequire() {
      await requierByPlanId(this.$route.params.planIs).then((res) => {
        if (res.isSuccess) {
          this.data = res.data.data
          this.options.color = ['#ADB0B8', '#63BDF9', '#3CB540', '#FFBE47']
          // this.data = res.data.data
          this.options.graphic.style.text = '总数' + res.data.count
          this.options.series[0].data = res.data.data
        }
      })
    },
    async getTask() {
      await taskByPlanId(this.$route.params.planIs).then((res) => {
        if (res.isSuccess) {
          this.data = res.data.data
          this.options.color = ['#ADB0B8', '#63BDF9', '#3CB540', '#FFBE47']
          // this.data = res.data.data
          this.options.graphic.style.text = '总数' + res.data.count
          this.options.series[0].data = res.data.data
        }
      })
    },
    async getBug() {
      await bugByPlanId(this.$route.params.planIs).then((res) => {
        if (res.isSuccess) {
          this.data = res.data.data
          this.options.color = ['#ADB0B8', '#63BDF9', '#3CB540', '#FFBE47']
          // this.data = res.data.data
          this.options.graphic.style.text = '总数' + res.data.count
          this.options.series[0].data = res.data.data
        }
      })
    },
  },
}
</script>
