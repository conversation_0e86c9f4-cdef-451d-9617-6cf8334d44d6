import request from '@/utils/axios-api'

// 工作台---缺陷趋势
export async function apiInsightBugComponent(projectId) {
  return request({
    url: `/api/insight/insight/bugComponent/findTrendByProjectId/${projectId}`,
    method: 'get'
  })
}
/**
 * @desc 查询工作台引擎资源
 * @param {}
 */
export async function apiInsightEngineInfos(data) {
  return request({
    url: `/api/insight/insight/engineComponent/dashboard`,
    method: 'get'
  })
}

// 工作台流水线列表
export async function getPipelineList(params) {
  return request({
    url: `/api/insight/insight/pipelineComponent/list/favourites`,
    method: 'get',
    params
  })
}

// 工作台近期流水线统计
export async function getPipelineCount(params) {
  return request({
    url: `/api/insight/insight/pipelineComponent/executeStatistics`,
    method: 'get',
    params
  })
}

// 工作台根据环境标签汇总制品包数量
export async function getEnvPackCount(dateType, envCodes) {
  return request({
    url: `/api/insight/insight/packStatus/getPackCountByEnvIdAndGrainSize/` + dateType,
    method: 'post',
    data: envCodes
  })
}

// cmdb 统计组件
export async function getResourceNum() {
  return request({
    url: `/api/insight/insight/resourceComponent/dashboard`,
    method: 'get'
  })
}

/**
 * @desc 查询迭代燃尽图
 * @param {}
 */
export async function apiInsightFindBurnDown(planId, data) {
  return request({
    url: `/api/insight/insight/planComponent/findBurnDownByPlanId/${planId}`,
    method: 'get',
    data
  })
}

/**
 * @desc 查询工作台日历
 * @param {}
 */
export async function apiInsightUserTodoWork(data) {
  return request({
    url: `/api/insight/insight/userTodoWorkComponent/dashboard`,
    method: 'get',
    data
  })
}
/**
 * @desc 查询工作台我的动态
 * @param {}
 */
export async function apiInsightMydynamic(data) {
  return request({
    url: `/api/insight/insight/userOptComponent/dashboard`,
    method: 'get',
    data
  })
}

/**
 * @desc 查询工作台-按项目角色维度统计任务
 * @param {}
 */
export async function apiInsightProjectRoleTask(data) {
  return request({
    url: `/api/alm/alm/issueItem/projectRole`,
    method: 'post',
    data
  })
}

/**
 * @desc 查询工作台-BUG的产生的修复趋势图
 * @param {}
 */
export async function apiInsightBugTrend(data) {
  return request({
    url: `/api/alm/alm/issueItem/bugTrend`,
    method: 'post',
    data
  })
}

/**
 * @desc 编辑分享仪表板
 * @param {}
 */
export async function apiInsightShareBoardPut(id, data) {
  return request({
    url: `/api/tm-base/base/dashboard/${id}/share`,
    method: 'put',
    data
  })
}
/**
 * @desc 查询仪表板详情
 * @param {}
 */
export async function apiInsightShareBoardInfo(id) {
  return request({
    url: `/api/tm-base/base/dashboard/${id}/share`,
    method: 'get'
  })
}

/**
 * @desc 数据下钻
 * @param {}
 */
export async function apiInsightDetailItemInfo(data) {
  return request({
    url: `/api/alm/alm/issueItem/dataDetail`,
    method: 'post',
    data
  })
}

/**
 * @desc  项目角色任务视图
 * @param {}
 */
export async function apiProjectRoleDetail(projectId, user) {
  return request({
    url: `/api/alm/alm/issueItem/projectRole/dataDetail?projectId=${projectId}&user=${user}`,
    method: 'get'
  })
}
// 查询我的任务
export function queryPeojectTask(data) {
  return request({
    url: `/api/alm/alm/project/getProjectWorkItemByProjectIdAndTypeClassify`,
    method: 'post',
    data
  })
}
// 查询用户访问量
export function queryUserPageview() {
  return request({
    url: `/api/base/base/loginLog/getLastMonthLoginLogAndOptLogLineStack`,
    method: 'get'
  })
}
