<template>
  <section class="lane-container">

    <!-- 一级分类标题 -->
    <header style="background:#F5F5F5">
      <el-input v-if="first.isRename" v-model="first.name" suffix-icon="iconfont el-icon-icon-line-huiche" @keyup.enter.native="changeName(first,'first')" />
      <template v-else>
        {{ first.name }}
        <el-dropdown v-if="isShowFirstToolbar" trigger="click" placement="bottom-start" @command="handleFirstCommand">
          <span class="el-dropdown-link">
            <i class="iconfont el-icon-icon-gengduo" />
          </span>
          <el-dropdown-menu slot="dropdown" class="bulletin-board-lane-header-el-dropdown-menu">
            <el-dropdown-item icon="iconfont el-icon-icon-system-add" command="create">{{ firstCreateCommandTitle }}</el-dropdown-item>
            <el-dropdown-item icon="iconfont el-icon-application-rename" command="rename">重命名</el-dropdown-item>
            <el-dropdown-item icon="iconfont el-icon-icon-system-delete" command="remove">删除</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
    </header>

    <draggable v-model="first.children" class="first-container" @change="$emit('change-second-sort',{first})">
      <div v-for="(second,secondIndex) in first.children" :key="second.id" class="second-container">

        <!-- 二级分类标题 -->
        <header>
          <el-input v-if="second.isRename" v-model="second.name" suffix-icon="iconfont el-icon-icon-line-huiche" @keyup.enter.native="changeName(second,'second')" />
          <template v-else>
            {{ second.name }}
            <el-dropdown trigger="click" placement="bottom-start" @command="handleSecondCommand($event,second,secondIndex)">
              <span class="el-dropdown-link">
                <i class="iconfont el-icon-icon-gengduo" />
              </span>
              <el-dropdown-menu slot="dropdown" class="bulletin-board-lane-header-el-dropdown-menu">
                <el-dropdown-item icon="iconfont el-icon-application-rename" command="rename">重命名</el-dropdown-item>
                <el-dropdown-item icon="iconfont el-icon-icon-system-delete" command="remove">删除</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </header>

        <!-- 子项容器 -->
        <drop-container
          :category="second"
          @drop-item="$emit('drop-item',{
            first,
            second,
            ...$event
          })"
          @remove-item="$emit('remove-item',{
            first,
            second,
            ...$event
          })"
        />
      </div>

      <div style="color:#6B7385;text-align:center;background: #FFFFFF;border-radius: 4px;padding:8px 12px;margin:0px 12px">
        <span style="cursor:pointer;" @click="handleFirstCommand('create')">
          <i class="iconfont el-icon-icon-system-add" />
          添加环节
        </span>
      </div>
    </draggable>

    <!-- 二级分类 -->
    <el-dialog v-model="secondDialogVisible" :title="secondDialogTitle" :close-on-click-modal="false" width="30%">
      <slot />
      <div slot="footer">
        <el-button @click="secondDialogVisible=false">取消</el-button>
        <el-button type="primary" @click="handleSecondSure">确定</el-button>
      </div>
    </el-dialog>
  </section>
</template>

<script>
import draggable from 'vuedraggable'
import dropContainer from './drop-container.vue'

export default {
  components: { draggable, dropContainer },
  props: {
    laneIndex: {
      type: Number,
      default: 0
    },
    /**
     * 是否显示一级分类工具栏
     */
    isShowFirstToolbar: {
      type: Boolean,
      default: true
    },
    first: {
      type: Object,
      default: null
    },
    firstCreateCommandTitle: {
      type: String,
      default: '新增'
    },
    secondDialogTitle: {
      type: String,
      default: '新增'
    }
  },
  data() {
    return {
      secondDialogVisible: false
    }
  },
  methods: {
    handleFirstCommand(command) {
      const { first } = this
      switch (command) {
        case 'create':
          this.secondDialogVisible = true
          break
        case 'rename':
          first.isRename = true
          break
        case 'remove':
          this.$emit('remove-first')
          break
      }
    },
    handleSecondCommand(command, second, secondIndex) {
      const { first } = this
      switch (command) {
        case 'rename':
          second.isRename = true
          break
        case 'remove':
          this.$confirm(`确定删除【${second.name}】？`, '删除', {
            dangerouslyUseHTMLString: true,
            confirmButtonText: '删除',
            cancelButtonText: '取消',
            type: 'warning',
            confirmButtonClass: 'el-button--danger'
          }).then(() => {
            this.$emit('remove-second', {
              first,
              second,
              callback: () => {
                first.children.splice(secondIndex, 1)
              }
            })
          })
          break
      }
    },
    /**
     * 二级分类弹窗处理确定操作，将新增项加入first下
     */
    handleSecondSure() {
      this.$emit('create-second', {
        first: this.first,
        callback: param => {
          if ('id' in param && 'name' in param) {
            // 构建数据结构
            param.children = param.children || []
            param.isRename = false
            this.first.children.push(param)
            this.secondDialogVisible = false
          } else {
            this.$message.error('必须包含id、name')
          }
        }
      })
    },
    changeName(o, category) {
      o.isRename = false
      if (category === 'first') {
        this.$emit('change-first-name', { first: o })
      } else if (category === 'second') {
        this.$emit('change-second-name', {
          first: this.first,
          second: o
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.lane-container {
  width: 200px;
  background: #fafafa;
  overflow: auto;
  header {
    cursor: move;
    padding: 0px 16px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: space-between;
    i {
      cursor: pointer;
      font-size: 14px;
    }
  }

  > header {
    height: 38px !important;
    color: #1d2129;
    .el-dropdown-link {
      color: #1d2129;
    }
  }
  .first-container {
    padding: 12px;
    display: flex;
    flex-direction: column;
    row-gap: 12px;
  }
  .second-container {
    background: #fff;
    header {
      height: 32px !important;
      color: #202124;
      border-bottom: 1px solid #eaecf0;
      .el-dropdown-link {
        color: #202124;
      }
    }
  }
}
</style>
<style lang="scss">
.bulletin-board-lane-header-el-dropdown-menu {
  .el-dropdown-menu__item {
    line-height: unset;
    display: flex;
    align-items: center;
  }
}
</style>
