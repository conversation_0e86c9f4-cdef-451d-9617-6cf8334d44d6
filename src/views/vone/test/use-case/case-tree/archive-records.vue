<template>
  <div style="margin: -16px">
    <div class="records">归档记录</div>
    <vone-empty
      v-if="recordList && recordList.length === 0"
      style="height: 500px"
    />
    <template v-else>
      <el-collapse
        v-model="active"
        v-loading="collapseLoading"
        accordion
        class="collapse"
      >
        <el-collapse-item
          v-for="(item, index) in recordList"
          :key="item.updateTime"
          :name="index"
          @click="handleChange(index)"
        >
          <template v-slot:title>
            <div class="collapse-title">
              <el-row type="flex" align="middle" justify="center">
                <span style="flex: 1">{{ item.updateTime }}</span>
                <span v-if="item.updatedBy" style="flex: 1">
                  <vone-user-avatar
                    :avatar-path="item.echoMap.updatedBy.avatarPath"
                    :name="item.echoMap.updatedBy.name"
                  />
                </span>

                <el-tooltip
                  class="item"
                  effect="dark"
                  content="撤销归档"
                  placement="top"
                >
                  <el-button
                    size="mini"
                    type="text"
                    :loading="item.revertLoading"
                    :icon="elIconApplicationUndo"
                    @click.stop="callback(item)"
                  />
                </el-tooltip>
              </el-row>
            </div>
          </template>
          <recordTable ref="recordTable" :library-id="libraryId" :data="item" />
        </el-collapse-item>
      </el-collapse>

      <el-pagination
        layout="->,total,sizes, prev, pager, next, jumper"
        v-model:page-size="pageData.size"
        v-model:current-page="pageData.current"
        :total="pageData.total"
        @size-change="handlePageChange"
        @current-change="handlePageChange"
      />
    </template>
  </div>
</template>

<script>
import {
  getArchiveRecord,
  recallArchiveRecord,
} from "@/api/vone/testmanage/case";
import recordTable from "./records/archive-table.vue";
export default {
  components: {
    recordTable,
  },
  props: {
    libraryId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      collapseLoading: false,
      active: "",
      recordList: [],
      pageData: {
        // 分页配置
        current: 1,
        size: 10,
        total: 0,
        sort: "id",
      },
      pageSizes: [10, 20, 50, 100],
    };
  },
  created() {
    this.getrecordList();
  },
  methods: {
    handleChange(val) {
      if (val >= 0) {
        this.$refs.recordTable[val].getTableData();
      }
    },
    handlePageChange() {
      this.getrecordList();
    },
    async callback(item) {
      try {
        await this.$confirm("确定撤销归档吗？", "提示", {
          type: "warning",
        });
        item["revertLoading"] = true;
        const res = await recallArchiveRecord(
          this.$route.params.caseId || this.libraryId,
          item.updateTime,
          item.updatedBy
        );
        item["revertLoading"] = false;
        if (res.isSuccess) {
          this.$message.success("撤销成功");

          this.getrecordList();
          this.active = "";
        }
      } catch (e) {
        item["loading"] = false;
      }
    },
    // 查记录
    async getrecordList() {
      const params = {
        ...this.pageData,
        model: {
          libraryId: this.$route.params.caseId || this.libraryId,
        },
      };
      this.collapseLoading = true;
      const res = await getArchiveRecord(params);
      if (res.isSuccess) {
        this.collapseLoading = false;
        this.recordList = res.data.records;
        this.pageData.total = Number(res.data.total);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.records {
  height: 48px;
  line-height: 48px;
  padding: 0 16px;
  font-weight: 600;
  font-size: 16px;
  border-bottom: 1px solid var(--disabled-bg-color);
}
.collapse {
  margin: 16px;
  height: calc(100vh - 218px);
}
:deep(.el-collapse-item__header) {
  height: 48px !important;
  line-height: 48px !important;
}
:deep(.collapse-title) {
  flex: 1 0 90%;
  order: 1;
  .el-collapse-item__header {
    flex: 1 0 auto;
    order: -1;
  }
}
</style>
