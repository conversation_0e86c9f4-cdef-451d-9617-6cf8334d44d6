<template>
  <div>
    <el-dialog
      :title="id ? '编辑迭代' : '新增迭代'"
      v-model:value="visible"
      :before-close="onClose"
      :close-on-click-modal="false"
      width="40%"
    >
      <el-form
        ref="iterationForm"
        :model="iterationForm"
        :rules="iterationRules"
        label-position="top"
      >
        <el-form-item label="名称" prop="name">
          <el-input
            v-model.trim="iterationForm.name"
            placeholder="请输入名称"
            style="width: 100%"
          />
        </el-form-item>

        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="开始日期" prop="planStime">
              <el-date-picker
                :shortcuts="pickerOptionsStart && pickerOptionsStart.shortcuts"
                :disabled-date="
                  pickerOptionsStart && pickerOptionsStart.disabledDate
                "
                :cell-class-name="
                  pickerOptionsStart && pickerOptionsStart.cellClassName
                "
                v-model="iterationForm.planStime"
                type="date"
                placeholder="请选择开始日期"
                value-format="yyyy-MM-dd"
                style="width: 100%"
                :editable="false"
                :disabled="disabledStart"
                @change="changeTime"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束日期" prop="planEtime">
              <el-date-picker
                :shortcuts="pickerOptions && pickerOptions.shortcuts"
                :disabled-date="pickerOptions && pickerOptions.disabledDate"
                :cell-class-name="pickerOptions && pickerOptions.cellClassName"
                v-model="iterationForm.planEtime"
                value-format="yyyy-MM-dd"
                type="date"
                placeholder="请选择结束日期"
                style="width: 100%"
                :editable="false"
                :disabled="disabledEnd"
                @change="changeEnd"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="负责人" prop="leadingBy">
              <vone-icon-select
                v-model:value="iterationForm.leadingBy"
                select-type="user"
                clearable
                filterable
                :data="userList"
                style="width: 100%"
              >
                <el-option
                  v-for="item in userList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                  <vone-user-avatar
                    v-if="item.echoMap"
                    :avatar-path="item.avatarPath"
                    :avatar-type="item.avatarType"
                    :show-name="false"
                    height="22px"
                    width="22px"
                  />
                  {{ item.name }}
                </el-option>
              </vone-icon-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="锁定" prop="enable">
              <el-radio-group v-model="iterationForm.enable">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="迭代状态" prop="state" style="display: none">
          <el-select
            v-model="iterationForm.state"
            placeholder="请选择迭代状态"
            style="width: 100%"
          >
            <el-option label="关闭" value="-1" />
            <el-option label="未开始" value="0" />
            <el-option label="已开始" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="迭代目标" prop="description">
          <el-input
            v-model="iterationForm.description"
            type="textarea"
            placeholder="请输入迭代目标"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <div>
          <el-button @click="onClose">取消</el-button>
          <el-button :loading="saveLoading" type="primary" @click="sureAdd"
            >确定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import storage from 'store'
import { apiProjectUserNoPage } from '@/api/vone/project/index'
import {
  apiAlmProjectPlanAdd,
  apiAlmPlanInfo,
} from '@/api/vone/project/iteration'
import { getProjectInfo } from '@/api/vone/pipeline'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      pickerOptionsStart: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7
        },
      },
      pickerOptions: {
        disabledDate: (time) => {
          if (this.iterationForm.planStime) {
            return (
              time.getTime() < new Date(this.iterationForm.planStime).getTime()
            )
          } else {
            time.getTime() < Date.now() - 8.64e7
          }
        },
      },
      iterationForm: {
        projectId: this.$route.params.id,
        type: 'SPRINT',
        enable: false,
      },
      iterationRules: {
        name: [
          { required: true, message: '请输入名称' },
          { pattern: '^.{1,30}$', message: '请输入不超过30个字符组成的名称' },
        ],
        planStime: [{ required: true, message: '请选择开始日期' }],
        planEtime: [{ required: true, message: '请选择结束日期' }],
        leadingBy: [{ required: true, message: '请选择负责人' }],
        // state: [{ required: true, message: '请选择迭代状态' }],
        description: [{ max: 255, message: '迭代目标不超过225个字符' }],
      },
      saveLoading: false,
      userList: [],
      disabledStart: false,
      disabledEnd: false,
    }
  },
  mounted() {
    this.getUserList()
    // this.getProInfo()
    if (this.id) {
      // 迭代详情
      this.getPlanInfo()
    }
  },
  methods: {
    async getProInfo() {
      const { data, success, message } = await getProjectInfo({
        key: this.projectKey,
      })
      if (!success) {
        this.$message.warning(message)
        return
      }
      // 项目开始时间、项目结束时间均有值   迭代时间可选范围为：项目开始时间=<迭代时间 <=项目结束时间
      // 项目开始时间未设置、项目结束时间未设置，对应的迭代开始时间、迭代结束时间不可选，tips提示：请先确定项目开始时间、请先确定项目结束时间
      if (data.planStime && data.planEtime) {
        this.pickerOptionsStart = {
          disabledDate(time) {
            return (
              time.getTime() < data.planStime || time.getTime() > data.planEtime
            )
          },
        }
        this.pickerOptions = {
          disabledDate(time) {
            return (
              time.getTime() < data.planStime || time.getTime() > data.planEtime
            )
          },
        }
      } else if (data.planStime && !data.planEtime) {
        // 只有开始时间
        this.pickerOptionsStart = {
          disabledDate(time) {
            return time.getTime() < data.planStime
          },
        }
        this.disabledEnd = true
      } else if (!data.planStime && data.planEtime) {
        // 只有结束时间
        this.pickerOptions = {
          disabledDate(time) {
            return time.getTime() > data.planEtime
          },
        }
        this.disabledStart = true
      } else if (!data.planStime && !data.planEtime) {
        this.disabledStart = true
        this.disabledEnd = true
      }
    },
    onClose() {
      $emit(this, 'update:visible', false)
      this.$refs.iterationForm.resetFields()
    },
    changeTime() {
      this.iterationForm['planEtime'] = ''
      // this.$refs.iterationForm.clearValidate();

      this.$nextTick(() => {
        this.$refs.iterationForm.clearValidate(['planEtime'])
      })
    },
    changeEnd() {
      if (
        this.iterationForm.planStime &&
        this.iterationForm.planStime > this.iterationForm.planEtime
      ) {
        this.$message.warning('开始日期不能大于结束日期')
        this.changeTime()
      }
    },
    // 查询负责人下拉框数据
    async getUserList() {
      const res = await apiProjectUserNoPage({
        projectId: this.$route.params.id,
      })

      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      this.userList = res.data
      const userInfo = storage.get('user')
      this.iterationForm['leadingBy'] = userInfo.id
    },
    async sureAdd() {
      this.saveLoading = true
      try {
        await this.$refs.iterationForm.validate()
      } catch (error) {
        this.saveLoading = false
        return
      }

      const { isSuccess, msg } = await apiAlmProjectPlanAdd({
        ...this.iterationForm,
      }).catch(() => {
        this.saveLoading = false
      })
      if (!isSuccess) {
        this.$message.warning(msg)
        this.saveLoading = false
        return
      }
      this.saveLoading = false
      this.$message.success('保存成功')
      this.onClose()
      $emit(this, 'success')
    },
    async getPlanInfo() {
      const res = await apiAlmPlanInfo(this.id)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.iterationForm = res.data
      this.iterationForm.enable = res.data.enable ?? false
    },
  },
  emits: ['update:visible', 'success'],
}
</script>
