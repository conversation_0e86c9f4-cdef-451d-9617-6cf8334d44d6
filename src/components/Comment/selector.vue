<template>
  <div class="content">
    <el-input
      v-model="value"
      size="mini"
      :placeholder="placeholder"
      @input="inputchang"
    />
    <ul
      v-if="datatype === 'user'"
      class="el-scrollbar__view el-select-dropdown__list"
    >
      <li
        v-for="item in filterFruitList"
        :key="item.id"
        class="user_item"
        @click="selector(item)"
      >
        <vone-user-avatar
          :avatar-path="item.avatarPath"
          :avatar-type="true"
          height="22px"
          width="22px"
          :name="item.name"
          :show-name="true"
        />
        <i
          class="iconfont el-icon-icon-system-selected"
          style="color: var(--main-theme-color)"
        ></i>
      </li>
      <vone-empty v-if="filterFruitList.length < 1" />
    </ul>
    <ul
      v-if="datatype === 'work'"
      class="el-scrollbar__view el-select-dropdown__list work"
    >
      <li
        v-for="item in opstinList"
        :key="item.id"
        class="el-select-dropdown__item"
        style="display: flex"
        @click="selector(item)"
      >
        <i
          :class="`iconfont ${item.echoMap.typeCode.icon}`"
          :style="{
            color: `${
              item.echoMap.typeCode ? item.echoMap.typeCode.color : '#ccc'
            }`,
          }"
        />
        <span class="fontstyle">{{ item.code }} {{ item.name }}</span>
      </li>
      <vone-empty v-if="opstinList.length < 1" />
    </ul>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from "../../utils/gogocodeTransfer";
import { apiProjectUserNoPage } from "@/api/vone/project/index";
import { apiBaseAllUserNoPage } from "@/api/vone/base/user";
import { getworkitem } from "@/api/vone/project/comment";

export default {
  components: {},
  name: "Selector",
  props: {
    placeholder: {
      type: String,
      default: "",
    },
    datatype: {
      type: String,
      default: "",
    },
    searchtype: {
      type: Boolean,
      default: false,
    },
    selected: {
      type: Array,
      default: () => [],
    },
    projectId: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      value: "",
      opstinList: [],
      timer: null,
    };
  },
  computed: {
    filterFruitList() {
      return this.opstinList.filter((item) => {
        return item.name.indexOf(this.value) !== -1;
      });
    },
  },
  mounted() {},
  methods: {
    getInitData() {
      if (this.opstinList.length > 0) return;
      if (this.datatype === "user") {
        const data = this.projectId || this.$route.params.id;
        if (data) {
          this.getProjectUser(data);
        } else {
          this.getUserList();
        }
      } else if (this.datatype === "work") {
        this.getworkitemFn();
      }
    },
    inputchang(e) {
      this.value = e;
      if (this.searchtype) {
        if (this.timer) clearTimeout(this.timer);
        this.timer = setTimeout(() => this.getworkitemFn(), 1000);
      }
    },
    async getProjectUser(e) {
      // const isPro = this.$route.params.id
      const res = await apiProjectUserNoPage({
        projectId: e,
      });

      if (!res.isSuccess) {
        return;
      }
      this.opstinList = res.data;
    },
    async getUserList() {
      const res = await apiBaseAllUserNoPage();

      if (!res.isSuccess) {
        return;
      }
      // res.data.forEach(element => {
      //   element.userId = element.id
      // })
      this.opstinList = res.data;
    },
    async getworkitemFn() {
      const res = await getworkitem({
        search: this.value,
      });

      if (!res.isSuccess) {
        return;
      }
      this.opstinList = res.data;
    },
    selector(e) {
      $emit(this, "selectedChang", e);
    },
  },
  emits: ["selectedChang"],
};
</script>

<style lang="scss" scoped>
.custom-theme-dark {
  .el-select-dropdown__item:hover {
    background: #3e4657 !important;
  }
}
.el-select-dropdown__item {
  line-height: 34px;
  cursor: pointer;
  align-items: center;
}
.user_item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 32px;
  padding-left: 4px;
  cursor: pointer;
  &:hover {
    background-color: var(--hover-bg-color);
  }
}
.el-scrollbar__view {
  height: 200px;
  margin-right: -12px;
  overflow-y: auto;
}
.iconfont {
  line-height: 22px;
  margin-right: 9px;
}
.fontstyle {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.work {
  width: 300px;
}
</style>
