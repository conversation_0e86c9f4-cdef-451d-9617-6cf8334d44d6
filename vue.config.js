'use strict'
const path = require('path')
const CompressionPlugin = require('compression-webpack-plugin')
const { codeInspectorPlugin } = require('code-inspector-plugin')

function resolve(dir) {
  return path.join(__dirname, dir)
}
const name = 'va' || ' ' // page title
const port = 8888 // dev port

module.exports = {
  publicPath: '/',
  outputDir: 'dist',
  assetsDir: 'static',
  lintOnSave: process.env.NODE_ENV === 'development',
  productionSourceMap: false,

  // 关键配置：转译指定依赖
  transpileDependencies: [
    'lib0',
    'yjs',
    '@aomao/engine'
  ],

  css: {
    requireModuleExtension: true,
    loaderOptions: {
      css: {
        modules: {
          localIdentName: '[local]_[hash:base64:5]'
        },
        localsConvention: 'camelCaseOnly'
      }
      // 安全配置
      // postcss: {
      //   plugins: []
      // }
    }
  },

  devServer: {
    port: port,
    public: 'localhost:8888',
    open: true,
    overlay: {
      warnings: false,
      errors: true
    },
    proxy: {
      '/api': {
        target: `http://*************:8770`,
        changeOrigin: true,
        pathRewrite: {}
      }
    }
  },

  configureWebpack: {
    name: name,
    resolve: {
      alias: {
        '@': resolve('src')
      }
    },
    plugins: [
      new CompressionPlugin({
        cache: false,
        test: /\.(js|css|html)?$/i,
        filename: '[path].gz[query]',
        algorithm: 'gzip',
        minRatio: 0.8
      })
    ]
  },

  chainWebpack(config) {
    config.plugins.delete('preload')
    config.plugins.delete('prefetch')

    // 添加 JavaScript 转译规则
    config.module
      .rule('modern-js')
      .test(/\.(js|mjs)$/)
      .include
      .add(/node_modules[\\/](lib0|yjs|@aomao)[\\/]/)
      .end()
      .use('babel-loader')
      .loader('babel-loader')
      .options({
        presets: [
          ['@babel/preset-env', {
            targets: {
              browsers: ['> 1%', 'last 2 versions', 'not ie <= 11']
            }
          }]
        ],
        plugins: [
          '@babel/plugin-proposal-optional-chaining',
          '@babel/plugin-proposal-nullish-coalescing-operator'
        ]
      })

    config.module
      .rule('bpmn')
      .test(/\.bpmn$/)
      .use('raw-loader')
      .loader('raw-loader')
      .end()

    config.module
      .rule('bpmnlint')
      .test(/\.bpmnlintrc$/)
      .use('bpmnlint-loader')
      .loader('bpmnlint-loader')
      .end()

    // SVG 配置
    config.module
      .rule('svg')
      .exclude.add(resolve('src/icons'))
      .end()

    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()

    // Vue 加载器配置
    config.module
      .rule('vue')
      .use('vue-loader')
      .loader('vue-loader')
      .tap(options => {
        options.compilerOptions.preserveWhitespace = true
        return options
      })
      .end()

    // Markdown 配置
    config.module
      .rule('md')
      .test(/\.md/)
      .use('vue-loader')
      .loader('vue-loader')
      .end()
      .use('vue-markdown-loader')
      .loader('vue-markdown-loader/lib/markdown-compiler')
      .options({
        raw: true,
        preventExtract: true
      })

    // 添加 codemirror CSS 特殊处理
    config.module
      .rule('codemirror-css')
      .test(/codemirror.*\.css$/)
      .use('vue-style-loader')
      .loader('vue-style-loader')
      .end()
      .use('css-loader')
      .loader('css-loader')
      .options({
        importLoaders: 1,
        esModule: false
      })
      .end()
      .use('postcss-loader')
      .loader('postcss-loader')
      .options({
        postcssOptions: {
          plugins: [
            require('autoprefixer')(),
            require('postcss-discard-empty')()
          ]
        }
      })

    // 禁用原有 CSS 规则对 codemirror 的处理
    config.module.rule('css').exclude.add(/node_modules[\\/]codemirror/)

    // 代码分割配置
    config
      .when(process.env.NODE_ENV !== 'development',
        config => {
          config
            .plugin('ScriptExtHtmlWebpackPlugin')
            .after('html')
            .use('script-ext-html-webpack-plugin', [{
              inline: /runtime\..*\.js$/
            }])
            .end()
          config
            .optimization.splitChunks({
              chunks: 'all',
              maxAsyncRequests: 5,
              maxInitialRequests: 3,
              minSize: 1000000,
              maxSize: 1024 * 800,
              minChunks: 1,
              cacheGroups: {
                vendors: {
                  test: /[\\/]node_modules[\\/]/,
                  priority: -10,
                  name(module) {
                    const packageName = module.context.match(/[\\/]node_modules[\\/](.*?)([\\/]|$)/)[1]
                    return `chunk.${packageName.replace('@', '')}`
                  },
                  reuseExistingChunk: true
                },
                elementUI: {
                  name: 'chunk-elementUI',
                  priority: 20,
                  test: /[\\/]node_modules[\\/]_?element-ui(.*)/
                },
                echarts: {
                  name: 'chunk-echarts',
                  priority: 19,
                  test: /[\\/]node_modules[\\/]_?echarts(.*)/
                },
                codemirror: {
                  name: 'chunk-codemirror',
                  priority: 18,
                  test: /[\\/]node_modules[\\/]_?codemirror(.*)/
                },
                bpmnJs: {
                  name: 'chunk-bpmnJs',
                  priority: 18,
                  test: /[\\/]node_modules[\\/]_?bpmn-js(.*)/
                },
                commons: {
                  name: 'chunk-commons',
                  test: resolve('src/components'),
                  minChunks: 3,
                  priority: 5,
                  reuseExistingChunk: true
                },
                default: {
                  priority: -20,
                  reuseExistingChunk: true,
                  filename: 'common.js'
                }
              }
            })
          config.optimization.runtimeChunk('single')
        }
      )

    config.plugin('code-inspector-plugin').use(
      codeInspectorPlugin({
        bundler: 'webpack'
      })
    )
  }
}
