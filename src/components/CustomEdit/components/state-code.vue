<template>
  <!-- 需求状态流转 -->
  <div>
    <el-dropdown
      ref="dropdown"
      trigger="click"
      :disabled="isDone || infoDisabled"
      @command="handleCommand"
    >
      <a @click="findNextNode">
        <span style="display: flex; align-items: center">
          <span class="codeName">
            <span
              v-if="form.stateCode && form.echoMap && form.echoMap.stateCode"
              >{{ form.echoMap.stateCode.name }}</span
            >
            <span v-else>
              {{ form.stateCode }}
            </span>
          </span>

          <el-icon><el-icon-loading /></el-icon>
          <el-icon class="iconfont el-icon--right"
            ><el-icon-direction-down
          /></el-icon>
        </span>
      </a>
      <template v-slot:dropdown>
        <el-dropdown-menu class="state-flow">
          <el-dropdown-item
            v-for="item in nextNode"
            :key="item.id"
            :command="item.stateCode"
            >{{ item.name }}</el-dropdown-item
          >
          <el-dropdown-item
            v-if="nextNode.length > 0"
            command="workFlow"
            divided
          >
            <span style="display: flex; align-items: center">
              <el-icon
                class="iconfont"
                style="color: #3e7bfa; margin-right: 4px"
                ><el-icon-gongzuoliu
              /></el-icon>
              查看工作流
            </span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    <el-dialog
      title="工作流"
      v-model:value="flowVisible"
      top="7vh"
      append-to-body
      @before-close="closeFlow"
    >
      <vone-work-flow
        ref="vone-g"
        :xml="xml"
        :show-bar="false"
        hide-text-annotation
        single
        :node-style="nodeStyle"
        :properties-props="{ width: 250 }"
      />
      <template v-slot:footer>
        <div>
          <el-button type="primary" @click="closeFlow">确认</el-button>
        </div>
      </template>
    </el-dialog>
    <requiredFields
      v-bind="requireParam"
      v-if="requireParam.visible"
      v-model:value="requireParam.visible"
      @success="$emit('changeFlow')"
    />
  </div>
</template>

<script>
import {
  Loading as ElIconLoading,
  DirectionDown as ElIconDirectionDown,
  Gongzuoliu as ElIconGongzuoliu,
} from "@element-plus/icons-vue";
import { $on, $off, $once, $emit } from "../../../utils/gogocodeTransfer";
import { apiFindNextNode, apiAlmChangeFlow } from "@/api/vone/project/issue";
import { jsonToXml } from "@/views/vone/base/project-config/tab/common/xmlUtils";
import requiredFields from "./require-fields.vue";
import { getFlowDetail } from "@/api/vone/project/index";

export default {
  components: {
    requiredFields,
    ElIconLoading,
    ElIconDirectionDown,
    ElIconGongzuoliu,
  },
  props: {
    code: {
      type: String,
      default: "",
    },
    form: {
      type: Object,
      default: () => {},
    },
    infoDisabled: {
      type: Boolean,
      default: false,
    },
    targetId: {
      // 需求/缺陷/任务/风险的id
      type: String,
      default: "",
    },
  },
  data() {
    return {
      typeCode: null,
      nextNode: [],
      onSearch: false,
      isDone: false,
      flowVisible: false,
      requireParam: { visible: false },
      formInfo: {},
      xml: null,
      nodeStyle: {
        width: 100,
        height: 36,
      },
    };
  },
  mounted() {
    this.formInfo = JSON.parse(JSON.stringify(this.form));
    if (this.code && this.code == "DONE") {
      this.isDone = true;
      return;
    }

    this.typeCode =
      this.formInfo &&
      this.formInfo.typeCode &&
      this.formInfo.echoMap &&
      this.formInfo.echoMap.typeCode
        ? this.formInfo.echoMap.typeCode.classify.code
        : "";
  },
  methods: {
    closeFlow() {
      this.flowVisible = false;
    },
    async getFlowInfo() {
      let nodes = [];
      let edges = [];
      // 查询工作流
      // const flowMap = {
      //   'IDEA': '1',
      //   'ISSUE': '2',
      //   'BUG': '4',
      //   'TASK': '3',
      //   'RISK': '5'

      // }
      const res = await getFlowDetail(
        this.form.projectId,
        this.typeCode,
        this.form.typeCode
      );
      if (res.isSuccess) {
        if (
          res.data.workflowInfo?.workflowNodes.length > 0 &&
          res.data.workflowInfo?.workflowTransitions.length > 0
        ) {
          this.workflowId = res.data.workflowInfo.workflowNodes[0].workflowId;
          nodes = res.data.workflowInfo.workflowNodes;
          edges = res.data.workflowInfo.workflowTransitions;
          nodes.map((item) => {
            item.nodeType = item.nodeType.code;
            item.color = item.echoMap?.stateCode?.color;
            item.onlyId = item.id;
            item.id = item.code;
            delete item.shape;
            delete item.size;
            delete item.echoMap;
          });
          edges.map((item) => {
            item.onlyId = item.id;
            item.id = item.code;
            item.source = item.sourceAnchor;
            item.target = item.targetAnchor;
          });
          this.xml = jsonToXml({ nodes, edges });
        }
      }
    },
    async handleCommand(val) {
      if (val === "workFlow") {
        this.flowVisible = true;
        this.getFlowInfo();
        return;
      }

      if (this.nextNode.find((r) => r.stateCode == val).echoMap.check.length) {
        const check = this.nextNode.find((r) => r.stateCode == val).echoMap
          .check;
        const list = [];
        check.forEach((element) => {
          list.push(element.echoMap.field);
        });

        const targetName = this.nextNode.find((r) => r.stateCode == val).name;

        this.requireParam = {
          visible: true,
          infoData: this.formInfo,
          fields: list,
          sourceNode: this.formInfo.stateCode,
          sourceName: this.formInfo?.echoMap?.stateCode?.name,
          targetName: targetName,
          targetNode: val,
          typeClassfiy: this.formInfo?.echoMap?.typeCode?.classify?.code,
          dataId: this.targetId,
        };

        return;
      }

      // 判断是项目下的风险还是项目集下的风险

      const urlMap = {
        IDEA: `/api/alm/alm/idea/transitionState/${this.targetId}/${this.code}/${val}`,
        ISSUE: `/api/alm/alm/requirement/transitionState/${this.targetId}/${this.code}/${val}`,
        BUG: `/api/alm/alm/bug/transitionState/${this.targetId}/${this.code}/${val}`,
        TASK: `/api/alm/alm/task/transitionState/${this.targetId}/${this.code}/${val}`,
        RISK: `/api/alm/alm/risk/transitionState/${this.targetId}/${
          this.code
        }/${val}/${
          this.$route.name == "projectm_risk" ? "program" : "project"
        }`,
      };

      const res = await apiAlmChangeFlow(urlMap[this.typeCode]);

      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      this.$message.success("状态流转成功");
      this.nextNode = [];
      $emit(this, "changeFlow");
    },
    async findNextNode() {
      try {
        if (this.isDone || this.infoDisabled) {
          return;
        }

        const urlMap = {
          IDEA: `/api/alm/alm/idea/findNextNode/${this.targetId}`,
          ISSUE: `/api/alm/alm/requirement/findNextNode/${this.targetId}`,
          BUG: `/api/alm/alm/bug/findNextNode/${this.targetId}`,
          TASK: `/api/alm/alm/task/findNextNode/${this.targetId}`,
          RISK: `/api/alm/alm/risk/findNextNode/${this.targetId}/${
            this.$route.name == "projectm_risk" ? "program" : "project"
          }`,
        };

        this.onSearch = true;
        const res = await apiFindNextNode(urlMap[this.typeCode]);
        this.onSearch = false;
        if (!res.isSuccess) {
          this.$message.warning(res.msg);
          return;
        }

        if (!res.data.length) {
          this.$refs.dropdown.hide();
          this.$message.warning("暂无当前节点流转权限");
          this.isDone = true;
          return;
        }
        this.nextNode = res.data;
      } catch (error) {
        this.onSearch = false;
      }
    },
  },
  emits: ["changeFlow"],
};
</script>

<style lang="scss" scoped>
:deep(.bpmn) {
  height: calc(100vh - 270px);
}
.codeName {
  margin-left: 12px;
  color: var(--main-font-color);
  max-width: 180px;
  display: inline-block;
  padding-left: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
:deep(.iconfont el-icon-direction-down) {
  display: none;
}
.state-flow.el-dropdown-menu {
  :deep() {
    .el-dropdown-menu__item {
      display: block;
      text-align: center;
    }
  }
}
</style>
