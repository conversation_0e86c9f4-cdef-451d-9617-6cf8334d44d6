<template>
  <div v-loading="pageLoading">
    <vone-lane
      v-if="allBoards.length > 0"
      ref="lane"
      :dragstart="dragstart"
      @added="laneAdded"
      @updateCardStatus="updateCardStatus"
    >
      <template v-slot:search>
        <el-popover
          v-model="boardShow"
          placement="bottom-start"
          trigger="click"
          popper-class="board-popper"
        >
          <el-input v-model="filterboard" placeholder="请输入关键字" />
          <div v-if="boardTypeList.length == 0" class="nothing">暂无数据</div>
          <ul v-else class="board-list" @click="boardTypeClick">
            <li
              v-for="(item, index) in boardTypeList"
              :key="item.id"
              class="board-item"
              :class="{ 'is-active': boardType === item.id }"
              :data-id="item.id"
              :data-name="item.name"
            >
              {{ item.name }}
              <span
                v-if="index === 0"
                class="default"
                :data-id="item.id"
                :data-name="item.name"
                >默认</span
              >
            </li>
          </ul>
          <footer @click="jumpToConfig">
            <el-icon class="iconfont"
              ><el-icon-application-step-setting
            /></el-icon>
            看板管理
          </footer>
          <template v-slot:reference>
            <span class="board-name">
              <el-tooltip
                v-if="boardTypeName && boardTypeName.length > 10"
                :content="boardTypeName"
                placement="top"
              >
                <span class="board-title text-over">{{ boardTypeName }}</span>
              </el-tooltip>
              <span v-else class="board-title text-over">{{
                boardTypeName
              }}</span>
              <el-icon class="iconfont el-icon--right"
                ><el-icon-direction-down
              /></el-icon>
            </span>
          </template>
        </el-popover>
        <el-divider direction="vertical" style="margin: 0px 8px 0px 16px" />
        <!-- 查询条件 -->
        <!-- 排序 -->
        <el-dropdown
          trigger="click"
          placement="bottom"
          @command="sortCmdChange"
        >
          <span class="filter-group">
            <span style="color: var(--font-second-color); margin-right: 4px"
              >排序</span
            >

            {{ sortActive.name }}
            <i
              v-if="sortActive.id !== 1"
              :class="[sortActive.icon, sortActive.id !== 1 ? 'active' : '']"
              style="color: #3e7bfa"
            />
          </span>
          <template v-slot:dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="item in sortList"
                :key="item.id"
                :command="item.id"
              >
                {{ item.name }}
                <i v-if="item.icon" :class="item.icon" />
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <!-- 分组 -->
        <el-dropdown
          trigger="click"
          placement="bottom"
          @command="groupCmdChange"
        >
          <span class="filter-group">
            <span style="color: var(--font-second-color); margin-right: 4px"
              >分组</span
            >
            <el-icon class="iconfont" style="margin-right: 6px"
              ><el-icon-line-fenzu /></el-icon
            >{{ groupActive.name }}
            <el-icon class="iconfont el-icon--right"
              ><el-icon-direction-down
            /></el-icon>
          </span>
          <template v-slot:dropdown>
            <el-dropdown-menu class="groupDrop">
              <el-dropdown-item
                v-for="item in dropList"
                :key="item.key"
                :command="item.key"
                :class="item.class"
                :disabled="!!item.class"
                >{{ item.name }}</el-dropdown-item
              >
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <vone-search-dynamic
          :show-filter="false"
          :project-id="$route.params.id"
          card-key="billboard-card"
          :extra="extraData"
          :model="searchForm"
          label-position="top"
          :is-cards="true"
          class="searchForm"
          @getTableData="searchBoardData"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="名称" prop="name">
                <el-input
                  v-model="searchForm.name"
                  placeholder="请输入名称"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="优先级" prop="priorityCode">
                <vone-icon-select
                  v-model:value="searchForm.priorityCode"
                  filterable
                  clearable
                  multiple
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in prioritList"
                    :key="item.key"
                    :label="item.name"
                    :value="item.code"
                  >
                    <i
                      :class="`iconfont ${item.icon}`"
                      :style="{
                        color: item.color,
                        fontSize: '16px',
                        paddingRight: '6px',
                      }"
                    />
                    {{ item.name }}
                  </el-option>
                </vone-icon-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="负责人" prop="leadingBy">
                <vone-remote-user
                  v-model:value="searchForm.leadingBy"
                  multiple
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="处理人" prop="handleBy">
                <vone-remote-user
                  v-model:value="searchForm.handleBy"
                  multiple
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="延期" prop="delay">
                <el-radio-group v-model="searchForm.delay">
                  <el-radio label="all">全部</el-radio>
                  <el-radio :label="true">是</el-radio>
                  <el-radio :label="false">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </vone-search-dynamic>
      </template>
      <template v-slot:actions>
        <el-dropdown trigger="click" class="stage-list" @command="changeSprint">
          <span class="board-name">
            {{ currentSprint ? currentSprint.name : "暂无迭代" }}
            <span v-if="currentSprint" style="margin-left: 4px">
              {{ format_filter(currentSprint.planStime) }} -
              {{ format_filter(currentSprint.planEtime) }}
            </span>
            <el-icon
              class="iconfont el-icon--right"
              style="color: var(--font-second-color)"
              ><el-icon-direction-down
            /></el-icon>
          </span>
          <template v-slot:dropdown>
            <el-dropdown-menu style="min-width: 100px">
              <el-dropdown-item
                v-for="sprint in sprintList"
                :key="sprint.id"
                :command="sprint"
                :class="{ 'is-active': currentSprint.id === sprint.id }"
              >
                {{ sprint.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <el-checkbox-group
          v-model="checkMain"
          style="margin-left: 16px"
          class="mainBtn"
          @change="showMyWork"
        >
          <el-checkbox-button label="main">只看我的</el-checkbox-button>
        </el-checkbox-group>
        <!-- 新建看板 -->
        <el-dropdown @command="showAdd">
          <el-button-group>
            <el-button
              class="subBtton"
              style="color: #fff; margin-right: 16px"
              type="primary"
              :disabled="!$permission('project_iteration_add_all')"
              :icon="ElIconPlus"
            />
          </el-button-group>
          <template v-slot:dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="ISSUE">需求</el-dropdown-item>
              <el-dropdown-item command="BUG">缺陷</el-dropdown-item>
              <el-dropdown-item command="TASK">任务</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
      <!-- 看板内容显示区域 -->
      <div class="board-wrap">
        <div
          v-if="boardColumns.length > 0"
          v-loading="boardLoading"
          class="scroll-wrap"
        >
          <header class="header-wrap">
            <span
              v-for="item in boardColumns"
              :key="item.name"
              class="stage-wrap"
              >{{ item.name
              }}<span class="num-wrap">{{
                boardList[item.id] && boardList[item.id].length
              }}</span></span
            >
          </header>
          <el-collapse v-if="groupActive.key !== 1" accordion>
            <template v-for="(value, key) in collapseGroup">
              <el-collapse-item v-if="key != '未分组'" :name="key">
                <template v-slot:title>
                  <el-icon
                    class="iconfont"
                    style="color: #6b7385; margin-right: 8px"
                    ><el-icon-direction-caret-down
                  /></el-icon>
                  <template v-if="groupActive.key === 'handleBy'">
                    <vone-user-avatar
                      v-if="hanldeEchoMap[key]"
                      :avatar-path="hanldeEchoMap[key].avatarPath"
                      :name="hanldeEchoMap[key].name"
                      height="24px"
                      width="24px"
                    />
                  </template>
                  <template v-else-if="groupActive.key === 'parentId'">
                    <div v-if="parentEchoMap[key]" class="parentItem">
                      <span class="name-wrap">
                        <!-- 类型图标 -->
                        <i
                          v-if="
                            parentEchoMap[key].echoMap &&
                            parentEchoMap[key].echoMap.typeCode
                          "
                          class="iconfont"
                          :class="parentEchoMap[key].echoMap.typeCode.icon"
                          :style="{
                            color: parentEchoMap[key].echoMap.typeCode.color,
                          }"
                        />
                        <!-- 编号 -->
                        <span v-if="parentEchoMap[key].code">{{
                          parentEchoMap[key].code
                        }}</span>
                        <!-- 名称 -->
                        <span v-if="parentEchoMap[key]"
                          >{{ parentEchoMap[key].name }}123</span
                        >
                      </span>
                      <span
                        >计划完成时间：{{ parentEchoMap[key].planEtime }}</span
                      >
                      <template v-if="parentEchoMap[key].echoMap">
                        <!-- 状态 -->
                        <span
                          v-if="parentEchoMap[key].echoMap.stateCode"
                          class="tag"
                          :style="{
                            color: parentEchoMap[key].echoMap.stateCode.color,
                          }"
                          >{{ parentEchoMap[key].echoMap.stateCode.name }}</span
                        >
                        <!-- 处理人 -->
                        <vone-user-avatar
                          v-if="parentEchoMap[key].echoMap.handleBy"
                          :avatar-path="
                            parentEchoMap[key].echoMap.handleBy.avatarPath
                          "
                          :name="parentEchoMap[key].echoMap.handleBy.name"
                          height="24px"
                          width="24px"
                        />
                        <!-- 优先级 -->
                        <i
                          v-if="parentEchoMap[key].echoMap.priorityCode"
                          class="iconfont"
                          :class="parentEchoMap[key].echoMap.priorityCode.icon"
                          :style="{
                            color:
                              parentEchoMap[key].echoMap.priorityCode.color,
                          }"
                        />
                      </template>
                    </div>
                  </template>
                  <template v-else-if="groupActive.key === 'requirementId'">
                    <div v-if="issueEchoMap[key]" class="parentItem">
                      <span class="name-wrap">
                        <!-- 编号 -->
                        <span v-if="issueEchoMap[key].code" class="code">{{
                          issueEchoMap[key].code
                        }}</span>
                        <!-- 名称 -->
                        <span>{{ issueEchoMap[key].name }}</span>
                      </span>
                      <template v-if="issueEchoMap[key].echoMap">
                        <!-- 状态 -->
                        <span
                          v-if="issueEchoMap[key].echoMap.stateCode"
                          class="tag"
                          :style="{
                            color: issueEchoMap[key].echoMap.stateCode.color,
                          }"
                          >{{ issueEchoMap[key].echoMap.stateCode.name }}</span
                        >
                        <!-- 处理人 -->
                        <vone-user-avatar
                          v-if="issueEchoMap[key].echoMap.handleBy"
                          :avatar-path="
                            issueEchoMap[key].echoMap.handleBy.avatarPath
                          "
                          :name="issueEchoMap[key].echoMap.handleBy.name"
                          height="24px"
                          width="24px"
                        />
                      </template>
                    </div>
                  </template>
                  <template v-else>
                    <div
                      v-if="customEchoMap[key]"
                      style="display: flex; align-items: center; gap: 4px"
                    >
                      <i
                        v-if="
                          customEchoMap[key].icon &&
                          groupActive.key != 'sourceCode' &&
                          groupActive.key !== 'productId'
                        "
                        class="iconfont"
                        :class="customEchoMap[key].icon"
                        :style="{ color: customEchoMap[key].color }"
                      />
                      <span v-if="customEchoMap[key].typeCode">{{
                        customEchoMap[key].code
                      }}</span>
                      {{ customEchoMap[key].name || customEchoMap[key] }}
                    </div>
                  </template>

                  <span class="title_len"> {{ value.length }}</span
                  >个工作项
                </template>
                <div
                  v-for="item in boardColumns"
                  :key="item.name"
                  class="stageBox"
                >
                  <vone-lane-item
                    :key="item.id"
                    :value="item.id"
                    :disabled="disabledBoard"
                    :list="value.list[item.id]"
                    :statuses="item.statuses"
                    :state-code-map="stateCodeMap"
                  >
                    <vone-lane-card
                      v-for="ele in value.list[item.id]"
                      :key="ele.id"
                      :title="ele.name"
                      :type="ele.typeCode"
                      :item="ele"
                      :code="ele.code"
                      :date="ele.planEtime"
                      date-tooltip="截止时间"
                      :user="ele.handleBy"
                      :default-fields="defaultFields"
                      :custom-fields="customFields"
                      @click="showDetails(ele)"
                      @flagClick="flagClick"
                    />
                  </vone-lane-item>
                </div>
              </el-collapse-item>
            </template>
            <!-- 未分组排在末尾 -->
            <el-collapse-item v-if="collapseGroup['未分组']" name="未分组">
              <template v-slot:title>
                <el-icon
                  class="iconfont"
                  style="color: #6b7385; margin-right: 8px"
                  ><el-icon-direction-caret-down
                /></el-icon>
                <template v-if="groupActive.key === 'handleBy'">
                  <vone-user-avatar name="未分组" height="24px" width="24px" />
                </template>
                <div v-else>未分组</div>
                <span class="title_len">
                  {{ collapseGroup["未分组"].length }}</span
                >个工作项
              </template>
              <div
                v-for="item in boardColumns"
                :key="item.name"
                class="stageBox"
              >
                <vone-lane-item
                  :key="item.id"
                  :value="item.id"
                  :disabled="disabledBoard"
                  :list="collapseGroup['未分组'].list[item.id]"
                  :statuses="item.statuses"
                  :state-code-map="stateCodeMap"
                >
                  <vone-lane-card
                    v-for="ele in collapseGroup['未分组'].list[item.id]"
                    :key="ele.id"
                    :title="ele.name"
                    :type="ele.typeCode"
                    :item="ele"
                    :code="ele.code"
                    :date="ele.planEtime"
                    date-tooltip="截止时间"
                    :user="ele.handleBy"
                    :default-fields="defaultFields"
                    :custom-fields="customFields"
                    @click="showDetails(ele)"
                    @flagClick="flagClick"
                  />
                </vone-lane-item>
              </div>
            </el-collapse-item>
          </el-collapse>
          <div v-else class="scroll-body">
            <div v-for="item in boardColumns" :key="item.name" class="stageBox">
              <vone-lane-item
                :key="item.id"
                :value="item.id"
                :disabled="disabledBoard"
                :list="boardList[item.id]"
                :statuses="item.statuses"
                :state-code-map="stateCodeMap"
              >
                <vone-lane-card
                  v-for="ele in boardList[item.id]"
                  :key="ele.id"
                  :title="ele.name"
                  :type="ele.typeCode"
                  :item="ele"
                  :code="ele.code"
                  :date="ele.planEtime"
                  date-tooltip="截止时间"
                  :user="ele.handleBy"
                  :default-fields="defaultFields"
                  :custom-fields="customFields"
                  @click="showDetails(ele)"
                  @flagClick="flagClick"
                />
              </vone-lane-item>
            </div>
          </div>
        </div>
        <vone-empty v-else />
      </div>
    </vone-lane>
    <emptyInfo v-else :sprint-list="sprintList" />
    <!-- 新增 -->
    <vone-custom-add
      v-bind="addParam"
      v-if="addParam.visible"
      v-model:value="addParam.visible"
      @success="updateBoardList"
    />
    <!-- 编辑完整需求 -->
    <vone-custom-edit
      v-bind="issueParam"
      v-if="issueParam.visible"
      v-model:value="issueParam.visible"
      type-code="ISSUE"
      :right-tabs="rightTabs"
      hide-prv
      @success="loadKanbanData"
      @initList="updateBoardList"
    />

    <!-- 编辑完整任务 -->
    <vone-custom-edit
      v-bind="taskParam"
      v-if="taskParam.visible"
      v-model:value="taskParam.visible"
      type-code="TASK"
      :right-tabs="rightTabs"
      hide-prv
      @success="updateBoardList"
    />
    <!-- 编辑完整缺陷 -->
    <vone-custom-edit
      v-bind="defectParam"
      v-if="defectParam.visible"
      v-model:value="defectParam.visible"
      type-code="BUG"
      :right-tabs="rightTabs"
      hide-prv
      @success="updateBoardList"
    />

    <!-- 流转前的必填字段校验 -->
    <requiredFields
      v-bind="requireParam"
      v-if="requireParam.visible"
      v-model:value="requireParam.visible"
      @success="updateBoardList"
    />
    <el-dialog
      v-if="flagVisible"
      title="添加标志和备注"
      width="40%"
      v-model:value="flagVisible"
      :close-on-click-modal="false"
    >
      <el-icon class="iconfont" style="color: #f27900; margin-right: 10px"
        ><el-icon-xiaoqi
      /></el-icon>
      <span class="flag-title">在添 {{ flagItem.name }} 加备注 </span>
      <el-form
        ref="flagForm"
        style="margin-top: 16px"
        :model="flagForm"
        :rules="rules"
      >
        <el-form-item label-width="0" label="" prop="remark">
          <el-input
            v-model="flagForm.remark"
            type="textarea"
            :rows="4"
            placeholder="请输入内容"
          />
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <div class="dialog-footer foot">
          <el-button
            @click="
              flagItem.name = '';
              flagVisible = false;
            "
            >取消</el-button
          >
          <el-button
            :loading="flagLoading"
            type="primary"
            @click="flagFormSubmit"
            >确定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  ApplicationStepSetting as ElIconApplicationStepSetting,
  DirectionDown as ElIconDirectionDown,
  LineFenzu as ElIconLineFenzu,
  DirectionCaretDown as ElIconDirectionCaretDown,
  Xiaoqi as ElIconXiaoqi,
  Plus as ElIconPlus,
} from "@element-plus/icons-vue";
import { cloneDeep } from "lodash";
import * as dayjs from "dayjs";

import {
  getProjectBoards,
  getBoardInfoByKanbanId,
  getBoardCardsInfo,
  addFlagRemark,
  getFlagRemark,
  dalFlagRemark,
} from "@/api/vone/project/board";
import { apiAlmProjectPlanNoPage } from "@/api/vone/project/iteration";
import {
  apiAlmIssueFindNextNode,
  apiAlmRequirementFlow,
} from "@/api/vone/project/issue";
import {
  apiAlmTaskFindNextNode,
  apiAlmTaskFlow,
} from "@/api/vone/project/task";
import {
  apiAlmBugFindNextNode,
  apiAlmBugFlow,
} from "@/api/vone/project/defect";
import { apiAlmStateNoPage, apiAlmTypePage } from "@/api/vone/base/work-flow";
import { apiAlmPriorityNoPage, apiAlmTypeNoPage } from "@/api/vone/alm";
import { apiVaBaseCustomProjectFormCheck } from "@/api/vone/base/customForm";

import { catchErr } from "@/utils";

import emptyInfo from "./empty-info.vue";
import requiredFields from "@/components/CustomEdit/components/require-fields.vue";
import { getSourceById, getSourceFormData } from "@/api/vone/base/source";

const originalDropList = [
  { name: "推荐", class: "group_title" },
  { key: 1, name: "无泳道" },
  { key: "handleBy", name: "按处理人" },
  { key: "requirementId", name: "按需求" },
  // { id: 'release', name: '按关联工作项' }
];

export default {
  data() {
    return {
      extraData: {},
      boardShow: false,
      filterboard: "",
      boardTypeName: "",
      // 切换看板类型
      boardType: null,
      // 看板类型列表
      boardTypeList: [],
      allBoards: [],
      checkMain: [],
      // 分组列表
      dropList: [],
      groupActive: {
        name: "无泳道",
        key: 1,
      },
      sortList: [
        { id: 1, name: "默认排序" },
        {
          id: "priority_up",
          name: "优先级从高到低",
          icon: "iconfont el-icon-direction-arrow-fall",
        },
        {
          id: "priority_down",
          name: "优先级从低到高",
          icon: "iconfont el-icon-direction-arrow-rise",
        },
        {
          id: "planEtime_up",
          name: "计划完成时间从高到低",
          icon: "iconfont el-icon-direction-arrow-fall",
        },
        {
          id: "planEtime_down",
          name: "计划完成时间从低到高",
          icon: "iconfont el-icon-direction-arrow-rise",
        },
      ],
      sortActive: {
        name: "默认排序",
        id: 1,
      },
      pageLoading: true,
      boardLoading: false,
      // 看板泳道列表
      boardColumns: [],
      // 每一列里面的状态
      columnStatusMap: {},
      boardData: [],
      originalBoardList: {},
      // 看板数据
      boardList: {},
      // 处理人数据
      hanldeEchoMap: {},
      // 父工作项分组数据
      parentEchoMap: {},
      // 需求分组数据
      issueEchoMap: {},
      customEchoMap: {},
      // 泳道列表
      collapseGroup: [],
      // 迭代列表
      sprintList: [],
      sourceFormData: {},
      sourceFields: {},
      // 选择的迭代
      currentSprint: {},
      disabledBoard: false,
      // 编辑需求
      issueParam: { visible: false },
      // 缺陷编辑
      defectParam: { visible: false },
      // 编辑任务
      taskParam: { visible: false },
      // 筛选表单
      searchForm: {
        name: "",
        delay: "all",
        priorityCode: [],
        handleBy: [],
        leadingBy: [],
        stateCode: [],
      },
      prioritList: [],
      // 状态筛选数据
      stateCodeList: [],
      stateCodeMap: {},
      boardTypeCode: {},
      typeMap: {
        ISSUE: [],
        TASK: [],
        BUG: [],
      },
      classifyMap: {},
      bordConfig: {},
      // 当前卡片配置数据
      cardConfig: {},
      customFields: {},
      customGroup: [],
      // 是否显示自定义分组
      isCustomGroup: false,
      cacheCustom: {},
      defaultFields: {
        ISSUE: ["编号", "规模", "优先级", "计划完成时间", "处理人"],
        TASK: ["编号", "工时", "优先级", "计划完成时间", "处理人"],
        BUG: ["编号", "规模", "优先级", "计划完成时间", "处理人"],
      },
      rightTabs: [
        {
          active: true,
          label: "评论",
          name: "comment",
        },
        {
          label: "活动",
          name: "active",
        },
        {
          label: "工时",
          name: "workTime",
        },
      ],
      addParam: {
        visible: false,
      },
      requireParam: { visible: false },
      nextNode: [],
      flagVisible: false,
      flagLoading: false,
      flagForm: {},
      rules: {},
      flagItem: {},
      flagList: [],
      ElIconPlus,
    };
  },
  components: {
    emptyInfo,
    requiredFields,
    ElIconApplicationStepSetting,
    ElIconDirectionDown,
    ElIconLineFenzu,
    ElIconDirectionCaretDown,
    ElIconXiaoqi,
  },
  watch: {
    filterboard(val, old) {
      this.boardTypeList = val
        ? this.allBoards?.filter((item) => item.name.indexOf(val) > -1) || []
        : [...this.allBoards];
    },
  },
  async mounted() {
    this.dropList = [
      ...originalDropList,
      { key: "parentId", name: "按父工作项" },
    ];
    this.getStatusList();
    this.getPrioritList();
    this.getWorkItemTypes("ISSUE");
    this.getWorkItemTypes("TASK");
    this.getWorkItemTypes("BUG");
    await this.loadProjectSprint();
    await this.getFlagRemark();

    this.getBoardsInfo();
  },
  methods: {
    format_filter(val) {
      if (!val) return "";
      return dayjs(val).format("MM月DD日");
    },
    async getFlagRemark() {
      const [res, err] = await catchErr(
        getFlagRemark({
          kanbanId: this.boardType,
        })
      );
      if (err) {
        return;
      }
      if (res.isSuccess) {
        this.flagList = res.data;
      }
    },
    async flagClick(e, t) {
      if (t == "add") {
        this.flagItem = e;
        this.flagVisible = true;
      } else {
        await this.$confirm("确定删除标志和备注吗?", "删除", {
          type: "warning",
          closeOnClickModal: false,
          customClass: "delConfirm",
        });
        const res = await dalFlagRemark([e.flagId]);
        if (!res.isSuccess) {
          this.$message.warning(res.msg);
          return;
        }
        this.flagVisible = false;
        this.flagForm.remark = "";
        this.$message.success("删除成功");
        await this.getFlagRemark();
        // this.loadKanbanData(true)
        this.updateBoardList();
      }
    },
    async flagFormSubmit() {
      if (!this.flagForm.remark) {
        this.$message.warning("请填写备注");
        return;
      }
      const params = {
        classify: this.flagItem.echoMap?.classify?.code,
        flag: true,
        itemId: this.flagItem.id,
        kanbanId: this.boardType,
        remark: this.flagForm.remark,
      };
      const res = await addFlagRemark(params);
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      this.flagVisible = false;
      this.flagForm.remark = "";
      this.$message.success("新增成功");
      await this.getFlagRemark();
      // this.loadKanbanData(true)
      this.updateBoardList();
    },
    // 新增页判断
    showAdd(command) {
      const map = {
        ISSUE: "新增需求",
        BUG: "新增缺陷",
        TASK: "新增任务",
      };
      this.addParam = {
        visible: true,
        sprintId: this.currentSprint.id,
        key: command,
        typeCode: command,
        title: map[command],
      };
    },
    jumpToConfig() {
      const { projectKey, projectTypeCode, id } = this.$route.params;

      this.$router.push({
        path: `/project/setting/${projectKey}/${projectTypeCode}/${id}`,
        query: {
          active: "kanban_config",
        },
      });
    },
    // 点击菜单项触发的事件回调
    groupCmdChange(cmd) {
      const params = this.dropList.find((v) => v.key == cmd) || {};
      this.groupActive = params.key != 1 ? params : { name: "无泳道", key: 1 };
      const result = {};
      let echoMap = {};
      if (cmd !== 1) {
        switch (cmd) {
          case "handleBy":
            echoMap = this.hanldeEchoMap;
            this.cmdHandleByGrouping(result, cmd, echoMap);
            break;
          case "parentId":
            echoMap = this.parentEchoMap;
            this.cmdParentGrouping(result, cmd, echoMap);
            break;
          case "requirementId":
            echoMap = this.issueEchoMap;
            this.cmdIssueGrouping(result, cmd, echoMap);
            break;
          default:
            this.customEchoMap = {};
            echoMap = this.customEchoMap;
            this.cmdCustomByGrouping(result, cmd, echoMap);
        }
        for (const key in result) {
          result[key].list = result[key].list.reduce((acc, cur) => {
            acc[cur] = this.boardList[cur].filter((ele) =>
              key == "未分组" && !echoMap[ele[cmd]] && !result[ele.id]
                ? true
                : ele[cmd] === key
            );
            return acc;
          }, {});
        }
      }
      this.collapseGroup = result;
    },
    cmdHandleByGrouping(result, cmd, echoMap) {
      this.boardData.map((item) => {
        this.cmdGroupItem({
          echoData: item.echoMap?.handleBy,
          item,
          result,
          cmd,
          echoMap,
        });
      });
    },
    cmdParentGrouping(result, cmd, echoMap) {
      this.boardData.map((item) => {
        const echoData = this.boardData.find((v) => v.id === item[cmd]);
        if (!result[item.id]) {
          this.cmdGroupItem({ echoData, item, result, cmd, echoMap });
        }
      });
    },
    cmdIssueGrouping(result, cmd, echoMap) {
      this.boardData.map((item) => {
        const echoData = this.boardData.find((v) => v.id === item[cmd]);
        if (!result[item.id]) {
          this.cmdGroupItem({ echoData, item, result, cmd, echoMap });
        }
      });
    },
    cmdCustomByGrouping(result, cmd, echoMap) {
      const current = this.customGroup.find((v) => v.key === cmd);
      const config = current?.config ? JSON.parse(current.config) : {};
      const relationShipsheet = config.relationShipsheet || null;
      const firstNameId = this.sourceFields[relationShipsheet]?.[0]?.id;
      const options = config.options || [];

      this.boardData.map((item) => {
        let echoData = item.echoMap?.[cmd] || item[cmd];
        if (current) {
          // 设置关联字段显示的名称
          if (current.type?.code === "LINKED" && item[cmd]) {
            echoData = {
              name: this.sourceFormData?.[relationShipsheet]?.[item[cmd]]?.[
                firstNameId
              ],
            };
            // 设置自定义字段显示的名称
          } else if (!current.isBasic && !current.isBuilt && item[cmd]) {
            const obj = options.find((v) => v.id === item[cmd]);
            echoData = { name: obj?.name };
          }
        }

        this.cmdGroupItem({ echoData: echoData, item, result, cmd, echoMap });
      });
    },
    cmdGroupItem({ echoData, item, result, cmd, echoMap }) {
      // 判断当前分组显示数据是否存在
      if (echoData) {
        if (!result[item[cmd]]) {
          echoMap[item[cmd]] = echoData;
          result[item[cmd]] = {
            list: this.boardColumns.map((val) => val.id),
            length: 1,
          };
        } else {
          result[item[cmd]].length++;
        }
      } else {
        if (result["未分组"]) {
          result["未分组"].length++;
        } else {
          echoMap["未分组"] = { name: "未分组", id: -1 };
          result["未分组"] = {
            list: this.boardColumns.map((val) => val.id),
            length: 1,
          };
        }
      }
    },
    // 是否显示自定义分组
    checkCustomGroup() {
      let codeList = [];
      for (const key in this.classifyMap) {
        codeList = [...codeList, ...this.classifyMap[key]];
        this.boardTypeCode[this.boardType] =
          key != "BUG" ? key : this.boardTypeCode[this.boardType] || key;
      }
      if (this.cacheCustom[this.boardType]) {
        this.isCustomGroup = this.cacheCustom[this.boardType].length > 0;
        const list =
          this.boardTypeCode[this.boardType] === "BUG"
            ? originalDropList
            : [...originalDropList, { key: "parentId", name: "按父工作项" }];
        const custom =
          this.cacheCustom[this.boardType].length > 0
            ? [
                { name: "自定义", class: "group_title" },
                ...this.cacheCustom[this.boardType],
              ]
            : [];
        this.dropList = [...list, ...custom];
      } else {
        this.dropList =
          this.boardTypeCode[this.boardType] === "BUG"
            ? originalDropList
            : [...originalDropList, { key: "parentId", name: "按父工作项" }];
      }

      return codeList;
    },
    // 查询类型下所有分类
    async getIssueType(codeList) {
      const res = await apiAlmTypePage({
        current: 1,
        extra: {},
        model: { classify: "" },
        order: "descending",
        size: 99999,
        sort: "createTime",
      });
      if (res.isSuccess) {
        const typeCodes =
          res.data.records?.filter((ele) => codeList.indexOf(ele.code) > -1) ||
          [];
        let formId = null;
        let formFields = [];
        for (const val of typeCodes) {
          console.log(val.echoMap?.customFormId?.id, "val");
          if (!formId) {
            formId = val.echoMap?.customFormId?.id;
            formFields = val.echoMap?.customFormId?.customFormFields;
            this.isCustomGroup = true;
          } else if (formId != val.echoMap.customFormId.id) {
            formId = null;
            formFields = [];
            this.isCustomGroup = false;
            this.cacheCustom[this.boardType] = [];
            return;
          }
        }
        const exclude = ["planId", "tagId", "projectId", "typeCode"];
        this.customGroup =
          formFields?.filter(
            (v) =>
              exclude.indexOf(v.key) === -1 &&
              (v.type.code === "SELECT" || v.type.code === "LINKED")
          ) || [];
        this.cacheCustom[this.boardType] = this.customGroup;

        const list =
          this.boardTypeCode[this.boardType] === "BUG"
            ? originalDropList
            : [...originalDropList, { key: "parentId", name: "按父工作项" }];
        const custom =
          this.customGroup.length > 0
            ? [{ name: "自定义", class: "group_title" }, ...this.customGroup]
            : [];
        this.dropList = [...list, ...custom];
        // 查询关联字段对应的数据源id
        let relationShipsheet = formFields
          .filter((v) => v.type.code === "LINKED")
          .map((v) => JSON.parse(v.config).relationShipsheet);
        relationShipsheet = [...new Set(relationShipsheet)];
        console.log(relationShipsheet, "relationShipsheet");
        relationShipsheet.forEach((id) => {
          this.getSourceList(id);
          this.getSourceFormData(id);
        });
      }
    },
    // 查询数据源列配置数据
    async getSourceList(formId) {
      const res = await getSourceById(formId);
      if (res.isSuccess) {
        this.sourceFields[formId] = res.data.fields;
      }
    },
    // 查询数据源表单数据
    async getSourceFormData(formId) {
      const res = await getSourceFormData(formId);
      if (res.isSuccess) {
        this.sourceFormData[formId] = res.data;
      }
    },
    // 排序功能
    sortCmdChange(cmd) {
      const params = this.sortList.find((v) => v.id == cmd);
      this.sortActive =
        params?.id === 1 ? { name: "默认排序", id: 1 } : params || {};

      const sortPriroty = (a, b) => {
        const priorityMap = {
          HIGHEST: 5,
          HIGH: 4,
          MEDIUM: 3,
          LOW: 2,
          LOWEST: 1,
        };
        const left = priorityMap[a.priorityCode];
        const right = priorityMap[b.priorityCode];
        return cmd.indexOf("down") > -1 ? left - right : right - left;
      };
      const sortPlanEtime = (a, b) => {
        const left = dayjs(a.planEtime);
        const right = dayjs(b.planEtime);
        return cmd.indexOf("down") > -1 ? left - right : right - left;
      };
      // 分组排序功能
      const sortCollapse = (cmb, cb) => {
        for (const key in this.collapseGroup) {
          const listMap = this.collapseGroup[key].list;
          for (const k in listMap) {
            listMap[k] = listMap[k].sort(cb);
          }
        }
      };
      // 无分组排序功能
      const sortStatusByDefault = (cmd, cb) => {
        const res = Object.entries(this.originalBoardList).reduce(
          (acc, prev) => {
            const key = prev[0];
            const value = prev[1].sort(cb);
            acc[key] = value;
            return acc;
          },
          {}
        );
        this.boardList = res;
      };
      const sortStatus = (cmd, cb) =>
        this.groupActive.key == 1
          ? sortStatusByDefault(cmd, cb)
          : sortCollapse(cmd, cb);

      switch (cmd) {
        case "priority_up":
        case "priority_down":
          sortStatus(cmd, sortPriroty);
          break;
        case "planEtime_up":
        case "planEtime_down":
          sortStatus(cmd, sortPlanEtime);
          break;
        default:
          this.boardList = cloneDeep(this.originalBoardList);
          break;
      }
    },
    boardTypeClick(e) {
      const { name, id } = e.target.dataset;
      this.boardTypeName = name;
      this.boardType = id;
      this.boardShow = false;
      this.boardTypeChange(id);
      this.groupActive = { name: "无泳道", key: 1 };
      this.sortActive = { name: "默认排序", id: 1 };
    },
    showMyWork() {
      this.searchForm = {
        name: "",
        delay: "all",
        priorityCode: [],
        handleBy: [],
        leadingBy: [],
        stateCode: [],
      };
      this.groupActive = { name: "无泳道", key: 1 };
      this.sortActive = { name: "默认排序", id: 1 };
      this.loadKanbanData();
    },
    async getBoardsInfo() {
      const [res, err] = await catchErr(
        getProjectBoards(this.$route.params.id)
      );
      if (err) {
        this.pageLoading = false;
        return;
      }
      if (res.isSuccess) {
        this.boardTypeList = res.data.filter((v) => v.state);
        if (this.boardTypeList.length === 0) {
          this.pageLoading = false;
          return;
        }
        this.boardType = this.boardTypeList[0]?.id;
        this.boardTypeName = this.boardTypeList[0]?.name;
        this.allBoards = [...this.boardTypeList];
        // 查询看板
        this.boardTypeChange(this.boardType);
      }
    },
    async changeSprint(cmd) {
      this.currentSprint = cmd;
      this.updateBoardList();
    },
    // 更新看板显示
    async updateBoardList() {
      try {
        // 查询数据
        await this.loadKanbanData();
      } catch (error) {
        // console.log(error)
      }
      this.sortCmdChange(this.sortActive.id);
      this.groupCmdChange(this.groupActive.key);
    },
    // 切换看板类型查询泳道数据
    async boardTypeChange(val) {
      const [res, err] = await catchErr(getBoardInfoByKanbanId(val));
      if (err) {
        this.pageLoading = false;
        return;
      }
      if (res.isSuccess) {
        this.bordConfig = res.data;
        this.cardConfig = JSON.parse(res.data.config) || {};

        this.classifyMap = {};
        this.columnStatusMap = {};
        res.data.columns.map((item) => {
          this.columnStatusMap[item.id] = [];
          // 状态筛选数据
          item.statuses.map((v) => {
            if (this.classifyMap[v.classifyCode]) {
              this.classifyMap[v.classifyCode].indexOf(v.typeCode) == -1 &&
                this.classifyMap[v.classifyCode].push(v.typeCode);
            } else {
              this.classifyMap[v.classifyCode] = [v.typeCode];
            }
            this.columnStatusMap[item.id].push(v.typeCode + "$" + v.stateCode);
          });
        });
        const codeList = this.checkCustomGroup();
        codeList.length > 0 && (await this.getIssueType(codeList));
        // 列数据
        this.boardColumns = res.data.columns;
        this.pageLoading = false;
        this.loadKanbanData(true);
      } else {
        this.$message(res.msg);
      }
    },
    searchBoardData() {
      this.groupActive = { name: "无泳道", key: 1 };
      this.sortActive = { name: "默认排序", id: 1 };
      this.loadKanbanData();
    },
    // 查询看板数据
    async loadKanbanData(init) {
      if (!this.boardType) return;
      if (!this.currentSprint.id) {
        this.$message.error("暂无可选择的迭代，请先创建迭代计划");
        return;
      }
      let colStates = []; // 查询显示的状态
      let filterState = []; // 筛选显示的数据
      const map = {};
      const result = {};

      /**
       * 将 { columnId:[typeCode_stateCode] }转换成{ typdeCode_stateCode:columnId }
       */
      for (const id in this.columnStatusMap) {
        result[id] = [];
        const val = this.columnStatusMap[id];
        filterState = [...filterState, ...val];
        const states = val.map((v) => v.split("$")[1]);
        colStates = [...colStates, ...states];
        val.reduce((acc, cur) => {
          acc[cur] = id;
          return acc;
        }, map);
      }
      colStates = [...new Set(colStates)];
      filterState = [...new Set(filterState)];

      const { name, priorityCode, leadingBy, handleBy, delay } =
        this.searchForm;
      const params = {
        name: name,
        priorityCode: priorityCode || [],
        leadingBy: leadingBy || [],
        handleBy: handleBy || [],
        // putBy: putBy || [],
        stateCode: colStates || [],
        delay: delay == "all" ? null : delay,
        self: this.checkMain.length > 0,
        types: this.classifyMap,
      };
      this.boardLoading = true;
      const res = await getBoardCardsInfo(this.currentSprint.id, params);
      this.boardLoading = false;
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }

      this.boardData = [];

      res.data.forEach((item) => {
        const key = item.typeCode + "$" + item.stateCode;
        const id = map[key];
        if (filterState.indexOf(key) > -1) {
          this.boardData.push(item);
          id && result[id].push(item);
        }
        this.flagList.map((e) => {
          if (item.id === e.itemId && e.flag) {
            item.flag = e.flag;
            item.remark = e.remark;
            item.flagId = e.id;
          }
        });
      });
      this.originalBoardList = result;
      this.boardList = cloneDeep(result);
      // 默认初始化看板
      if (init) {
        const { customs, defaultFields } = this.cardConfig;
        // 默认是否有泳道
        if (this.cardConfig.channel && this.cardConfig.channel !== "default") {
          this.groupCmdChange(this.cardConfig.channel);
        }
        // 默认排序
        if (this.cardConfig.sort && this.cardConfig.sort != "default") {
          this.sortCmdChange(this.cardConfig.sort);
        }
        // 卡片自定义
        if (
          this.cardConfig.cardConfig &&
          this.cardConfig.cardConfig == "custom"
        ) {
          this.defaultFields = defaultFields;
          // 查询当前字段名称数据
          for (const key in customs) {
            customs[key].length > 0 && this.getCustomFields(key, customs[key]);
          }
        }
      }
    },
    // 判断流转方向
    async dragstart({ element }) {
      const typeCode = element.echoMap.typeCode.classify.code;

      const res =
        typeCode == "ISSUE"
          ? await apiAlmIssueFindNextNode(element.id)
          : typeCode == "BUG"
          ? await apiAlmBugFindNextNode(element.id)
          : typeCode == "TASK"
          ? await apiAlmTaskFindNextNode(element.id)
          : await apiAlmIssueFindNextNode(element.id);
      if (res.isSuccess) {
        this.nextNode = res.data;
        const colDragStates = {};
        const status = res.data.map((item) => item.stateCode);
        for (const i in this.columnStatusMap) {
          colDragStates[i] = [];
          const states = this.columnStatusMap[i].map((v) => {
            const ary = v.split("$") || [];
            return {
              typeCode: ary[0],
              stateCode: ary[1],
            };
          });
          colDragStates[i] = states
            .filter(
              (item) =>
                status.indexOf(item.stateCode) > -1 &&
                item.typeCode === element.typeCode
            )
            .map((v) => v.stateCode);
        }
        return colDragStates;
      } else {
        this.$message.warning(res.msg);
        return;
      }
    },
    async updateCardStatus({ element, nextStatus }) {
      const isCheck = this.nextNode.find((r) => r.stateCode == nextStatus)
        .echoMap.check;
      if (isCheck.length) {
        const list = [];
        isCheck.forEach((element) => {
          list.push(element.echoMap.field);
        });

        const targetName = this.nextNode.find(
          (r) => r.stateCode == nextStatus
        ).name;

        this.requireParam = {
          visible: true,
          infoData: element,
          fields: list,
          sourceNode: element.stateCode,
          sourceName: element?.echoMap?.stateCode?.name,
          targetName: targetName,
          targetNode: nextStatus,
          typeClassfiy: element?.echoMap?.typeCode?.classify?.code,
          dataId: element.id,
        };
        return;
      }
      const typeCode = element.echoMap.typeCode.classify.code;
      const res =
        typeCode == "BUG"
          ? await apiAlmBugFlow(element.id, element.stateCode, nextStatus)
          : typeCode == "TASK"
          ? await apiAlmTaskFlow(element.id, element.stateCode, nextStatus)
          : await apiAlmRequirementFlow(
              element.id,
              element.stateCode,
              nextStatus
            );
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      this.updateBoardList();
      this.nextNode = [];
    },
    async laneAdded({ element, laneItem }) {
      const typeCode = element.echoMap.typeCode.classify.code;
      const state = this.boardColumns.find((status) => status.id === laneItem);
      const targetState = state.statuses.filter(
        (item) => element.typeCode === item.typeCode
      )[0];
      const res =
        typeCode == "ISSUE"
          ? await apiAlmRequirementFlow(
              element.id,
              element.stateCode,
              targetState.stateCode
            )
          : typeCode == "BUG"
          ? await apiAlmBugFlow(
              element.id,
              element.stateCode,
              targetState.stateCode
            )
          : typeCode == "TASK"
          ? await apiAlmTaskFlow(
              element.id,
              element.stateCode,
              targetState.stateCode
            )
          : await apiAlmRequirementFlow(
              element.id,
              element.stateCode,
              targetState.stateCode
            );
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      this.updateBoardList();
    },
    showDetails(item) {
      // 获取分类,判断当前数据属于需求/任务/缺陷
      const type =
        item.echoMap && item.echoMap.typeCode
          ? item.echoMap.typeCode.classify.code
          : null;
      if (type == "ISSUE") {
        this.issueParam = {
          visible: true,
          title: "编辑需求",
          id: item.id,
          key: Date.now(),
          infoDisabled: false,
          rowTypeCode: item.typeCode,
          stateCode: item.stateCode,
          leftTabs: [
            {
              label: "需求",
              name: "IssueToIssue",
            },
            {
              label: "关联任务",
              name: "TaskTab",
            },
            {
              label: "关联缺陷",
              name: "BugTab",
            },
            {
              label: "关联代码",
              name: "DevelopTab",
            },
            {
              label: "测试用例",
              name: "TestCase",
            },
          ],
        };
      } else if (type == "BUG") {
        this.defectParam = {
          visible: true,
          title: "编辑缺陷",
          id: item.id,
          key: Date.now(),
          infoDisabled: false,
          rowTypeCode: item.typeCode,
          stateCode: item.stateCode,
          leftTabs: [],
        };
      } else if (type == "TASK") {
        this.taskParam = {
          visible: true,
          title: "编辑任务",
          id: item.id,
          key: Date.now(),
          infoDisabled: false,
          rowTypeCode: item.typeCode,
          stateCode: item.stateCode,
          leftTabs: [
            {
              label: "任务",
              name: "TaskTask",
            },
          ],
        };
      }
    },
    // 查询不同类型自定义字段
    async getCustomFields(type, list) {
      const res = await apiVaBaseCustomProjectFormCheck(
        this.$route.params.id,
        type
      );
      if (res.isSuccess) {
        const nameMap = list.map(
          (key) => res.data.find((v) => v.key === key) || {}
        );
        this.customFields[type] = nameMap;
      }
    },
    // 查询工作项类型
    async getWorkItemTypes(type) {
      const res = await apiAlmTypeNoPage({ classify: type });
      if (res.isSuccess) {
        this.typeMap[type] = res.data;
      }
    },
    // 查询迭代列表
    async loadProjectSprint() {
      const { data, isSuccess, msg } = await apiAlmProjectPlanNoPage({
        projectId: this.$route.params.id,
      });
      if (!isSuccess) {
        this.$message.warning(msg);
        return;
      }
      if (data.length > 0) {
        this.sprintList = data.filter((item) => item.stateCode !== "3");
        this.currentSprint = this.sprintList[0] || {};
      }
    },
    // 查询状态列表
    async getStatusList() {
      const res = await apiAlmStateNoPage();
      if (res.isSuccess) {
        this.stateCodeList = res.data;
        this.stateCodeMap = res.data.reduce(
          (acc, cur) => (acc[cur.code] = cur) && acc,
          {}
        );
      }
    },
    // 查优先级
    async getPrioritList() {
      const [res, err] = await catchErr(apiAlmPriorityNoPage());
      if (err) return;
      if (!res.isSuccess) {
        return;
      }
      this.prioritList = res.data;
    },
  },
};
</script>

<style lang="scss" scoped>
:deep(.el-card) {
  border: none;
}
:deep() {
  .flag-title {
    color: #1d2129;
  }
  .vone-lane-scroll {
    height: calc(100vh - 92px - 40px);
    overflow-x: hidden;
  }
  .vone-lane-item__draggable {
    height: calc(100vh - 270px);
  }
  .table-search-view .table-search-main .footer {
    display: flex;
    align-items: center;
    i {
      color: #6b7385;
    }
  }

  .lane-item {
    padding: 8px;
    height: 100%;
    min-width: 236px;
    overflow-y: auto;
    background: #eaedf0;

    &__can_put {
      background-color: #f0f7ff;
    }
  }
}
.mainBtn {
  margin: 0 16px;
  :deep() {
    .el-checkbox-button__inner {
      color: var(--font-second-color);
      border: 1px solid var(--input-border-color);
      border-radius: 4px;
      font-size: 14px;

      &:hover,
      &:focus,
      &:active {
        color: #3e7bfa;
        background-color: #fff;
        border-color: #3e7bfa;
      }
    }
    .is-focus {
      .el-checkbox-button__inner {
        border-color: var(--input-border-color);
      }
    }
    .is-checked {
      .el-checkbox-button__inner {
        color: #3e7bfa;
        background-color: #fff;
        border-color: #3e7bfa;
      }
    }
  }
}
.board-wrap {
  width: 100%;
  height: 100%;
  padding: 16px 16px 8px;
  overflow-y: hidden;
  overflow-x: auto;
  :deep() {
    .el-collapse {
      height: calc(100vh - 232px);
      margin-bottom: 0;
      overflow-y: auto;
    }
    .el-collapse-item__header {
      position: relative;
      padding-left: 12px;
      font-weight: 400;
      color: #1d2129;
      i.el-icon-direction-caret-down {
        transform: rotate(270deg);
      }
      &.is-active {
        i.el-icon-direction-caret-down {
          transform: rotate(0deg);
        }
      }
    }
    .title_len {
      margin-left: 16px;
      font-weight: 500;
    }
    .el-collapse-item__arrow {
      display: none;
    }
    .el-collapse-item__content {
      display: flex;
      padding: 0;
      height: calc(100vh - 276px);
    }
  }
}
.scroll-wrap {
  width: fit-content;
}
.header-wrap {
  white-space: nowrap;
  width: max-content;
}
.stage-wrap {
  display: inline-block;
  width: 266px;
  height: 40px;
  line-height: 40px;
  padding-left: 16px;
  font-weight: 500;
  color: #1d2129;
  background: #f5f5f5;
  border-radius: 4px 4px 0px 0px;
  &:not(:last-child) {
    margin-right: 12px;
  }
  .num-wrap {
    font-size: 14px;
    color: #838a99;
    margin-left: 4px;
    font-weight: 400;
  }
}
.scroll-body {
  display: flex;
  padding: 0;
  height: calc(100vh - 215px);
} /*// 阶段样式*/
.stageBox {
  width: 266px;
  height: 100%;
  background: #f7f7f7;
  & + & {
    margin-left: 12px;
  }

  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    background: #fafafa;
  }
}
.parentItem {
  display: flex;
  align-items: center;
  gap: 16px;
  .name-wrap {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .tag {
    height: 18px;
    line-height: 16px;
    padding: 0 6px;
    border-radius: 2px;
    border: 1px solid currentColor;
  }
  .code {
    color: #3e7bfa;
    background: #e6f2ff;
    height: 20px;
    line-height: 20px;
    padding: 0 6px;
  }
}
.group_title.is-disabled {
  font-size: 12px;
  color: var(--auxiliary-font-color);
  cursor: default;
}
.board-name {
  display: flex;
  align-items: center;
  height: 32px;
  color: var(--font-main-color);
  cursor: pointer;
  min-width: max-content;
  &:hover {
    background-color: #f2f3f5;
  }
}
.board-title {
  display: inline-block;
  font-size: 16px;
  font-weight: 500;
}
.el-dropdown-menu__item.is-active {
  color: var(--main-theme-color);
}
:deep(.toolbar) {
  border-bottom: 1px solid var(--solid-border-color);
  .el-button {
    color: #6b7385;
    &:hover,
    &:active,
    &:focus {
      color: #3e7bfa;
    }
  }
  .el-col {
    display: inline-flex;
    align-items: center;
    &-10 {
      justify-content: end;
    }
  }
}
.groupDrop {
  max-height: 240px;
  overflow: auto;
}
.filter-group {
  display: flex;
  align-items: center;
  height: 32px;
  padding: 0 12px;
  color: #1d2129;
  cursor: pointer;
  i {
    color: #6b7385;
  }
  &:hover {
    color: #3e7bfa;
    background-color: #f2f3f5;
  }
  &.active {
    color: #3e7bfa;
  }
}
.lane-add {
  &_clause {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    height: 32px;
    width: 100%;
    border-radius: 4px;
    background-color: #fff;
    cursor: pointer;
  }
  &_card {
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0px 5px 12px rgba(96, 101, 112, 0.12);
    .add-card_type {
      display: flex;
      justify-content: space-between;
      padding: 8px 12px 0;
    }
    .add-card_body {
      display: flex;
      flex-direction: column;
      gap: 8px;
      padding: 12px;
    }
    .placeholder {
      font-weight: 400;
      color: #b2b6bf;
    }
    .add-card_date {
      width: 140px;

      :deep() {
        .el-input__inner {
          padding-left: 25px;
          border: none;

          &:hover,
          &:focus,
          &:active {
            border: none;
          }
        }
        .el-input__prefix {
          left: -5px;
        }
        i {
          font-weight: 400;
          color: #b2b6bf;
        }
      }
    }
    .add-card_user {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      height: 32px;
      cursor: pointer;

      &.selected {
        position: relative;
        padding: 6px 8px;
        border-radius: 32px;
        border: 1px solid transparent;

        .clear {
          display: none;
        }
        &:hover {
          border-color: var(--main-theme-color);
          .clear {
            display: inline;
            position: absolute;
            top: -5px;
            right: -2px;
            border-radius: 50%;
            color: #fff;
            background-color: var(--main-theme-color);
          }
        }
      }

      svg {
        width: 20px;
        height: 20px;
      }
    }
    .add-card_footer {
      height: 48px;
      padding: 12px 16px;
      text-align: right;
      border-top: 1px solid #f2f3f5;
      .el-button {
        height: 24px;
        min-width: 48px;
        &--primary {
          color: #fff;
          border-color: var(--main-theme-color);
        }
      }
      .el-button + .el-button {
        margin-left: 12px;
      }
    }
  }
}
.user-list {
  margin: 0 -12px;
  max-height: 320px;
  overflow: auto;
}
.board-item {
  padding: 6px 16px;
  line-height: 22px;
  cursor: pointer;
  &:hover {
    background-color: #f2f3f5;
  }

  &.is-active {
    color: var(--main-theme-color);
  }
} /*// 夜间主题样式*/
.custom-theme-dark {
  .stageBox {
    background-color: #333947;
  }
}
</style>

<style lang="scss">
.board-popper {
  padding: 16px 16px 0;
  .nothing {
    line-height: 42px;
    height: 42px;
    text-align: center;
    color: #999;
  }
  .board-list {
    padding: 12px 0;
  }
  .board-item {
    margin: 0 -16px;
    padding: 6px 16px;
    line-height: 22px;
    cursor: pointer;
    &:hover {
      background-color: #f2f3f5;
    }

    &.is-active {
      color: var(--main-theme-color);
      .default {
        color: var(--main-theme-color);
        border-color: var(--main-theme-color);
      }
    }
  }
  .default {
    line-height: 18px;
    padding: 0 6px;
    margin-left: 6px;
    font-weight: 500;
    color: #838a99;
    border-radius: 2px;
    border: 1px solid #838a99;
  }
  footer {
    margin: 0 -16px;
    height: 46px;
    line-height: 46px;
    text-align: center;
    cursor: pointer;
    box-shadow: 0px -2px 4px rgba(29, 33, 41, 0.06);
    &:hover {
      color: var(--main-theme-color);
    }
  }
}
</style>
