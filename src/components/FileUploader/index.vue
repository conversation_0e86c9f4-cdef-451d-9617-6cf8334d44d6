<template>
  <div id="global-uploader">
    <!-- 上传 -->
    <uploader
      ref="uploader"
      :options="options"
      :auto-start="true"
      class="uploader-app"
      :file-status-text="statusText"
      v-bind="$attrs"
     
      @file-added="onFileAdded"
      @file-error="onFileError"
    >
      <uploader-unsupport />

      <uploader-btn
        id="global-uploader-btn"
        ref="uploadBtn"
        class="global-uploader-btn"
        :attrs="attrs"
      >
        <i class="iconfont el-icon-edit-upload" />
        {{ title }}
      </uploader-btn>

      <slot name="tips" />
      <uploader-list>
        <div slot-scope="props" class="file-view">
          <ul class="file-list">
            <li v-for="file in props.fileList" :key="file.id">
              <uploader-file ref="files" :file="file" :list="true" />
            </li>
          </ul>
        </div>
      </uploader-list>
    </uploader>
  </div>
</template>

<script>
/**
 *   全局上传插件
 *   调用方法：this.$bus.$emit('openUploader', {}) 打开文件选择框，参数为需要传递的额外参数
 *   上传函数：this.$bus.$on('fileAdded', fn); 文件选择后的回调
 *   成功函数：this.$bus.$on('fileSuccess', fn); 文件上传成功的回调
*/

// import { ACCEPT_CONFIG } from './upload-type'
import SparkMD5 from 'spark-md5'
import { getToken } from '@/utils/auth'
export default {
  components: {},
  props: {
    title: {
      type: String,
      default: '上传文件'
    },
    bizType: {
      type: String,
      default: 'script'
    },
    checkMd5: {
      type: Boolean,
      default: true
    },
    singleFile: {
      type: Boolean,
      default: true
    },
    storageType: {
      type: String,
      default: 'LOCAL'
    }
  },
  data() {
    const _that = this
    return {
      options: {
        target: '/api/base/base/file/anyone/upload',
        singleFile: _that.singleFile,
        chunkSize: 2 * 1024 * 1024 * 1024,
        parseTimeRemaining: function(timeRemaining, parsedTimeRemaining) {
          return parsedTimeRemaining
            .replace(/\syears?/, '年')
            .replace(/\days?/, '天')
            .replace(/\shours?/, '小时')
            .replace(/\sminutes?/, '分钟')
            .replace(/\sseconds?/, '秒')
        },
        maxChunkRetries: 0,
        testChunks: false, // 是否开启服务器分片校验
        // 服务器分片校验函数，秒传及断点续传基础
        // checkChunkUploadedByResponse: function(chunk, message) {
        //   const objMessage = JSON.parse(message)
        //   if (objMessage.skipUpload) {
        //     return true
        //   }
        //   return (objMessage.uploaded || []).indexOf(chunk.offset + 1) >= 0
        // },
        headers: {
          Authorization: 'Basic dm9uZV93ZWI6dm9uZV93ZWJfc2VjcmV0',
          token: 'Bearer ' + getToken()
        },
        processParams(params) {
          return {
            // chunk: params.chunkNumber,
            // chunks: params.totalChunks,
            // ext: '',
            // folderId: '',
            // lastModifiedDate: '',
            // md5: params.identifier,
            // name: params.filename,
            // size: params.totalSize,
            // type: ''
            bizType: _that.bizType,
            bucket: '',
            storageType: _that.storageType
          }
        },
        query() {}
      },
      attrs: {
        accept: '*'
      },
      statusText: {
        success: '上传成功',
        error: '上传失败',
        uploading: '上传中',
        paused: '暂停中',
        waiting: '等待中'
      }
    }
  },
  computed: {
    // Uploader实例
    uploader() {
      return this.$refs.uploader.uploader
    }
  },
  // watch: {
  //   'singleFile': { // 深度监听，可监听到对象、数组的变化
  //     handler(val) {
  //       this.options.singleFile = val
  //     },
  //     deep: true, // true 深度监听
  //     immediate: true
  //   }
  // },
  mounted() {
    // this.$bus.$on('openUploader', query => {
    //   this.params = query || {}
    //   if (this.$refs.uploadBtn) {
    //     this.$refs.uploadBtn.$emit('click')
    //   }
    // })
    this.options.singleFile = this.singleFile
  },
  beforeUnmount() {
    // this.$bus.$off('openUploader')
    this.$refs.uploader = null
  },
  methods: {
    onFileAdded(file) {
      this.checkMd5 && this.computeMD5(file)
    },
    onFileError(rootFile, file, response, chunk) {
      this.$message({
        message: response,
        type: 'warning'
      })
    },

    /**
		 * 计算md5，实现断点续传及秒传
		 * @param file
		*/
    computeMD5(file) {
      const fileReader = new FileReader()
      const time = new Date().getTime()
      const blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice
      let currentChunk = 0
      const chunkSize = 10 * 1024 * 1024
      const chunks = Math.ceil(file.size / chunkSize)
      const spark = new SparkMD5.ArrayBuffer()
      // file.pause()

      loadNext()

      fileReader.onload = e => {
        spark.append(e.target.result)

        if (currentChunk < chunks) {
          currentChunk++
          loadNext()
        } else {
          const md5 = spark.end()
          this.computeMD5Success(md5, file)
        }
      }

      fileReader.onerror = function() {
        this.error(`文件${file.name}读取出错，请检查该文件`)
        file.cancel()
      }

      function loadNext() {
        const start = currentChunk * chunkSize
        const end = ((start + chunkSize) >= file.size) ? file.size : start + chunkSize

        fileReader.readAsArrayBuffer(blobSlice.call(file.file, start, end))
      }
    },

    computeMD5Success(md5, file) {
      file.fileMd5 = md5
      // file.resume()
    },

    error(msg) {
      this.$notify({
        title: '错误',
        message: msg,
        type: 'error',
        duration: 2000
      })
    }
  }
}
</script>

<style scoped lang="scss">
#global-uploader {
  .uploader-app .uploader-btn {
    margin-right: 4px;
  }
  .uploader-app .uploader-list {
    max-height: 440px;
    overflow: auto;
    overflow-x: hidden;
    overflow-y: auto;
  }
  .global-uploader-btn{
    border-color: var(--main-theme-color);
    color: var(--main-theme-color);
    border-radius: 4px;

  }
}
</style>
