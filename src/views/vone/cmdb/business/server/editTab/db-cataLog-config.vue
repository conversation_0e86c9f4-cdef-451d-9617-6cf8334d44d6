<template>
  <!-- db目录 -->
  <div v-loading="dbLoading">
    <vone-search-wrapper v-if="this.$route.params.type != 1">
      <template v-slot:actions>
        <el-button type="primary" @click="save">保存</el-button>
      </template>
    </vone-search-wrapper>
    <main
      style="margin-top: 16px"
      :style="{
        height:
          $route.params.type != 1
            ? 'calc(100vh - 418px)'
            : 'calc(100vh - 370px)',
      }"
    >
      <vxe-table
        class="vone-vxe-table"
        border
        resizable
        height="auto"
        show-overflow="tooltip"
        :empty-render="{ name: 'empty' }"
        :data="tableData"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column title="库名称" field="name" width="180">
          <template #default="{ row }">
            <el-select
              v-model="row.name"
              style="width: 100%"
              placeholder="请选择库名称"
              :disabled="$route.params.type == 1"
            >
              <el-option
                v-for="(item, index) in projectlList"
                :key="index"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </template>
        </vxe-column>
        <vxe-column field="moduleId" title="工程名称">
          <template #default="{ row }">
            <el-select
              v-model="row.moduleId"
              style="width: 100%"
              placeholder="请选择工程名称"
              :disabled="$route.params.type == 1"
            >
              <el-option
                v-for="(item, index) in moduleList"
                :key="index"
                :label="item.moduleName"
                :value="item.id"
              />
            </el-select>
          </template>
        </vxe-column>
        <vxe-column field="sqlPath" title="发布包执行目录">
          <template #default="{ row }">
            <el-input
              v-model="row.sqlPath"
              placeholder="不以斜杆开头"
              :disabled="$route.params.type == 1"
            />
          </template>
        </vxe-column>
        <vxe-column field="includePath" title="打包指定目录">
          <template #default="{ row }">
            <el-input
              v-model="row.includePath"
              placeholder="不以斜杆开头"
              :disabled="$route.params.type == 1"
            />
          </template>
        </vxe-column>
        <vxe-column field="excludePath" title="打包排除目录">
          <template #default="{ row }">
            <el-input
              v-model="row.excludePath"
              placeholder="不以斜杆开头"
              :disabled="$route.params.type == 1"
            />
          </template>
        </vxe-column>
        <vxe-column v-if="TYPE != 1" title="操作" width="120" align="left">
          <template #default="{ rowIndex }">
            <el-button
              type="text"
              :icon="elIconTipsPlusCircle"
              @click.prevent="addRow(rowIndex, tableData)"
            />
            <el-button
              type="text"
              :icon="elIconApplicationDelete"
              @click.prevent="deleteRow(rowIndex, tableData)"
            />
          </template>
        </vxe-column>
      </vxe-table>
    </main>
    <el-row
      v-if="this.$route.params.type != 1"
      type="flex"
      class="mt-3"
      justify="center"
    >
      <!-- <el-button
            v-if="TYPE == 0"
            v-show="active > 0"
            @click="stepFoaward"
          >上一步</el-button>
          <span :style="{ flex: 1 }" />
          <el-button
            v-if="TYPE == 0"
            type="primary"

            @click="saveNext"
          >保存并下一步</el-button> -->
      <!-- <el-button type="primary" @click="save">保存</el-button> -->
    </el-row>
  </div>
</template>

<script>
import {
  $on,
  $off,
  $once,
  $emit,
} from "../../../../../../utils/gogocodeTransfer";

import {
  apiBaseSelectDbCatalogBySystemId,
  apiBaseSaveDbCatalog,
} from "@/api/vone/cmdb/server";
import { apiCmdbSelectModuleBySystemId } from "@/api/vone/cmdb/application";
export default {
  props: {
    active: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      dbLoading: false,
      tableData: [
        {
          moduleId: "",
          sqlPath: "",
          includePath: "",
          excludePath: "",
        },
      ],
      moduleList: [],
      moudleIdList: [],
      compentsIdList: [],
      modName: "",
      projectlList: [],
      TYPE: undefined,
    };
  },
  mounted() {
    this.TYPE = this.$route.params.type;
    // if (this.type === 1) {
    // 查询库名称/工程名称下拉框数据
    this.getProject();
    // 查询列表
    this.getDbData();
    // }
  },
  methods: {
    // 点击+号图标添加新的一栏
    addRow(index, rows) {
      const obj = {};
      obj.moduleId = "";
      obj.sqlPath = "";
      obj.includePath = "";
      obj.excludePath = "";
      obj.name = this.projectlList[0].name;
      this.tableData.push(obj);
    },
    // 点击减号删除一行
    deleteRow(index, rows) {
      if (this.tableData.length > 1) {
        this.tableData.splice(index, 1);
      } else {
        this.tableData = [{}];
        return;
      }
    },
    // 获取工程名称
    async getProject() {
      this.dbLoading = true;
      const res = await apiCmdbSelectModuleBySystemId(
        this.$route.params.systemId
      );
      if (!res.isSuccess) {
        return;
      }

      this.dbLoading = false;
      this.projectlList = [];
      this.projectlList.push(res.data[0]);
      this.tableData[0].name = this.projectlList[0].name;
      this.moduleList = res.data;
    },
    // 查询列表
    async getDbData() {
      this.dbLoading = true;
      const res = await apiBaseSelectDbCatalogBySystemId(
        this.$route.params.systemId
      );
      this.dbLoading = false;
      if (!res.isSuccess) {
        return;
      }

      if (res.data.length) {
        this.tableData = res.data;
        if (this.projectlList.length > 0) {
          this.tableData.forEach((element) => {
            element.name = this.projectlList[0].name;
          });
        }
      } else {
        if (this.TYPE == 1) {
          this.tableData = [
            {
              moduleId: "",
              sqlPath: "",
              includePath: "",
              excludePath: "",
            },
          ];
        }
      }
    },
    async save() {
      const applicationDbCatalogs = this.tableData.map((r) => {
        let name = "";
        this.moduleList.map((item) => {
          if (item.id == r.moduleId) {
            name = item.moduleName;
          }
        });
        return {
          moduleName: name,
          sqlPath: r.sqlPath,
          includePath: r.includePath,
          excludePath: r.excludePath,
          moduleId: r.moduleId,
          applicationId: this.$route.params.systemId,
        };
      });

      const res = await apiBaseSaveDbCatalog({
        applicationId: this.$route.params.systemId,
        applicationDbCatalogs,
      });
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      this.$message.success("保存成功");
      this.getDbData();
    },
    stepFoaward() {
      $emit(this, "stepFoaward");
    },
    async saveNext() {
      await this.save();
      // this.activeIndex++;
      $emit(this, "success");
    },
  },
  emits: ["stepFoaward", "success"],
};
</script>
