<template>
  <div class="time-box">
    <header>
      <a @click="settingWorktm">
        <el-icon class="iconfont"><el-icon-icon-line-overflow /></el-icon>
        <span>登记工时</span>
      </a>
    </header>
    <section v-loading="commentloading">
      <vone-empty v-if="!commentList.length" />
      <template v-else>
        <div v-for="(e, index) in commentList" :key="index">
          <div class="sContent">
            <el-row class="flexRow">
              <vone-user-avatar
                :avatar-path="e.echoMap.filledBy.avatarPath"
                :name="e.echoMap.filledBy.name"
              />
              <span style="flex: 1; padding-left: 8px" class="time">{{
                formatVal_filter(e.fillingTime)
              }}</span>
              <span
                :style="{ color: stateCodesMap[e.stateCode].color }"
                style="
                  padding: 0 5px;
                  font-size: 12px;
                  width: 100px;
                  text-align: center;
                "
              >
                {{ stateCodesMap[e.stateCode].name }}
              </span>
              <el-button
                :disabled="e.verified ? true : false"
                type="text"
                :icon="elIconIconSystemDelete"
                @click="deleteTime(e.id)"
              />
            </el-row>
            <el-row
              style="padding-left: 32px; line-height: 22px; margin: 6px 0px"
            >
              <el-tag v-if="!e.verified" type="info" class="unVerified"
                >未审批</el-tag
              >
              <el-tag v-if="e.verified && e.valid" type="success"
                >已确认</el-tag
              >
              <el-tag v-if="e.verified && !e.valid" type="danger"
                >已驳回</el-tag
              >
              <span class="hour">{{ e.duration }} 小时</span>
            </el-row>
            <el-row v-if="e.description">
              <div class="desContent">
                <span>{{ e.description }}</span>
              </div>
            </el-row>
          </div>
        </div>
      </template>
    </section>
    <workDialog
      v-bind="worktimeForm"
      v-if="worktimeForm.visible"
      v-model:value="worktimeForm.visible"
      @success="getWorkingHoursInfoFn"
    />
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import workDialog from "./work-dialog.vue";
import dayjs from "dayjs";
import {
  getWorkingHoursInfo,
  deleteWorkingHoursInfo,
} from "@/api/vone/manhour/index";

const stateCodesMap = {
  UAT_TEST_COM: {
    name: "UAT测试_已完成",
    color: "#df6311",
  },
  UAT_TEST_DO: {
    name: "UAT测试_进行中",
    color: "#72eedf",
  },
  UAT_TEST_NS: {
    name: "UAT测试_未开始",
    color: "#8a8585",
  },
  TEST_COM: {
    name: "软件测试_已完成",
    color: "#0c1910",
  },
  TEST_DOING: {
    name: "软件测试_进行中",
    color: "#8cc733",
  },
  TEST_NS: {
    name: "软件测试_未开始",
    color: "#877d7d",
  },
  CodeDev_COM: {
    name: "代码开发_已完成",
    color: "#841cd9",
  },
  CodeDev_Do: {
    name: "代码开发_进行中",
    color: "#b86f6f",
  },
  CodeDev_NS: {
    name: "代码开发_未开始",
    color: "#5f5959",
  },
  UIDesign_Com: {
    name: "UI设计_已完成",
    color: "#13d5d8",
  },
  UIDesign_DO: {
    name: "UI设计_进行中",
    color: "#c6a9c0",
  },
  UIDesign_NO: {
    name: "UI设计_未开始",
    color: "#60549c",
  },
  Prototype_Design_Com: {
    name: "需求分析丨原型设计_已完成",
    color: "#db10ea",
  },
  Prototype_Design_Do: {
    name: "需求分析丨原型设计_进行中",
    color: "#0cd4bd",
  },
  Prototype_Design_No: {
    name: "需求分析丨原型设计_未启动",
    color: "#787279",
  },
  DEMAND_POOL: {
    name: "需求池",
    color: "#ADB0B8",
  },
  HANG_UP: {
    name: "已挂起",
    color: "#BD7FFA",
  },
  ANALYSIS_DO: {
    name: "分析中",
    color: "#FFBF47",
  },
  DEVELOP_DO: {
    name: "开发中",
    color: "#37CDDE",
  },
  TEST_DO: {
    name: "测试中",
    color: "#FC9772",
  },
  DONE: {
    name: "已完成",
    color: "#3CB540",
  },
  TODO: {
    name: "未开始",
    color: "#ADB0B8",
  },
  PROCESS_DO: {
    name: "处理中",
    color: "#37CDDE",
  },
  BUG_DONE: {
    name: "已修复",
    color: "#4BCCBB",
  },
  CHECK_TODO: {
    name: "待验证",
    color: "#F5B4B2",
  },
  OPEN_AGAIN: {
    name: "再打开",
    color: "#37CDDE",
  },
  REFUSE_DONE: {
    name: "已拒绝",
    color: "#FA6B57",
  },
  CLOSE: {
    name: "已关闭",
    color: "#3CB540",
  },
  Verificationfailed: {
    name: "验证不通过",
    color: "#f41010",
  },
  CLOSE: {
    name: "已关闭",
    color: "#3CB540",
  },
  CLOSE: {
    name: "已关闭",
    color: "#3CB540",
  },
  CLOSE: {
    name: "已关闭",
    color: "#3CB540",
  },
  CLOSE: {
    name: "已关闭",
    color: "#3CB540",
  },
  CLOSE: {
    name: "已关闭",
    color: "#3CB540",
  },
  CLOSE: {
    name: "已关闭",
    color: "#3CB540",
  },
};
export default {
  name: "WorkTime",
  components: {
    workDialog,
  },
  props: {
    sourceId: {
      type: String,
      default: "",
    },
    sourceType: {
      type: String,
      default: "",
    },
    rowTypeCode: {
      // 需求中心/项目里,从列表数据取到的类型
      type: String,
      default: undefined,
    },
    projectId: {
      type: String,
      default: undefined,
    },
    height: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      stateCodesMap,
      worktimeForm: {
        visible: false,
      },
      commentList: [],
      commentloading: false,
    };
  },
  computed: {
    headers() {
      return {
        token: "Bearer " + getToken(),
      };
    },
  },
  mounted() {
    this.getWorkingHoursInfoFn();
  },
  methods: {
    formatVal_filter(val) {
      if (!val) return "";
      return dayjs(val).format("YYYY-MM-DD");
    },
    async getWorkingHoursInfoFn() {
      this.commentloading = true;
      const res = await getWorkingHoursInfo(this.sourceId, this.sourceType);
      if (!res.isSuccess) {
        this.commentloading = false;
        return;
      }
      this.commentloading = false;
      this.commentList = res.data;
    },
    settingWorktm() {
      this.worktimeForm = {
        visible: true,
        bizId: this.sourceId,
        type: this.sourceType,
        projectId: this.projectId,
        rowTypeCode: this.rowTypeCode,
      };
    },
    async deleteTime(e) {
      try {
        await this.$confirm(`确定删除此条工时记录吗?`, "删除", {
          type: "warning",
          closeOnClickModal: false,
          customClass: "delConfirm",
        });

        const res = await deleteWorkingHoursInfo([e]);
        if (!res.isSuccess) {
          this.$message.warning(res.msg);
          return;
        } else {
          this.$message.success("删除成功");
          this.getWorkingHoursInfoFn();
        }
      } catch (e) {
        return;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.time-box {
  height: calc(90vh - 235px - 48px);
  overflow-x: auto;
  padding: 0 16px;
  header {
    height: 22px;
    line-height: 22px;
    text-align: right;
    margin: 16px 0px 12px 0px;
    a {
      color: var(--main-theme-color);
      font-weight: 500;
    }
  }
  section {
    .sContent {
      margin-bottom: 12px;
    }
    .flexRow {
      height: 22px;
      line-height: 22px;
      display: flex;
      align-items: center;
      // justify-content: space-between;
    }
    .desContent {
      padding: 0 72px 0 32px;
      font-size: 12px;
      color: var(--main-font-color);
    }
  }
}
:deep(.el-button.is-disabled) {
  border: none;
  &:hover {
    border: none;
  }
}
:deep(.el-button) {
  min-width: unset;
  padding: 0px;
}
.hour {
  color: var(--main-font-color);
  margin-left: 12px;
}
.time,
.hour {
  font-size: 12px;
}
</style>
