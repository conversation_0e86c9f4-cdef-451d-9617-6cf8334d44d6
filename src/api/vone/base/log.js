import request from '@/utils/axios-api'

/**
 * @desc 查询日志
 */
export function apiBaseLogList(data) {
  return request({
    url: '/api/base/base/optLog/page',
    method: 'POST',
    data
  })
}

/**
 * @desc 查询日志详情
 */
export function apiBaseGetLogInfo(id) {
  return request({
    url: `/api/base/base/optLog/get?id=` + id,
    method: 'get'
  })
}

/**
 * @desc 查询登录日志
 */
export function apiBaseLoginLogList(data) {
  return request({
    url: '/api/base/base/loginLog/page',
    method: 'POST',
    data
  })
}

// 清理日志
export function apiBaseOptLogClear(data) {
  return request({
    url: '/api/base/base/optLog/clear?type=' + data,
    method: 'DELETE'
  })
}
