<template>
  <div>
    <el-dialog
      title="产品维度统计"
      width="40%"
      v-model:value="visible"
      :before-close="onClose"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-form ref="productForm" :model="productForm" :rules="productFormRules">
        <el-form-item label="产品" prop="product">
          <el-select
            v-model="productForm.product"
            placeholder="请选择产品"
            multiple
            filterable
          >
            <el-option
              v-for="item in productList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <div>
          <el-button @click="onClose">取 消</el-button>&nbsp;
          <el-button :loading="saveLoading" type="primary" @click="sureCopy"
            >保存</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'

import { queryListByProductId } from '@/api/vone/product'
export default {
  props: {
    visible: {
      type: Boolean,
      default: true,
    },
    id: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      productForm: {
        product: [],
      },
      productFormRules: {},
      saveLoading: false,
      productList: [],
    }
  },
  watch: {
    visible(v) {
      if (!v) return
    },
  },
  mounted() {
    this.queryListByProductList()
  },
  methods: {
    onClose() {
      $emit(this, 'update:visible', false)
      this.$refs['productForm'].resetFields()
    },
    async sureCopy() {
      $emit(this, 'update:visible', false)
      $emit(this, 'success', this.productForm.product)
    },
    async queryListByProductList() {
      const res = await queryListByProductId()

      if (!res.isSuccess) {
        return
      }
      this.productList = res.data
    },
  },
  emits: ['update:visible', 'success'],
}
</script>
