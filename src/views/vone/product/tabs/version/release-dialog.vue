<template>
  <el-dialog
    class="dialogContainer"
    title="新增发布记录"
    :model-value="visible"
    width="35%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="close"
  >
    <div>
      <!-- <p><i class="el-icon-warning-outline" style="color:var(--placeholder-color)"> 包含12个未完成的发布内容条需求</i></p>
          <p>共发布内容 216 条，发布至
            <el-tag type="success">PRD</el-tag>
            环境
          </p> -->
      <el-form
        ref="appForm"
        :model="appForm"
        label-width="80px"
        label-position="top"
        :rules="rules"
      >
        <!-- <el-row :gutter="24"> -->
        <!-- <el-col :span="12"> -->
        <el-form-item label="发布时间" prop="releaseTime">
          <el-date-picker
            v-model="appForm.releaseTime"
            type="date"
            placeholder="选择日期"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>
        <!-- </el-col> -->
        <!-- <el-col :span="12"> -->
        <el-form-item label="发布人" prop="releaseUser">
          <vone-remote-user v-model:value="appForm.releaseUser" />
        </el-form-item>
        <!-- </el-col> -->
        <el-form-item label="发布环境" prop="envKey">
          <el-select
            v-model="appForm.envKey"
            placeholder="请选择"
            style="width: 220px; width: 100%"
          >
            <el-option
              v-for="item in envList"
              :key="item.id"
              :value="item.code"
              :label="item.name"
            />
          </el-select>
        </el-form-item>
        <!-- <el-col> -->
        <el-form-item label="流水线" prop="pipelineIds">
          <el-select
            v-model="appForm.pipelineIds"
            placeholder="请选择"
            multiple
          >
            <el-option
              v-for="item in lineList"
              :key="item.id"
              :value="item.echoMap.pipelineId.id"
              :label="item.echoMap.pipelineId.name"
            />
          </el-select>
        </el-form-item>
        <!-- </el-col> -->
        <!-- </el-row> -->
      </el-form>
    </div>

    <template v-slot:footer>
      <span class="dialog-footer">
        <el-button size="small" @click="$emit('update:visible', false)"
          >取消</el-button
        >
        <el-button
          type="primary"
          size="small"
          :loading="saveLoading"
          @click="save"
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import { apiBaseDictNoPage } from '@/api/vone/base/dict.js'
import { apiGetSelectUser } from '@/api/vone/pipeline'
import { productReleaseAdd } from '@/api/vone/product/workitem'
import storage from 'store'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      appForm: {
        productId: this.$route.params.productId,
        productVersionId: this.$route.params.versionId,
        pipelineIds: [],
      },
      rules: {
        releaseTime: [{ required: true, message: '请选择发布时间' }],
        leadingBy: [{ required: true }],
        envKey: [{ required: true, message: '请选择发布环境' }],
        pipelineIds: [
          { required: true, message: '请选择流水线', type: 'array' },
        ],
      },
      lineList: [],
      envList: [],
      saveLoading: false,
    }
  },
  mounted() {
    this.getbaseList()
    this.getenvList()
  },
  methods: {
    // 保存
    async save() {
      await this.$refs.appForm.validate()
      try {
        this.saveLoading = true
        const res = await productReleaseAdd(this.appForm)
        this.saveLoading = false
        if (!res.isSuccess) return
        this.$message.success('发布成功')
        this.close()
        $emit(this, 'success')
      } catch (e) {
        this.saveLoading = false
      }
    },
    close() {
      this.$refs.appForm.resetFields()
      $emit(this, 'update:visible', false)
    },
    async getbaseList() {
      const userInfo = storage.get('user')
      const res = await apiGetSelectUser({ userId: userInfo.id })

      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.lineList = res.data
    },
    async getenvList() {
      const res = await apiBaseDictNoPage({ type: 'ENVIRONMENT' })
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.envList = res.data
    },
  },
  emits: ['update:visible', 'success'],
}
</script>

<style lang="scss" scoped>
/*// :deep(.el-dialog__body)*/
 {
  /*//   display: flex;*/ /*//   justify-content: center;*/ /*//*/
}
</style>
