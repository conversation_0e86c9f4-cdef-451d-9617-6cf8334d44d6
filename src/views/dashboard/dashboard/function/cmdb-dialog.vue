<template>
  <el-dialog :close-on-click-modal="false" :title="formInfo ? formInfo.title : '通用配置'" v-model="cmdbvisible" @closed="close">
    <el-form ref="form" :model="formData" :rules="rules">
      <el-form-item label="标题" label-width="80px">
        <el-input v-model="formData.title" style="width: 90%" />
      </el-form-item>
      <el-form-item label="资源类型" label-width="80px" prop="cmdbType">
        <el-select v-model="formData.cmdbType" clearable style="width:90%" filterable>
          <el-option v-for="item in cmdbTypeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button
        type="primary"
        @click="save"
      >确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import _ from 'lodash'
export default {
  name: 'PipelineDialog',
  props: {
    isEditForm: Boolean,
    cmdbvisible: {
      type: Boolean,
      default: false
    },
    formInfo: {
      type: Object,
      default: null
    },
    layout: {
      type: [Object, Array],
      default: null
    }
  },
  data() {
    return {
      formData: {
        title: this.formInfo.title

      },
      rules: {
        cmdbType: [{ required: true, message: '请选择资源', trigger: 'blur' }]
      },
      cmdbTypeList: [
        {
          value: '主机',
          label: '主机'
        },
        {
          value: '服务应用数量',
          label: '服务应用数量'
        },
        {
          value: '应用组件',
          label: '应用组件'
        },
        {
          value: '数据库组',
          label: '数据库组'
        }
      ]
    }
  },
  mounted() {
    if (this.isEditForm) {
      this.formData = this.formInfo
    }
  },
  methods: {
    async save() {
      try {
        await this.$refs.form.validate()
      } catch (e) {
        return
      }
      const layouts = _.cloneDeep(this.layout)
      if (this.isEditForm) {
        layouts.map(item => {
          if (item.id == this.formData.id && item.widgetCode == this.formData.key) {
            item.queryParams = JSON.stringify({ ...this.formData })
          }
        })
      } else {
        layouts.push({
          x: (this.layout.length * 2) % (12),
          y: this.layout.length + (12), // puts it at the bottom
          w: 3,
          h: 3,
          i: this.layout.length + 1,
          queryParams: JSON.stringify({ ...this.formInfo, ...this.formData }),
          widgetCode: this.formInfo.key
        })
      }
      this.$emit('update:layout', layouts)
      this.$emit('update:cmdbvisible', false)
      this.$emit('success')
    },
    close() {
      this.$emit('update:cmdbvisible', false)
      this.$refs.form.resetFields()
    }
  }
}
</script>

