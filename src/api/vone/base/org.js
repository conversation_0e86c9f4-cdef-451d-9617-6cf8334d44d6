import request from '@/utils/axios-api'

// 机构查询
export function orgList(data) {
  return request({
    url: '/api/base/base/org/tree',
    method: 'get',
    data
  })
}
// 根据用户查询机构
export function getOrgByUser(data) {
  return request({
    url: '/api/base/base/org/sessionUser/tree',
    method: 'get',
    data
  })
}
// 根据机构查询详情
export function getOrgDetail(id) {
  return request({
    url: `/api/base/base/org/${id}`,
    method: 'get'
  })
}
// 修改机构
export function editOrg(data) {
  return request({
    url: `/api/base/base/org`,
    method: 'put',
    data
  })
}
// 新增机构
export function addOrg(data) {
  return request({
    url: `/api/base/base/org`,
    method: 'post',
    data
  })
}
// 删除机构
export function deleteOrg(data) {
  return request({
    url: `/api/base/base/org`,
    method: 'delete',
    data
  })
}

// 新增机构
export function apiBaseOrgNoPage(data) {
  return request({
    url: `/api/base/base/org/query`,
    method: 'post',
    data
  })
}
// 修改机构排序和父机构
export function updateSort(data) {
  return request({
    url: '/api/base/base/org/updateSort',
    method: 'put',
    data
  })
}
// 检测机构名称是否可用
export function checkOrgName(data) {
  return request({
    url: '/api/base/base/org/check',
    data
  })
}

// 查询机构详情
export function apiBaseOrgInfo(id) {
  return request({
    url: `/api/base/base/org/${id}`,
    method: 'get'
  })
}

// 机构列表
export function apiBasePageForOrg(data) {
  return request({
    url: `/api/base/base/user/pageForOrg`,
    method: 'post',
    data
  })
}

// 同步OA系统机构信息
export function syncOrg() {
  return request({
    url: `/api/base/base/org/sync`,
    method: 'get'
  })
}
