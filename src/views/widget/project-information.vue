<template>
  <div v-loading="show" class="projectInfo">
    <div class="information">
      <div class="projectImg">
        <!-- 头像 -->
        <vone-user-avatar :avatar-path="data.avatarPath" :avatar-type="true" :show-name="false" width="40px" height="40px" />
      </div>
      <div class="itemList">
        <div class="item name">{{ data.name }}</div>
        <div class="item test">
          <!-- <span v-for="item in data.roles" :key="item.id" class="itemRole">{{ item.name +'  ' }}</span> -->
          <span class="itemRole"> {{ Array.isArray(data.roles)&& data.roles.length>0 ? data.roles.map(r=>r.name).join("、"):"" }}  </span>
        </div>
        <div class="item test">我的项目:{{ data.projectInfos && data.projectInfos.length }}</div>
      </div>
    </div>
    <el-row class="userbox">
      <el-row>
        <a @click="showInfo">个人设置</a>
      </el-row>
    </el-row>
    <!-- <div style="position:relative">
      <vone-user style="position:relative" :name="data.avatarPath" :size="60" />
    </div> -->
    <!-- 个人设置 -->
    <ownSettingDialog v-if="ownSettingParams.visible" :visible.sync="ownSettingParams.visible" v-bind="ownSettingParams" />
  </div>

</template>

<script>

import { projectComponent } from '@/api/vone/project/overview.js'
import ownSettingDialog from '@/layout/components/AppMenu/setting/own-setting-dialog.vue'

export default {
  components: {
    ownSettingDialog
  },
  props: {
    formInfo: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      show: true,
      data: {
        leadingUser: {}
      },
      ownSettingParams: { visible: false }
    }
  },
  mounted() {
    // if (this.formInfo.projectId) {
    this.getDelay()
    // }
  },
  methods: {
    async getDelay() {
      this.show = true
      await projectComponent().then(res => {
        this.show = false
        if (res.isSuccess) {
          this.data = res.data
          // this.data.avatarPath = `http://${window.location.host}/api/base/webapps/${this.data.avatarPath}`
        }
      })
    },
    showInfo() {
      this.ownSettingParams = { visible: true }
    }
  }

}
</script>

<style lang="scss" scoped>
.projectInfo {
  overflow: hidden;
  height: 100%;
  border-radius: 4px;
  background: var(--main-bg-color);
  border: 1px solid var(--solid-border-color);
  box-shadow: var(--main-bg-shadow);
  .information {
    display: flex;
    // flex-direction: column;
    justify-content: center;
    align-items: center;
    // padding-top: 30px;
    height: 75%;
    background-color: #2d69e5;
    background: linear-gradient(144.09deg, #3f7dff 9.56%, #b08cff 79%) no-repeat;

    // background: url("~@/assets/dashboard-bg.png") no-repeat;
    // background-size: 100%;
    .projectImg {
      // margin-left: 10%;
      // width: 64px;
      // float: left;
      // text-align: center;
      margin-left: 3%;

      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .avatar {
        padding: 4px;
        background-color: unset;
        display: inline-block;
        border: 2px solid #fff;
        border-radius: 50%;
      }
    }
    .itemList {
      margin-left: 16px;
      padding-right: 3%;
      .item {
        margin-bottom: 8px;
        // white-space: nowrap;
      }
      .itemRole {
        margin-bottom: 3px;
        // white-space: nowrap;
        display: table;
        line-height: 25px;
      }
      .name {
        color: #fff;
        font-weight: bold;
        font-size: 16px;
      }
      .test {
        color: #fff;
      }
    }
  }
  .userbox {
    text-align: center;
    height: 25%;
    color: #2d69e5;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
}
</style>
