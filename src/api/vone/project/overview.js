import request from '@/utils/axios-api'

// 工作台查询任务概览组件

export function taskComponent(id) {
  return request({
    url: `/api/insight/insight/taskComponent/findTypeByProjectId/${id}`,
    method: 'get'
  })
}
// 工作台查询缺陷概览组件

export function bugComponent(id) {
  return request({
    url: `/api/insight/insight/bugComponent/findTypeByProjectId/${id}`,
    method: 'get'
  })
}
// 工作台查询需求概览组件

export function requirementComponent(id) {
  return request({
    url: `/api/insight/insight/requirementComponent/findTypeByProjectId/${id}`,
    method: 'get'
  })
}
// 工作台查询项目延期事项组件

export function delayComponent(id) {
  return request({
    url: `/api/insight/insight/delayComponent/findDelayByProjectId/${id}`,
    method: 'get'
  })
}
// 工作台查询项目基本信息

export function projectComponent() {
  return request({
    url: '/api/insight/insight/userComponent/dashboard',
    method: 'get'
  })
}
// 工作台查询项目任务状态按人员分布组件

export function findStateByProjectId(id) {
  return request({
    url: `/api/insight/insight/taskComponent/findStateByProjectId/${id}`,
    method: 'get'
  })
}
// 项目查询项目需求概览组件

export function requirement(id) {
  return request({
    url: `/api/alm/alm/requirement/findTypeByProjectId/${id}`,
    method: 'get'
  })
}
// 项目查询项目缺陷概览组件

export function bug(id) {
  return request({
    url: `/api/alm/alm/bug/findTypeByProjectId/${id}`,
    method: 'get'
  })
}
// 项目查询项目任务概览组件

export function task(id) {
  return request({
    url: `/api/alm/alm/task/findTypeByProjectId/${id}`,
    method: 'get'
  })
}
// 项目查询项目任务状态按人员分布组件

export function taskStatus(id) {
  return request({
    url: `/api/alm/alm/task/findStateByProjectId/${id}`,
    method: 'get'
  })
}
// 项目查询查询项目延期事项组件

export function delay(id) {
  return request({
    url: `/api/alm/issue/delayComponent/findDelayByProjectId/${id}`,
    method: 'get'
  })
}
// 根据产品id查询需求概览数据
export function requireProductId(id) {
  return request({
    url: `/api/alm/alm/requirement/findRequirementComponentByProductId/${id}`,
    method: 'get'
  })
}
// 根据产品id查询缺陷概览数据
export function bugProductId(id) {
  return request({
    url: `/api/alm/alm/bug/findBugComponentByProductId/${id}`,
    method: 'get'
  })
}
// 根据计划id查询需求概览数据
export function requierByPlanId(id) {
  return request({
    url: `/api/alm/alm/requirement/findRequirementComponentByPlanId/${id}`,
    method: 'get'
  })
}
// 根据计划id查询需求任务数据
export function taskByPlanId(id) {
  return request({
    url: `/api/alm/alm/task/findTaskComponentByPlanId/${id}`,
    method: 'get'
  })
}
// 根据计划id查询bug数据
export function bugByPlanId(id) {
  return request({
    url: `/api/alm/alm/bug/findBugComponentByPlanId/${id}`,
    method: 'get'
  })
}
// 查询计划任务状态按人员分布组件
export function statusPlanId(id) {
  return request({
    url: `/api/alm/alm/task/findStateByPlanId/${id}`,
    method: 'get'
  })
}
// 查询迭代燃尽图
export function findBurnDownByPlanId(planId, data) {
  return request({
    url: `/api/alm/alm/projectPlan/findBurnDownByPlanId/${planId}`,
    method: 'get',
    data
  })
}

// 查询项目缺陷趋势
export function findTrendByProjectId(projectId) {
  return request({
    url: `/api/alm/alm/bug/findTrendByProjectId/${projectId}`,
    method: 'get'
  })
}
/**
 * @description 查询项目用例概览
 * @param {string} projectId 项目id
 */
export function getProjectCasesInfo(projectId) {
  return request({
    url: `/api/testm/testCase/findTypeByProjectId/${projectId}`,
    method: 'get'
  })
}
/**
 * @description 查询项目成员用例数量
 * @param {string} projectId 项目id
 */
export function getProjectMemberCases(projectId) {
  return request({
    url: `/api/testm/testCase/findCaseUserById/${projectId}`,
    method: 'get'
  })
}
/**
 * @description 查询测试项目缺陷趋势数据
 * @param {string} projectId 项目id
 */
export function getProjectDefectTrends(projectId) {
  return request({
    url: `/api/insight/insight/bugComponent/findAllTrendByProjectId/${projectId}`,
    method: 'get'
  })
}
/**
 * @description 查询测试项目用例人员分布数据
 * @param {string} projectId 项目id
 */
export function getProjectUsersScatter(projectId) {
  return request({
    url: `/api/testm/testPlan/findCaseUserById/${projectId}`,
    method: 'get'
  })
}
/**
 * @description 查询测试计划概览
 * @param {string} projectId 项目id
 * @param {string} stateId 状态id
 */
export function getTestPlansOverView(projectId, stateId) {
  return request({
    url: `/api/testm/testPlan/findPlanNumById/${projectId}/${stateId}`,
    method: 'get'
  })
}

// 项目概览缺陷趋势下钻
export function projectQueryView(data) {
  return request({
    url: `/api/alm/alm/bug/queryView`,
    method: 'post',
    data
  })
}

