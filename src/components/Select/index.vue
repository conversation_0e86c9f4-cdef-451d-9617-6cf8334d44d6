<template>
  <el-select
    ref="vone-select"
    v-model="selectVal"
    v-bind="$attrs"
    class="vone-select"
   
    @change="(val) => $emit('update:formValue', val)"
  >
    <div slot="prefix" class="select-header">
      <span ref="ele">{{ label }}</span>
    </div>
    <slot />
  </el-select>
</template>
<script>
export default {
  name: 'VoneSelect',
  props: {
    label: {
      type: String,
      default: () => ''
    },
    // eslint-disable-next-line vue/require-default-prop
    formValue: [String, Array, Number]
  },
  data() {
    return {
      selectVal: this.formValue
    }
  },
  mounted() {
    if (this.$refs['vone-select'].multiple) {
      this.$refs['vone-select'].$refs.tags.style.paddingLeft = this.$refs['vone-select'].$refs.tags && this.$refs.ele.clientWidth + 10 + 'px'
      this.$refs['vone-select'].$children[0].$refs.input.style.paddingLeft = this.$refs.ele.clientWidth + 10 + 'px'
    } else {
      this.$refs['vone-select'].$children[0].$refs.input.style.paddingLeft = this.$refs.ele.clientWidth + 10 + 'px'
    }
  },
  methods: {
  }
}
</script>
<style lang="scss" scoped>
	.vone-select {
		padding-right: 10px;
	}
	:deep(.select-header) {
		min-width: 28px;
		height: 100%;
		display: flex;
		align-items: center;
		text-align: center;
		font-size: 14px;
		color: #4983F3;
	}
</style>
