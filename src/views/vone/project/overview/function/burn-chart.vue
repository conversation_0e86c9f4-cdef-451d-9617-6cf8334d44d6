<template>
  <!-- 燃尽图 -->
  <el-card v-loading="listLoading" class="clearfix">
    <template v-slot:header>
      <div>
        <span>{{ title }}</span>

        <el-select
          v-model="burnType"
          :disabled="!planId"
          style="float: right; padding: 3px 0; width: 12%"
          @change="geBurnChartOptions"
        >
          <el-option label="故事点" value="0" />
          <el-option label="工时" value="1" />
        </el-select>

        <el-select
          v-model="planId"
          style="float: right; padding: 3px 0; width: 20%; margin-right: 10px"
          @change="geBurnChartOptions"
        >
          <el-option
            v-for="item in planList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </div>
    </template>
    <vone-empty v-if="noData" style="height: 300px" />
    <vone-echarts v-else :options="options" :height="'300px'" />
  </el-card>
</template>

<script>
import { findBurnDownByPlanId } from '@/api/vone/project/overview'
import { apiAlmProjectPlanNoPage } from '@/api/vone/project/iteration'

import * as echarts from 'echarts'
export default {
  props: {
    title: {
      type: String,
      default: '',
    },
    projectList: {
      type: Array,
      default: () => [],
    },
    name: {
      type: String,
      default: '名称',
    },
  },

  data() {
    return {
      options: {},
      chartData: null,
      burnType: '0',
      planId: undefined,
      planList: [],
      listLoading: false,
      noData: false,
    }
  },
  mounted() {
    this.getPlanList()
  },
  methods: {
    async geBurnChartOptions() {
      if (!this.planId) return
      this.listLoading = true
      const res = await findBurnDownByPlanId(this.planId, {
        type: this.burnType,
        planId: this.planId,
      })
      if (res.isSuccess) {
        this.chartData = res.data

        this.listLoading = false

        this.options = {
          color: ['#ADB0B8', '#64BEFA'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },
            backgroundColor: '#fff',
            borderColor: 'none',
            extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);', // 附加阴影样式
            textStyle: {
              color: '#53565C',
            },
          },

          legend: {
            x: 'right', // 居右显示
            itemHeight: 12,
            itemWidth: 24,
            data: [
              {
                name: '计划',
                icon: 'rect',
              },
              {
                name: '实际',
                icon: 'rect',
              },
            ],
            textStyle: {
              // 图例文字的样式
              color: '#8A8F99',
            },
          },
          grid: {
            left: '5%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
          },
          xAxis: [
            {
              type: 'category',
              data: this.chartData.date,
              boundaryGap: false,
              axisLabel: {
                show: true,
                rotate: 50,
                textStyle: {
                  color: '#8A8F99', // 更改坐标轴文字颜色
                },
              },
              axisLine: {
                lineStyle: {
                  color: '#EBEEF5', // 更改坐标轴颜色
                },
              },
            },
          ],
          yAxis: [
            {
              type: 'value',
              minInterval: 1,
              axisTick: {
                // y轴刻度线
                show: false,
              },
              axisLine: {
                show: false, // 不显示坐标轴轴线
              },
              splitLine: {
                // 网格线
                lineStyle: {
                  type: 'dashed', // 设置网格线类型 dotted：虚线   solid:实线
                  width: 1,
                  color: '#EBEEF5',
                },
              },
              axisLabel: {
                show: true,
                textStyle: {
                  color: '#8A8F99', // 更改坐标轴文字颜色
                },
              },
            },
          ],
          series: [
            {
              name: '计划',
              type: 'line',
              emphasis: {
                focus: 'series',
              },
              data: this.chartData.plan,
            },
            {
              name: '实际',
              type: 'line',
              areaStyle: {
                // 前四个参数代表位置 左下右上，如下表示从上往下渐变色 紫色到暗蓝色，
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: 'rgba(0, 136, 255, 0.5)' },
                  { offset: 1, color: 'rgba(0, 136, 255, 0)' },
                ]),
              },
              emphasis: {
                focus: 'series',
              },
              data: this.chartData.reality,
            },
          ],
        }
      }
    },

    // 查询迭代计划
    async getPlanList() {
      this.planList = []
      this.planId = ''
      this.listLoading = true
      const res = await apiAlmProjectPlanNoPage({
        projectId: this.$route.params.id,
        type: 'SPRINT',
      })
      this.listLoading = false

      if (!res.isSuccess) {
        return
      }
      if (!res.data.length) {
        this.noData = true
        this.chartData = null
        return
      } else {
        this.planList = res.data
        this.planId = res.data[0].id
        this.noData = false
      }
      this.geBurnChartOptions()
    },
  },
}
</script>

<style lang="scss" scoped>
.burnType {
  float: right;
}
.clearfix {
  font-weight: bold;
  :deep(.el-card__header) {
    border-bottom: none;
  }
}
</style>
