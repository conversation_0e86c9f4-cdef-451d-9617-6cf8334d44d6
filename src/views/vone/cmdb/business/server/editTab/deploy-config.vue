<template>
  <!-- 部署配置 -->
  <div v-loading="loading">
    <vone-search-wrapper v-if="this.$route.params.type != 1">
      <template v-slot:actions>
        <el-button type="primary" @click="saveDeployConfig">保存</el-button>
      </template>
    </vone-search-wrapper>
    <el-form
      ref="form"
      :auto-layout="false"
      :rules="rules"
      label-width="0"
      :model="baseForm"
    >
      <main
        style="margin-top: 16px"
        :style="{
          height:
            $route.params.type != 1
              ? 'calc(100vh - 418px)'
              : 'calc(100vh - 370px)',
        }"
      >
        <vxe-table
          class="vone-vxe-table deployTable"
          border
          resizable
          height="auto"
          :loading="tableLoading"
          show-overflow="tooltip"
          :empty-render="{ name: 'empty' }"
          :data="baseForm.tableData"
          :column-config="{ minWidth: '120px' }"
          :checkbox-config="{ reserve: true }"
          row-id="id"
        >
          <vxe-column title="环境" field="envKey" width="135">
            <template #default="{ row, rowIndex }">
              <el-form-item
                :prop="`tableData.${rowIndex}.envKey`"
                :rules="rules.envKey"
              >
                <el-select
                  v-model="row.envKey"
                  placeholder="请选择环境"
                  :disabled="$route.params.type == 1"
                >
                  <el-option
                    v-for="(item, index) in envList"
                    :key="index"
                    :label="item.name"
                    :value="item.code"
                  />
                </el-select>
              </el-form-item>
            </template>
          </vxe-column>
          <vxe-column field="appComponentsId" title="组件实例" width="180">
            <template #default="{ row, rowIndex }">
              <el-form-item
                :prop="`tableData.${rowIndex}.appComponentsId`"
                :rules="rules.appComponentsId"
              >
                <el-select
                  v-model="row.appComponentsId"
                  placeholder="请选择组件实例"
                  :disabled="$route.params.type == 1"
                >
                  <el-option
                    v-for="(item, index) in getCompentOpts(row.appComponentsId)"
                    :key="index"
                    :label="item.appComponentsTypeKey"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </template>
          </vxe-column>
          <vxe-column field="serverIp" title="主机IP" width="130">
            <template #default="{ row }">
              <span
                v-if="row.appComponentsId && compentMap[row.appComponentsId]"
              >
                {{ compentMap[row.appComponentsId].serverIp }}
              </span>
              <span v-else> -- </span>
            </template>
          </vxe-column>
          <vxe-column field="serverPort" title="端口" width="100">
            <template #default="{ row }">
              <span
                v-if="row.appComponentsId && compentMap[row.appComponentsId]"
              >
                {{ compentMap[row.appComponentsId].serverPort }}
              </span>
              <span v-else> -- </span>
            </template>
          </vxe-column>
          <vxe-column field="deployDir" title="部署目录">
            <template #default="{ row }">
              <span
                v-if="row.appComponentsId && compentMap[row.appComponentsId]"
              >
                {{ compentMap[row.appComponentsId].deployDir }}
              </span>
              <span v-else> -- </span>
            </template>
          </vxe-column>
          <vxe-column field="version" title="运行版本" width="120">
            <template #default="{ row }">
              <span v-if="row.deployVersion">
                <a @click="checkVersion(row)">{{ row.deployVersion || "" }}</a>
              </span>
              <span v-else>--</span>
            </template>
          </vxe-column>
          <vxe-column field="moduleId" title="工程包名称" width="120">
            <template #default="{ row, rowIndex }">
              <el-form-item
                :prop="`tableData.${rowIndex}.moduleId`"
                :rules="rules.moduleId"
              >
                <el-select
                  v-model="row.moduleId"
                  placeholder="请选择工程包名称"
                  :disabled="$route.params.type == 1"
                >
                  <el-option
                    v-for="(item, index) in moduleIdList"
                    :key="index"
                    :label="item.productName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </template>
          </vxe-column>
          <vxe-column field="checkUrl" title="URL检查路径">
            <template #default="{ row }">
              <el-input
                v-model="row.checkUrl"
                placeholder="URL检查路径"
                :disabled="$route.params.type == 1"
              />
            </template>
          </vxe-column>
          <vxe-column v-if="$route.params.type != 1" title="操作" width="80">
            <template #default="{ rowIndex }">
              <el-button
                type="text"
                :icon="elIconTipsPlusCircle"
                @click.prevent="addRow(rowIndex, tableData)"
              />
              <el-button
                type="text"
                :icon="elIconApplicationDelete"
                @click.prevent="deleteRow(rowIndex, tableData)"
              />
            </template>
          </vxe-column>
        </vxe-table>
      </main>

      <!-- <el-row v-if="this.$route.params.type != 1" class="mt-3" type="flex" justify="center">
            <el-button type="primary" @click="saveDeployConfig">保存</el-button>
          </el-row> -->
      <!-- <el-row v-if="TYPE == 0"  >
            <div style="float: left">
              <el-button v-show="active > 0" @click="stepFoaward">上一步</el-button>
            </div>
            <div style="float: right">
              <el-button type="primary" @click="saveNext">保存并下一步</el-button>
            </div>
          </el-row> -->
    </el-form>
  </div>
</template>

<script>
import {
  $on,
  $off,
  $once,
  $emit,
} from "../../../../../../utils/gogocodeTransfer";
import {
  apiBaseSelectDeployConfigBySystemId,
  apiBuniesSaveDeployConfig,
} from "@/api/vone/cmdb/server";
// import { apiBaseDictNoPage } from '@/api/vone/base/dict'

import {
  apiCmdbSelectModuleBySystemId,
  apiCmdbFindApplication,
} from "@/api/vone/cmdb/application";

export default {
  props: {
    active: {
      type: Number,
      default: 0,
    },
    applicationInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      loading: true,
      baseForm: {
        tableData: [],
      },
      rules: {
        envKey: [{ required: true, message: "请选择环境" }],
        appComponentsId: [{ required: true, message: "请选择实例" }],
        moduleId: [{ required: true, message: "请选择实例" }],
      },
      VERSION: "",
      envList: [],
      appComponentsIdList: [],
      compentMap: {},
      moduleIdList: [],
      TYPE: undefined,
      tableLoading: false,
      // applicationList: []
    };
  },
  async mounted() {
    this.TYPE = this.$route.params.type;
    await this.getComponentList();
    await this.getModuleList();
    await this.getTableData();
    // 查询服务应用下的应用组件
    // this.getApplicationList()
    this.$refs.form.clearValidate();
    this.loading = false;

    if (this.applicationInfo.applicationEnvs.length) {
      this.envList = this.applicationInfo.applicationEnvs.map((r) => ({
        code: r.envKey,
        name: r.envKey,
      }));
    }
  },
  methods: {
    // 运行版本一致性检测
    async checkVersion(val, row) {
      // let productName = ''
      // const moduleId = row.moduleId[0]
      // for (let i = 0; i < this.moduleIdList.length; i++) {
      //   if (moduleId === this.moduleIdList[i].id) {
      //     productName = this.moduleIdList[i].productName
      //   }
      // }
      // const res = await apiCmdbversionChecking({
      //   ip: val.IP,
      //   appReleaseDirectory: val.APP_RELEASE_DIRECTORY,
      //   productName: productName,
      //   productVersion: val.VERSION
      // })
      // if (res.data !== null && success) {
      //   this.$confirm(res.message, '提示', {
      //     closeOnClickModal: false
      //   }).then(
      //     res => {
      //       if (res == 'confirm') {
      //         const res = apiCmdbupdateDeployInfo({
      //           'appCompentId': val.ID,
      //           'productDate': data.productDate,
      //           'productVersion': data.productVersion
      //         }).then(
      //           res => {
      //             if (res.success) {
      //               this.getTableData()
      //               this.getComponentList()
      //             } else {
      //               this.$message.warning(res.message)
      //               return
      //             }
      //           }
      //         )
      //       }
      //     }
      //   )
      // } else {
      //   this.$message.warning(res.msg)
      // }
    },
    // async getApplicationList() {
    //   const res = await apiCmdbFindApplication(
    //     this.$route.params.systemId
    //   )
    //   if (!res.isSuccess) {
    //     this.$message.warning(res.msg)
    //     return
    //   }
    //   this.applicationList = res.data
    // },
    // 点击+号图标添加新的一栏
    addRow(index) {
      this.baseForm.tableData.splice(index + 1, 0, {});
      this.$nextTick(() => {
        this.$refs.form.clearValidate(`tableData.${index + 1}.moduleId`);
      });
    },
    // 点击减号删除一行
    deleteRow(index, rows) {
      if (this.baseForm.tableData.length > 1) {
        this.baseForm.tableData.splice(index, 1);
      } else {
        this.baseForm.tableData = [{}];
      }
    },
    getCompentOpts(appComponentsId) {
      const usedIds = this.baseForm.tableData.map((r) => r.appComponentsId);
      return this.appComponentsIdList.filter(
        (r) => r.id === appComponentsId || usedIds.indexOf(r.id) === -1
      );
    },

    // 查实例
    async getComponentList() {
      const res = await apiCmdbFindApplication(this.$route.params.systemId);
      if (!res.isSuccess) {
        return;
      }

      const map = {};
      res.data.map((d) => (map[d.id] = d));
      this.appComponentsIdList = res.data;
      this.compentMap = map;
    },
    // 查询工程包名称
    async getModuleList() {
      const res = await apiCmdbSelectModuleBySystemId(
        this.$route.params.systemId
      );
      if (!res.isSuccess) {
        return;
      }
      this.moduleIdList = res.data;
    },
    async getTableData() {
      this.tableLoading = true;
      const res = await apiBaseSelectDeployConfigBySystemId(
        this.$route.params.systemId
      );
      this.tableLoading = false;
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }

      // res.data.forEach(el => {
      //   el.moduleId = [el.moduleId]
      // })
      this.baseForm.tableData = res.data;
      if (res.data.length == 0) {
        this.addRow();
      }
    },
    // 保存
    async saveDeployConfig() {
      await this.$refs.form.validate();
      // const applicationDeploys = []
      // this.baseForm.tableData.forEach(deployData => {
      //   deployData.moduleId.forEach(moduleId => {
      //     applicationDeploys.push({
      //       systemId: this.$route.params.systemId,
      //       envKey: deployData.envKey,
      //       appComponentsId: deployData.appComponentsId,
      //       moduleId: moduleId,
      //       checkUrl: deployData.checkUrl
      //     })
      //   })
      // })
      const applicationDeploys = this.baseForm.tableData.map((r) => ({
        appComponentsId: r.appComponentsId,
        applicationId: this.$route.params.systemId,
        checkUrl: r.checkUrl,
        envKey: r.envKey,
        moduleId: r.moduleId,
      }));

      const res = await apiBuniesSaveDeployConfig({
        applicationId: this.$route.params.systemId,
        applicationDeploys,
      });
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      this.$message.success("保存成功");
      this.getTableData();
    },
    stepFoaward() {
      $emit(this, "stepFoaward");
    },
    async saveNext() {
      await this.saveDeployConfig();
      // this.activeIndex++;
      $emit(this, "success", "deployConfig");
    },
  },
  emits: ["success", "stepFoaward"],
};
</script>

<style lang="scss" scoped>
.deployTable {
  .el-form-item--small.el-form-item {
    margin-bottom: 0;
  }
}
</style>
