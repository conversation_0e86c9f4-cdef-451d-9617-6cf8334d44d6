<template>
  <vone-edit-wrapper v-loading="editLoading" show-footer>

    <div>
      <!-- 基本配置 -->

      <vone-card-wrapper title="基本配置">
        <baseConfig ref="baseConfig" :host-datas="hostDatas" :engine-data="engineData" @onCategoryChange="onCategoryChange" @classfiyChange="classfiyChange" />
      </vone-card-wrapper>

      <!-- 高级属性 -->

      <vone-card-wrapper v-if="hasAdvanceConfig" title="高级配置">
        <advanceConfig ref="advanceConfig" :advance-params="advanceParams" :engine-data="engineData" :engine-category-id="engineCategoryId" />
      </vone-card-wrapper>

      <!-- jenkinMaster 的节点配置 -->
      <template v-if="advanceConfigComponent">
        <component :is="advanceConfigComponent" :id="id" :engine-data="engineData" :host-datas="hostDatas" />
      </template>
      <!-- jenkinMaster 的动态节点配置 -->
      <vone-card-wrapper
        v-if="advanceConfigComponent"
        style="margin-bottom:16px"
        title="动态节点配置"
      >
        <trendsNode :id="id" />
      </vone-card-wrapper>
    </div>
    <el-row slot="footer">
      <el-button @click="cancle">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="save">
        保存</el-button>
    </el-row>
  </vone-edit-wrapper>
</template>

<script>

import { apiBaseEngineAdd, apiBaseEngineTestConnect, apiBaseEngineInfoGetById } from '@/api/vone/base/engine'
import { apiCmdbHostNoPage } from '@/api/vone/cmdb/host'
import { apiBaseEngineExtendInstance } from '@/api/vone/base/engine'

import baseConfig from './baseConfig'
import customConfig from './customConfig'
import advanceConfig from './advanceConfig'
import advanceCustomConfig from './advanceCustomConfig'
import trendsNode from './trendsNode'
export default {
  components: {
    baseConfig,
    customConfig,
    advanceConfig,
    trendsNode
  },
  data() {
    return {
      id: undefined,
      // 当前引擎
      engineCategoryId: undefined,
      // 高级属性参数
      advanceParams: undefined,
      // 编辑引擎查询的数据
      engineData: undefined,
      // 编辑Loading
      editLoading: false,
      // 所属服务器列表
      hostDatas: [],
      saveLoading: false
    }
  },
  computed: {
    advanceConfigComponent() {
      return advanceCustomConfig[this.engineCategoryId]
    },
    hasAdvanceConfig() {
      return !!this.advanceParams && this.advanceParams.length
    }
  },
  created() {
    this.id = this.$route.params.id
    if (this.id) {
      this.editLoading = true
      this.loadEngine()
    }
    this.selectHostDatas()
  },
  methods: {
    classfiyChange() {
      this.advanceParams = undefined
      this.engineCategoryId = undefined
    },
    /**
     * 获取所属服务器下拉框数据
     */
    async selectHostDatas() {
      const res = await apiCmdbHostNoPage()
      if (!res.isSuccess) {
        return
      }
      this.hostDatas = res.data
    },
    /**
     * 编辑，查询引擎数据
     */
    async loadEngine() {
      const res = await apiBaseEngineInfoGetById(this.id)
      if (!res.isSuccess) {
        return
      }
      this.engineData = res.data

      if (res.data && res.data.enginePassword) {
        this.$set(this.engineData, 'enginePassword', '******')
      }

      if (res.data.instance) {
        await this.onCategoryChange(res.data.instance.code)
      }
      // await this.onCategoryChange(res.data.instance.code)
      this.editLoading = false
    },
    /**
     * 类型change事件
     */
    async onCategoryChange(v) {
      this.advanceParams = null
      if (v == 'JENKINS_MASTER') {
        this.engineCategoryId = 'JenkinsMaster'
      } else {
        this.engineCategoryId = v
      }
      const res = await apiBaseEngineExtendInstance(v)
      if (!res.isSuccess) {
        return this.$message.error(res.msg)
      }
      this.advanceParams = res.data
    },

    // 保存
    async save() {
      // try {
      const [
        baseConfig,
        // _customAttributeDatas,
        engineExtendeds
      ] = await Promise.all([
        this.$refs.baseConfig.getFormData(),
        // this.$refs.customConfig.getFormData(),
        this.$refs.advanceConfig
          ? this.$refs.advanceConfig.getFormData()
          : Promise.resolve()
      ])

      // if (engineExtendeds.length) {
      //   const engineExtendeds = engineExtendeds
      // } else {
      //   const engineExtendeds = []
      // }

      const formData = {
        ...baseConfig,
        engineExtendeds: engineExtendeds || [],
        enginePassword: baseConfig.enginePassword || null
      }

      this.saveLoading = true
      const { data } = await apiBaseEngineTestConnect(formData)

      this.saveLoading = false
      if (!data) {
        await this.$confirm('引擎连通性测试失败，是否继续保存？', '删除', {
          type: 'warning'
          // closeOnClickModal: false
        })
        formData.state = false
      } else {
        formData.state = true
      }
      const res = await apiBaseEngineAdd(formData)
      if (!res.isSuccess) {
        return this.$message.error(res.isSuccess)
      }
      this.$message.success('保存成功')

      // this.$route.go(-1)
      this.$router.push({
        name: 'base_engine_view'
      })
      // } catch (e) {
      //   this.saveLoading = false
      //   return
      // }
    },
    cancle() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
