<template>
  <el-dialog
    :visible="visible"
    :before-close="close"
    title="新增工作台"
    :close-on-click-modal="false"
   
  >
    <el-form ref="form" :model="form">
      <el-form-item prop="name" label="名称" :rules="[{ required: true, message: '请输入名称' }]">
        <el-input ref="name" v-model.trim="form.name" />
      </el-form-item>
      <el-form-item label="选择模版" prop="customDashboardDatas">
        <el-radio-group v-model="form.customDashboardDatas" clearable @change="changeRadio">
          <el-radio>自定义工作台</el-radio>
          <div v-for="temp in temps" :key="temp.id">
            <el-radio :label="temp.id">{{ temp.name }}</el-radio>
          </div>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="submit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { searchDefaultDashboard, setTemplate } from '@/api/vone/dashboard'
import reduce from 'lodash/reduce'

export default {
  props: {
    visible: Boolean,
    isEdit: Boolean,
    addName: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      temps: [],
      tempMap: {},
      form: {
        name: ''
      },
      saveLoading: false
    }
  },
  mounted() {
    this.getTemps()
  },
  methods: {
    close() {
      this.$emit('update:visible', false)
      this.$refs.form.resetFields()
    },
    async getTemps() {
      const { data, isSuccess, msg } = await searchDefaultDashboard()
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.temps = data
      this.tempMap = reduce(data, (r, v) => (r[v.id] = v) && r, {})
    },
    changeRadio(val) {
      if (val) {
        this.$set(this.form, 'name', this.tempMap[val].name)
      } else {
        this.$set(this.form, 'name', '')
      }
    },
    async submit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (!this.form.customDashboardDatas) {
            this.$emit('update:addName', this.form.name)
            this.$emit('update:isEdit', true)
            this.close()
          } else {
            setTemplate([this.form.customDashboardDatas]).then(res => {
              if (res.isSuccess) {
                this.close()
                this.$message.success(res.msg)
                this.$emit('refreshDashboard')
              }
            })
          }
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.el-radio{
	margin-top: 8px;
}
</style>
