import { createRouter, createWebHistory } from 'vue-router'
import store from '../store'
import Layout from '@/layout/index.vue'
import RouterWrapper from '@/components/RouterWrap/index.vue'
import EmbeddedPage from '@/components/EmbeddedPage/index.vue'
import routers from './routers'

import { getToken, getUser, getRouter, getExpiration, setPermission, getPermission, removeToken } from '@/utils/auth'
import { getRouterMenu } from '@/api/user'
import { ElMessageBox, ElMessage } from 'element-plus'
import getPageTitle from '@/utils/get-page-title'

// import pathToRegexp from 'path-to-regexp'
import dayjs from 'dayjs'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { apiProjectAuth } from '@/api/vone/project/index'

import { isEqual, cloneDeep } from 'lodash'
import { name } from 'dayjs/locale/zh-cn'

const pathType = {
  'idea': 'reqm_center_idea_view',
  'issue': 'reqm-center-require-list',
  'P-defect': 'project_defect',
  'P-task': 'project_task',
  'P-issue': 'project_issue',
  'P-risk': 'project_risk_view'
}
NProgress.configure({ showSpinner: false })

// create router
const router = createRouter({
  history: createWebHistory(),
  routes: routers,
  scrollBehavior: () => ({ top: 0 })
})
const whiteList = ['/login', '/yjs/loginVone'] // 白名单
let asyncRouter // 异步的路由信息，应用菜单会根据其来控制显示隐藏
// 路由拦截
router.beforeEach(async(to, from, next) => {
  NProgress.start()
  if (to?.name == from?.name) NProgress.done()
  document.title = getPageTitle(to.meta.title)
  // 注意：在Vue3中需要通过getCurrentInstance获取全局属性
  // 这里暂时注释掉，后续在组件中处理
  // if (to && to?.name != 'wiki_document') {
  //   if (getCurrentInstance()?.appContext.config.globalProperties.$axiosList.length > 0) {
  //     getCurrentInstance()?.appContext.config.globalProperties.$axiosList.forEach(item => {
  //       item('已经中断请求')
  //     })
  //   }
  // }
  // getCurrentInstance()?.appContext.config.globalProperties.$axiosList = []
  const hasToken = getToken()
  const userRouter = getRouter()
  const expiration = getExpiration()
  const newDate = dayjs().format('YYYY-MM-DD HH:mm:ss')
  const userInfo = getUser()
  if (hasToken && userInfo) {
    if (expiration) {
      if (dayjs(newDate).diff(dayjs(expiration)) > 0) {
        if (to.path === '/login' && (to.query.code || to.query.IDnumber)) {
          // 清理缓存
          removeToken()
          resetRouter()
          router.push(to.fullPath)
          return
        }
        await store.dispatch('user/logout', userInfo.id)
        return
      }
    }
    if (to.path === '/login') {
      next({ path: '/' })
      NProgress.done()
    } else {
      if (!asyncRouter) {
        if (!userRouter) {
          getRouterMenu().then(res => {
            asyncRouter = res.data
            if (!(asyncRouter && asyncRouter?.length > 0)) {
              asyncRouter = []
            }
            asyncRouter.push({
              redirect: '/exception/404',
              hidden: true,
              path: '/:pathMatch(.*)*',
              component: 'Layout',
              name: 'NotFound'
            })

            // 处理菜单数据 添加meta信息
            asyncRouter.map(item => {
              if (item.path != '*') {
                item.meta.activeApp = item?.meta?.code
                if (item?.children && item?.children.length > 0) {
                  item.redirect = item.children[0].path
                  const parentData = item // 一级数据
                  gainMenuList(item.children, parentData)
                }
              }
            })

            store.commit('user/set_router', asyncRouter)
            if (asyncRouter[0].path != 'dashboard' && to.path == '/') {
              routerPush(asyncRouter[0], next)
            } else {
              routerPush(to, next)
            }
          })
        } else {
          asyncRouter = userRouter
          store.commit('user/set_router', asyncRouter)
          if (asyncRouter[0].path != 'dashboard' && to.path == '/') {
            routerPush(asyncRouter[0], next)
          } else {
            if ((to.query?.id || to.query?.queryId) && (to.query?.copyurl == '1' || to.query?.showDialog) && (to.query?.projectid || to.query?.projectId) && to.path != '/reqmcenter/require/requireList') {
              gotoproject(to.query, to.path, to, next)
            } else {
              routerPush(to, next)
            }
          }
        }
      } else {
        // 对项目菜单进行特殊处理，由项目角色自己管控
        const asyncProject = asyncRouter.find(ele => ele?.name == 'project') ? asyncRouter.find(ele => ele.name == 'project').children?.map(item => item?.name) : []
        const userPeoject = userRouter.find(ele => ele?.name == 'project') ? userRouter.find(ele => ele.name == 'project').children?.map(item => item?.name) : []

        // 处理菜单数据 添加meta里的activeApp信息
        asyncRouter.map(item => {
          if (item.path != '*') {
            item.meta.activeApp = item.meta.code
            if (item.children && item.children.length > 0) {
              item.redirect = item.children[0].path
              const parentData = item // 一级数据
              gainMenuList(item.children, parentData)
            }
          }
        })

        if (!isEqual(asyncProject, userPeoject)) {
          asyncRouter = userRouter
          asyncRouter = filterAsyncRouter(asyncRouter)
          // 解决动态路由加载重复问题
          resetRouter()
          asyncRouter.forEach(route => {
            router.addRoute(route)
          })

          next(to)
          return
        }

        if (userRouter[0].path != 'dashboard' && to.path == '/') {
          next({ path: userRouter[0].path })
        } else {
          next()
        }
      }
    }
  } else {
    asyncRouter = null
    // if (whiteList.indexOf(to.path) !== -1) {
    if (whiteList.includes(to.path)) {
      next()
    } else {
      ElMessageBox.alert('登录已失效，请重新登录', '提醒', {
        confirmButtonText: '确定',
        callback: async() => {
          // await store.dispatch('user/logout')
          removeToken()
          resetRouter()
          router.push(`/login`)
        }
      })
      NProgress.done()
    }
  }
})
async function gotoproject(udata, path, to, next) {
  const pathdata = path.split('/')
  const routes = asyncRouter.find(ele => ele?.name == 'project').children?.find(e => e?.name == 'project_' + pathdata[2])
  if (routes) {
    routerPush(to, next)
    return
  }
  const { data, isSuccess, msg } = await apiProjectAuth(udata.projectid || udata.projectId)
  const jumpRouter = cloneDeep(data)
  if (!isSuccess) {
    ElMessage.error(msg)
    return
  }
  if (!data.length || !jumpRouter.find(e => e?.name == pathType['P-' + pathdata[2]])) {
    ElMessage.warning({
      showClose: true,
      message: '当前登录用户【项目角色】查看当前项目信息权限不足,请联系项目经理授权',
      duration: 2000
    })
    return
  }
  const hasPermissionList = []
  var findPermission = function(V) {
    V.forEach(item => {
      if (item.meta.isButton === true) {
        hasPermissionList.push(item.meta.code)
      }
      if (item.children) {
        findPermission(item.children)
      }
    })
  }
  findPermission(data)
  const permission = getPermission()
  const allPermision = [...new Set([...permission, ...hasPermissionList])]
  setPermission(allPermision)
  const routerMenu = cloneDeep(getRouter())
  const projectSettingMenu = cloneDeep(routerMenu.find(ele => ele?.name == 'project').children.find(ele => ele.name == 'project_view'))
  routerMenu.map(item => {
    if (item?.name == 'project') {
      item.children = []
      item.children.push(projectSettingMenu)
      item.children = [...item.children, ...data]
    }
  })

  store.commit('user/set_router', routerMenu)
  store.dispatch('project/itemProject', pathdata[3])
  router.push(to)
}
// 处理菜单数据 添加meta信息
function gainMenuList(childrenData, parentData, secondItem) {
  childrenData.map(item => {
    item.meta.activeApp = parentData.meta.code
    item.meta.activeMenu = secondItem ? secondItem.path : item.path // 存在三级，那么指定的菜单为二级的path
    if (item.children && item.children.length > 0) {
      item.redirect = item.children[0].path
      gainMenuList(item.children, parentData, item)
    }
  })
}

// 过滤并处理router
function filterAsyncRouter(routes, parent) {
  return routes.filter((route) => {
    const component = route.component
    if (component) {
      if (route.component === 'Layout') {
        route.component = Layout
      } else if (route.component === 'RouterWrapper') {
        route.component = RouterWrapper
      } else if (route.component === 'EmbeddedPage') {
        route.component = EmbeddedPage
      } else {
        route.component = requireCompoent(route.component)
      }
      if (route.children && route.children.length) {
        route.children = filterAsyncRouter(route.children, route)
      }
      return true
    }
  })
}
const requireCompoent = (path) => {
  return (resolve) => require([`@/views/${path}.vue`], resolve)
}

// 路由拦截跳转
function routerPush(to, next) {
  asyncRouter = filterAsyncRouter(asyncRouter)

  resetRouter()
  asyncRouter.forEach(route => {
    router.addRoute(route)
  })
  next({ ...to, replace: true })
}

router.afterEach(() => {
  NProgress.done()
})
// 为了解决动态加载路由的bug
export function resetRouter() {
  // Vue Router 4中需要手动移除路由
  const routes = router.getRoutes()
  routes.forEach(route => {
    if (route.name && !routers.find(r => r.name === route.name)) {
      router.removeRoute(route.name)
    }
  })
}

export default router
