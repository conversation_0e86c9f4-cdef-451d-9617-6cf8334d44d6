<template>
  <el-dialog
    :visible="visible"
    :before-close="close"
    title="编辑部件"
    :close-on-click-modal="false"
    width="600px"
   
  >
    <el-form ref="form" :model="form" :rules="formRule">

      <el-form-item prop="tableViewId" label="筛选器">
        <el-select v-model="form.tableViewId" filterable>
          <el-option v-for="(item,index) in filterList" :key="index" :label="item.name" :value="item.id">{{ item.name }}</el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="name" label="部件名称">
        <el-input ref="name" v-model.trim="form.name" placeholder="请输入" />
      </el-form-item>
      <el-form-item prop="x" label="X轴">
        <el-select v-model="form.x" placeholder="请选择" filterable @change="changeX">
          <el-option v-for="(item,index) in typeList" :key="index" :disabled="item.key == form.group" :value="item.key" :label="item.name" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="form.type == 'DATE'" prop="timeCycle" label="时间类型">
        <el-select v-model="form.timeCycle" placeholder="请选择">
          <el-option v-for="(item,index) in timeList" :key="index" :value="item.key" :label="item.name" />
        </el-select>
      </el-form-item>
      <el-form-item prop="y" label="Y轴">
        <el-select v-model="form.y" placeholder="请选择">
          <el-option value="count(*)" label="统计记录总数">统计记录总数</el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="sortT" label="排序">
        <el-select v-model="form.sortT" placeholder="请选择">
          <el-option value="1" label="横轴值">横轴值</el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="sortrule" label="排序规则">
        <el-select v-model="form.sortrule" placeholder="请选择">
          <el-option value="normal" label="正序">正序</el-option>
          <el-option value="over" label="倒序">倒序</el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="isGroup">
        <el-checkbox v-model="form.isGroup">分组聚合</el-checkbox>
      </el-form-item>
      <el-form-item v-if="form.isGroup" prop="group" label="分组聚合">
        <el-select v-model="form.group" placeholder="请选择" filterable @change="changeX">
          <el-option v-for="(item,index) in typeList" :key="index" :disabled="item.key == form.x" :value="item.key" :label="item.name" />
        </el-select>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="submit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getFilterList } from '@/api/vone/dashboard'
import { queryFilterConditions } from '@/api/vone/dashboard/filter'
import _ from 'lodash'
export default {
  props: {
    visible: Boolean,
    isEdit: Boolean,
    isEditForm: Boolean,
    formInfo: {
      type: Object,
      default: () => {}
    },
    layout: {
      type: [Object, Array],
      default: null
    }
  },
  data() {
    return {
      form: {
        name: '',
        y: 'count(*)',
        sortT: '1',
        sortrule: 'normal'
      },
      formRule: {
        name: [{ required: true, message: '请输入名称' }],
        tableViewId: [{ required: true, message: '请选择筛选器' }],
        x: [{ required: true, message: '请选择X轴' }],
        y: [{ required: true, message: '请选择Y轴' }],
        timeCycle: [{ required: true, message: '请选择时间' }]
        // sortT: [{ required: true, message: '请选择排序' }],
        // sortrule: [{ required: true, message: '请选择排序规则' }]
      },
      filterList: [],
      saveLoading: false,
      timeList: [
        {
          name: '每日',
          key: 'DAY'
        }, {
          name: '每周',
          key: 'WEEK'
        }, {
          name: '每月',
          key: 'MOUTH'
        }, {
          name: '每年',
          key: 'YEAR'
        }
      ],
      typeList: []
    }
  },
  watch: {
    'form.isGroup'(val) {
      if (!val) {
        this.form.group = ''
      }
    }
  },
  mounted() {
    if (this.isEditForm) {
      this.form = this.formInfo
    }
    this.getList()
    this.getTypeList()
  },
  methods: {
    getList() {
      getFilterList({
        table: 'work_item_entity'
      }).then(res => {
        if (res.isSuccess) {
          this.filterList = res.data
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    getTypeList() {
      queryFilterConditions('WORK_ITEM').then(res => {
        if (res.isSuccess) {
          this.typeList = res.data.filter(r => r.key != 'tagId')
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    changeX(e) {
      this.typeList.map(item => {
        if (item.key == e) {
          this.form.type = item.type.code
        }
      })
    },
    close() {
      this.$emit('update:visible', false)
      this.$refs.form.resetFields()
    },
    async submit() {
      try {
        await this.$refs.form.validate()
      } catch (e) {
        return
      }
      const layouts = _.cloneDeep(this.layout)
      if (this.isEditForm) {
        layouts.map(item => {
          if (item.id == this.form.id && item.widgetCode == this.form.key) {
            item.queryParams = JSON.stringify({ ...this.form })
          }
        })
      } else {
        layouts.push({
          x: (this.layout.length * 2) % (12),
          y: this.layout.length + (12), // puts it at the bottom
          w: 4,
          h: 5,
          i: this.layout.length + 1,
          queryParams: JSON.stringify({ ...this.formInfo, ...this.form }),
          widgetCode: this.formInfo.key
        })
      }
      this.$emit('update:layout', layouts)
      this.$emit('update:visible', false)
      this.$emit('success')
    }
  }
}
</script>
