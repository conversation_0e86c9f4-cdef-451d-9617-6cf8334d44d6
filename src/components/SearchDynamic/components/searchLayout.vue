<template>
  <div>
    <el-dropdown class="grouping-style" trigger="click" @command="CardCommand($event,true)">
      <span class="label">
        <span class="weak-title">布局</span>
        {{ cardItem.name }}
        <i class="iconfont el-icon-direction-down el-icon--right" />
      </span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item v-for="e in cardOptions" :key="e.value" :class="['grouping-item', cardItem.value == e.value? 'is-active' : '' ]" :command="e.value">
          {{ e.name }}
          <i v-if="e.active" class="grouping-icon iconfont el-icon-tips-done" />
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
export default {
  props: {
    showCard: {
      type: Boolean,
      default: false
    },
    valueObj: {
      type: Object,
      default: () => {}
    },
    cardOptions: {
      type: Array,
      default: () => []
    }

  },
  data() {
    return {

      cardItem: {
        // name: '卡片',
        // value: 'card',
        // icon: 'el-icon-application-biaogeshitu'
      }
    }
  },
  watch: {
    valueObj: {
      handler(val) {
        if (val && val.extra && val.extra.cardView) {
          this.cardItem = this.cardOptions.find(e => e.value == val.extra.cardView)
        } else {
          this.cardItem = this.cardOptions[0]
        }
      },
      immediate: true
    }
  },
  methods: {
    CardCommand(id, type) {
      this.cardItem = this.cardOptions.find(e => e.value == id)

      this.$emit('success', id)
    }
  }

}
</script>

<style lang="scss" scoped>
@use '../index.scss' as *;
</style>
<style lang="scss">
@use '../index-global.scss' as *;
</style>
