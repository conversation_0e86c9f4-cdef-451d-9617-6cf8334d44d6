<!-- 我的动态卡片 -->
<template>
  <div class="dynamicBox">
    <header>
      <div class="title">{{ formInfo.title||'我的动态' }}</div>
      <div class="countBox">
        <span>共计<b>{{ activities.length }}</b>条</span>
      </div>
    </header>
    <vone-empty v-if="activities.length === 0 && !loading" />
    <el-timeline v-else class="dynamicList">
      <el-timeline-item v-for="(activity, index) in activities" :key="index" :class="'active_'+index" placement="top" :timestamp="activity.createTime|formatTime">
        {{ activity.description }}
      </el-timeline-item>
    </el-timeline>
  </div>
</template>

<script>
import { apiInsightMydynamic } from '@/api/vone/weidget'
import dayjs from 'dayjs'
// const colors = [ '#64BEFA', '#3CB540', '#BD7FFA', '#FC9772', '#8791FA', '#4BCCBB', '#FFBF47', '#ADB0B8', '#FA6B57', '#37CDDE' ]
export default {
  filters: {
    formatTime(val) {
      if (!val) return ''
      return dayjs(val).format('MM-DD HH:mm')
    }
  },
  props: {
    formInfo: { type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      activities: []
    }
  },
  created() {
    this.getDynamicData()
  },
  methods: {
    async getDynamicData() {
      try {
        this.loading = true
        const res = await apiInsightMydynamic()
        this.loading = false
        if (res.isSuccess) {
          this.activities = res.data
        }
      } catch (e) {
        this.loading = false
      }
    }
  }
}
</script>
<style lang='scss' scoped>
.dynamicBox {
  position: relative;
  height: 100%;
  width: 100%;
  padding: 12px 16px;
  color: #202124;
  background-color: var(--main-bg-color,#fff);
  border-radius: 4px;
  border: 1px solid var(--solid-border-color);
  box-shadow: var(--main-bg-shadow);
  transition: 0.3s;
  header {
    // display: flex;
    // justify-content: space-between;
    // align-items: center;
    height: 26px;
    margin-bottom: 12px;
    .title {
      color: #202124;
      font-size: 600;
      display: inline-block;
    }
    .countBox {
      display: inline-block;
      margin-left: 4px;
      span {
        color:#777F8E
      }
      b {
        color:var(--font-main-color);
        margin: 0 4px;
      }
    }
  }
  // 动态列表样式
  .dynamicList {
    display: inline-block;
    width: 100%;
    height: calc(100% - 38px);
    overflow-y: overlay;

    ::v-deep .el-timeline-item {
      padding-bottom: 12px;
      // 时间线样式
      .el-timeline-item__tail {
        left: 91px;
      }
      // 时间节点样式
      .el-timeline-item__node {
        left: 86px;
        top: 2px;
        background-color: var(--main-bg-color,#fff);
        border: 2px solid #fff;
      }
      // 内容部分样式
      .el-timeline-item__wrapper {
        display: flex;
        align-items: center;
        padding: 0;
        height: 22px;
        // 时间样式
        .el-timeline-item__timestamp {
          width: 80px;
          margin: 0;
          padding: 0;
          margin-right: 36px;
          font-size: 14px;
        }
        // 显示内容样式
        .el-timeline-item__content {
          flex: 1;
          font-size: 14px;
          font-weight: 400;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
    ::v-deep .active_ {
      &0 {
        .el-timeline-item__node {
          border-color: #37cdde;
        }
      }
      &1 {
        .el-timeline-item__node {
          border-color: #3cb540;
        }
      }
      &2 {
        .el-timeline-item__node {
          border-color: #64befa;
        }
      }
      &3 {
        .el-timeline-item__node {
          border-color: #bd7ffa;
        }
      }
      &4 {
        .el-timeline-item__node {
          border-color: #fc9772;
        }
      }
      &5 {
        .el-timeline-item__node {
          border-color: #8791fa;
        }
      }
      &6 {
        .el-timeline-item__node {
          border-color: #4bccbb;
        }
      }
      &7 {
        .el-timeline-item__node {
          border-color: #ffbf47;
        }
      }
      &8 {
        .el-timeline-item__node {
          border-color: #adb0b8;
        }
      }
      &9 {
        .el-timeline-item__node {
          border-color: #fa6b57;
        }
      }
    }

    &::-webkit-scrollbar {
      width: 8px;
    }

  }
}

</style>
