
<template>
  <page-wrapper id="graph" ref="graph" class="graph" />
</template>
<script>
import G6 from '@antv/g6'
import customNode from './registerNode'
import customEdge from './registerEdge'
import tooltip from './tooltip'
export default {
  name: 'ServerGraph',
  props: {
    graphData: {
      type: Object,
      default: () => ({ })
    }
  },
  data() {
    return {
      data: {},
      graph: null,
      container: null
    }
  },
  watch: {
    async graphData(val) {
      if (val) {
        this.data = val
        // this.graph.changeData(val)
        // 自定义节点
        customNode.init()
        // 自定义边
        customEdge.init()
        await this.initGraph(this.data)
        window.addEventListener('resize', this.handleWindowResize)
      }
    }
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleWindowResize)
    this.graph = null
    this.container = null
  },
  methods: {
    initGraph(data) {
      const container = this.$refs.graph.$el
      const width = container.clientWidth
      const height = container.clientHeight
      const graph = new G6.TreeGraph({
        container: container,
        width,
        height,
        plugins: [tooltip],
        modes: {
          default: [
            'drag-canvas',
            {
              type: 'zoom-canvas',
              minZoom: 0.5,
              maxZoom: 2
            }
          ]
        },
        defaultNode: {
          type: 'flow-rect'
        },
        defaultEdge: {
          type: 'hvh',
          style: {
            lineWidth: 2,
            stroke: '#8862FA'
          }
        },
        layout: {
          type: 'mindmap',
          direction: 'H',
          getHeight: () => {
            return 60
          },
          getVGap: () => {
            return 40
          },
          getHGap: () => {
            return 160
          },
          getSide: () => {
            return 'right'
          }
        }
      })
      this.container = container
      this.graph = graph
      graph.data(data)
      graph.render()
      graph.fitView()
    },
    handleWindowResize() {
      if (typeof window !== 'undefined') {
        window.onresize = () => {
          if (!this.graph || this.graph.get('destroyed')) return
          if (!this.container || !this.container.scrollWidth || !this.container.scrollHeight) return
          this.graph.changeSize(this.container.scrollWidth, this.container.scrollHeight)
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
  .g6-component-tooltip {
    background-color: rgba(255, 255, 255, 0.8);
    padding: 0px 10px 24px 10px;
    box-shadow: rgb(174, 174, 174) 0px 0px 10px;
  }
</style>
