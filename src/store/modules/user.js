import { logout } from '@/api/user'
import { setToken, setUser, removeToken, setRouter } from '@/utils/auth'
import { resetRouter } from '@/router'
import router from '@/router'
import { getUserDetail } from '@/api/vone/base/user'
import LFUCache from '@/utils/LFUCache.js'
import { getToken } from '@/utils/auth'

const initState = () => {
  return {
    token: '',
    appRouterMenu: [], // 应用菜单列表
    permission: [],
    currentAppMenu: [],
    user: {},

    userMap: new LFUCache(50) // 缓存用户信息
  }
}

const state = initState()
const mutations = {
  reset_token: (state) => {
    Object.assign(state, initState())
  },
  set_token: (state, token) => {
    setToken(token)
    state.token = token
  },
  set_user: (state, data) => {
    setUser(data)
    state.user = data
  },
  set_router: (state, appRouterMenu) => {
    state.appRouterMenu = appRouterMenu
    setRouter(appRouterMenu)
  },
  set_permission: (state, permission) => {
    state.permission = permission
  },
  set_current_menu: (state, currentMenu) => {
    state.currentAppMenu = currentMenu
  },

  set_userData: (state, { key, value }) => {
    state.userMap.put(key, value)
  }
}

const actions = {
  // set current menu
  setCurrentMenu({ commit }, data) {
    commit('set_current_menu', data)
  },
  // user logout
  logout({ commit, state }, id) {
    const hasToken = getToken()
    return new Promise((resolve, reject) => {
      logout(id, hasToken
      ).then(async() => {
        removeToken()
        resetRouter()
        await commit('reset_token')
        if (router.currentRoute.path?.indexOf('exception') == -1) {
          router.push(`/login?redirect=${router.currentRoute.path}`)
        } else {
          router.push(`/login`)
        }
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },
  // 查询用户信息
  getUserData({ commit, state, dispatch }, key) {
    const userData = state.userMap.get(key)
    // 延迟查询同参数的用户信息，确保接口调用一次
    if (userData === 'loading') {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve()
        }, 200)
      }).then(() => {
        return dispatch('getUserData', key)
      })
    }
    // 存在用户信息直接返回
    if (userData) return { data: userData, isSuccess: true }
    // 不存在用户信息，设置loading状态
    commit('set_userData', { key, value: 'loading' })
    // 查询用户信息
    return getUserDetail(key).then(res => {
      commit('set_userData', { key, value: res.data || {}})
      return res
    }).catch(err => {
      commit('set_userData', { key, value: null })
      return err
    })
  },
  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      removeToken()
      commit('reset_token')
      resolve()
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

