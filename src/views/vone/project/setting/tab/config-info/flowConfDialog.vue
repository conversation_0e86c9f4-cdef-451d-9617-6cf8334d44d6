<template>
  <div>
    <el-dialog
      title="任务流转配置"
      width="50%"
      v-model:value="visible"
      :before-close="onClose"
      :close-on-click-modal="false"
    >
      <p>配置状态流转用户权限，只有有权限的用户才允许做此流转。</p>
      <el-form v-loading="loading" label-position="top">
        <el-form-item
          v-if="sourceTargetData.length"
          prop="name"
          label="当前流转"
        >
          <span v-for="(item, i) in sourceTargetData" :key="item.id">
            <el-divider v-if="i !== 0" direction="vertical" />
            <!-- <JTfStatus :value="item.sourceNodeId" /> -->
            <el-tag>{{ item.sourceName }}</el-tag>
            <el-icon class="mx-2"><el-icon-right /></el-icon>
            <el-tag>{{ item.targetName }}</el-tag>
            <!-- <JTfStatus :value="item.targetNodeId" /> -->
          </span>
        </el-form-item>
        <el-form-item prop="name" label="指定角色权限">
          <el-checkbox-group v-model="userRole">
            <el-checkbox
              v-for="item in roleList"
              :key="item.id"
              :label="item.id"
              >{{ item.name }}</el-checkbox
            >
          </el-checkbox-group>
        </el-form-item>
        <el-form-item prop="name" label="指定人员字段">
          <el-checkbox-group v-model="userType">
            <el-checkbox :label="'RESPONSIBLE'">负责人</el-checkbox>
            <el-checkbox :label="'HANDLER'">处理人</el-checkbox>
            <el-checkbox :label="'CREATOR'">创建人/提出人</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item prop="name" label="指定用户">
          <el-select v-model="personData" multiple filterable>
            <el-option
              v-for="item in userList"
              :key="item.id"
              :value="item.id"
              :label="item.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="typeCode" label="应用工作项类型">
          <el-checkbox-group v-model="typeCodes">
            <el-checkbox
              v-for="(item, index) in treeData"
              :key="index"
              :label="item.code"
              :disabled="item.code == typeCode ? true : false"
              >{{ item.name }}</el-checkbox
            >
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <div class="dialog-footer">
          <el-button @click="onClose">取消</el-button>
          <el-button type="primary" @click="configFlow">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { Right as ElIconRight } from "@element-plus/icons-vue";
import {
  $on,
  $off,
  $once,
  $emit,
} from "../../../../../../utils/gogocodeTransfer";
import {
  apiAlmProjectRoleNoPage,
  apiAlmcheckWorkflowByInfo,
} from "@/api/vone/project/setting";
import {
  apiProjectUserNoPage,
  apiProjectWorkflowPut,
  getFlowDetail,
} from "@/api/vone/project/index";
export default {
  components: {
    ElIconRight,
  },
  props: {
    visible: {
      type: Boolean,
      default: true,
    },
    type: {
      default: undefined,
      type: String,
    },
    typeCode: {
      default: undefined,
      type: String,
    },
    targetNode: {
      default: () => {},
      type: Object,
    },
    sourceNode: {
      default: () => {},
      type: Object,
    },
    workFlowId: {
      default: undefined,
      type: String,
    },
    selectMoreData: {
      default: () => [],
      type: Array,
    },
    treeData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      typeCodes: [this.typeCode],
      userRole: [], // 指定角色
      userType: [], // 指定用户 'RESPONSIBLE', 'HANDLER', 'CREATOR'
      personData: [], // 其它用户
      userList: [],
      roleList: [],
      loading: false,
      sourceTargetData: [],
      userRoleData: [], // 所有的人
      edgeLines: [], // 所有的线
      allNode: [], // 所有的点
      allInfo: {}, // 工作流全部信息
      more: false,
    };
  },
  watch: {
    currentTargetData() {
      this.showChecked();
    },
  },
  mounted() {
    this.getUser();
    this.getRole();

    if (this.workFlowId) {
      this.getFlowInfo();
      this.check();
    }
  },
  methods: {
    // 查询任务流信息
    async getFlowInfo() {
      this.sourceTargetData = [];
      this.loading = true;
      const res = await getFlowDetail(
        this.$route.params.id,
        this.type,
        this.typeCodes
      );
      this.loading = false;
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      this.allInfo = res.data?.workflowInfo; // 工作流完整信息
      this.userRoleData = res.data?.workflowInfo?.workflowAuthorities; // 所有的人员权限数据

      this.edgeLines = res.data?.workflowInfo?.workflowTransitions; // 所有的线
      // this.allNode = res.data.workflowNodes // 所有的点
      if (this.selectMoreData.length > 0) {
        this.sourceTargetData = this.selectMoreData;
      } else {
        const process = {
          sourceId: this.sourceNode.id,
          sourceName: this.sourceNode.name,
          targetId: this.targetNode.id,
          targetName: this.targetNode.name,
        };
        this.sourceTargetData.push(process);

        // 获取线
        const edgeData = this.edgeLines
          .filter(
            (r) =>
              r.source == this.sourceNode.id && r.target == this.targetNode.id
          )
          .map((j) => j.id);
        // 查询线上的人员角色

        const chooseEdge = this.userRoleData.filter((item) => {
          return edgeData.indexOf(item.transitionId) != -1;
        });
        // 判断当前连线有权限的  用户/角色/处理人/负责人
        const TYPE = chooseEdge.map((r) => r.type.code);
        const hasUser = TYPE.filter((r) => r == "USER");
        const hasRole = TYPE.filter((r) => r == "ROLE");
        const hasRESPONSIBLE = TYPE.filter((r) => r == "RESPONSIBLE");
        const hasHANDLER = TYPE.filter((r) => r == "HANDLER");
        const hasCREATOR = TYPE.filter((r) => r == "CREATOR");

        // 用户集合
        const User = chooseEdge
          .filter((r) => r.type.code == "USER")
          .map((r) => r.userOrRole);
        // 角色集合
        const Role = chooseEdge
          .filter((r) => r.type.code == "ROLE")
          .map((r) => r.userOrRole);

        // 指定人
        if (hasRESPONSIBLE.length) {
          this.userType.push("RESPONSIBLE");
        }

        if (hasHANDLER.length) {
          this.userType.push("HANDLER");
        }

        if (hasCREATOR.length) {
          this.userType.push("CREATOR");
        }
        // 指定角色
        if (hasRole.length) {
          this.userRole = Role;
        }
        // 其它用户
        if (hasUser.length) {
          this.personData = User;
        }
      }
    },
    // 获取角色列表
    async getRole() {
      const res = await apiAlmProjectRoleNoPage({
        projectId: this.$route.params.id,
      });
      if (!res.isSuccess) {
        return;
      }
      this.roleList = res.data;
    },
    // 获取用户列表
    async getUser() {
      const res = await apiProjectUserNoPage({
        projectId: this.$route.params.id,
      });
      if (!res.isSuccess) {
        return;
      }
      this.userList = res.data;
    },
    showChecked() {
      // 关闭时清空勾选值
      this.role = [];
      this.type = [];
      this.person = [];

      this.currentTargetData.map((item) => {
        switch (Number(item.userType)) {
          case 0:
            this.role.push(item.userId);
            break;
          case 2:
            this.type.push(Number(item.userType));
            break;
          case 3:
            this.type.push(Number(item.userType));
            break;
          case 4:
            this.person.push(item.userId);
            break;
          case 5:
            this.type.push(Number(item.userType));
            break;
          default:
        }
      });
    },
    async check() {
      // 先检查是否被其它工作流应用
      const resCheck = await apiAlmcheckWorkflowByInfo(
        this.$route.params.id,
        this.type,
        this.typeCode
      );

      if (!resCheck.isSuccess) {
        this.$message.warning(resCheck.msg);
        return;
      }
      const defaultCode = [this.typeCode];
      this.typeCodes = defaultCode.concat(resCheck.data.map((r) => r.code));
    },
    // 保存
    async configFlow() {
      // const checkSource = this.sourceTargetData.map(r => r.sourceId)
      // const checkTarget = this.sourceTargetData.map(r => r.targetId)
      const chooseLine = [];
      let chooseUserR = [];
      let chooseUserU = [];
      let chooseUserC = [];
      let chooseRole = [];
      let chooseOther = [];
      this.edgeLines.filter((item) => {
        this.sourceTargetData.forEach((element) => {
          if (
            element.sourceId == item.source &&
            element.targetId == item.target
          ) {
            chooseLine.push(item.id);
          }
        });
      });

      // 选择的线

      // 如果有指定人--负责人
      if (this.userType.length) {
        chooseUserR = this.userType
          .filter((r) => r === "RESPONSIBLE")
          .map((r) => ({
            type: "RESPONSIBLE",
            workflowId: this.workFlowId,
          }));
      }
      // 如果有指定人--处理人
      if (this.userType.length) {
        chooseUserU = this.userType
          .filter((r) => r === "HANDLER")
          .map((r) => ({
            type: "HANDLER",
            workflowId: this.workFlowId,
          }));
      }
      if (this.userType.length) {
        chooseUserC = this.userType
          .filter((r) => r === "CREATOR")
          .map((r) => ({
            type: "CREATOR",
            workflowId: this.workFlowId,
          }));
      }
      // 如果有角色
      if (this.userRole.length) {
        chooseRole = this.userRole.map((r) => ({
          type: "ROLE",
          userOrRole: r,
          workflowId: this.workFlowId,
        }));
      }
      // 其它人员
      if (this.personData.length) {
        chooseOther = this.personData.map((r) => ({
          type: "USER",
          userOrRole: r,
          workflowId: this.workFlowId,
        }));
      }

      const allRoleUser = [
        ...chooseUserR,
        ...chooseUserU,
        ...chooseUserC,
        ...chooseRole,
        ...chooseOther,
      ];
      const authData = [];
      allRoleUser.map((item) => {
        chooseLine.map((itm) => {
          authData.push({
            transitionId: itm,
            type: item.type,
            userOrRole: item.userOrRole,
            workflowId: item.workflowId,
          });
        });
      });
      const params = {};
      params.more = this.more;
      params.projectId = this.$route.params.id;
      params.typeClassify = this.type;
      params.typeCode = this.typeCodes;
      params.workflowAuthorities = authData;
      params.workflowId = this.workFlowId;

      const res = await apiProjectWorkflowPut({ ...params });

      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }

      this.$message.success("工作流状态权限配置完成");
      this.onClose();
      $emit(this, "success");
    },
    onClose() {
      $emit(this, "update:selectMoreData", []);
      $emit(this, "update:visible", false);
      $emit(this, "noCheck");
    },
  },
  emits: ["update:selectMoreData", "update:visible", "success", "noCheck"],
};
</script>
