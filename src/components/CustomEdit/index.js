const fileMap = {
  ISSUE: 'ISSUE_FILE_UPLOAD',
  BUG: 'BUG_FILE_UPLOAD',
  TASK: 'TASK_FILE_UPLOAD',
  RISK: 'RISK_FILE_UPLOAD',
  IDEA: 'IDEA_FILE_UPLOAD'
}

const iconMap = {
  tagId: 'el-icon-icon-fill-biaoqian',
  planStime: 'el-icon-icon-fill-wanchengshijian',
  startTime: 'el-icon-icon-fill-wanchengshijian',
  planEtime: 'el-icon-icon-fill-wanchengshijian',
  endTime: 'el-icon-icon-fill-wanchengshijian',
  expectedTime: 'el-icon-icon-fill-wanchengshijian',
  stateCode: 'el-icon-icon-fill-zhuangtai'
}

// 处理-连接拼接成驼峰命名
function toHump(str) {
  const reg = /-(\w)/g
  return str.replace(reg, function ($0, $1) {
    return $1.toUpperCase()
  })
}
// 处理首字母大写
function upperFirst(str) {
  return str.charAt(0).toUpperCase() + str.slice(1)
}
// 读取当前文件夹下components文件夹下.vue文件
const requireComponents = import.meta.glob('./commonTab/components/*.vue', { eager: true })
const componentsObj = {}
Object.entries(requireComponents).forEach(([path, module]) => {
  const fileName = path.split('/').pop().replace(/\.vue$/, '')
  const componentName = upperFirst(toHump(fileName))
  componentsObj[componentName] = module.default || module
})

import { apiAlmUpdate, apiAlmGetInfo, apiAlmSourceNoPage } from '@/api/vone/project/issue'
import {
  apiAlmPriorityNoPage,
  apiAlmTypeNoPage,
  getAllProductInfoList,
  apiAlmGetTypeNoPage
} from '@/api/vone/alm/index'

import {
  apiVaBaseCustomFormField,
  apiVaBaseCustomFormFieldProgram,
  apiVaBaseCustomFormFieldProject
} from '@/api/vone/base/customForm'
import { productListByCondition, queryListByCondition } from '@/api/vone/project/index'
import { planListByCondition } from '@/api/vone/project/iteration'
import { requirementListByCondition } from '@/api/vone/project/issue'
import { bugListByCondition } from '@/api/vone/project/defect'
import { getProjectPlans } from '@/api/vone/testmanage/case'
import { getProductVersionList, getModule } from '@/api/vone/product/index'

import tagSelect from './commonTab/components/tag-select.vue'
import projectRemoteUser from './commonTab/components/project-user-remote.vue'
import { apiBaseDictNoPage } from '@/api/vone/base/dict'

// import storage from 'store'

import activeTab from './commonTab/active.vue'
import activityRecord from './commonTab/activity-record.vue'

import StateCode from './components/state-code.vue'
import workTime from './commonTab/work-time.vue'
import { gainTreeList } from '@/utils'
import { orgList } from '@/api/vone/base/org'
import dataSelect from './commonTab/components/data-select.vue'
// import { apiBaseAllUserNoPage } from '@/api/vone/base/user'
import { getSourceById } from '@/api/vone/base/source'
import _ from 'lodash'
export default {
  name: 'VoneCustomEdit',
  components: {
    ...componentsObj,
    activeTab,
    activityRecord,
    workTime,
    tagSelect,
    projectRemoteUser,
    StateCode,
    dataSelect
  },
  directives: {
    focus: {
      inserted: function (el) {
        el.querySelector('input').focus()
      }
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: undefined
    },
    id: {
      type: String,
      default: undefined
    },
    infoDisabled: {
      type: Boolean,
      default: false
    },
    sprintId: {
      type: String,
      default: undefined
    },
    tableList: {
      type: Array,
      default: () => []
    },
    typeCode: {
      type: String,
      default: undefined
    },
    leftTabs: {
      type: Array,
      default: () => []
    },
    rightTabs: {
      type: Array,
      default: () => []
    },
    formId: {
      // 平台配置,预览表单接口,需要根据formId查询模板
      type: String,
      default: undefined
    },

    rowTypeCode: {
      // 需求中心/项目里,从列表数据取到的类型
      type: String,
      default: undefined
    },
    stateCode: {
      // 需求中心/项目里,从列表数据取到的状态
      type: String,
      default: undefined
    },
    rowProjectId: {
      // 需求中心,从列表数据取到的关联项目的项目id
      type: String,
      default: undefined
    },
    hidePrv: {
      // 不显示上一个下一个按钮
      type: Boolean,
      default: false
    },
    // 拆分子项是否弹框
    showPopupForSplit: {
      type: Boolean,
      default: false
    }
  },
  data: function () {
    return {
      chatVisible: false,
      iconMap,
      page: null,
      drawerLoading: false,
      fileMap,
      tabActive: 'basic',
      rightTabActive: 'comment',
      saveLoading: false,
      pageLoading: false,
      currentIndex: undefined, // 当前数据的索引
      customForm: {},
      fixedForm: {},
      otherForm: {},
      formRules: {},
      fixdFormRules: {},
      basicProperty: [],
      fixedProperty: [],
      customList: [],
      infoLoading: true,
      selectOptionWidth: '',
      commentId: '',
      planIdList: [], // 计划
      orgData: [], // 组织机构
      isChange: false,
      projectId: this.$route.params.id
    }
  },
  watch: {
    'customForm.projectId': {
      handler(value) {
        if (!value) {
          this.$set(this.customForm, 'planId', '')
          this.setData(this.customList, 'planId', [])
          return
        }

        this.getPlanList(value)
      },
      immediate: true
    },
    'customForm.productId': {
      handler(value) {
        if (!value) {
          this.$set(this.customForm, 'productVersionId', '')
          this.$set(this.customForm, 'productRepairVersionId', '')
          this.$set(this.customForm, 'productModuleFunctionId', null)
          this.setData(this.customList, 'productVersionId', [])
          this.setData(this.customList, 'productRepairVersionId', [])
          this.setData(this.customList, 'productModuleFunctionId', [])
          return
        }
        this.getVersion(value)
        this.getModel(value)
      },
      deep: true,
      immediate: true
    }
  },
  async mounted() {
    this.commentId = this.id || ''
    this.currentIndex = this.tableList.findIndex(item => item.id === this.id)
    await this.getFormTemplete()
    this.getSelectSource()

    this.leftTabs.forEach(element => {
      element.active = false
    })
    // this.getUserList()
  },

  methods: {
    pickerOptions(e) {
      var _this = this
      if (e == 'planEtime') {
        return {
          disabledDate(time) {
            if (_this.customForm['planStime']) {
              return time.getTime() < new Date(_this.customForm['planStime']).getTime()
            }
          }
        }
      } else if (e == 'planStime') {
        return {
          disabledDate(time) {
            if (_this.fixedForm['planEtime']) {
              return time.getTime() > new Date(_this.fixedForm['planEtime']).getTime()
            }
          }
        }
      }
    },
    fileClick(link) {
      if (link && (_.startsWith(link, 'http') || _.startsWith(link, 'https'))) {
        window.open(link, '_blank')
      } else {
        window.open('http://' + link, '_blank')
      }
    },
    editFn(e, vlaue) {
      this.$set(e, 'isShowLink', vlaue)
    },
    remoteMethod: _.debounce(function (e, t) {
      if (t == 'requirementId') {
        this.getRequirementList(e)
      } else if (t == 'planId') {
        this.getPlanList(this.customForm.projectId, e)
      } else if (t == 'projectId') {
        this.getProjectList(e)
      } else if (t == 'bugId') {
        this.getBugList(e)
      }
    }, 1000),
    async getQuoteType() {
      this.customList.forEach((e, index) => {
        if (e.type == 'QUOTE') {
          const config = JSON.parse(e.config)
          this.getTableConfig(config.relationShipsheet, e, item => {
            e.quoteType = item?.type
            this.$set(this.customList, index, e)
          })
        }
      })
    },
    async getTableConfig(id, e, callback) {
      const config = JSON.parse(e.config)
      const res = await getSourceById(id)
      if (res.isSuccess) {
        const list = res.data.fields.map(v => {
          v.prop = v.id
          const obj = JSON.parse(v.config)
          return {
            ...v,
            ...obj
          }
        })
        const objs = list.find(e => e.id == config.relationField && !e.primary)
        callback(objs)
      }
    },
    dataSelectChange({ item, list, user }, key) {
      this.customList.forEach(e => {
        if (e.type == 'QUOTE') {
          const config = JSON.parse(e.config)
          if (key == config.queryCriteriaC) {
            this.customForm[e.key] = item[config.relationField]
            const obj = list.find(e => e.id == config.relationField && !e.primary)
            e.quoteType = obj.type
            if (e.quoteType == 'user') {
              e.user = user[item[config.relationField]]
            }
          }
        }
      })
    },
    changeSelect() {
      this.isChange = true
    },
    // 数据源接口返回数据以后,把值塞到表单模板里
    setData(list, key, data) {
      list.forEach((e, index) => {
        if (e.key == key) {
          this.$set(e, 'options', data)
        }
      })
    },
    // 下拉框弹出时，设置弹框的宽度
    setOptionWidth(event) {
      this.$nextTick(() => {
        this.selectOptionWidth = event.target.parentNode.parentNode.clientWidth + 'px'
      })
    },

    async getSelectSource() {
      this.getOrgList()
      this.getSourceList()
      this.getPrioritList()
      this.getProjectList()

      const keys = this.customList.map(r => r.key)
      if (keys.includes('envCode')) {
        this.getEnvList()
      }

      if (this.$route.params.projectKey) {
        this.getRequirementList()
        this.getBugList()
        this.getAllTypeCode()
        this.getProjectProductList()
      } else {
        this.getIssueType()
        this.productList()
      }

      this.typeCode == 'BUG' ? this.getProjectTestPlan() : null
    },
    // 工作项显示
    async getFormTemplete(val) {
      this.fixdForm = {}
      this.form = {}
      this.drawerLoading = true

      // 需求中心，项目，项目集，查询表单项时调不同的接口url，参数格式也不同
      // 需求中心和其它使用：apiVaBaseCustomFormField
      // 项目使用：apiVaBaseCustomFormFieldProject
      // apiVaBaseCustomFormFieldProgram

      const activeApp = this.$route.meta.activeApp
      const params = {
        typeCode: this.rowTypeCode || this.$route.query.rowTypeCode,
        stateCode: this.stateCode,
        projectId: this.rowProjectId || null
      }
      const res =
        activeApp == 'projectm'
          ? await apiVaBaseCustomFormFieldProgram(this.$route.params.id, this.typeCode, params)
          : activeApp == 'project'
          ? await apiVaBaseCustomFormFieldProject(this.$route.params.id, this.typeCode, params)
          : await apiVaBaseCustomFormField(this.typeCode, params)

      this.drawerLoading = false
      if (!res.isSuccess) {
        return
      }

      res.data.forEach(element => {
        element.placeholder = JSON.parse(element.config)?.placeholder
        element.multiple = JSON.parse(element.config)?.multiple
        element.message = JSON.parse(element.config)?.message
        element.validator = JSON.parse(element.config)?.validator
        element.defaultTime = JSON.parse(element.config)?.defaultTime
        element.options = !element.isBuilt ? JSON.parse(element.config)?.options : []
        element.disabled = element.key == 'planId'
        element.type = element.type.code
        element.precision = JSON.parse(element.config)?.precision

        // if (element.type == 'QUOTE') {
        //   this.getQuoteType()
        // }
      })

      // 所有基本属性
      const basicAll = res.data && res.data.length ? res.data.filter(r => r.isBasic && r.isShow && r.state) : []

      // 其它基本属性
      const other = basicAll.filter(r => r.key == 'files' || r.key == 'description')

      // 固定基本属性
      const fixed = _.difference(basicAll, other)

      // 固定基本属性
      this.fixedProperty = fixed.sort(function (a, b) {
        return a.sort - b.sort
      })
      // 固定属性[文件和描述]
      this.basicProperty = other.sort(function (a, b) {
        return a.sort - b.sort
      })
      // 排序
      // 自定义属性
      const custom = res.data && res.data.length ? res.data.filter(r => !r.isBasic && r.isShow && r.state) : []
      this.customList = custom.sort(function (a, b) {
        return a.sort - b.sort
      })

      // 固定属性的校验规则
      const fixedRoule = this.fixedProperty.map(r => ({
        required: r.key !== 'typeCode' ? r.isRequired : false,
        message: r.message,
        max: r.max,
        pattern: r.validator,
        key: r.key,
        type: r.key == 'tagId' ? 'array' : 'string'
      }))

      fixedRoule.forEach(item => {
        if (!this.fixdFormRules[item.key]) {
          this.fixdFormRules[item.key] = [item]
        } else {
          this.fixdFormRules[item.key].push(item)
        }
      })
      this.customList.forEach(r => {
        const rulesObj = {
          required: r.isRequired,
          message: r.message ? r.message : '该字段为必填项',
          max: r.max,
          pattern: r.validator,
          key: r.key,
          type: r.multiple ? 'array' : r.type == 'INT' ? 'number' : 'string'
        }
        if (r.type == 'LINKED') {
          this.formRules[r.key] = [{ required: r.isRequired, message: r.message }]
        } else if (r.type == 'INT') {
          this.formRules[r.key] = [{ required: r.isRequired, message: r.message, trigger: 'change' }]
        } else {
          this.formRules[r.key] = [rulesObj]
        }
      })
      this.getQuoteType()
      await this.getIssueInfo(val)
    },
    // 查询所有机构
    async getOrgList() {
      const res = await orgList()
      if (!res.isSuccess) {
        return
      }
      const orgTree = gainTreeList(res.data)
      this.orgData = orgTree
    },
    // 查询来源
    async getSourceList() {
      const res = await apiAlmSourceNoPage({
        typeClassify: this.typeCode
      })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.customList, 'sourceCode', res.data)
    },
    async getEnvList() {
      const res = await apiBaseDictNoPage({
        type: 'ENVIRONMENT'
      })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.customList, 'envCode', res.data)
    },
    // 查询项目关联的产品，用于缺陷和任务表单关联产品【主办/辅办】
    async getProjectProductList() {
      const res = await getAllProductInfoList(this.$route.params.id || '0')
      if (!res.isSuccess) {
        return
      }
      this.setData(this.customList, 'productId', res.data)
    },
    // 关联产品版本
    async getVersion(val) {
      if (this.isChange) {
        this.$set(this.customForm, 'productVersionId', '')
        this.$set(this.customForm, 'productRepairVersionId', '')
      }

      const res = await getProductVersionList({ productId: val })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.customList, 'productVersionId', res.data)
      this.setData(this.customList, 'productRepairVersionId', res.data)
    },
    // 关联产品功能
    async getModel(val) {
      if (this.isChange) {
        this.$set(this.customForm, 'productModuleFunctionId', null)
      }
      const form = {}
      this.$set(form, 'productId', val)
      const parameter = {
        model: { ...form },
        extra: { tableSave: false }
      }
      const res = await getModule(parameter)
      if (!res.isSuccess) {
        return
      }
      this.setData(this.customList, 'productModuleFunctionId', this.getTreeList(res.data))
    },
    getTreeList(data, obj = []) {
      data.map(item => {
        const type = item.nodeType == 1 ? ' (模块)' : item.nodeType == 2 ? ' (功能)' : ''
        item.label = item.name + type
        if (item.children && item.children.length > 0) {
          this.getTreeList(item.children)
        }
      })
      return data
    },
    // 查优先级
    async getPrioritList() {
      const res = await apiAlmPriorityNoPage()
      if (!res.isSuccess) {
        return
      }

      this.setData(this.customList, 'priorityCode', res.data)
    },
    // 查项目下需求
    async getRequirementList(e) {
      const res = await requirementListByCondition({
        projectId: this.$route.params.id,
        name: e
      })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.customList, 'requirementId', res.data)
    },
    // 查项目下缺陷
    async getBugList(e) {
      const res = await bugListByCondition({
        projectId: this.$route.params.id,
        name: e
      })
      if (!res.isSuccess) {
        return
      }

      this.setData(this.customList, 'bugId', res.data)
    },
    // 查询项目下类型
    async getAllTypeCode() {
      const res = await apiAlmGetTypeNoPage(this.$route.params.id, this.typeCode)
      if (!res.isSuccess) {
        return
      }

      this.setData(this.fixedProperty, 'typeCode', res.data)
    },
    // 查询所有类型
    async getIssueType() {
      const res = await apiAlmTypeNoPage({
        classify: this.typeCode
      })
      if (!res.isSuccess) {
        return
      }

      this.setData(this.fixedProperty, 'typeCode', res.data)
    },
    // 归属产品
    async productList() {
      const res = await productListByCondition()

      if (!res.isSuccess) {
        return
      }

      this.setData(this.customList, 'productId', res.data)
    },
    // 归属项目
    async getProjectList(e) {
      const res = await queryListByCondition({
        name: e
      })

      if (!res.isSuccess) {
        return
      }

      this.setData(this.customList, 'projectId', res.data)
    },

    // 迭代计划
    async getPlanList(val, e) {
      if (this.isChange) {
        this.$set(this.customForm, 'planId', '')
      }
      const res = await planListByCondition({
        isFiled: false,
        projectId: val,
        name: e
      })
      if (!res.isSuccess) {
        return
      }
      this.planIdList = this.sprintId ? res.data.filter(r => r.stateCode != 4) : res.data
      this.setData(this.customList, 'planId', this.planIdList)
    },
    // 测试计划
    async getProjectTestPlan() {
      const res = await getProjectPlans({
        projectId: this.$route.params.id
      })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.customList, 'testPlanId', res.data)
    },
    initList() {
      this.$emit('success')
    },
    eventDisposalRangeChange(value) {
      const textLength = this.$refs.editor[0].$el.innerText.replace(/[|]*\n/, '').length
      if (textLength >= 1000) {
        this.$refs.otherForm.validateField(['description'])
      } else {
        this.$refs.otherForm.clearValidate(['description'])
      }
    },
    updateFiles(files, key) {
      this.customForm[key] = files.map(v => v.id).join(',')
    },
    removeFile(file, key) {
      const re = new RegExp(file.id, 'g')
      this.customForm[key] = this.customForm[key].replace(re, '').replace(',,', ',')
    },
    onClose() {
      this.$emit('update:visible', false)
    },
    handleClick(event, list) {
      list.forEach(v => {
        if (v.name == event.name) {
          v.active = true
        } else {
          v.active = false
        }
      })
    },
    validatePromise(component, message) {
      return new Promise((resolve, reject) => {
        component.validate((valid, rules) => {
          if (!Object.keys(rules)?.length) {
            resolve(valid)
          } else {
            // const firstRule = rules[Object.keys(rules)?.[0]]
            // const errorMessage = firstRule?.[0]?.message || message
            this.$message.warning('请填写必填项')
            reject({ valid, rules })
          }
        })
      })
    },
    // 保存
    async saveInfo() {
      if (this.tabActive != 'basic') {
        this.onClose()
        return
      }
      try {
        const fixdForm_promise = this.validatePromise(this.$refs.fixedForm, '请填写必填项')
        await fixdForm_promise
        const other_promise = this.validatePromise(this.$refs.otherForm, '请填写必填项')
        await other_promise
        const custom_promise = this.validatePromise(this.$refs.customForm, '请填写必填项')
        await custom_promise
        this.saveLoading = true
        this.$set(this.otherForm, 'files', this.$refs['uploadFile'][0].uploadFiles)

        // 自定义属性下的文件
        const customFile = this.customList.find(r => r.type == 'FILE')?.key
        if (customFile) {
          this.$set(this.customForm, customFile, this.$refs['formUploadFile'][0].uploadFiles.map(r => r.id).join(','))
        }
        const params = {
          ...this.fixdForm,
          ...this.otherForm,
          ...this.customForm
        }

        // 判断key里面有没有C1到C30的，有的话，他的值如果是[],就是用逗号分隔成字符串传给后端
        var arr = Array.from(Array(30), (v, k) => `c${k + 1}`)
        const multipleList = this.customList.filter(r => r.multiple).map(r => r.key)
        for (var item in params) {
          if (arr.includes(item)) {
            if (multipleList.includes(item) && Array.isArray(params[item])) {
              params[item] = params[item].map(r => r).join(',') || null
            }
          }
        }

        const urlMap = {
          ISSUE: '/api/alm/alm/requirement',
          BUG: '/api/alm/alm/bug',
          TASK: '/api/alm/alm/task',
          RISK: '/api/alm/alm/risk',
          IDEA: '/api/alm/alm/idea'
        }
        const res = await apiAlmUpdate(urlMap[this.typeCode], params)
        this.saveLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }

        this.$message.success('保存成功')
        this.$emit('success')
        this.onClose()
      } catch (e) {
        this.saveLoading = false
      }
    },
    changeFlow() {
      this.$emit('success')
      this.onClose()
    },

    // 详情
    async getIssueInfo(val) {
      this.drawerLoading = true
      this.infoLoading = true

      const urlMap = {
        ISSUE: `/api/alm/alm/requirement/${val || this.id}`,
        BUG: `/api/alm/alm/bug/${val || this.id}`,
        TASK: `/api/alm/alm/task/${val || this.id}`,
        RISK: `/api/alm/alm/risk/${val || this.id}`,
        IDEA: `/api/alm/alm/idea/${val || this.id}`
      }

      const res = await apiAlmGetInfo(urlMap[this.typeCode])

      if (!res.isSuccess) {
        return
      }

      if (!res.data) {
        return
      }
      // 判断key里面有没有C1到C30的，有的话，如果是多选,就是分隔成数组
      var arr = Array.from(Array(30), (v, k) => `c${k + 1}`)
      const multipleList = this.customList.filter(r => r.multiple).map(r => r.key)
      for (var item in res.data) {
        if (arr.includes(item)) {
          if (multipleList.includes(item)) {
            res.data[item] = res.data[item] ? res.data[item].split(',') : []
          }
        }
      }
      if (res.data.tagId && res.data.tagId.length && res.data.echoMap && res.data.echoMap.tagId) {
        this.$set(res.data, 'tagId', res.data.echoMap.tagId.map(r => r.name) || [])
      }
      this.customForm = res.data
      this.fixedForm = res.data
      this.otherForm = res.data

      // if (this.customForm && this.customForm.projectId) {
      //   this.isChange = false
      //   this.getPlanList(this.customForm.projectId)
      // }

      if (this.customForm && this.customForm.productId) {
        this.isChange = false

        this.getVersion(this.customForm.productId)
        this.getModel(this.customForm.productId)
      }

      this.infoLoading = false
      this.drawerLoading = false
      this.customList.forEach(e => {
        if (e.key == 'requirementId' || e.key == 'planId' || e.key == 'projectId' || e.key == 'bugId') {
          this.$set(e, 'options', res.data.echoMap[e.key] ? [res.data.echoMap[e.key]] : [])
        }
      })
      // 如果需求没有关联项目,不允许填报工时
      this.$emit('hideTab', res.data.projectId ? 1 : 0)
    },
    // 上一条
    dataPrev() {
      if (this.currentIndex === 0) {
        this.$message.warning('已经是第一条数据啦')
        return
      }
      this.currentIndex--
      this.getFormTemplete(this.tableList[this.currentIndex].id)
      this.commentId = this.tableList[this.currentIndex].id
    },
    // 下一条
    dataNext() {
      if (this.currentIndex === this.tableList.length - 1) {
        this.$message.warning('已经是最后一条数据啦')
        return
      }
      this.currentIndex++

      this.getFormTemplete(this.tableList[this.currentIndex].id)
      this.commentId = this.tableList[this.currentIndex].id
    },
    async editStatus() {
      this.getIssueInfo()
      this.$emit('success')
      // 查询当前表格项数据
    },
    // 取消数字输入框的校验
    eventDisposalRangeChangeINT(value, prop) {
      if (value != undefined) {
        this.$refs.customForm.clearValidate(prop)
      }
    }
  }
}
