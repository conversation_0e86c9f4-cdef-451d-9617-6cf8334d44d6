import request from '@/utils/axios-api'
// import { get } from 'store'
// 引擎列表查询
export function apiBaseMessageEngine(data) {
  return request({
    url: '/api/base/base/msgEngine/page',
    method: 'post',
    data
  })
}
// 查询引擎扩展字段
export function apiBaseMsgEngineExtend(params) {
  return request({
    url: `/api/base/base/msgEngine/extendProperties?type=${params}`,
    method: 'get'
  })
}
// 消息引擎新增/编辑/删除
export function apiBaseMessageEngineHandle(data, method) {
  return request({
    url: '/api/base/base/msgEngine',
    method: method,
    data
  })
}
// 单体查询
export function apiBaseMessageEngineInfo(id, params) {
  return request({
    url: `/api/base/base/msgEngine/${id}`,
    method: 'get',
    params
  })
}
// 修改引擎状态
export function apiBaseMsgUpdateStatus(data) {
  return request({
    url: `/api/base/base/msgEngine/updateStatus`,
    method: 'put',
    data
  })
}

// 模版列表查询
export function apiBaseMsgTemplatePage(data) {
  return request({
    url: '/api/base/base/msgTemplate/page',
    method: 'post',
    data
  })
}

// 模版列表不分页
export function apiBaseMsgTemplateQuery(data) {
  return request({
    url: '/api/base/base/msgTemplate/query',
    method: 'post',
    data
  })
}

// 模版
export function apiBaseMsgTemplateHandle(data, method) {
  return request({
    url: '/api/base/base/msgTemplate',
    method: method,
    data
  })
}
// 模版单体查询
export function apiBaseMsgTemplateInfo(id) {
  return request({
    url: `/api/base/base/msgTemplate/${id}`,
    method: 'get'
  })
}

// 查询消息事件
export function apiBaseMsgEvent(data) {
  return request({
    url: `/api/base/base/msgEvent/query`,
    method: 'post',
    data
  })
}

// 接收规则分页
export function apiMsgReceivePage(data) {
  return request({
    url: `/api/base/msgReceive/page`,
    method: 'post',
    data
  })
}
// 接收规则不分页
export function apiMsgReceiveQuery(data) {
  return request({
    url: `/api/base/msgReceive/query`,
    method: 'post',
    data
  })
}

// 接收规则
export function apiMsgReceiveHandle(data, method) {
  return request({
    url: '/api/base/msgReceive',
    method: method,
    data
  })
}
// 接收规则单体查询
export function apiMsgReceiveInfo(id) {
  return request({
    url: `/api/base/msgReceive/${id}`,
    method: 'get'
  })
}

// 查询消息配置方案
export function apiMsgSchemePage(data) {
  return request({
    url: `/api/base/msgScheme/page`,
    method: 'post',
    data
  })
}
// 方案
export function apiMsgSchemeHandle(data, method) {
  return request({
    url: '/api/base/msgScheme',
    method: method,
    data
  })
}
// 方案单体查询
export function apiMsgSchemeInfo(id) {
  return request({
    url: `/api/base/msgScheme/${id}`,
    method: 'get'
  })
}
// 接收规则单体查询
export function apiMsgEventVariable(params) {
  return request({
    url: `/api/base/base/msgEvent/variable?eventIds=${params}`,
    method: 'get'
  })
}

// 查询消息发送历史
export function apiMsgHistoryPage(data) {
  return request({
    url: `/api/base/msgHistory/page`,
    method: 'post',
    data
  })
}
