import request from '@/utils/axios-api'

// 获取代码审核列表
export function getCodeReviewList(data) {
  return request({
    url: '/api/codem/codeReview/page',
    method: 'post',
    data
  })
}

// 批量审核
export function updateCodeReviewList(params) {
  return request({
    url: '/api/codem/codeReview/updateReviewStatusByCodeReviewIds/' + params.reviewStatus,
    method: 'post',
    data: params.codeReviewIds
  })
}

// 查询MergeRequestChanges的内容变更
export function getMergeRequestChange(codeReviewId) {
  return request({
    url: '/api/codem/codeReview/getMergeRequestChanges/' + codeReviewId,
    method: 'get'
  })
}

// 查询所有评论
export function getcodeVersionCommentList(data) {
  return request({
    url: '/api/codem/codeVersionComment/query',
    method: 'post',
    data
  })
}

// 新增评论
export function holdcodeVersionComment(data) {
  return request({
    url: '/api/codem/codeVersionComment',
    method: 'post',
    data
  })
}

// 删除评论
export function delcodeVersionComment(data) {
  return request({
    url: '/api/codem/codeVersionComment',
    method: 'DELETE',
    data
  })
}

// 新增行评论
export function holdcodeLineComment(data) {
  return request({
    url: '/api/codem/codeLineComment',
    method: 'post',
    data
  })
}

// 获取行评论列表
export function getcodeLineCommentList(data) {
  return request({
    url: '/api/codem/codeLineComment/query',
    method: 'post',
    data
  })
}

// 删除行评论
export function delcodeLineComment(data) {
  return request({
    url: '/api/codem/codeLineComment',
    method: 'DELETE',
    data
  })
}

// 分支对比compare
export function branchCompare(data) {
  return request({
    url: '/api/codem/codeBranch/branchCompare',
    method: 'post',
    data
  })
}

// 代码比对展开更多
export function getMoreCode(data) {
  return request({
    url: '/api/codem/codem/fileContent/queryFileContentSnippet',
    method: 'get',
    data: data
  })
}

// 代码撤版
export function codeRevert(data) {
  return request({
    url: '/api/codem/codeBranch/codeRevert',
    method: 'post',
    data
  })
}

// 获取代码撤版列表
export function getRevertCodeList(data) {
  return request({
    url: '/api/codem/codeBranch/gitCommitListByItemCode',
    method: 'post',
    data
  })
}
