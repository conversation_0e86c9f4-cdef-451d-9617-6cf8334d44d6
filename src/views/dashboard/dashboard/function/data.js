export const data = {
  activeNames: '我的',
  widgetList: [
    {
      title: '我的',
      widget: [
        {
          name: 'project-information',
          title: '个人卡片',
          desc: '显示个人角色、项目数量、个人设置按钮',
          img: 'my-mine',
          key: 'project-information'
        },
        {
          name: 'my-dynamic',
          title: '我的动态',
          desc: '展示最近我的动态',
          img: 'my-kalendar',
          key: 'my-dynamic'
        },
        {
          name: 'project-kalendar',
          title: '我的日历',
          desc: '基于日历展示我的事项情况',
          img: 'my-dynamic',
          key: 'project-kalendar'
        }
      ]
    },
    {
      title: '产品',
      widget: [
        {
          name: 'product-bug',
          title: '缺陷概览',
          desc: '展示特定项目下缺陷按状态的分布情况',
          img: 'product-bug',
          key: 'product-bug'
        },
        {
          name: 'product-require',
          title: '需求概览',
          desc: '展示特定项目下需求按状态的分布情况',
          img: 'product-require',
          key: 'product-require'
        },
        {
          name: 'product-bug-trends',
          title: '缺陷趋势',
          desc: '展示特定项目内近期缺陷的趋势',
          img: 'product-bug-trends',
          key: 'product-bug-trends'
        },
        {
          name: 'product-version',
          title: '产品版本',
          desc: '产品计划节点展示',
          img: 'product-version',
          key: 'product-version'
        }
      ]
    },
    {
      title: '项目',
      widget: [
        {
          name: 'project-card',
          title: '项目卡片',
          desc: '展示特定项目的基本信息，快捷进入项目详情页面',
          img: 'project-card',
          key: 'project-card'
        },
        {
          name: 'project-task',
          title: '任务概览',
          desc: '展示特定项目下任务按状态的分布情况',
          img: 'project-task',
          key: 'project-task'
        },
        {
          name: 'project-bug',
          title: '缺陷概览',
          desc: '展示特定项目下缺陷按状态的分布情况',
          img: 'project-bug',
          key: 'project-bug'
        },
        {
          name: 'project-require',
          title: '需求概览',
          desc: '展示特定项目下需求按状态的分布情况',
          img: 'project-require',
          key: 'project-require'
        },
        {
          name: 'project-bug-trends',
          title: '缺陷趋势',
          desc: '展示特定项目内近期缺陷的趋势',
          img: 'project-bug-trends',
          key: 'project-bug-trends'
        },
        {
          name: 'project-burn-chart',
          title: '迭代燃尽图',
          desc: '根据故事点和任务工时展示特定迭代的燃尽图',
          img: 'project-burn-chart',
          key: 'project-burn-chart'
        },

        // {
        //   name: 'product-version',
        //   title: '版本计划',
        //   img: 'product_version',
        //   key: 'product-version'
        // },
        {
          name: 'project-task-user',
          title: '任务分布',
          desc: '展示特定项目内以项目成员为维度统计的任务状态情况',
          img: 'project-task-user',
          key: 'project-task-user'
        },
        {
          name: 'project-delay-event',
          title: '延期事项',
          desc: '展示特定项目的延期事项情况',
          img: 'project-delay-event',
          key: 'project-delay-event'
        },
        {
          name: 'project-role-task',
          title: '按项目角色维度统计任务',
          desc: '显示项目角色维度统计任务视图',
          img: 'custom-histogram',
          key: 'project-role-task'
        }

      ]
    },
    {
      title: '迭代',
      widget: [
        {
          name: 'iteration-task',
          title: '任务概览',
          desc: '展示特定项目下任务按状态的分布情况',
          img: 'project-task',
          key: 'iteration-task'
        },
        {
          name: 'iteration-bug',
          title: '缺陷概览',
          desc: '展示特定项目下缺陷按状态的分布情况',
          img: 'project-bug',
          key: 'iteration-bug'
        },
        {
          name: 'iteration-require',
          title: '需求概览',
          desc: '展示特定项目下需求按状态的分布情况',
          img: 'project-require',
          key: 'iteration-require'
        },
        {
          name: 'iteration-bug-trends',
          title: '缺陷趋势',
          desc: '展示特定项目内近期缺陷的趋势',
          img: 'project-require',
          key: 'iteration-bug-trends'
        },
        {
          name: 'iteration-task-user',
          title: '任务分布',
          desc: '展示特定项目内以项目成员为维度统计的任务状态情况',
          img: 'project-task-user',
          key: 'iteration-task-user'
        },
        {
          name: 'iteration-burn-chart',
          title: '迭代燃尽图',
          desc: '根据故事点和任务工时展示特定迭代的燃尽图',
          img: 'iteration_line',
          key: 'iteration-plan'
        },
        {
          name: 'iteration-plans',
          title: '迭代计划',
          desc: '展示特定项目内近期迭代的情况',
          img: 'iteration-plans',
          key: 'iteration-plans'
        }
      ]
    },
    // {
    //   title: '流水线',
    //   widget: [
    //     {
    //       name: 'pipeline-card',
    //       title: '流水线卡片',
    //       desc: '我的流水线',
    //       img: 'pipeline-card',
    //       key: 'pipeline-card'
    //     },
    //     {
    //       name: 'pipeline-count',
    //       title: '近期流水线统计',
    //       desc: '展示近期流水线执行情况',
    //       img: 'pipeline-count',
    //       key: 'pipeline-count'
    //     }
    //   ]
    // },
    // {
    //   title: '代码',
    //   widget: [
    //     {
    //       name: 'code-cards',
    //       title: '代码库卡片',
    //       desc: '我的代码库',
    //       img: 'code-cards',
    //       key: 'code-cards'
    //     }
    //   ]
    // },
    {
      title: '资源',
      widget: [
        {
          name: 'cmdb-engie-view',
          title: '引擎概览',
          desc: '展示平台对接的引擎资源情况',
          img: 'cmdb-engie-view',
          key: 'cmdb-resource-view'
        },
        {
          name: 'cmdb-count',
          title: '统计资源',
          desc: '资源数量统计',
          img: 'cmdb-count',
          key: 'cmdb-count'
        }
      ]
    },
    // {
    //   title: '制品',
    //   widget: [
    //     {
    //       name: 'package-count',
    //       title: '制品统计',
    //       desc: '展示近期的制品情况',
    //       img: 'package-count',
    //       key: 'package-count'
    //     }
    //   ]
    // },
    {
      title: '自定义',
      widget: [
        {
          name: 'custom-pie',
          title: '饼图',
          desc: '以饼图的方式显示项目或筛选的内容',
          img: 'custom-pie',
          key: 'custom-pie'
        },
        {
          name: 'custom-trend',
          title: '趋势图',
          desc: '展示数据在维度上的趋势和变化',
          img: 'custom-trend',
          key: 'custom-trend'
        },
        {
          name: 'custom-histogram',
          title: '柱状图',
          desc: '展示不同分类间的数值对比',
          img: 'custom-histogram',
          key: 'custom-histogram'
        },
        {
          name: 'custom-list',
          title: '二维表',
          desc: '显示统计数据',
          img: 'custom-list',
          key: 'custom-list'
        }
        // {
        //   name: 'project-bug-repair',
        //   title: 'bug的产生和修复趋势图',
        //   desc: '显示项目的已创建与已解决的问题对比图',
        //   img: 'project-bug-repair',
        //   key: 'project-bug-repair'
        // }
      ]
    }
    // {
    //   title: '表格',
    //   widget: [
    //     {
    //       name: 'workflow-task',
    //       title: '我的流程',
    //       desc: '展示我的流程待办任务',
    //       img: 'workflow',
    //       key: 'workflow-task'
    //     },
    //     {
    //       name: 'project-task-table',
    //       title: '我的任务',
    //       desc: '展示我处理的项目任务',
    //       img: 'project-task-table',
    //       key: 'project-task-table'
    //     },
    //     {
    //       name: 'my-notice',
    //       title: '我的公告',
    //       desc: '展示已发布的公告',
    //       img: 'proclamation',
    //       key: 'my-notice'
    //     }
    //   ]
    // },
    // {
    //   title: '用户访问量',
    //   widget: [
    //     {
    //       name: 'user-pageview',
    //       title: '用户访问量',
    //       desc: '展示用户访问量',
    //       img: 'visit',
    //       key: 'user-pageview'
    //     }
    //   ]
    // }
  ]
}
export const templateList = [
  {
    id: 0,
    title: '自定义工作台',
    img: 'custom',
    describe: '工作台拥有强大的自定义组装配置能力，覆盖各类管理场景。'
  },
  {
    id: 1,
    title: '项目管理工作台',
    img: 'project',
    describe: '为项目经理打造的项目管理工作台，覆盖各类管理场景，帮助各类项目减少项目阻碍，提高协作效率。'
  },
  {
    id: 2,
    title: '研发工作台',
    img: 'development',
    describe: '为研发打造的个人工作台，专注于个人的工作项完成情况，包含个人日历、个人动态等。便于快速浏览待办事项。'
  },
  {
    id: 3,
    title: '配置管理工作台',
    img: 'allocation',
    describe: '为配置管理角色打造，依据配置管理规范，提供软件交付过程中的配置集中管控面板，提高产品质量、缩短产品交付时间。'
  }
]
