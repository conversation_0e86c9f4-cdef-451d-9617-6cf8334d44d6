<template>
  <div class="app-wrapper">
    <app-menu />
    <div class="main-container">
      <div v-if="currentAppMenu.length > 0" class="fixed-header">
        <top-menu />
      </div>
      <app-main />
    </div>
    <!-- <vue-fab
      v-drag
      :global-options="{ spacing: 45, delay: 0.05 }"
      size="big"
      class="fabstyle"
      icon="menu"
      auto-hide-direction="up"
      main-btn-color="#FFFFFF"
    >
      <fab-item
        :idx="0"
        title="Nexus"
        icon="iconfont el-icon-nexus"
        color="#29b473"
        @clickItem="gotoUrl()"
      >
        <i class="iconfont el-icon-nexus" />
      </fab-item>
    </vue-fab> -->
  </div>
</template>

<script>

import { AppMain, AppMenu, TopMenu } from './components'
import { cloneDeep } from 'lodash'

export default {
  name: 'Layout',
  components: {
    AppMain,
    AppMenu,
    TopMenu
  },
  data() {
    return {
    }
  },
  computed: {

    currentAppMenu() {
      const { meta, path } = this.$route

      const allMenu = this.$store.state.user.appRouterMenu

      let currentMenu = []
      allMenu.map(item => {
        const code = item.meta && `${item.meta.code}`.toLowerCase()
        if (item.path != '*') {
          if (code == meta.activeApp && !item.meta.hideMenu) {
            if (item.children && item.children.length > 0) {
              item.children.map(itm => {
                if (itm.children) {
                  delete itm.children
                }
              })
              currentMenu = item.children
              // 不显示隐藏的菜单
              // currentMenu = item.children.filter(route => !route?.meta?.hideMenu)
            }
          }
        }
      })
      // 项目管理模块处理 需要设置二级菜单展示隐藏属性
      if (meta.activeApp == 'project' || meta.activeApp == 'projectm' || meta.activeApp == 'producm' || meta.activeApp == 'productfit') {
        let newCurrentMenu = cloneDeep(currentMenu)
        // if (projectType == 'AGILE') {
        //   // project_iteration
        //   newCurrentMenu = newCurrentMenu.filter(item => item.name != 'project_milestone')
        // } else if (projectType == 'TEST') {
        //   newCurrentMenu = newCurrentMenu.filter(item => item.name != 'project_iteration' && item.name != 'project_billboard' && item.name != 'project_milestone' && item.name != 'project_risk_view' && item.name != 'project_version')
        // } else {
        //   newCurrentMenu = newCurrentMenu.filter(item => item.name != 'project_iteration' && item.name != 'project_billboard')
        // }
        newCurrentMenu.map((item, index) => {
          if (path.includes(item.path) && item.meta.hideMenu) {
            newCurrentMenu = []
          } else if (!path.includes(item.path) && item.meta.hideMenu) {
            newCurrentMenu.splice(index, 1)
          }
        })

        currentMenu = newCurrentMenu
      }
      if (currentMenu.length > 0) {
        this.$store.dispatch('user/setCurrentMenu', currentMenu)
      } else {
        this.$store.dispatch('user/setCurrentMenu', [])
      }
      return currentMenu
    }
  },
  methods: {
    gotoUrl(url) {
      const winOpen = window.open('http://**************:18082', '_blank')
      winOpen.opener = null
    }
  }
}
</script>

<style lang="scss" scoped>
// 全局
.app-wrapper {
  height: 100%;
  width: 100%;
  overflow: auto;
  background-color: var(--content-bg-color);
}
.main-container {
  min-height: 100%;
  padding: 10px 10px 10px 82px;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.1);
}
// top菜单重置
.fixed-header {
  position: relative;
  z-index: 1500;
  width: calc(100% - 104px);
}
.fabstyle {
  right: 10% !important;
  bottom: 20% !important;
  :deep(.fab) {
    color: #1966ff;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.15) !important;
    .vue-fab-material-icons {
      font-size: 25px;
    }
  }
  :deep(.fab-item-image) {
    display: flex;
    justify-content: center;
    align-items: center;
    .iconfont {
      font-size: 24px;
    }
  }
}
</style>
