<template>
  <vone-echarts-card :title="formInfo.title" :name="formInfo.name">
    <div style="min-height:200px">
      <div style="margin-top: 10px;">
        <vxe-table
          ref="noticeTable"
          class="vone-vxe-table"
          border
          height="auto"
          show-overflow="tooltip"
          :loading="tableLoading"
          :empty-render="{ name: 'empty' }"
          :data="tableData.records"
          :column-config="{ resizable: true, minWidth: 120 }"
        >
          <vxe-column title="名称" field="title" min-width="80">
            <template #default="{ row }">
              <a @click="noticeDetail(row)">{{ row.title }}</a>
            </template>
          </vxe-column>
          <vxe-column title="发布人" field="createdBy" min-width="100">
            <template #default="{ row }">
              <vone-user-avatar
                :avatar-path="row.echoMap&&row.echoMap.createdBy &&row.echoMap.createdBy.avatarPath"
                :name="row.echoMap&&row.echoMap.createdBy &&row.echoMap.createdBy.name"
              />
            </template>
          </vxe-column>
          <vxe-column title="发布日期" field="createTime" min-width="180" />
        </vxe-table>
      </div>
      <vone-pagination ref="pagination" small :layout="`total, prev, pager, next`" :show-custom-sizes="false" :pager-count="5" :total="tableData.total" style="float: right;margin-top: 10px;" @update="getTableList" />
      <addEditDialog v-if="visible" :visible.sync="visible" :title="title" :click-row="clickRow" :is-edit="false" />
    </div>
  </vone-echarts-card>
</template>
<script>
import { getNoticeList } from '@/api/vone/base/notice'
import addEditDialog from '@/views/vone/base/notice/add-edit-dialog.vue'
export default {
  components: {
    addEditDialog
  },
  props: {
    formInfo: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      tableData: {
        records: []
      },
      tableLoading: false,
      title: '公告详情',
      clickRow: {},
      visible: false
    }
  },
  mounted() {
    this.getTableList()
  },
  methods: {
    async getTableList() {
      this.tableLoading = true
      const pageObj = this.$refs.pagination.exportPages()
      const params = {
        ...pageObj,
        extra: {},
        model: {
          state: 1
        }
      }
      const res = await getNoticeList(params)
      this.tableLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.tableData = res.data
    },
    // 编辑引擎
    noticeDetail(row) {
      this.clickRow = row
      this.visible = true
    }
  }
}
</script>
