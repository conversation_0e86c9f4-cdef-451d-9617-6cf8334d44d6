<template>
  <div class="tree">
    <el-tree
      ref="tree"
      class="filter-tree"
      :data="docList"
      node-key="id"
      highlight-current
      default-expand-all
      :current-node-key="currentDoc"
      :expand-on-click-node="false"
      @node-click="nodeClick"
    >
      <span slot-scope="{ node, data }" class="custom-tree-node">
        <span class="text-over">{{ data.name }}</span>
        <span class="tree-operation">
          <i class="iconfont el-icon-tips-plus-circle" @click="() => operationTreeNode({data, type: 'add'})" />
          <i class="iconfont el-icon-application-edit" @click="() => operationTreeNode({data, type: 'edit'})" />
          <i
            class="iconfont el-icon-application-delete"
            style="padding-right: 8px;"
            @click="() => operationTreeNode({data, type: 'del'})"
          />
        </span>
      </span>
    </el-tree>
  </div>
</template>
<script>

export default {
  props: {
    docList: {
      type: Array,
      default: () => []
    },
    currentDoc: {
      type: String,
      default: null
    }
  },
  data() {
    return {
    }
  },
  computed: {

  },
  watch: {
    currentDoc: {
      handler: async function(val, oldVal) {
        if (val != '' && val != undefined) {
          this.$nextTick(() => {
            this.$refs.tree.setCurrentKey(val)
          })
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    operationTreeNode(row) {
      if (row.type == 'add') {
        this.$emit('addDoc', row.data.id)
      } else if (row.type == 'edit') {
        this.$emit('editDoc', row.data)
      } else {
        this.$emit('deleteDoc', row.data.id)
      }
    },
    nodeClick(data) {
      if (data.id == this.currentDoc) return
      this.$emit('clickNode', data)
      this.$router.push({
        name: 'wiki_document',
        params: {
          spaceId: this.$route.params.spaceId,
          docId: data.id
        }
      })
    }
  }

}
</script>

<style lang="scss" scoped>
.tree {
	.custom-tree-node{
		flex: 1;
			display: flex;
			align-items: center;
			justify-content: space-between;
			font-size: 14px;
			padding-right: 8px;
      width: 90%;
      .tree-operation {
        opacity: 0;
      }
	}
	.el-button {
		padding: 0 4px;
		min-width: 40px;
		line-height: 26px;
	}
  .tree-operation{
    i {
      color: var(--font-second-color);
    }
  }
  .custom-tree-node:hover {
    .tree-operation{
      opacity: 1;
    }
  }
  .custom-tree-node {
    .tree-operation{
    i:hover {
      color: var(--main-theme-color);
    }
  }
  }
  ::v-deep .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
    background-color: #F5F6FA;
  }
}

</style>
<style lang="scss" scoped>
  .wiki-dropdown {
		top: -3px;
    .el-dropdown-menu__item--divided {
      padding: 0 16px;
    }
  }
</style>
