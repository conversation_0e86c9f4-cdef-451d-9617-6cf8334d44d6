<template>
  <div class="fieldData">
    <el-table
      v-loading="fieldsLoading"
      :data="fieldData"
      style="width: 100%"
      class="vone-table"
    >
      <el-table-column
        prop="name"
        label="字段"
        width="180"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column prop="flowRequired" label="必填" align="center">
        <template v-slot="scope">
          <el-checkbox
            v-model="scope.row.flowRequired"
            @change="checkChange(scope.$index, scope.row, 'flowRequired')"
          />
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getRequiredFieldsOfProjectid } from '@/api/vone/alm'
export default {
  props: {
    formFields: {
      type: Array,
      default: () => [],
    },
    lineId: {
      type: String,
      default: '',
    },
    typeClassify: {
      type: String,
      default: undefined,
    },
    typeCode: {
      type: String,
      default: undefined,
    },
    activeName: {
      type: String,
      default: undefined,
    },
    type: {
      // 1项目 2平台
      type: String,
      default: '1',
    },
  },
  data() {
    return {
      fieldsLoading: false,
      fieldData: [],
    }
  },
  watch: {
    lineId: {
      deep: true,

      handler(val, old) {
        if (val && val !== old && this.type == '1') {
          this.getLineRequired(val)
        }
      },

      immediate: true,
    },
    // activeName: {
    //   handler(val) {
    //     if (val && this.lineId) {
    //       this.getLineRequired(this.lineId)
    //     }
    //   },
    //   immediate: true
    // }
  },
  mounted() {
    // 不包括的个字段
    const code = ['stateCode', 'typeCode']
    const codeFilter = this.formFields.filter((item) => {
      return code.indexOf(item.key) == -1
    })
    this.fieldData = codeFilter
  },
  methods: {
    // 全选
    checkChange(index, val, key) {
      if (index === 0) {
        // 全选
        if (val[key]) {
          this.fieldData.forEach((element) => {
            element[key] = true
          })
        } else {
          this.fieldData.forEach((element) => {
            element[key] = false
          })
        }
      } else {
        const allRequire = this.fieldData.find((v) => v.name == '全选')
        // 连线上必填字段table
        const reqireFields = this.fieldData.filter((v) => v.name !== '全选')
        allRequire['flowRequired'] = reqireFields.every((j) => j.flowRequired)
        this.$forceUpdate()
      }
    },
    async getLineRequired(val) {
      try {
        this.fieldsLoading = true
        const { data, isSuccess, msg } = await getRequiredFieldsOfProjectid({
          projectId: this.$route.params.id,
          rule: 'NOT_NULL',
          transitionId: val,
          typeClassify: this.typeClassify,
          typeCode: this.typeCode,
        })
        if (!isSuccess) {
          this.$message.warning(msg)
          return
        }

        if (data.length) {
          const checkId = data.map((r) => r.field)

          this.fieldData.forEach((element) => {
            element.flowRequired =
              element.id == 0 && checkId.length == this.fieldData.length - 1
                ? true
                : checkId.indexOf(element.key) !== -1
          })
        } else {
          this.fieldData.forEach((element) => {
            element.flowRequired = false
          })
        }
        this.fieldsLoading = false
      } catch (e) {
        this.fieldsLoading = false
      }
    },
  },
}
</script>

<style>
.fieldData {
  margin-bottom: 60px;
  width: 380px;
  overflow-y: auto;
}
</style>
