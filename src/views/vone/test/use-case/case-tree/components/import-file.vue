<template>
  <el-dialog
    title="导入用例"
    width="40%"
    v-model:value="visible"
    :before-close="onClose"
    :close-on-click-modal="false"
  >
    <el-form ref="form" :model="form" :rules="rules">
      <el-form-item label="文件类型">
        <el-radio-group v-model="form.fileType" @change="changeFileType">
          <el-radio label="xls">xls、xlsx</el-radio>
          <el-radio label="xmind"
            >xmind
            <el-tooltip content="同一树级下禁止出现同名用例" placement="top">
              <el-icon style="color: #cf3b3c"
                ><el-icon-warning-outline
              /></el-icon>
            </el-tooltip>
          </el-radio>
          <!-- <el-radio label="xml">xml</el-radio> -->
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="file">
        <el-upload
          class="upload-demo"
          action
          :limit="1"
          :auto-upload="true"
          :multiple="false"
          :http-request="httpRequest"
          :before-upload="beforeUpload"
          :on-remove="clearFiles"
        >
          <template v-slot:trigger>
            <el-button size="small" type="primary" :icon="elIconApplicationFile"
              >选取文件</el-button
            >
          </template>
          <el-button type="text" class="ml-3" @click="getDownload"
            >下载批量上传模板</el-button
          >
          <template v-slot:tip>
            <div class="el-upload__tip">只能上传xlsx、xls、xmind类型文件</div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>

    <template v-slot:footer>
      <div class="dialog-footer">
        <el-button v-show="needConflictResolve" @click="resolveConflict"
          >解决冲突</el-button
        >
        <el-button @click="onClose">取消</el-button>
        <el-button
          type="primary"
          :icon="elIconEditImport"
          :loading="upLoading"
          :disabled="!form.file || needConflictResolve"
          @click="submit"
          >上传</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script>
import {
  $on,
  $off,
  $once,
  $emit,
} from "../../../../../../utils/gogocodeTransfer";
import {
  downloadRequireImportTemplate,
  importXmindFile2Test,
  importExcelFile2Test,
} from "@/api/vone/testmanage/case";
import { catchErr, download } from "@/utils";
import {
  deleteXmindFileCacheIntestm,
  getImportRepeatCasesCountIntestm,
} from "@/api/vone/testmanage/file";

export default {
  props: {
    visible: {
      type: Boolean,
      default: true,
    },
    // 选中的树id
    id: {
      type: String,
      default: null,
    },
    libraryId: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      form: {
        fileType: "xls",
        file: null,
      },
      needConflictResolve: false,
      upLoading: false,
      hasError: false,
      dialogFormVisible: false,
      errorImportParam: { visible: false },
      rules: {
        // file: [
        //   { required: true, message: '请选择文件' },
        //   {
        //     validator: (r, file, cb) => {
        //       const isExcel =
        //         file.name.substr(file.name.lastIndexOf('.') + 1) === 'xls'
        //       const size = file.size / 1024 <= 500
        //       if (!isExcel) {
        //         cb('只能上传xls类型文件!')
        //       } else if (!size) {
        //         cb('上传文件大小不能超过500M!')
        //       } else {
        //         cb()
        //       }
        //     }
        //   }
        // ]
      },
    };
  },
  methods: {
    onClose() {
      $emit(this, "update:visible", false);
      this.$refs.form.resetFields();
    },
    httpRequest(file) {
      this.form["file"] = file.file;
    },
    async changeFileType(val) {
      this.needConflictResolve = false;
      if (val !== "xmind") return;
      const res = await getImportRepeatCasesCountIntestm(this.libraryId);
      if (res.isSuccess) {
        this.needConflictResolve = res.data.length > 0;
        this.needConflictResolve &&
          this.$message.warning("存在未处理冲突用例，需手动处理冲突后再导入");
      }
    },
    resolveConflict() {
      $emit(this, "conflict");
    },
    // 下载下载批量用户导入模板
    async getDownload() {
      try {
        if (this.form.fileType === "xmind") {
          const xmindFile = require("@/assets/testm/xmind.png");
          const down = document.createElement("a");
          down.style.display = "none";
          down.href = xmindFile;
          down.setAttribute("download", "测试用例导入模板.png");
          document.body.appendChild(down);
          down.click();
          document.body.removeChild(down);
        } else {
          download(
            `测试用例导入模板.xls`,
            await downloadRequireImportTemplate(0)
          );
        }
      } catch (e) {
        this.$message.error("模板下载失败");
      }
    },
    // 上传xmind
    async uploadXmindFile() {
      return await importXmindFile2Test(this.libraryId, this.id, {
        file: this.form.file,
      });
    },
    async uploadXlsFile() {
      return await importExcelFile2Test(this.libraryId, {
        file: this.form.file,
        treeId: this.id,
      });
    },
    async submit() {
      const { fileType, file } = this.form;
      const uploadType = file.name.slice(file.name.lastIndexOf(".") + 1);
      if (!uploadType.includes(fileType)) {
        this.$message.error("选择的文件类型与上传文件不一致，请重新选择！");
        return;
      }

      this.upLoading = true;
      const [res, err] = await catchErr(
        fileType === "xmind" ? this.uploadXmindFile() : this.uploadXlsFile()
      );
      this.upLoading = false;
      if (err) return;
      if (res.isSuccess) {
        $emit(this, "success", fileType);
        this.onClose();
        this.$message.success(res.msg);
      } else {
        if (res.code === 1) {
          this.$confirm(res.data, "提示", {
            customClass: "conflictTip",
            confirmButtonText: "是",
            cancelButtonText: "否",
            type: "info",
          })
            .then(() => {
              $emit(this, "conflict");
            })
            .catch(async () => {
              const res = await deleteXmindFileCacheIntestm(this.libraryId);
              if (!res.isSuccess) {
                this.$message.error(res.msg);
              }
            });
        } else if (res.code === 2) {
          this.$confirm("存在冲突用例，是否手动处理冲突后导入？", "提示", {
            customClass: "conflictTip",
            confirmButtonText: "是",
            cancelButtonText: "否",
            type: "info",
          })
            .then(() => {
              $emit(this, "conflict");
            })
            .catch(async () => {
              const res = await deleteXmindFileCacheIntestm(this.libraryId);
              if (!res.isSuccess) {
                this.$message.error(res.msg);
              }
            });
        } else {
          this.$message.warning(res.msg || "上传失败");
        }

        if (res.extra) {
          $emit(this, "hasError", res);
          this.$message.warning(res.msg);
        }
      }
    },
    clearFiles() {
      this.form.file = null;
    },
    // 文件验证规则
    beforeUpload(file) {
      const allowTypes = ["xlsx", "xls", "xmind", "xml"];
      const fileType = file.name.substr(file.name.lastIndexOf(".") + 1);
      const isCanUpload = allowTypes.includes(fileType);

      if (!isCanUpload) {
        this.$message.error("只支持xls,xmind,xml类型文件!");
      }
      // 上传文件和表单类型一致
      this.form.fileType = fileType === "xlsx" ? "xls" : fileType;
      this.changeFileType(fileType);
      return isCanUpload;
    },
  },
  emits: ["update:visible", "success", "hasError", "conflict"],
};
</script>

<style lang="scss">
.conflictTip {
  .el-message-box__content,
  .el-message-box__btns {
    padding: 16px 20px;
  }
  .el-message-box__message {
    font-size: 14px;
    font-weight: 400;
  }
  .el-icon-info {
    color: var(--main-theme-color, #3e7bfa);
  }
}
</style>
