<template>
  <div class="custom-search-form">
    <el-row v-for="(item, index) in fieldFormList" :key="index" :gutter="20">
      <el-col :span="8">
        <el-select
          v-model="item.key"
          placeholder="请选择"
          @change="keyChange($event, item, index)"
        >
          <el-option
            v-for="e in fieldOptions"
            :key="e.key"
            :label="e.name"
            :value="e.key"
          />
        </el-select>
      </el-col>
      <el-col :span="14">
        <el-form-item label="" :prop="item.value">
          <vone-remote-user
            v-if="item.type.code == 'USER'"
            v-model:value="item.value"
            :no-name="false"
            multiple
          />
          <input-number-range
            v-else-if="item.type.code == 'INT'"
            v-model:value="item.value"
            :precision="1"
          />
          <el-input
            v-else-if="item.type.code == 'INPUT' || item.type.code == 'EDITOR'"
            v-model="item.value"
            placeholder="请输入"
            style="width: 100%"
          />
          <!-- 功能模块 -->
          <vone-tree-select
            v-else-if="
              item.type.code == 'SELECT' &&
              item.key == 'productModuleFunctionId'
            "
            v-model:value="item.value"
            search-nested
            :tree-data="maps[item.key]"
            :placeholder="item.placeholder"
            :multiple="item.multiple"
            :disabled="item.disabled"
          />

          <vone-icon-select
            v-else-if="
              item.type.code == 'SELECT' ||
              item.key == 'delay' ||
              item.type.code == 'LINKED'
            "
            v-model:value="item.value"
            filterable
            clearable
            style="width: 100%"
            :multiple="item.key !== 'delay' && item.key != 'productId'"
            :no-permission="item.disabled"
            @change="changeValue(item)"
          >
            <el-option
              v-for="e in maps[item.key] || item.options"
              :key="e.key"
              :label="e.name"
              :value="
                item.key == 'priorityCode' || item.key == 'sourceCode'
                  ? e.code
                  : e.id
              "
            >
              <i
                v-if="item.key == 'priorityCode'"
                :class="`iconfont ${e.icon}`"
                :style="{
                  color: e.color,
                  fontSize: '16px',
                  paddingRight: '6px',
                }"
              />
              {{ e.name }}
            </el-option>
          </vone-icon-select>

          <div v-else-if="item.type.code == 'QUOTE'">
            <vone-remote-user
              v-if="item.quoteType == 'user'"
              v-model:value="item.value"
              :no-name="false"
              multiple
            />
            <el-input
              v-else-if="item.quoteType == 'text'"
              v-model="item.value"
              placeholder="请输入"
              style="width: 100%"
            />
            <el-date-picker
              v-else-if="item.quoteType == 'date'"
              v-model="item.value"
              style="width: 100%"
              value-format="yyyy-MM-dd"
              type="date"
              placeholder="选择日期"
            />
          </div>
          <el-date-picker
            :default-time="
              ['00:00:00', '23:59:59'].map((d) => dayjs(d, 'hh:mm:ss').toDate())
            "
            v-else-if="item.type.code == 'DATE'"
            v-model="item.value"
            style="width: 100%"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></el-date-picker>
        </el-form-item>
      </el-col>
      <el-col :span="2" style="line-height: 32px">
        <el-icon class="iconfont"><el-icon-application-delete /></el-icon>
      </el-col>
    </el-row>
    <el-dropdown
      style="cursor: pointer"
      placement="bottom-start"
      trigger="click"
      @command="dropdownCommand"
    >
      <span class="el-dropdown-link">
        <el-icon><el-icon-plus /></el-icon> 添加条件
      </span>
      <template v-slot:dropdown>
        <el-dropdown-menu class="custom-search-dropdown">
          <el-input
            v-model="searchField"
            style="margin-bottom: 12px"
            placeholder="搜索关键字"
            @input="searchFieldChange"
          />
          <div class="dropdown-box">
            <span class="dropdown-item-grouping">推荐</span>
            <div v-for="e in fieldList" :key="e.id">
              <el-dropdown-item v-if="e.isBasic || e.isBuilt" :command="e">{{
                e.name
              }}</el-dropdown-item>
            </div>

            <span class="dropdown-item-grouping"
              >自定义
              <el-tooltip
                class="item"
                effect="dark"
                content="使用工作项类型所选择表单的自定义字段"
                placement="top-start"
              >
                <el-icon><el-icon-warning-outline /></el-icon>
              </el-tooltip>
            </span>
            <div v-for="e in fieldList" :key="e.key">
              <el-dropdown-item v-if="!e.isBasic && !e.isBuilt" :command="e">{{
                e.name
              }}</el-dropdown-item>
            </div>
          </div>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script>
import {
  ApplicationDelete as ElIconApplicationDelete,
  Plus as ElIconPlus,
  WarningOutline as ElIconWarningOutline,
} from "@element-plus/icons-vue";
import * as dayjs from "dayjs";
import { $on, $off, $once, $emit } from "../../utils/gogocodeTransfer";
import _ from "lodash";
import { queryFieldList } from "@/api/common";
import { apiAlmPriorityNoPage } from "@/api/vone/alm/index";
import { apiAlmProjectPlanNoPage } from "@/api/vone/project/iteration";
import {
  apiAlmSourceNoPage,
  requirementListByCondition,
} from "@/api/vone/project/issue";
import { productListByCondition } from "@/api/vone/project";
import { getProjectPlans } from "@/api/vone/testmanage/case";
import { ideaListByCondition } from "@/api/vone/reqmcenter/idea";
import { bugListByCondition } from "@/api/vone/project/defect";
import InputNumberRange from "./inputNumberRange.vue";
import { getSourceFormData, getSourceById } from "@/api/vone/base/source";
import { getProductVersionList, getModule } from "@/api/vone/product/index";
import { gainTreeList } from "@/utils";

export default {
  components: {
    InputNumberRange,
    ElIconApplicationDelete,
    ElIconPlus,
    ElIconWarningOutline,
  },
  data() {
    return {
      searchField: "",
      fieldOptions: [],
      fieldFormList: this.defaultFieldFormList,
      prioritList: [],
      fieldList: [],
      maps: {
        priorityCode: [],
        planId: [],
        sourceCode: [],
        delay: [
          { name: "是", id: true },
          { name: "否", id: false },
        ],
        productId: [],
        productVersionId: [],
        productModuleFunctionId: [],
        productRepairVersionId: [],
      },
      sheetOptions: [],
      dayjs,
    };
  },
  name: "Field",
  props: {
    width: {
      type: Number,
      default: 604,
    },
    typeCode: {
      type: String,
      default: "",
    },
    typeCodes: {
      type: Array,
      default: () => [],
    },
    defaultFieldFormList: {
      type: Array,
      default: () => [],
    },
    projectId: {
      type: String,
      default: "",
    },
  },
  watch: {
    typeCodes: {
      handler(val, oldval) {
        if (val && val.length > 0 && val !== oldval) {
          this.getQueryFieldList();
        }
      },
      deep: true,
      immediate: false,
    },
    defaultFieldFormList: {
      handler(e) {
        this.fieldFormList = this.defaultFieldFormList;
        this.fieldFormList.forEach((element) => {
          if (element.type.code == "LINKED") {
            this.getTableConfig(element.config.relationShipsheet, element.key);
          }
        });
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {},
  methods: {
    getOptions() {
      this.getQueryFieldList();
      this.getplanId();
      this.getpriorityCode();
      this.getsourceCode();
      this.productList();
      this.getProjectTestPlan();
      this.getIdeaList();
      this.getBugList();
      this.getRequirementList();

      console.log(this.maps["productId"], "productId");

      this.getPrdcVersion();
      this.getPrdcRepairVersion();
      this.getPrdcFunction();
    },
    keyChange(key, item, index) {
      // console.log(key, item, index, 'key, item, index')
      // console.log(this.fieldFormList, 'fieldFormList')

      // 处理产品相关的三个字段
      var arry = [
        "productVersionId",
        "productModuleFunctionId",
        "productRepairVersionId",
      ];
      const isProductId = this.fieldFormList.filter(
        (r) => r.key == "productId"
      ).length;
      console.log(isProductId, "isProductId");
      if (arry.indexOf(key) > -1 && isProductId == 0) {
        this.$message.warning("请先添加【 关联产品 】字段");
        item["disabled"] = true;
      } else if (isProductId == 1) {
        item["disabled"] = false;
      }
      // 处理产品相关的三个字段

      const e = this.fieldOptions.find((e) => e.key == key);
      item.key = e.key;
      item.name = e.name;
      item.value = null;
      item.type = e.type;
      item.config = JSON.parse(e.config);
      if (item.type.code == "LINKED") {
        this.getTableConfig(item.config.relationShipsheet, item.key);
      } else if (item.type.code == "QUOTE") {
        this.getQuoteType(item, (type) => {
          item.quoteType = type;
          this.fieldFormList[index] = item;
        });
      }
      $emit(this, "update:defaultFieldFormList", this.fieldFormList);
    },
    deleteRow(index, item) {
      this.fieldFormList.splice(index, 1);
      $emit(this, "update:defaultFieldFormList", this.fieldFormList);
    },
    dropdownCommand(item) {
      console.log(item, "item");
      // if (item.key == 'productModuleFunctionId') { this.$set(item, 'value', []) }
      // 处理产品相关的三个字段
      var arry = [
        "productVersionId",
        "productModuleFunctionId",
        "productRepairVersionId",
      ];
      const isProductId = this.fieldFormList.filter(
        (r) => r.key == "productId"
      ).length;
      console.log(isProductId, "isProductId");
      if (arry.indexOf(item.key) > -1 && isProductId == 0) {
        this.$message.warning("请先添加【 关联产品 】字段");
        item["disabled"] = true;
      } else if (isProductId == 1) {
        item["disabled"] = false;
      }
      // 处理产品相关的三个字段

      const e = JSON.parse(JSON.stringify(item));
      e.keyid = Date.now();
      e.config = JSON.parse(e.config);
      if (e.type.code == "LINKED") {
        this.getTableConfig(e.config.relationShipsheet, e.key);
      } else if (e.type.code == "QUOTE") {
        this.getQuoteType(e, (type) => {
          e.quoteType = type;
          this.fieldFormList[this.fieldFormList.length - 1] = e;
        });
      }
      const config = e.config;
      const obj = {
        ...config,
        key: e.key,
        name: e.name,
        value: item.key == "productModuleFunctionId" ? null : "",
        config: config,
        type: e.type,
        disabled: e.disabled,
      };
      this.fieldFormList.push(obj);
      $emit(this, "update:defaultFieldFormList", this.fieldFormList);
    },
    async getQueryFieldList() {
      const fixedField = [
        "name",
        "handleBy",
        "stateCode",
        "tagId",
        "createTime",
        "files",
        "projectId",
        "typeCode",
      ];
      const form = {
        projectId: this.projectId,
        typeClassify: this.typeCode,
        typeCodes: this.typeCodes,
      };
      const res = await queryFieldList(form);
      if (!res.isSuccess) {
        return;
      }
      res.data = res.data.filter((e) => {
        return !fixedField.includes(e.key) && e.isSearch;
      });
      this.fieldOptions = res.data;
      this.fieldList = res.data;
    },
    searchFieldChange: _.debounce(function (e) {
      this.fieldList = this.fieldOptions.filter((item) => {
        return (
          item.name.toLowerCase().indexOf(e.toLowerCase()) != -1 &&
          item.isSearch
        );
      });
    }, 500),
    // 下拉数据集
    async getQuoteType(e, callback) {
      this.getTableConfigs(e.config.relationShipsheet, e, (type) => {
        callback(type);
      });
    },
    async getTableConfigs(id, e, callback) {
      const res = await getSourceById(id);
      if (res.isSuccess) {
        const list = res.data.fields.map((v) => {
          const obj = JSON.parse(v.config);
          return {
            ...v,
            ...obj,
          };
        });
        const type = list.find(
          (item) => item.id == e.config.relationField && !item.primary
        ).type;
        callback(type);
      }
    },
    async getTableConfig(id, key) {
      const res = await getSourceById(id);
      if (res.isSuccess) {
        this.colConfig = res.data.fields.map((v) => {
          v.prop = v.id;
          const obj = JSON.parse(v.config);
          return {
            ...v,
            ...obj,
          };
        });
        const fixedKey = this.colConfig.find((e) => e.primary)?.prop;
        this.getSourceTableList(id, key, fixedKey);
      }
    },
    // 关联字段
    async getSourceTableList(id, key, fixedKey) {
      this.tableLoading = true;
      const res = await getSourceFormData(id);
      if (res.isSuccess) {
        const list = res.data.map((e) => {
          return {
            id: e.id,
            key: e.id,
            name: e.columns[fixedKey],
          };
        });
        this.maps[key] = list;
      }
    },
    // 迭代计划
    async getplanId() {
      if (this.maps["planId"].length > 0) return;
      const res = await apiAlmProjectPlanNoPage({
        projectId: this.$route.params.id || "0",
      });
      if (!res.isSuccess) {
        return;
      }
      this.maps["planId"] = res.data;
    },
    // 查优先级
    async getpriorityCode() {
      if (this.maps["priorityCode"].length > 0) return;
      const res = await apiAlmPriorityNoPage();
      if (!res.isSuccess) {
        return;
      }
      this.maps["priorityCode"] = res.data;
    },
    // 归属产品
    async productList() {
      if (this.maps["productId"]?.length > 0) return;
      const res = await productListByCondition();

      if (!res.isSuccess) {
        return;
      }

      this.maps["productId"] = res.data;
    },
    // 查项目下需求
    async getRequirementList() {
      if (this.maps["requirementId"]?.length > 0) return;
      const res = await requirementListByCondition({
        projectId: this.$route.params.id || "0",
      });
      if (!res.isSuccess) {
        return;
      }
      this.maps["requirementId"] = res.data;
    },
    // 测试计划
    async getProjectTestPlan() {
      if (this.maps["testPlanId"]?.length > 0) return;
      const res = await getProjectPlans({
        projectId: this.$route.params.id || "0",
      });
      if (!res.isSuccess) {
        return;
      }
      this.maps["testPlanId"] = res.data;
    },
    // 关联用户需求
    async getIdeaList() {
      if (this.maps["ideaId"]?.length > 0) return;
      const res = await ideaListByCondition();
      if (!res.isSuccess) {
        return;
      }
      this.maps.ideaId = res.data;
    },
    // 查项目下缺陷
    async getBugList() {
      if (this.maps["bugId"]?.length > 0) return;
      const res = await bugListByCondition(
        this.$route.params.id
          ? {
              projectId: this.$route.params.id,
            }
          : {}
      );
      if (!res.isSuccess) {
        return;
      }

      this.maps.bugId = res.data;
    },
    // 查询来源
    async getsourceCode() {
      if (this.maps["sourceCode"].length > 0) return;
      const res = await apiAlmSourceNoPage({
        typeClassify: this.typeCode,
      });
      if (!res.isSuccess) {
        return;
      }
      this.maps.sourceCode = res.data;
    },
    // 查询产品版本
    async getPrdcVersion(val) {
      // if (!this.maps['productId'].length) return
      const res = await getProductVersionList({
        productId:
          val || this.fieldFormList.find((r) => r.key == "productId")?.value,
      });
      if (!res.isSuccess) {
        return;
      }
      this.maps["productVersionId"] = res.data;
    },
    // 查询产品修复版本
    async getPrdcRepairVersion(val) {
      // if (!this.maps['productId'].length) {
      //   return this.$set(this.maps, 'productRepairVersionId', [])
      // }
      const res = await getProductVersionList({
        productId:
          val || this.fieldFormList.find((r) => r.key == "productId")?.value,
      });
      if (!res.isSuccess) {
        return;
      }
      this.maps["productRepairVersionId"] = res.data;
    },
    // 查询产品功能模块
    async getPrdcFunction(val) {
      // if (!this.maps['productId'].length) return

      const res = await getModule({
        productId:
          val || this.fieldFormList.find((r) => r.key == "productId")?.value,
      });
      if (!res.isSuccess) {
        return;
      }
      this.maps["productModuleFunctionId"] = gainTreeList(res.data);
    },
    changeValue(item) {
      // console.log(item, 'item')

      if (item.key == "productId") {
        this.fieldFormList.forEach((element) => {
          var arry = [
            "productVersionId",
            "productModuleFunctionId",
            "productRepairVersionId",
          ];

          if (item.value == "" && arry.indexOf(element.key) > -1) {
            element["value"] = null;
            element["disabled"] = true;
          } else {
            element["disabled"] = false;
          }
        });

        this.getPrdcRepairVersion(item.value);
        this.getPrdcVersion(item.value);
        this.getPrdcFunction(item.value);
      }
    },
  },
  emits: ["update:defaultFieldFormList"],
};
</script>

<style lang="scss" scoped>
.custom-search-dropdown {
  padding: 16px;
  .dropdown-box {
    max-height: 200px;
    overflow-y: auto;
  }
  .el-dropdown-menu__item {
    padding: 0;
  }
  .dropdown-item-grouping {
    display: inline-block;
    line-height: 32px;
    color: #838a99;
    // margin-bottom: 12px;
  }
}
.custom-search-form {
  padding: 16px 0;
  border-top: 1px solid #f2f3f5;
  .el-dropdown {
    color: #3e7bfa;
  }
}
</style>
