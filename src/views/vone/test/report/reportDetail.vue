<template>
  <div v-if="visible" class="fullBox">
    <div id="pdfDom">
      <div class="reportTop">
        <div>
          <div class="headerTitle">
            <span>测试报告</span>
            <el-tooltip v-if="showFlag" content="下载" placement="bottom">
              <el-icon class="iconfont"><el-icon-edit-export /></el-icon>
            </el-tooltip>
          </div>
          <div class="mainTitle">{{ reportData.testReportName }}</div>
        </div>
        <el-icon class="iconfont close"><el-icon-tips-close /></el-icon>
      </div>
      <div class="subTitle">
        <el-col :span="12" class="text-over"
          ><span>关联测试计划：</span>
          <span
            v-for="(item, index) in reportData.echoMap.planName"
            :key="index"
          >
            {{ item ? item : "" }}
          </span></el-col
        >
        <el-col :span="12"
          ><span>生成时间：</span>{{ reportData.createTime }}</el-col
        >
      </div>
      <div class="tableBox">
        <div class="boxTitle">
          <span class="title">测试结论</span>
        </div>
        <el-row class="contentbox">
          <div class="item">
            <span>{{ reportData.sum }}</span>
            <p>总用例数</p>
          </div>
          <el-divider direction="vertical" class="divider" />
          <div class="item">
            <span>{{ reportData.systemNum }}</span>
            <p>通过数</p>
          </div>
          <el-divider direction="vertical" class="divider" />
          <div class="item">
            <span>{{ reportData.smokeNum }}</span>
            <p>失败数</p>
          </div>
          <el-divider direction="vertical" class="divider" />
          <div class="item">
            <span>{{ reportData.blockingNum }}</span>
            <p>阻塞数</p>
          </div>
          <el-divider direction="vertical" class="divider" />
          <div class="item">
            <span>{{ reportData.skipNum }}</span>
            <p>跳过数</p>
          </div>
          <el-divider direction="vertical" class="divider" />
          <div class="item">
            <span>{{ reportData.undoNum }}</span>
            <p>未测数</p>
          </div>
          <el-divider direction="vertical" class="divider" />
          <div class="item">
            <span>{{ reportData.executedRate }}%</span>
            <p>
              <el-tooltip
                class="item"
                effect="dark"
                :content="'执行率不包括未测用例数'"
                placement="left-start"
              >
                <el-icon class="iconfont"
                  ><el-icon-tips-exclamation-circle
                /></el-icon> </el-tooltip
              >执行率
            </p>
          </div>
        </el-row>
      </div>
      <div class="tableBox">
        <div class="boxTitle"><span class="title">用例执行结果</span></div>
        <el-row type="flex" align="middle">
          <el-col :span="10">
            <vone-echarts
              :options="planResultOptions"
              style="min-height: 242px"
            />
          </el-col>
          <el-col :span="8">
            <el-table class="vone-table" :data="tableDataList">
              <el-table-column prop="name" label="结果" />
              <el-table-column prop="num" label="数量" />
              <el-table-column prop="rate" label="占比(%)" />
            </el-table>
          </el-col>
          <el-col :span="6" class="reportList">
            <div class="executeBox">
              <div class="item">
                <span>{{ reportData.executedRate }}%</span>
                <p>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="'执行率不包括未测用例数'"
                    placement="left-start"
                  >
                    <el-icon class="iconfont"
                      ><el-icon-tips-exclamation-circle
                    /></el-icon> </el-tooltip
                  >执行率
                </p>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <div v-if="defectData.length > 0" class="tableBox">
        <div class="boxTitle"><span class="title">缺陷分布</span></div>
        <el-row type="flex" align="middle">
          <el-col :span="10">
            <vone-echarts :options="defectOption" style="min-height: 242px" />
          </el-col>
          <el-col :span="8">
            <el-table class="vone-table" :data="defectData">
              <el-table-column prop="name" label="严重程度" />
              <el-table-column prop="num" label="数量" />
              <el-table-column prop="rate" label="占比(%)" />
            </el-table>
          </el-col>
        </el-row>
      </div>
      <div v-if="priorityData.length > 0" class="tableBox">
        <div class="boxTitle">
          <span class="title">优先级维度测试报告</span>
        </div>
        <el-row type="flex" align="middle">
          <el-col :span="10">
            <vone-echarts
              :options="priorityOptions"
              style="min-height: 242px"
            />
          </el-col>
          <el-col :span="10">
            <el-table class="vone-table" :data="priorityData">
              <el-table-column prop="name" label="优先级" />
              <el-table-column prop="noStart" label="未测数" />
              <el-table-column prop="system" label="通过数" />
              <el-table-column prop="smoke" label="失败数" />
              <el-table-column prop="skip" label="跳过数" />
              <el-table-column prop="blocking" label="阻塞数" />
              <el-table-column prop="rate" label="占比(%)" />
            </el-table>
          </el-col>
        </el-row>
      </div>
      <div v-if="tabData.length > 0" class="tableBox">
        <div class="boxTitle"><span class="title">标签维度测试报告</span></div>
        <el-row type="flex" align="middle">
          <el-col :span="10">
            <vone-echarts :options="tabOption" style="min-height: 242px" />
          </el-col>
          <el-col :span="10">
            <el-table class="vone-table" :data="tabData">
              <el-table-column prop="name" label="标签名称" />
              <el-table-column prop="noStart" label="未测数" />
              <el-table-column prop="system" label="通过数" />
              <el-table-column prop="smoke" label="失败数" />
              <el-table-column prop="skip" label="跳过数" />
              <el-table-column prop="blocking" label="阻塞数" />
              <!-- <el-table-column prop="rate" label="占比(%)" /> -->
            </el-table>
          </el-col>
        </el-row>
      </div>
      <div v-if="tabData.length > 0" class="tableBox">
        <div class="boxTitle">
          <span class="title">执行人维度测试报告</span>
        </div>
        <el-row type="flex" align="middle">
          <el-col :span="10">
            <vone-echarts :options="pelOption" style="min-height: 242px" />
          </el-col>
          <el-col :span="11">
            <el-table class="vone-table" :data="pelData">
              <el-table-column prop="name" label="名称" />
              <el-table-column prop="noStart" label="未测数" />
              <el-table-column prop="system" label="通过数" />
              <el-table-column prop="smoke" label="失败数" />
              <el-table-column prop="skip" label="跳过数" />
              <el-table-column prop="blocking" label="阻塞数" />
              <el-table-column prop="sum" label="总计" />
              <el-table-column prop="rate" label="占比(%)" />
            </el-table>
          </el-col>
        </el-row>
      </div>
      <div v-if="personalData && personalData.length > 0" class="tableBox">
        <div class="boxTitle"><span class="title">成员用例执行统计</span></div>
        <el-row type="flex" align="middle">
          <el-col :span="10">
            <vone-echarts
              :options="personalOption"
              :style="{ height: '300px' }"
            />
          </el-col>
        </el-row>
        <el-col :span="8">
          <el-table ref="table" class="vone-table" :data="personalData">
            <el-table-column prop="userName" label="执行人" />
            <el-table-column prop="successNum" label="通过" />
            <el-table-column prop="failureNum" label="失败" />
            <el-table-column prop="blockNum" label="阻塞" />
            <el-table-column prop="skipNum" label="跳过" />
            <el-table-column prop="sum" label="总计" />
          </el-table>
        </el-col>
      </div>
    </div>
  </div>
</template>

<script>
import {
  EditExport as ElIconEditExport,
  TipsClose as ElIconTipsClose,
  TipsExclamationCircle as ElIconTipsExclamationCircle,
} from "@element-plus/icons-vue";
import { $on, $off, $once, $emit } from "../../../../utils/gogocodeTransfer";
import htmlToPdf from "@/utils/html-to-pdf";

import { testReportSummary } from "@/api/vone/testreport/index";
export default {
  components: {
    ElIconEditExport,
    ElIconTipsClose,
    ElIconTipsExclamationCircle,
  },
  props: {
    visible: {
      type: Boolean,
      default: true,
    },
    // 报告详情数据
    reportData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      color: ["#FA6B57", "#FC9772", "#3E7BFA", "#3CB540", "#4BCCBB"],
      pelName: [],
      tabData: [],
      tabOption: {},
      name: [],
      priorityData: [],
      loading: false,
      tableData: [],
      tableDataList: [], // 详情列表
      planResultOptions: {}, // 用例执行结果饼图
      defectData: [], // 缺陷分布数据
      defectMap: {
        HIGHEST: 0,
        HIGH: 0,
        MEDIUM: 0,
        LOW: 0,
        LOWEST: 0,
      },
      view: {},
      defectOption: {}, // 缺陷分布饼图
      personalData: [], // 成员用例数据
      personalOption: {}, // 成员用例执行统计
      showFlag: false,
      darkTheme: false,
    };
  },
  watch: {
    visible(v) {
      if (!v) return;
      this.showFlag = true;
      this.darkTheme = document.body.className.indexOf("dark") > -1;
      this.getPlanReportAll();
      // this.getPlanReportDefect()

      this.gettestReportSummary();
    },
  },
  methods: {
    async gettestReportSummary() {
      const res = await testReportSummary({ reportId: this.reportData.id });
      this.view = res.data;
      this.getInitTableData();
      this.getInittabData();
      this.getInitproirityData();
      this.getInitPelData();
    },
    // 下载pdf报告
    createReport() {
      if (!this.$permission("testm_report_download")) return;
      this.showFlag = false;
      setTimeout(() => {
        htmlToPdf("测试报告-" + this.reportData.testReportName, "#pdfDom");
        this.onClose();
      }, 0);
    },
    // 获取计划详情表格信息
    async getPlanReportAll() {
      this.tableData = [this.reportData];
      const { undoNum, systemNum, smokeNum, skipNum, blockingNum } =
        this.reportData;
      // 总数
      const sum =
        Number(undoNum) +
          Number(systemNum) +
          Number(smokeNum) +
          Number(skipNum) +
          Number(blockingNum) || 0;
      this.reportData.sum = sum;
      // 执行率
      this.reportData.executedRate =
        sum > 0 ? (((sum - undoNum) / sum) * 100).toFixed(2) : 0;
      // 设置表格显示数据
      this.tableDataList = [
        {
          name: "未测数",
          num: undoNum,
          rate: sum > 0 ? ((undoNum / sum) * 100).toFixed(2) : 0,
        },
        {
          name: "通过数",
          num: systemNum,
          rate: sum > 0 ? ((systemNum / sum) * 100).toFixed(2) : 0,
        },
        {
          name: "跳过数",
          num: skipNum,
          rate: sum > 0 ? ((skipNum / sum) * 100).toFixed(2) : 0,
        },
        {
          name: "失败数",
          num: smokeNum,
          rate: sum > 0 ? ((smokeNum / sum) * 100).toFixed(2) : 0,
        },
        {
          name: "阻塞数",
          num: blockingNum,
          rate: sum > 0 ? ((blockingNum / sum) * 100).toFixed(2) : 0,
        },
      ];
      // 设置饼图
      this.getPlanReportAllEchart(this.reportData);
    },
    // 获取计划详情图表信息
    async getPlanReportAllEchart(data) {
      this.planResultOptions = {
        color: ["#ADB0B8", "#3CB540", "#BD7FFA", "#FA6B57", "#FFBF47"],
        backgroundColor: this.darkTheme ? "#1b1d23" : "#fff",
        tooltip: {
          trigger: "item",
          axisPointer: {
            type: "shadow",
          },
          backgroundColor: this.darkTheme ? "#1b1d23" : "#fff",
          borderColor: "none",
          extraCssText: "box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);", // 附加阴影样式
          textStyle: {
            color: this.darkTheme ? "#e6e9f0" : "#53565C",
          },
        },
        legend: {
          orient: "vertical",
          right: 20,
          bottom: 20,
          data: ["未测", "通过", "跳过", "失败", "阻塞"],
          icon: "circle",
          itemWidth: 8,
          itemHeight: 8,
          textStyle: {
            // 图例文字的样式
            color: this.darkTheme ? "#e6e9f0" : "#8A8F99",
          },
        },
        title: {
          text: data.sum,
          left: "center",
          top: "50%",
          textStyle: {
            color: this.darkTheme ? "#fff" : "#000",
            fontSize: 28,
            align: "center",
          },
        },
        graphic: {
          type: "text",
          left: "center",
          top: "44%",
          style: {
            text: "总用例数",
            textAlign: "center",
            fill: this.darkTheme ? "#e6e9f0" : "#333",
            fontSize: 14,
          },
        },
        series: [
          {
            name: "用例执行结果",
            type: "pie",
            radius: ["50%", "70%"],
            avoidLabelOverlap: false,
            label: {
              show: false,
            },
            data: [
              { value: data.undoNum, name: "未测" },
              { value: data.systemNum, name: "通过" },
              { value: data.skipNum, name: "跳过" },
              { value: data.smokeNum, name: "失败" },
              { value: data.blockingNum, name: "阻塞" },
            ],
            itemStyle: {
              normal: {
                borderWidth: 2,
                borderColor: this.darkTheme ? "#1b1d23" : "#fff",
              },
              emphasis: {
                borderWidth: 0,
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      };
    },
    // 查询用例缺陷tu
    async getInitTableData() {
      this.defectData =
        this.view?.testReportBugComponents.map((item) => {
          return {
            name:
              item.priorityCode === "HIGHEST"
                ? "最高"
                : item.priorityCode === "HIGH"
                ? "较高"
                : item.priorityCode === "MEDIUM"
                ? "普通"
                : item.priorityCode === "LOW"
                ? "较低"
                : "最低",
            num: item.num,
            rate: item.rate,
            value: item.rate,
          };
        }) ?? [];
      this.getPlanReportDefect();
    },
    // 查询priorityData
    getInitproirityData() {
      this.priorityData =
        this.view?.testReportPriorityComponents.map((item) => {
          return {
            name: item.priority,
            blocking: item.blocking,
            noStart: item.noStart,
            rate: item.rate,
            skip: item.skip,
            value: item.rate,
            smoke: item.smoke,
            system: item.system,
            itemStyle: {
              color:
                item.priority == "最高"
                  ? "#FA6B57"
                  : item.priority == "较高"
                  ? "#FC9772"
                  : item.priority == "普通"
                  ? "#3E7BFA"
                  : item.priority == "较低"
                  ? "#3CB540"
                  : "#4BCCBB", // 图例文字颜色
            },
          };
        }) ?? [];
      this.getPlanReportProirity();
    },
    // 查询tab
    async getInittabData() {
      this.tabData =
        this.view?.testReportTabComponents.map((item) => {
          this.name.push(item.tabName);
          return {
            name: item.tabName,
            blocking: item.blocking,
            noStart: item.noStart,
            rate: item.rate,
            skip: item.skip,
            value: item.rate,
            smoke: item.smoke,
            system: item.system,
          };
        }) ?? [];
      this.getPlanReportTab();
    },
    // 查询pel
    async getInitPelData() {
      this.pelData =
        this.view?.testReportExecComponents.map((item) => {
          this.pelName.push(item.name);
          return {
            name: item.name,
            blocking: item.blocking,
            noStart: item.noStart,
            rate: item.rate,
            skip: item.skip,
            value: item.rate,
            smoke: item.smoke,
            system: item.system,
            sum: item.sum,
          };
        }) ?? [];
      this.getPlanReportPel();
    },
    // 缺陷严重程度分布
    async getPlanReportDefect() {
      this.defectOption = {
        color: this.color,
        backgroundColor: this.darkTheme ? "#1b1d23" : "#fff",
        tooltip: {
          trigger: "item",
          axisPointer: {
            type: "shadow",
          },
          backgroundColor: this.darkTheme ? "#1b1d23" : "#fff",
          borderColor: "none",
          extraCssText: "box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);", // 附加阴影样式
          textStyle: {
            color: this.darkTheme ? "#e6e9f0" : "#53565C",
          },
          formatter(params) {
            return `<span style="display: inline-block;margin-right: 4px;background-color:${params.color};width:12px;height:12px;border-radius:50%;"></span>${params.name}：${params.value}%`;
          },
        },
        legend: {
          orient: "vertical",
          right: 20,
          bottom: 20,
          data: ["最高", "较高", "普通", "较低", "最低"],
          icon: "circle",
          itemWidth: 8,
          itemHeight: 8,
          textStyle: {
            // 图例文字的样式
            color: this.darkTheme ? "#e6e9f0" : "#8A8F99",
          },
        },
        graphic: {
          type: "text",
          left: "center",
          top: "44%",
          style: {
            text: "用例数",
            textAlign: "center",
            fill: this.darkTheme ? "#e6e9f0" : "#333",
            fontSize: 14,
          },
        },
        series: [
          {
            name: "缺陷分布",
            type: "pie",
            // radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
            },
            data: this.defectData,
            itemStyle: {
              normal: {
                borderWidth: 2,
                borderColor: this.darkTheme ? "#1b1d23" : "#fff",
              },
              emphasis: {
                borderWidth: 0,
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      };
    },
    getPlanReportProirity() {
      this.priorityOptions = {
        backgroundColor: this.darkTheme ? "#1b1d23" : "#fff",
        tooltip: {
          trigger: "item",
          axisPointer: {
            type: "shadow",
          },
          backgroundColor: this.darkTheme ? "#1b1d23" : "#fff",
          borderColor: "none",
          extraCssText: "box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);", // 附加阴影样式
          textStyle: {
            color: this.darkTheme ? "#e6e9f0" : "#53565C",
          },
          formatter(params) {
            return `<span style="display: inline-block;margin-right: 4px;background-color:${params.color};width:12px;height:12px;border-radius:50%;"></span>${params.name}：${params.value}%`;
          },
        },
        legend: {
          orient: "vertical",
          right: 20,
          bottom: 20,
          data: ["最高", "较高", "普通", "较低", "最低"],
          icon: "circle",
          itemWidth: 8,
          itemHeight: 8,
          textStyle: {
            // 图例文字的样式
            color: this.darkTheme ? "#e6e9f0" : "#8A8F99",
          },
        },
        graphic: {
          type: "text",
          left: "center",
          top: "44%",
          style: {
            text: "用例数",
            textAlign: "center",
            fill: this.darkTheme ? "#e6e9f0" : "#333",
            fontSize: 14,
          },
        },
        series: [
          {
            name: "优先级分布",
            type: "pie",
            // radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
            },
            data: this.priorityData,
            itemStyle: {
              normal: {
                borderWidth: 2,
                borderColor: this.darkTheme ? "#1b1d23" : "#fff",
              },
              emphasis: {
                borderWidth: 0,
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      };
    },
    async getPlanReportTab() {
      this.tabOption = {
        color: this.color,
        backgroundColor: this.darkTheme ? "#1b1d23" : "#fff",
        tooltip: {
          trigger: "item",
          axisPointer: {
            type: "shadow",
          },
          backgroundColor: this.darkTheme ? "#1b1d23" : "#fff",
          borderColor: "none",
          extraCssText: "box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);", // 附加阴影样式
          textStyle: {
            color: this.darkTheme ? "#e6e9f0" : "#53565C",
          },
          formatter(params) {
            return `<span style="display: inline-block;margin-right: 4px;background-color:${params.color};width:12px;height:12px;border-radius:50%;"></span>${params.name}：${params.value}%`;
          },
        },
        legend: {
          orient: "vertical",
          right: 20,
          bottom: 20,
          data: this.name,
          icon: "circle",
          itemWidth: 8,
          itemHeight: 8,
          textStyle: {
            // 图例文字的样式
            color: this.darkTheme ? "#e6e9f0" : "#8A8F99",
          },
        },
        graphic: {
          type: "text",
          left: "center",
          top: "44%",
          style: {
            text: "用例数",
            textAlign: "center",
            fill: this.darkTheme ? "#e6e9f0" : "#333",
            fontSize: 14,
          },
        },
        series: [
          {
            name: "标签分布",
            type: "pie",
            // radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
            },
            data: this.tabData,
            itemStyle: {
              normal: {
                borderWidth: 2,
                borderColor: this.darkTheme ? "#1b1d23" : "#fff",
              },
              emphasis: {
                borderWidth: 0,
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      };
    },
    async getPlanReportPel() {
      this.pelOption = {
        color: this.color,
        backgroundColor: this.darkTheme ? "#1b1d23" : "#fff",
        tooltip: {
          trigger: "item",
          axisPointer: {
            type: "shadow",
          },
          backgroundColor: this.darkTheme ? "#1b1d23" : "#fff",
          borderColor: "none",
          extraCssText: "box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);", // 附加阴影样式
          textStyle: {
            color: this.darkTheme ? "#e6e9f0" : "#53565C",
          },
          formatter(params) {
            return `<span style="display: inline-block;margin-right: 4px;background-color:${params.color};width:12px;height:12px;border-radius:50%;"></span>${params.name}：${params.value}%`;
          },
        },
        legend: {
          orient: "vertical",
          right: 20,
          bottom: 20,
          data: this.pelName,
          icon: "circle",
          itemWidth: 8,
          itemHeight: 8,
          textStyle: {
            // 图例文字的样式
            color: this.darkTheme ? "#e6e9f0" : "#8A8F99",
          },
        },
        graphic: {
          type: "text",
          left: "center",
          top: "44%",
          style: {
            text: "用例数",
            textAlign: "center",
            fill: this.darkTheme ? "#e6e9f0" : "#333",
            fontSize: 14,
          },
        },
        series: [
          {
            name: "标签分布",
            type: "pie",
            // radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
            },
            data: this.pelData,
            itemStyle: {
              normal: {
                borderWidth: 2,
                borderColor: this.darkTheme ? "#1b1d23" : "#fff",
              },
              emphasis: {
                borderWidth: 0,
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      };
    },

    // 查询计划报告人员数据
    async getPlanReportPersonal() {
      const data = [];

      this.personalData = data;

      const personlFilter = data.filter((item) => item.userName !== "总计");

      this.personalOption = {
        color: ["#67C23A", "#FA5555", "#E6A23C", "#0085FF", "#DCDFE6"],
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          backgroundColor: this.darkTheme ? "#1b1d23" : "#fff",
          borderColor: "none",
          extraCssText: "box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);", // 附加阴影样式
          textStyle: {
            color: this.darkTheme ? "#e6e9f0" : "#53565C",
          },
        },
        legend: {
          right: 20,
          data: ["通过", "失败", "阻塞", "跳过"],
          icon: "circle",
          itemWidth: 8,
          itemHeight: 8,
          textStyle: {
            // 图例文字的样式
            color: this.darkTheme ? "#e6e9f0" : "#8A8F99",
          },
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        yAxis: {
          type: "value",
          axisTick: {
            // y轴刻度线
            show: false,
          },
          splitLine: {
            // 网格线
            lineStyle: {
              type: "dashed", // 设置网格线类型 dotted：虚线   solid:实线
              width: 1,
              color: this.darkTheme ? "#495266" : "#EBEEF5",
            },
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: this.darkTheme ? "#e6e9f0" : "#8A8F99", // 更改坐标轴文字颜色
            },
          },
          axisLine: {
            lineStyle: {
              color: this.darkTheme ? "#495266" : "#EBEEF5", // 更改坐标轴颜色
            },
          },
        },
        xAxis: {
          type: "category",
          data: personlFilter.map((item) => {
            return item.userName;
          }),
          boundaryGap: false,
          splitLine: {
            show: false, // 去掉网格线
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: this.darkTheme ? "#e6e9f0" : "#8A8F99", // 更改坐标轴文字颜色
            },
          },
          axisLine: {
            lineStyle: {
              color: this.darkTheme ? "#495266" : "#EBEEF5", // 更改坐标轴颜色
            },
          },
        },
        series: [
          {
            name: "通过",
            type: "bar",
            stack: "总量",
            label: {
              show: false,
            },
            barWidth: 30,
            data: personlFilter.map((item) => {
              return item.successNum;
            }),
          },
          {
            name: "失败",
            type: "bar",
            stack: "总量",
            barWidth: 30,
            label: {
              show: false,
            },
            data: personlFilter.map((item) => {
              return item.failureNum;
            }),
          },
          {
            name: "阻塞",
            type: "bar",
            stack: "总量",
            barWidth: 40,
            label: {
              show: false,
            },
            data: personlFilter.map((item) => {
              return item.blockNum;
            }),
          },
          {
            name: "跳过",
            type: "bar",
            stack: "总量",
            barWidth: 40,
            label: {
              show: false,
            },
            data: personlFilter.map((item) => {
              return item.skipNum;
            }),
          },
        ],
      };
    },

    onClose() {
      $emit(this, "update:visible", false);
    },
  },
  emits: ["update:visible"],
};
</script>

<style lang="scss" scoped>
.fullBox {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 2000;
  color: #202124;
  background-color: var(--main-bg-color, #fff);
  overflow-y: overlay;
  #pdfDom {
    padding: 12px;
  }
}
.reportTop {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0px 4px 16px;
  .headerTitle {
    font-weight: 600;
    font-size: 24px;
    color: #202124;
    margin-bottom: 16px;
    i {
      font-size: 20px;
      margin-left: 12px;
      color: var(--main-theme-color, #3e7bfa);
      cursor: pointer;
    }
  }
  .mainTitle {
    font-weight: bold;
    color: #202124;
  }
  .close {
    font-size: 18px;
    cursor: pointer;
  }
}
.subTitle {
  height: 38px;
  color: #202124;
  font-weight: 400;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
  span {
    color: #8a8f99;
  }
}
.reportList {
  display: flex;
  justify-content: center;
  align-items: center;
  ul {
    display: flex;
    justify-content: center;
    &:nth-child(1) {
      color: #8c8c8c;
    }
    li {
      width: 100px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
:deep(.vone-echarts div) {
  margin: 0 auto;
}
.h40 {
  margin-top: 30px;
  :deep(.el-card__body) {
    padding: 0px;
  }
} /*// 统计数据样式*/
.tableBox {
  margin: 16px 0 0;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
  .vone-table {
    border: 1px solid #ebeef5;
  }
} /*// 模块标题样式*/
.boxTitle {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  .title {
    font-size: 16px;
    font-weight: 600;
    color: #202124;
    display: flex;
    align-content: center;

    &::before {
      content: "";
      display: inline-block;
      width: 4px;
      margin-right: 12px;
      background-color: var(--main-theme-color, #3e7bfa);
    }
  }
  i {
    font-size: 26px;
    color: var(--main-theme-color, #3e7bfa);
    cursor: pointer;
  }
}
.contentbox,
.executeBox {
  display: flex;
  align-items: center;
  border-radius: 6px;
  background: #f0f5ff;
  margin: 0 12px;
  padding: 30px 0;
  .item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 18px;
    width: 196px;
    color: #8a8f99;
    span {
      font-size: 22px;
      color: var(--main-theme-color, #3e7bfa);
      font-weight: bold;
    }
    p {
      display: flex;
      align-items: center;
      font-size: 12px;
      color: #202124;
      i {
        font-size: 12px;
        margin-right: 4px;
        width: auto;
        cursor: pointer;
      }
    }
  }
  .divider {
    height: 24px;
    color: #c1c8d6;
  }
}
:deep(.el-card) {
  border-radius: 0px;
  margin-bottom: 0;
}
.custom-theme-dark {
  .fullBox {
    background-color: #1a1d24;
    color: #e6e9f0;
    .reportTop {
      .headerTitle,
      .mainTitle {
        color: #e6e9f0;
      }
    }
  }
  .subTitle {
    color: #8a8f99;
    border-color: #495266;
    span {
      color: #e6e9f0;
    }
  }
  .tableBox {
    border-color: #495266;
    .vone-table {
      border-color: #495266;
    }
  }
  .contentbox,
  .executeBox {
    background-color: #495266;
    .divider {
      color: #495266;
    }
    .item {
      p {
        color: #e6e9f0;
      }
    }
  }
  .boxTitle {
    .title {
      color: #e6e9f0;
    }
  }
}
</style>
