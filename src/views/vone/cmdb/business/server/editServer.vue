<template>
  <div>
    <el-card shadow="never">
      <vone-back :title="ruleForm.name">
        <template v-slot:custom>
          <div>
            <el-tag
              v-for="(item, index) in regionList"
              :key="index"
              :style="{
                borderColor: item.color,
                color: item.color,
                background: 'unset',
                marginLeft: '12px',
              }"
              >{{ item.name }}</el-tag
            >
          </div>
        </template>
        <template v-slot:toolbar>
          <div>
            <div v-if="this.$route.params.type != 2">
              <el-button
                :icon="elIconApplicationSetting"
                :disabled="!$permission('cmdb_server_set')"
                type="primary"
                @click="toEditConfig"
                >配置</el-button
              >
            </div>
          </div>
        </template>
      </vone-back>
      <el-row type="flex" justify="space-between" style="margin-top: 10px">
        <el-col :span="2" class="iconBox">
          <svg class="vone-icon" aria-hidden="true">
            <use xlink:href="#el-icon-webxitong" />
          </svg>
        </el-col>
        <el-col :span="22">
          <vone-desc>
            <vone-desc-item label="CI/CD类型">
              <span v-if="ruleForm.type">{{ ruleForm.type.desc }}</span>
              <span v-else>--</span>
            </vone-desc-item>
            <vone-desc-item label="维护人">
              <span
                v-if="
                  ruleForm.responsible &&
                  ruleForm.echoMap &&
                  ruleForm.echoMap.responsible
                "
              >
                <vone-user-avatar
                  :avatar-path="ruleForm.echoMap.responsible.avatarPath"
                  :name="ruleForm.echoMap.responsible.name"
                />
              </span>
              <span v-else> {{ ruleForm.responsible }}</span>
            </vone-desc-item>
            <vone-desc-item label="应用类型">
              <span v-if="ruleForm.programType">{{
                ruleForm.programType.desc
              }}</span>
              <span v-else>--</span>
            </vone-desc-item>
            <vone-desc-item label="更新时间">
              {{ ruleForm.updateTime }}
            </vone-desc-item>
            <vone-desc-item label="分支策略">
              <span
                v-if="
                  ruleForm.branchingStrategyKey &&
                  ruleForm.echoMap.branchingStrategyKey
                "
                >{{ ruleForm.echoMap.branchingStrategyKey.name }}</span
              >
              <span v-else>--</span>
            </vone-desc-item>
            <vone-desc-item label="所属机构">
              <span v-if="ruleForm.orgId && ruleForm.echoMap.orgId">
                {{ ruleForm.echoMap.orgId.name }}
              </span>
              <span v-else>--</span>
            </vone-desc-item>

            <vone-desc-item
              v-if="ruleForm.type && ruleForm.type.code != 'CD'"
              :calc-width="true"
              label="仓库地址"
            >
              {{ ruleForm.adress }}
            </vone-desc-item>
            <vone-desc-item label="应用描述">
              {{ ruleForm.description }}
            </vone-desc-item>
          </vone-desc>
        </el-col>
      </el-row>
    </el-card>

    <div class="tabContent">
      <el-tabs
        v-if="typeList"
        v-model="active"
        style="height: calc(100vh - 270px)"
        class="vone-tab-line"
      >
        <el-tab-pane
          v-for="item in typeList"
          :key="item.moduleCode"
          :label="item.moduleName"
          :name="item.moduleCode"
        >
          <template>
            <component
              :is="typeListMap[active]"
              v-if="typeList && active == item.moduleCode"
              :git-data="gitData"
              :application-info="ruleForm"
              :strategy-type="strategyType"
              :code-rep-path="codeRepPath"
              :i-s-s-u-v="ISSUV"
              :url="URL"
              :adress="adress"
              @success="getBaseInfo"
            />
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import {
  apiBaseSelectBaseDatagById,
  apiBaseUpdateApplicationBaseData,
  apiBuniesSelectWarehouseBySystemId,
} from "@/api/vone/cmdb/server";
import { apiCmdbSelectModuleBySystemId } from "@/api/vone/cmdb/application";

import projectConfig from "./editTab/project-config";
import deployConfig from "./editTab/deploy-config.vue";
import dbCataLogConfig from "./editTab/db-cataLog-config.vue";
import dbConfig from "./editTab/db-config.vue";

import engineConfig from "./editTab/engine-config.vue";

import grantConfig from "./editTab/grant-config.vue";
import warehouseConfig from "./editTab/warehouse-config.vue";

import projectBranchConfig from "./editTab/project-branch-config.vue";
import codeLineConfig from "./editTab/code-line-config.vue";
import buildConfig from "./editTab/build-config.vue";
import tagConfig from "./editTab/tagConfig.vue";
const components = {
  projectConfig,
  deployConfig,
  dbCataLogConfig,
  dbConfig,

  engineConfig,
  grantConfig,
  warehouseConfig,
  projectBranchConfig,
  codeLineConfig,
  buildConfig,
  tagConfig,
};

const tabList = [
  {
    isGuide: "true",
    moduleCode: "warehouseConfig",
    moduleName: "仓库配置",
  },
  {
    isGuide: "true",
    moduleCode: "projectConfig",
    moduleName: "工程配置",
  },

  {
    isGuide: "true",
    moduleCode: "codeLineConfig",
    moduleName: "分支配置",
  },
  {
    isGuide: "true",
    moduleCode: "tagConfig",
    moduleName: "Tag配置",
  },
  {
    isGuide: "true",
    moduleCode: "projectBranchConfig",
    moduleName: "工程分支配置",
  },
  {
    isGuide: "true",
    moduleCode: "buildConfig",
    moduleName: "构建配置",
  },
  {
    isGuide: "true",
    moduleCode: "deployConfig",
    moduleName: "部署配置",
  },
  {
    isGuide: "true",
    moduleCode: "dbCataLogConfig",
    moduleName: "DB目录",
  },
  {
    isGuide: "true",
    moduleCode: "dbConfig",
    moduleName: "DB配置",
  },

  {
    isGuide: "false",
    moduleCode: "engineConfig",
    moduleName: "引擎信息",
  },
];

export default {
  components: {},
  data() {
    return {
      codeRepName: undefined,
      ID: "",
      ruleForm: {},
      content: "",
      regionList: [],
      // tabActive: "projectConfig",
      typeList: null,
      typeListMap: null,
      active: "warehouseConfig",
      envCheckList: [],
      checkList: [],
      envList: [],
      URL: undefined,
      FULLURL: undefined,
      adress: "",
      strategyType: undefined,
      codeRepPath: undefined,
      ISSUV: undefined,
      baseInfoKey: 0,
      tabList,
      gitData: {}, // 仓库配置数据
      projectInfo: {}, // 工程配置数据
    };
  },
  watch: {
    $route(to, from) {
      this.getBaseInfo();
    },
  },
  mounted() {
    this.getBaseInfo();
  },
  methods: {
    // 查询仓库回显数据
    async getGitData() {
      const res = await apiBuniesSelectWarehouseBySystemId(
        this.$route.params.systemId
      );
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      this.gitData = res.data;
    },
    // 查询工程回显数据
    async getProjectInfo() {
      const res = await apiCmdbSelectModuleBySystemId(
        this.$route.params.systemId
      );
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      this.projectInfo = res.data;
    },
    async getBaseInfo(val) {
      try {
        await Promise.all([this.getGitData(), this.getProjectInfo()]);
      } catch (error) {
        return;
      }

      const res = await apiBaseSelectBaseDatagById(this.$route.params.systemId);
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }

      const envMap = {
        DEV: "#00D1E1",
        SIT: "#C87BFF",
        DAT: "#FF9169",
        UAT: "#8591FF",
        VIR: "#38C1FF",
        PRD: "#00D6C3",
      };

      if (res.data.applicationEnvs) {
        this.regionList = res.data.applicationEnvs.map((r) => ({
          name: r.envKey,
          color: envMap[r.envKey] ? envMap[r.envKey] : "#ccc",
        }));
      } else {
        this.regionList = [];
      }

      // this.regionList = res.data.applicationEnvs ? res.data.applicationEnvs.map(r => r.echoMap.envKey.code) : []
      this.ID = res.data.id;

      this.ruleForm = res.data;
      if (this.gitData && !this.projectInfo.length) {
        const path = this.gitData.echoMap.engineId.engineUrl;
        const fullPath = `${path}/${this.ruleForm.codeRepPath}`;

        this.ruleForm["adress"] = fullPath;
      } else if (this.projectInfo.length && this.gitData) {
        const path = this.gitData.echoMap.engineId.engineUrl;
        const fullPath = `${path}/${this.ruleForm.codeRepPath}/${this.projectInfo[0].name}.git`;
        this.ruleForm["adress"] = fullPath;
      } else {
        this.ruleForm["adress"] = "--";
      }

      this.codeRepPath = this.ruleForm.codeRepPath;
      // 传给子组件根据服务应用的分支策略查数据用
      this.strategyType = res.data.branchingStrategyKey;
      const typeListMap = {};

      if (this.ruleForm.type.code == "CD") {
        // 部署类型
        this.typeList = this.tabList.filter(
          (r) =>
            r.moduleCode != "warehouseConfig" &&
            r.moduleCode != "projectBranchConfig" &&
            r.moduleCode != "codeLineConfig" &&
            r.moduleCode != "buildConfig"
        );
      } else {
        // 构建。构建+部署类型
        this.typeList = this.tabList;
      }

      this.tabList.forEach((item) => {
        typeListMap[item.moduleCode] = components[item.moduleCode];
      });
      this.typeListMap = typeListMap;

      this.active = val || this.typeList[0].moduleCode;
      if (
        !this.active ||
        this.tabList.every(({ moduleCode }) => moduleCode !== this.active)
      ) {
        this.active = this.tabList[0].moduleCode;
      }

      this.baseInfoKey = Date.now();
    },
    async saveBasicInfo() {
      const res = await apiBaseUpdateApplicationBaseData({
        id: this.ruleForm.id,
        name: this.ruleForm.name,
        personLiable: this.ruleForm.personLiable,
        type: this.ruleForm.type,
        description: this.ruleForm.description,
      });
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      this.$message.success("保存成功");
      this.edit = false;
    },

    toEditConfig() {
      if (!this.$permission("cmdb_server_set")) {
        this.$message.warning("当前登录账号没有配置权限，请联系管理员授权");
        return;
      }
      this.$router.push({
        name: "cmdb_server_set",
        params: { systemId: this.$route.params.systemId, type: 2 },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.iconBox {
  display: flex;
  justify-content: center;
  align-items: center; /*// 详情头像样式*/
  .vone-icon {
    width: 64px;
    height: 64px;
  }
}
:deep() {
  .el-tabs__content {
    padding: 16px;
    position: unset;
  }
}
.tabContent {
  margin-top: 10px;
  background-color: #fff;
}
:deep(.vone-desc-item) {
  height: 20px;
  line-height: 20px;
}
.vone-tab-line {
  position: relative;
}
</style>
