import request from '@/utils/axios-api'
import requestUtils from '@/utils/requestUtils'

// 查询需求列表

export function apiAlmIssuePage(data) {
  return request({
    url: '/api/alm/alm/requirement/page',
    method: 'post',
    data
  })
}
// 下拉条件查询需求列表无权限
export function requirementListByCondition(data) {
  return request({
    url: '/api/alm/alm/requirement/queryListByCondition',
    method: 'post',
    data
  })
}
// 查询需求来源-不分页

export function apiAlmSourceNoPage(data) {
  return request({
    url: '/api/alm/alm/source/query',
    method: 'post',
    data
  })
}

// 需求新增

export function apiAlmSaveIssue(data) {
  return request({
    url: '/api/alm/alm/requirement',
    method: data.id ? 'put' : 'post',
    data
  })
}

// 需求详情
export function apiAlmIssueInfo(id) {
  return request({
    url: `/api/alm/alm/requirement/${id}`,
    method: 'get'
  })
}

/**
 * 根据ID删除需求
 * @param id
 */
export async function apiAlmIssueDel(data) {
  return request({
    url: `/api/alm/alm/requirement`,
    method: 'delete',
    data
  })
}

// 需求--不分页
export function apiAlmRequirementNoPage(data) {
  return request({
    url: '/api/alm/alm/requirement/query',
    method: 'post',
    data
  })
}
/**
 * @desc 查询项目下未完成需求
 * @param {string} projectId
 */
export function apiUndoRequirementList(projectId) {
  return request({
    url: `/api/alm/alm/requirement/findUndoneByProjectId/${projectId}`,
    method: 'get'
  })
}
// 查询需求可流转的状态
export function apiAlmIssueFindNextNode(requirementId) {
  return request({
    url: `/api/alm/alm/requirement/findNextNode/${requirementId}`,
    method: 'get'
  })
}

// 需求状态流转
export function apiAlmRequirementFlow(requirementId, sourceStateCode, targetStateCode) {
  return request({
    url: `/api/alm/alm/requirement/transitionState/${requirementId}/${sourceStateCode}/${targetStateCode}`,
    method: 'put'
  })
}

// 根据项目id查询未完成且未关联父需求的需求
export function apiAlmFindTodoByProjectIde(projectId) {
  return request({
    url: `/api/alm/alm/requirement/findTodoByProjectId/${projectId}`,
    method: 'get'
  })
}

// 需求关联或解除关联父需求
export function apiAlmCorrelatedRequirement(data) {
  return request({
    url: '/api/alm/alm/requirement/correlatedRequirement',
    method: 'put',
    data
  })
}

// 下载需求导入模板
export function downloadIssueTemplate(data) {
  return request({
    url: `/api/alm/alm/requirement/excel/downloadImportTemplate`,
    method: 'get',
    data,
    headers: {
      'Content-Type': 'application/json; charset=UTF-8'
    },
    responseType: 'blob' // 在请求中加上这一行，特别重要
  })
}

// 需求--导入
export function apiAlmIssueImport(data) {
  return request({
    url: `/api/alm/alm/requirement/excel/import`,
    method: 'post',
    data: requestUtils.fileFormData(data)
  })
}
// 意向关联需求列表
export function iderTorequire(data) {
  return request({
    url: '/api/alm/alm/requirement/queryList',
    method: 'post',
    data
  })
}

// 根据需求id查询需求状态变更记录
export function findRequirementByIdCode(id, data) {
  return request({
    url: `/api/alm/alm/projectWorkflowTransitionHistory/findRequirementByIdCode/${id}`,
    method: 'post',
    data
  })
}
// 根据id查询任务状态变更记录
export function findTaskByIdCode(id, data) {
  return request({
    url: `/api/alm/alm/projectWorkflowTransitionHistory/findTaskByIdCode/${id}`,
    method: 'post',
    data
  })
}
// 根据id查询风险状态变更记录
export function findBugByIdCode(id, data) {
  return request({
    url: `/api/alm/alm/projectWorkflowTransitionHistory/findBugByIdCode/${id}`,
    method: 'post',
    data
  })
}
// 根据id查询意向状态变更记录
export function findIdeaByIdCode(id, data) {
  return request({
    url: `/api/alm/alm/projectWorkflowTransitionHistory/findIdeaByIdCode/${id}`,
    method: 'post',
    data
  })
}

// 根据项目id查询需求的依赖关系
export function apiAlmProjectRelation(type_classify, requirement_id) {
  return request({
    url: `/api/alm/alm/issue_item/relation/${type_classify}/${requirement_id}`,
    method: 'get'
  })
}

// 添加需求的依赖关系
export function apiAlmAddDepend(data) {
  return request({
    url: `/api/alm/alm/issue_item/relation`,
    method: 'post',
    data
  })
}

// 修改需求的依赖关系
export function apiAlmPutDepend(relationId, issueRelationType) {
  return request({
    url: `/api/alm/alm/issue_item/relation/${relationId}/${issueRelationType}`,
    method: 'put'
  })
}

// 删除需求的依赖关系
export function apiAlmDeleteDepend(relation_id) {
  return request({
    url: `/api/alm/alm/issue_item/relation/${relation_id}`,
    method: 'delete'
  })
}

// 工作项新增

export function apiAlmAdd(url, data) {
  return request({
    url: url,
    method: 'post',
    data
  })
}

// 工作项修改

export function apiAlmUpdate(url, data) {
  return request({
    url: url,
    method: 'put',
    data
  })
}

// 工作项状态修改

export function apiAlmStateChange(url) {
  return request({
    url: url,
    method: 'put'
  })
}

// 工作项详情回显

export function apiAlmGetInfo(url, data) {
  return request({
    url: url,
    method: 'get',
    data

  })
}

// 工作项详情回显

export function apiAlmFindNextNode(url) {
  return request({
    url: url,
    method: 'get'
  })
}

// 查询可流转的状态
export function apiFindNextNode(url) {
  return request({
    url: url,
    method: 'get'
  })
}

// 需求状态流转
export function apiAlmChangeFlow(url) {
  return request({
    url: url,
    method: 'put'
  })
}

// 根据项目id查询未完成且未关联需求的缺陷
export function getTodoBugList(projectId) {
  return request({
    url: `/api/alm/alm/bug/findTodoByProjectId/${projectId}`,
    method: 'get'
  })
}

// 需求关联或解除关联缺陷
export function creatBugForProject(data) {
  return request({
    url: `/api/alm/alm/requirement/correlatedBug`,
    method: 'put',
    data
  })
}
// 查询分组
export async function getGroup(data) {
  return request({
    url: `/api/alm/alm/requirement/group`,
    method: 'post',
    data
  })
}
