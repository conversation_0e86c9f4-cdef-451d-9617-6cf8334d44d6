<template>
  <div class="progress-example">
    <h3>WorkFlow进度显示示例</h3>
    
    <div class="controls">
      <el-button @click="toggleProgress">
        {{ showProgress ? '隐藏进度' : '显示进度' }}
      </el-button>
      <el-button @click="updateCurrentNode">切换当前节点</el-button>
      <el-button @click="randomizeSpendTime">随机耗时</el-button>
    </div>

    <div class="workflow-container">
      <vone-work-flow
        :xml="xml"
        :show-progress="showProgress"
        :show-bar="false"
        hide-text-annotation
        single
        preview
        :properties-props="{ width: 250 }"
      />
    </div>

    <div class="info-panel">
      <h4>当前配置：</h4>
      <ul>
        <li>显示进度：{{ showProgress ? '是' : '否' }}</li>
        <li>当前节点：{{ currentNodeIndex + 1 }}</li>
        <li>节点耗时：{{ nodeSpendTimes.join(', ') }}秒</li>
      </ul>
    </div>
  </div>
</template>

<script>
import { formatTime } from '../utils/timeFormatter'

export default {
  name: 'ProgressExample',
  data() {
    return {
      showProgress: true,
      currentNodeIndex: 0,
      nodeSpendTimes: [120, 300, 1800, 3661, 0], // 示例耗时数据
      xml: '' // 将在mounted中生成
    }
  },
  mounted() {
    this.generateExampleXML()
  },
  methods: {
    /**
     * 切换进度显示
     */
    toggleProgress() {
      this.showProgress = !this.showProgress
      this.generateExampleXML()
    },

    /**
     * 切换当前节点
     */
    updateCurrentNode() {
      this.currentNodeIndex = (this.currentNodeIndex + 1) % this.nodeSpendTimes.length
      this.generateExampleXML()
    },

    /**
     * 随机化耗时数据
     */
    randomizeSpendTime() {
      this.nodeSpendTimes = this.nodeSpendTimes.map(() => 
        Math.floor(Math.random() * 7200) // 0-2小时随机耗时
      )
      this.generateExampleXML()
    },

    /**
     * 生成示例XML数据
     */
    generateExampleXML() {
      const nodes = [
        {
          id: 'start',
          name: '开始',
          type: 'bpmn:StartEvent',
          x: 100,
          y: 100,
          w: 36,
          h: 36
        },
        {
          id: 'task1',
          name: '需求分析',
          type: 'bpmn:UserTask',
          x: 200,
          y: 80,
          w: 100,
          h: 80
        },
        {
          id: 'task2',
          name: '设计评审',
          type: 'bpmn:UserTask',
          x: 350,
          y: 80,
          w: 100,
          h: 80
        },
        {
          id: 'task3',
          name: '开发实现',
          type: 'bpmn:UserTask',
          x: 500,
          y: 80,
          w: 100,
          h: 80
        },
        {
          id: 'task4',
          name: '测试验证',
          type: 'bpmn:UserTask',
          x: 650,
          y: 80,
          w: 100,
          h: 80
        },
        {
          id: 'end',
          name: '结束',
          type: 'bpmn:EndEvent',
          x: 800,
          y: 100,
          w: 36,
          h: 36
        }
      ]

      const edges = [
        { id: 'flow1', source: 'start', target: 'task1' },
        { id: 'flow2', source: 'task1', target: 'task2' },
        { id: 'flow3', source: 'task2', target: 'task3' },
        { id: 'flow4', source: 'task3', target: 'task4' },
        { id: 'flow5', source: 'task4', target: 'end' }
      ]

      // 为节点添加进度信息
      nodes.forEach((node, index) => {
        if (this.showProgress && node.type !== 'bpmn:StartEvent' && node.type !== 'bpmn:EndEvent') {
          const taskIndex = index - 1 // 排除开始节点
          if (taskIndex >= 0 && taskIndex < this.nodeSpendTimes.length) {
            node.showProgress = true
            node.spendTime = this.nodeSpendTimes[taskIndex]
            node.currentNode = taskIndex === this.currentNodeIndex
          }
        }
      })

      this.xml = this.jsonToXml({ nodes, edges })
    },

    /**
     * 简化的JSON转XML方法（示例用）
     */
    jsonToXml(data) {
      const { nodes, edges } = data
      
      let xml = `<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" 
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" 
                  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" 
                  xmlns:di="http://www.omg.org/spec/DD/20100524/DI"
                  id="Definitions_1" 
                  targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="Process_1" isExecutable="true">`

      // 添加节点
      nodes.forEach(node => {
        const attrs = []
        if (node.showProgress) {
          attrs.push(`showProgress="${node.showProgress}"`)
        }
        if (node.spendTime !== undefined) {
          attrs.push(`spendTime="${node.spendTime}"`)
        }
        if (node.currentNode) {
          attrs.push(`currentNode="${node.currentNode}"`)
        }
        
        const attrString = attrs.length > 0 ? ' ' + attrs.join(' ') : ''
        
        if (node.type === 'bpmn:StartEvent') {
          xml += `\n    <bpmn:startEvent id="${node.id}" name="${node.name}"${attrString} />`
        } else if (node.type === 'bpmn:EndEvent') {
          xml += `\n    <bpmn:endEvent id="${node.id}" name="${node.name}"${attrString} />`
        } else {
          xml += `\n    <bpmn:userTask id="${node.id}" name="${node.name}"${attrString} />`
        }
      })

      // 添加连线
      edges.forEach(edge => {
        xml += `\n    <bpmn:sequenceFlow id="${edge.id}" sourceRef="${edge.source}" targetRef="${edge.target}" />`
      })

      xml += `\n  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1">`

      // 添加图形信息
      nodes.forEach(node => {
        xml += `\n      <bpmndi:BPMNShape id="${node.id}_di" bpmnElement="${node.id}">
        <dc:Bounds x="${node.x}" y="${node.y}" width="${node.w}" height="${node.h}" />
      </bpmndi:BPMNShape>`
      })

      xml += `\n    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>`

      return xml
    }
  }
}
</script>

<style scoped>
.progress-example {
  padding: 20px;
}

.controls {
  margin-bottom: 20px;
}

.controls .el-button {
  margin-right: 10px;
}

.workflow-container {
  border: 1px solid #ddd;
  border-radius: 4px;
  height: 400px;
  margin-bottom: 20px;
}

.info-panel {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
}

.info-panel h4 {
  margin-top: 0;
  color: #333;
}

.info-panel ul {
  margin: 0;
  padding-left: 20px;
}

.info-panel li {
  margin-bottom: 5px;
  color: #666;
}
</style>
