<template>
  <section class="page-box">
    <div class="countRow">
      <header>
        <div class="title">数据统计</div>
        <el-select
          v-model="selectedLibrary"
          clearable
          filterable
          multiple
          collapse-tags
          placeholder="请选择用例库"
          class="filterSelect"
          popper-class="multipleSelect"
          @change="refreshData"
        >
          <el-option
            v-for="item in libraryList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
            <svg class="icon" aria-hidden="true">
              <use xlink:href="#el-application-filejia2" />
            </svg>
            <span style="margin-left: 4px">{{ item.name }}</span>
          </el-option>
        </el-select>
      </header>
      <section class="section">
        <caseNum ref="caseNum" title="用例统计" />
        <div class="line" />
        <caseplan ref="casePlan" title="计划统计" />
        <div class="line" />
        <bugNum ref="bugNum" title="缺陷统计" />
        <div class="line" />
        <div class="bgBox">
          <div class="bg">
            <div class="header">
              <span class="title">趋势报表</span>
              <span class="subTitle">快速查看趋势变化</span>
            </div>
            <div class="img" />
            <div class="itemBtn" @click="trendsChart">点击查看</div>
          </div>
        </div>
      </section>
    </div>

    <testReport title="测试报告新增" style="grid-column: 1/7; grid-row: 5/9" />

    <caseStatus
      title="测试用例分布状态"
      style="grid-column: 1/7; grid-row: 9/13"
    />

    <caseDoing
      title="测试计划-进行中"
      style="grid-column: 7/13; grid-row: 5/13"
    />

    <!-- 趋势报表 -->
    <trendsDialog
      v-bind="trendsParams"
      v-if="trendsParams.visible"
      v-model:value="trendsParams.visible"
      :library-list="libraryList"
      :selected-id="selectedLibrary"
    />
  </section>
</template>

<script>
import caseNum from './components/case-num.vue'
import caseplan from './components/case-plan.vue'
import bugNum from './components/bug-num.vue'
import testReport from './components/test-report.vue'
import caseStatus from './components/case-status.vue'
import caseDoing from './components/case-doing.vue'
import trendsDialog from './components/trends-chart/trends-dialog.vue'
import { getAllLibraryInfo, getOverview } from '@/api/vone/testmanage'

export default {
  components: {
    caseNum,
    caseplan,
    bugNum,
    testReport,
    caseStatus,
    caseDoing,
    trendsDialog,
  },
  data() {
    return {
      visible: false,
      selectedLibrary: [],
      data: [],
      libraryList: [],
      statusFun: {},
      trendsParams: {
        visible: false,
      },
    }
  },
  mounted() {
    this.getLibraryList()
    this.getData()
  },
  methods: {
    // 查询用例库列表
    async getLibraryList() {
      const res = await getAllLibraryInfo()
      if (res.isSuccess) {
        this.libraryList = res.data
        // this.selectedLibrary = res.data?.length > 0 && [res.data[0].id]
        // this.$nextTick(() => {
        // this.refreshData()
        // })
      }
    },
    // 查询概览数据
    async getData() {
      const res = await getOverview()
      if (res.isSuccess) {
        this.$refs.caseNum?.datafun(res.data[2])
        this.$refs.casePlan?.datafun(res.data[0])
        this.$refs.bugNum?.datafun(res.data[1])
      }
    },
    refreshData(val) {
      if (val.length == 0) {
        this.getData()
        return false
      } else {
        this.$refs.caseNum?.getData(val)
        this.$refs.casePlan?.getData(val)
        this.$refs.bugNum?.getData(val)
      }
    },
    addReport() {
      this.$router.push({
        name: 'testm_report_list',
      })
    },
    trendsChart() {
      this.trendsParams = {
        visible: true,
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.page-box {
  width: 100%;
  height: calc(100vh - 86px);
  display: grid;
  gap: 16px;
  grid-template-columns: repeat(12, 1fr);
  grid-template-rows: repeat(12, 1fr);
  .countRow {
    // height: 210px;
    grid-column: 1/13;
    grid-row: 1/5;
    background-color: var(--main-bg-color, #fff);
    border-radius: 6px;
    header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 52px;
      padding: 12px 16px;
    }
    .title {
      font-weight: 600;
      font-size: 14px;
      color: var(--main-font-color);
    }

    .title,
    :deep(header.title) {
      color: var(--main-font-color);
    }

    .filterSelect {
      width: 240px;
      :deep() {
        .el-input__inner {
          background: var(--hover-bg-color);
          border-color: var(--hover-bg-color);
        }
        .el-select__tags {
          width: 160px;
          .el-tag {
            max-width: 150px;
          }
        }
      }
    }
  }
  .section {
    display: flex;
    align-items: center;
    padding: 0 16px 16px;
    height: calc(100% - 56px);

    .boxItem {
      flex: 1;
    }

    .line {
      height: 80px;
      width: 1px;
      background-color: var(--tab-bg-color);
    }
  }
}
.bg {
  position: relative;
  padding: 16px;
  flex: 1;
  position: relative;
  border-radius: 4px;
  background-color: rgba(135, 145, 250, 0.12);
  .header {
    display: flex;
    align-items: center;
    gap: 0 4px;
    height: 24px;
  }
  .title {
    font-size: 16px;
    font-weight: 500;
  }
  .subTitle {
    font-size: 12px;
    font-weight: 400;
    color: var(--auxiliary-font-color);
  }
  .img {
    position: absolute;
    bottom: 16px;
    width: 68px;
    height: 60px;
    background: url('@/assets/testm/trends.png') no-repeat;
    background-size: 100% 100%;
  }
}
.bgBox {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex: 1;
  height: 100%;
  padding: 16px 0px 0px 28px;
}
.itemBtn {
  position: absolute;
  bottom: 16px;
  right: 16px;
  border-radius: 4px;
  color: #fff;
  width: 80px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  cursor: pointer;
  background-color: #8791fa;
}
</style>

<style lang="scss">
.custom-theme-light .multipleSelect {
  border-color: #f5f6fa;
}
</style>
