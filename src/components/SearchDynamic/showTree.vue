<template>
  <div>
    <el-dropdown
      class="grouping-style"
      trigger="click"
      @command="hierarchyCommand($event, true)"
    >
      <span class="label">
        <span class="weak-title">布局</span>
        {{ hierarchyItem.name }}
        <el-icon class="iconfont el-icon--right"
          ><el-icon-direction-down
        /></el-icon>
      </span>
      <template v-slot:dropdown>
        <el-dropdown-menu>
          <el-dropdown-item
            v-for="e in hierarchyOptions"
            :key="e.value"
            :class="[
              'grouping-item',
              hierarchyItem.value == e.value ? 'is-active' : '',
            ]"
            :command="e.value"
          >
            {{ e.name }}
            <el-icon class="grouping-icon"><el-icon-check /></el-icon>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script>
import {
  DirectionDown as ElIconDirectionDown,
  Check as ElIconCheck,
} from "@element-plus/icons-vue";
import { $on, $off, $once, $emit } from "../../utils/gogocodeTransfer";
export default {
  components: {
    ElIconDirectionDown,
    ElIconCheck,
  },
  props: {
    valueObj: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      hierarchyItem: {},
      hierarchyOptions: [
        {
          name: "表格",
          value: "table",
          icon: "el-icon-application-biaogeshitu",
        },
        {
          name: "层级",
          value: "tree",
          icon: "el-icon-application-cengjishitu",
        },
      ],
    };
  },
  watch: {
    valueObj: {
      deep: true,

      handler(val) {
        const obj = {
          name: "表格",
          value: "table",
          icon: "el-icon-application-biaogeshitu",
        };
        this.hierarchyItem = val.treeView
          ? this.hierarchyOptions.find((e) => e.value == "tree")
          : obj;
      },

      immediate: true,
    },
  },
  methods: {
    // 层级
    hierarchyCommand(id, type) {
      this.hierarchyItem = this.hierarchyOptions.find((e) => e.value == id);
      $emit(this, "success", id);
    },
  },
  emits: ["success"],
};
</script>

<style lang="scss" scoped>
@use "./index.scss" as *;
</style>

<style lang="scss">
@use "./index-global.scss" as *;
</style>
