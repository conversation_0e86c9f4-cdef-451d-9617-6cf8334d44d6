const rowData = [{
  'id': 'require',
  'label': '需求',
  'attrs': [{
    key: '1',
    types: 'demand',
    user: '马克',
    title: '知识库空间开发',
    number: 'ISSUE-STORY-2',
    date: '2021-12-08',
    avatar: 'avatar1',
    'relation': [{
      'key': '3',
      'nodeId': 'planing'
    }]
  },
  {
    key: '2',
    types: 'demand',
    user: '杨子安',
    title: '知识库空间编辑',
    number: 'ISSUE-STORY-2',
    date: '2021-12-06',
    avatar: 'avatar2',
    relation: [{
      key: '5',
      nodeId: 'planing'
    }]
  }
  ]
},
{
  id: 'planing',
  label: '规划',
  attrs: [{
    title: '迭代一',
    types: 'iteration',
    key: '3',
    begintime: '2020.08.20',
    endtime: '2020.09.30',
    relation: [{
      key: '6',
      nodeId: 'develop'
    }]
  },
  {
    key: '4',
    types: 'demand',
    user: '徐梦利',
    title: '知识库空间权限开发',
    number: 'ISSUE-STORY-2',
    date: '2021-12-05',
    avatar: 'avatar1'
  },
  {
    key: '5',
    types: 'demand',
    user: '姜旭',
    title: '知识库空间新增',
    number: 'ISSUE-STORY-2',
    date: '2021-12-07',
    avatar: 'avatar3',
    relation: [{
      key: '7',
      nodeId: 'develop'
    }]
  }
  ]
},
{
  'id': 'develop',
  'label': '开发',
  'attrs': [
    {
      // id: 'node5',
      user: '付老师',
      title: 'Vone前端',
      key: '6',
      types: 'branch',
      url: 'http://192.168.99.21/vone/front'
    },
    {
      // id: 'node3',
      title: 'dev',
      types: 'branch',
      key: '7',
      desc: '开发分支',
      count: 35
    },
    {
      id: 'node3',
      title: 'master',
      key: '8',
      types: 'branch',
      desc: '主分支',
      count: 2
    },
    {
      id: 'node5',
      user: '付老师',
      title: 'Vone后端',
      key: '9',
      types: 'codding',
      url: 'http://192.168.99.21/vone/backend'
    },
    {
      id: 'node3',
      title: 'dev',
      key: '10',
      types: 'branch',
      desc: '开发分支',
      count: 22,
      relation: [{
        key: '13',
        nodeId: 'testing'
      }]
    },
    {
      id: 'node3',
      title: 'master',
      key: '11',
      types: 'branch',
      desc: '主分支',
      count: 1
    }
  ]
},

{
  'id': 'testing',
  'label': '测试',
  'attrs': [

    {
      id: 'node8',
      title: 'Vone专业版前端流水线',
      user: '冯威',
      comboId: 'testing',
      key: '12',
      types: 'pipeline',
      mode: 'CUSTOM', // 快捷
      date: '2021-11-12 10:20'
    },
    {
      id: 'node7',
      user: '付老师',
      title: '制品包-202111121021',
      versionId: '6374375435523',
      comboId: 'planing',
      key: '13',
      types: 'package',
      updatetime: '2021-11-12 10:20'
    },
    {
      id: 'node9',
      title: 'Vone企业版前端流水线',
      user: '郝哲',
      comboId: 'testing',
      key: '14',
      types: 'pipeline',
      mode: 'SCHEDULE', // 调度
      date: '2020-11-12 10:20',
      relation: [{
        key: '16',
        nodeId: 'production'
      }]
    },
    {
      id: 'node7',
      user: '付老师',
      title: '制品包-202111121021',
      versionId: '6374375435523',
      comboId: 'planing',
      key: '15',
      types: 'package',
      updatetime: '2020-11-12 10:20'
    }
  ]
},
{
  'id': 'production',
  'label': '投产',
  'attrs': [
    {
      id: 'node8',
      title: 'Vone专业版后端流水线',
      user: '冯威',
      comboId: 'testing',
      key: '16',
      types: 'pipeline',
      mode: 'CUSTOM', // 快捷
      date: '2020-12-03 11:28'
    },
    {
      id: 'node7',
      user: '付老师',
      title: '制品包-202112031131',
      versionId: '6374375435523',
      comboId: 'planing',
      key: '17',
      types: 'package',
      updatetime: '2020-12-03 11:31'
    },
    {
      id: 'node9',
      title: 'Vone企业版后端流水线',
      user: '郝哲',
      comboId: 'testing',
      key: '18',
      types: 'pipeline',
      mode: 'SCHEDULE', // 调度
      date: '2020-12-03 11:28'
    },
    {
      id: 'node7',
      user: '付老师',
      title: '制品包-202112031131',
      versionId: '6374375435523',
      comboId: 'planing',
      key: '19',
      types: 'package',
      updatetime: '2020-12-03 11:31'
    }
  ]
}
]
export { rowData }
