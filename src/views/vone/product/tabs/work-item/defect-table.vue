<template>
  <div>
    <vone-search-wrapper>
      <template v-slot:search>
        <vone-search-dynamic
          v-if="defaultFileds.length"
          ref="searchForm"
          table-search-key="work-defect-table"
          :model="formData"
          :table-ref="$refs['work-defect-table']"
          show-grouping
          :grouping-options="tableOptions.groupingOptions"
          :hide-columns="tableOptions.hideColumns"
          :default-fileds="defaultFileds"
          show-basic
          :show-column-sort="true"
          :extra="extraData"
          @getTableData="getInitTableData"
        />
      </template>
    </vone-search-wrapper>
    <div style="height: calc(100vh - 184rem)">
      <vxe-table
        ref="workDefectTable"
        class="vone-vxe-table"
        border
        height="auto"
        show-overflow="tooltip"
        :loading="pageLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column
          title="标题"
          field="name"
          min-width="480"
          fixed="left"
          class-name="name_col custom-title-style"
          show-overflow="ellipsis"
          tree-node
        >
          <template #default="{ row }">
            <span v-if="row.delay" style="position: absolute; left: 0">
              <el-tooltip
                :show-after="500"
                content="当前工作项已延期"
                placement="top"
              >
                <el-icon class="color-danger ml-2"
                  ><el-icon-warning-outline
                /></el-icon>
              </el-tooltip>
            </span>
            <el-tooltip
              v-showWorkItemTooltips
              :content="row.code + ' ' + row.name"
              placement="top-start"
              :visible-arrow="false"
            >
              <span class="custom-title-main" @click="showInfo(row)">
                <i
                  v-if="row.typeCode && row.echoMap && row.echoMap.typeCode"
                  :class="`iconfont ${row.echoMap.typeCode.icon} custom-title-style-icon `"
                  :style="{
                    color: `${
                      row.echoMap.typeCode ? row.echoMap.typeCode.color : '#ccc'
                    }`,
                  }"
                />
                <span class="custom-title-style-text">{{
                  row.code + " " + row.name
                }}</span>
              </span>
            </el-tooltip>
            <span
              class="custom-title-style-copy"
              :style="{
                position: 'absolute',
                top: ' -4px',
                right: '-40px',
                display: copyRow && copyRow.id == row.id ? 'block' : '',
              }"
            >
              <el-dropdown
                trigger="click"
                :hide-on-click="true"
                @visible-change="(e) => visibleChange(e, row)"
                @command="customCopy"
              >
                <el-button type="text" :icon="elIconApplicationMore" />
                <template v-slot:dropdown>
                  <el-dropdown-menu class="custom-title-copy-dropdown">
                    <el-dropdown-item
                      :icon="elIconEditCharacterB"
                      command="title"
                    >
                      <span>复制编号</span>
                    </el-dropdown-item>
                    <el-dropdown-item
                      :icon="elIconApplicationCopyContent"
                      command="code"
                    >
                      <span>复制标题</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </span>
          </template>
        </vxe-column>
        <vxe-column title="状态" field="stateCode" width="120">
          <template #default="{ row, rowIndex }">
            <defectStatus
              v-if="row && !row.groupType"
              :workitem="row"
              :no-permission="!$permission('reqm_center_require_flow')"
              @changeFlow="editRowStatus(row, rowIndex)"
            />
          </template>
        </vxe-column>
        <!-- <vxe-column title="标题" field="name" min-width="150">
              <template slot-scope="scope">
                <span
                  v-if="
                    scope.row.typeCode &&
                      scope.row.echoMap &&
                      scope.row.echoMap.typeCode
                  "
                >
                  <i
                    :class="`iconfont ${scope.row.echoMap.typeCode.icon}`"
                    :style="{
                      color: `${
                        scope.row.echoMap.typeCode
                          ? scope.row.echoMap.typeCode.color
                          : '#ccc'
                      }`,
                    }"
                  />
                </span>
                {{ scope.row.name }}
              </template>
            </vxe-column> -->
        <!-- <vxe-column title="邮箱" field="email" min-width="150" /> -->
        <!-- <vxe-column title="角色" field="role" min-width="150">
              <template #default="{ row }">
                {{ row.allRole }}
              </template>
            </vxe-column> -->

        <vxe-column title="负责人" field="leadingBy" min-width="150">
          <template v-slot="scope">
            <span
              v-if="
                scope.row && scope.row.echoMap && scope.row.echoMap.leadingBy
              "
            >
              <vone-user-avatar
                :avatar-path="getUserInfo(scope.row, 'leadingBy').avatarPath"
                :name="getUserInfo(scope.row, 'leadingBy').name"
              />
            </span>
          </template>
        </vxe-column>
        <vxe-column title="提出时间" field="createTime" width="120">
          <template #default="{ row }">
            <span v-if="row.createTime">
              {{ dayjs(row.createTime).format("YYYY-MM-DD") }}
            </span>
            <span v-else>{{ row.createTime }}</span>
          </template>
        </vxe-column>
        <vxe-column title="优先级" field="priorityCode" width="100">
          <template #default="{ row }">
            <vone-icon-select
              v-model:value="row.priorityCode"
              :data="prioritList"
              filterable
              style="width: 100%"
              class="userList"
              :no-permission="!$permission('reqm_center_require_priority')"
              @change="workitemChange(row, $event, 'priorityCode')"
            >
              <el-option
                v-for="item in prioritList"
                :key="item.key"
                :label="item.name"
                :value="item.code"
              >
                <i
                  :class="`iconfont ${item.icon}`"
                  :style="{
                    color: item.color,
                    fontSize: '16px',
                    paddingRight: '6px',
                  }"
                />
                {{ item.name }}
              </el-option>
            </vone-icon-select>
          </template>
        </vxe-column>

        <vxe-column title="计划开始时间" field="planStime" width="135">
          <template #default="{ row }">
            <span v-if="row.planStime">
              {{ dayjs(row.planStime).format("YYYY-MM-DD") }}
            </span>
            <span v-else>{{ row.planStime }}</span>
          </template>
        </vxe-column>
        <vxe-column title="计划完成时间" field="planEtime" width="135">
          <template #default="{ row }">
            <span v-if="row.planEtime">
              {{ dayjs(row.planEtime).format("YYYY-MM-DD") }}
            </span>
            <span v-else>{{ row.planEtime }}</span>
          </template>
        </vxe-column>

        <vxe-column title="所属项目" field="projectId">
          <template v-slot="{ row }">
            <span v-if="row.echoMap && row.echoMap.projectId">
              {{ row.echoMap.projectId.name }}
            </span>
          </template>
        </vxe-column>
        <vxe-column field="tag" title="标签" width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <span v-for="(item, index) in row.tag" :key="index">
              <el-tag style="margin-right: 6px" type="success">
                {{ item }}
              </el-tag>
            </span>
          </template>
        </vxe-column>
        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <el-tooltip class="item" content="编辑" placement="top">
              <el-button
                type="text"
                :disabled="!$permission('reqm-center-require-edit')"
                :icon="elIconApplicationEdit"
                @click="editBug(row)"
              />
            </el-tooltip>
            <el-divider direction="vertical" />
            <el-tooltip class="item" content="删除" placement="top">
              <el-button
                type="text"
                :disabled="!$permission('reqm-center-require-del')"
                :icon="elIconApplicationDelete"
                @click="deleteBug(row)"
              />
            </el-tooltip>
            <el-divider direction="vertical" />
            <el-dropdown
              trigger="click"
              :hide-on-click="false"
              @command="(e) => e && e(row)"
            >
              <el-button
                type="text"
                :icon="elIconApplicationMore"
                class="operation-dropdown"
              />
              <template v-slot:dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    :icon="elIconApplicationCopy"
                    :command="() => titleCopy(row)"
                  >
                    <span>复制标题</span>
                  </el-dropdown-item>
                  <el-dropdown-item
                    :icon="ElIconDocumentCopy"
                    :command="() => workItemCopy(row)"
                  >
                    <span>复制工作项</span>
                  </el-dropdown-item>
                  <!-- <el-dropdown-item :disabled="row.stateCode == 'CLOSE'" icon="iconfont el-icon-application-type" :command="() => getStatus(row)">
                      <span>变更类型</span>
                    </el-dropdown-item> -->
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <vone-pagination
      ref="pagination"
      :total="tableData.total"
      @update="getInitTableData"
    />

    <!-- 缺陷详情 -->
    <vone-custom-info
      v-bind="defectInfoParam"
      v-if="defectInfoParam.visible"
      v-model:value="defectInfoParam.visible"
      :type-code="'BUG'"
      :left-tabs="leftTabs"
      :right-tabs="rightTabs"
      @success="getInitTableData({}, 'edit')"
    />

    <!-- 新增 -->
    <vone-custom-add
      v-bind="defectParamAdd"
      v-if="defectParamAdd.visible"
      :type-code="'BUG'"
      v-model:value="defectParamAdd.visible"
      :is-tooltip="true"
      :title="defectParamAdd.title"
      @success="getInitTableData({})"
    />

    <!-- 编辑完整缺陷 -->
    <vone-custom-edit
      v-bind="defectParam"
      v-if="defectParam.visible"
      v-model:value="defectParam.visible"
      :type-code="'BUG'"
      :left-tabs="leftTabs"
      :right-tabs="rightTabs"
      @success="getInitTableData({})"
    />
  </div>
</template>

<script>
import defectStatus from "@/views/vone/project/common/change-status/index.vue";
import { catchErr } from "@/utils";
import {
  apiAlmBugInfo,
  apiAlmBugDel,
  apiAlmGetDefectData,
} from "@/api/vone/project/defect";
import { editById, getWorkItemState } from "@/api/vone/project/index";
import {
  apiAlmPriorityNoPage,
  apiAlmGetTypeNoPage,
} from "@/api/vone/alm/index";
import { queryFieldList } from "@/api/common";
import { productListByCondition } from "@/api/vone/project/index";
import {
  apiAlmSourceNoPage,
  requirementListByCondition,
} from "@/api/vone/project/issue";
import { apiAlmProjectPlanNoPage } from "@/api/vone/project/iteration";
import setDataMixin from "@/mixin/set-data";

export default {
  components: {
    defectStatus,
  },
  // props: {
  //   formData: {
  //     type: Object,
  //     default: () => {},
  //   },
  // },
  mixins: [setDataMixin],
  data() {
    return {
      formData: {},
      extraData: {},
      defaultFileds: [],
      tableOptions: {
        isOperation: true,
        isSelection: true,
        hideColumns: [
          "files",
          "code",
          "description",
          "delay",
          "estimatePoint",
          "planStime",
          "projectId",
          "ideaId",
          "sourceCode",
          "typeCode",
          "putBy",
          "leadingBy",
          "testPlanId",
          "rateProgress",
        ], // 默认隐藏列
      },
      pageLoading: false,
      tableData: { records: [] },
      prioritList: [],
      defectInfoParam: { visible: false }, // 详情
      defectParamAdd: { visible: false }, // 新增
      defectParam: { visible: false }, // 编辑
      rightTabs: [
        {
          active: true,
          label: "评论",
          name: "comment",
        },
        {
          label: "活动",
          name: "active",
        },
        {
          label: "变更记录",
          name: "activityRecord",
        },
        {
          label: "工时",
          name: "workTime",
        },
      ],
      copyRow: null,
      leftTabs: [
        {
          label: "关联缺陷",
          name: "DefectToDefect",
        },
      ],
    };
  },
  computed: {
    getUserInfo() {
      return function (row, key) {
        return row && row[key] && row.echoMap[key];
      };
    },
  },
  mounted() {
    this.getQueryFieldList();
  },
  methods: {
    customCopy(command) {
      const _this = this;
      const message =
        command == "title" ? this.copyRow.code : this.copyRow.name;
      this.$copyText(message).then(
        function (e) {
          _this.$message.success(" 已复制到剪贴板！");
        },
        function (e) {
          _this.$message.warning(" 该浏览器不支持自动复制");
        }
      );
      this.copyRow = null;
    },
    visibleChange(e, row) {
      if (e) {
        this.copyRow = row;
      } else {
        this.copyRow = null;
      }
    },

    async getQueryFieldList(typeCodes) {
      const fixedField = [
        "name",
        "handleBy",
        "stateCode",
        "tagId",
        "createTime",
      ];
      const form = {
        projectId: this.$route.params.id,
        typeClassify: "BUG",
        typeCodes: typeCodes,
      };
      const res = await queryFieldList(form);
      if (!res.isSuccess) {
        return;
      }
      const vId = ["productId", "planId"];
      const filter = res.data.filter((r) => r.isSearch && r.key != "projectId");
      filter.forEach((element) => {
        element.isBasicFilter = !fixedField.includes(element.key);
        element.multiple = element.type.code != "ICON";

        element.valueType = vId.includes(element.key) ? "id" : null;
      });
      this.defaultFileds = filter;
      this.getOptions();
    },
    getOptions() {
      this.getPrioritList();
      // this.getIssueType()
      this.getAllStatus();
      this.getDaley();
      this.getsourceCode();
      this.productList();
      this.getplanId();
    },
    getDaley() {
      const delay = [
        { name: "是", code: true },
        { name: "否", code: false },
      ];
      this.setData(this.defaultFileds, "delay", delay);
    },
    // 查询来源
    async getsourceCode() {
      const res = await apiAlmSourceNoPage({
        typeClassify: "BUG",
      });
      if (!res.isSuccess) {
        return;
      }
      this.setData(this.defaultFileds, "sourceCode", res.data);
    },
    // 查优先级
    async getPrioritList(val) {
      const res = await apiAlmPriorityNoPage();
      if (!res.isSuccess) {
        return;
      }
      this.prioritList = res.data;
      this.setData(this.defaultFileds, "priorityCode", res.data);
    },
    // 归属产品
    async productList() {
      const res = await productListByCondition();
      if (!res.isSuccess) {
        return;
      }
      this.setData(this.defaultFileds, "productId", res.data);
    },
    // 查询全部工作流状态
    async getAllStatus() {
      const res = await getWorkItemState({
        projectId: this.$route.params.id,
        typeClassify: "BUG",
      });
      if (!res.isSuccess) {
        return;
      }
      this.setData(this.defaultFileds, "stateCode", res.data);
    },
    // 查询需求类型
    // async getIssueType() {
    //   const res = await apiAlmGetTypeNoPage(this.$route.params.productId, 'BUG')
    //   if (!res.isSuccess) {
    //     return
    //   }
    //   this.typeCodeList = res.data
    //   this.setData(this.defaultFileds, 'typeCode', res.data)
    // },
    // 迭代计划
    async getplanId() {
      // if (this.maps['planId'].length > 0) return
      const res = await apiAlmProjectPlanNoPage({
        projectId: this.$route.params.id || "0",
      });
      if (!res.isSuccess) {
        return;
      }
      this.setData(this.defaultFileds, "planId", res.data);
    },
    // 查项目下需求
    async getRequirementList() {
      // if (this.maps['requirementId']?.length > 0) return
      const res = await requirementListByCondition({
        projectId: this.$route.params.id || "0",
      });
      if (!res.isSuccess) {
        return;
      }
      this.setData(this.defaultFileds, "requirementId", res.data);
    },

    async getInitTableData() {
      const pageObj = this.$refs.pagination?.pageObj || {
        current: 1,
        size: 20,
      };

      this.formData["productId"] = [this.$route.params.productId];
      // this.$set(this.formData, 'typeCode', ['BUG'])
      const params = {
        ...pageObj,
        extra: {},
        model: {
          ...this.formData,
        },
      };
      this.pageLoading = true;
      const [res, err] = await catchErr(apiAlmGetDefectData(params));
      this.pageLoading = false;
      if (err) return;
      if (res.isSuccess) {
        res.data.records.forEach((element) => {
          element.tag =
            element.echoMap && element.echoMap.tags
              ? element.echoMap.tags.map((r) => r.name)
              : [];
        });
        this.tableData = res.data || {};
      } else {
        this.$message.error(res.msg);
      }
    },
    async workitemChange(row, e, t) {
      const params = {
        id: row.id,
      };
      params[t] = e;
      const res = await editById("bug", params);
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      row[t] = e;
      this.$message.success("修改成功");
    },
    showInfo(row) {
      this.rowData = row;
      this.defectInfoParam = {
        visible: true,
        title: "缺陷详情",
        key: Date.now(),
        id: row.id,
        infoDisabled: true,
        tableList: this.tableList,
        rowTypeCode: row.typeCode,
        stateCode: row.stateCode,
      };
    },
    // 更新表格缺陷状态
    async editRowStatus(row, index, table) {
      // 查询当前表格项数据
      const [res, err] = await catchErr(apiAlmBugInfo(row.id));
      if (err) return;
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      if (res.data.tagId && res.data.tagId.length) {
        res.data["tag"] = res.data.echoMap.tagId
          ? res.data.echoMap.tagId.map((r) => r.name)
          : [];
      }
      if (table) {
        table.childrenData.splice(index, 1, res.data);
      } else {
        this.tableData.records.splice(index, 1, res.data);
      }
    },
    // 复制标题到剪贴板
    titleCopy(row) {
      const _this = this;
      this.$copyText(`${row.code} ${row.name}`).then(
        function (e) {
          _this.$message.success(" 已复制到剪贴板！");
        },
        function (e) {
          _this.$message.warning(" 该浏览器不支持自动复制");
        }
      );
    },
    workItemCopy(e) {
      // this.createSimple = false
      this.defectParamAdd = {
        visible: true,
        key: Date.now(),
        infoDisabled: false,
        rowTypeCode: e.typeCode,
        title: "复制缺陷",
        id: e.id,
      };
    },
    editBug(row) {
      this.rowData = row;
      this.defectParam = {
        visible: true,
        title: "编辑缺陷",
        key: Date.now(),
        id: row.id,
        infoDisabled: false,
        tableList: this.tableList,
        rowTypeCode: row.typeCode,
        stateCode: row.stateCode,
      };
    },
    async deleteBug(row) {
      await this.$confirm(`确定删除【${row.name}】吗?`, "删除", {
        type: "warning",
        closeOnClickModal: false,
        customClass: "delConfirm",
        showClose: false,
      });

      const { isSuccess, msg } = await apiAlmBugDel([row.id]);
      if (!isSuccess) {
        this.loading = false;
        this.$message.error(msg);
        return;
      }
      this.$message({
        type: "success",
        message: "删除成功",
        duration: 0,
      });
      this.getInitTableData();
    },
  },
};
</script>

<style lang="scss" scoped>
:deep(.vone-vxe-table .vxe-tree-cell a .iconfont) {
  display: inline-block;
}
:deep(.name_col) {
  .cell {
    display: flex;
    div.name_icon {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: normal;
      word-break: break-all;
      flex: 1;
      display: flex;
      align-items: center;
    }
  }
}
.tag {
  padding: 2px 6px;
  border-radius: 2px;
  background: #f0f0f0;
}
</style>

<style>
.userList .el-input__inner {
  border: 0;
}
.userList .el-input__icon {
  display: none;
}
</style>
