<template>
  <el-row>
    <!-- 右侧用例执行进度 -->
    <div class="progressContainer">
      <process
        ref="process"
        :progress-data="progressData"
        :rate-of-coverage="rateOfCoverage"
        :plan-data="planData"
      />
    </div>
    <el-row class="planContainer">
      <!-- 左侧计划列表 -->
      <el-col :span="6" class="left-aside">
        <el-row
          type="flex"
          align="middle"
          justify="space-between"
          class="testTitle"
        >
          <el-input
            v-model="filterText"
            placeholder="搜索"
            :prefix-icon="ElIconSearch"
          />
        </el-row>
        <div v-loading="leftLoading" class="custom-tree">
          <vone-tree
            ref="treeGroupCase"
            node-key="id"
            class="treeTag"
            :data="dataTree"
            default-expand-all
            highlight-current
            :filter-node-method="filterNode"
            :expand-on-click-node="false"
            :props="defaultProps"
            @node-click="treeCaseClick"
          >
            <template v-slot="{ node, data }">
              <span class="custom-tree-node">
                <span :title="node.label" class="item">
                  <svg
                    v-if="node.level !== 1"
                    class="icon iconCls"
                    aria-hidden="true"
                  >
                    <use xlink:href="#el-icon-wenjianjia2" />
                  </svg>

                  <span v-if="node.level === 1" class="treeNode rootNode">{{
                    node.label
                  }}</span>

                  <span v-else class="treeNode">{{ node.label }}</span>
                </span>
                <span v-if="data.name !== '未分组' && data.num"
                  >({{ data.num }})</span
                >
                <span v-if="data.name !== '未分组'" class="operation-icon">
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="取消关联"
                    placement="top-start"
                  >
                    <el-button
                      type="text"
                      size="mini"
                      :icon="elIconEditUnrelate"
                      @click.stop="() => disassociate(node, data)"
                    />
                  </el-tooltip>
                </span>
              </span>
            </template>
          </vone-tree>
        </div>
      </el-col>
      <el-col :span="18" class="right-aside">
        <vone-search-wrapper>
          <template v-slot:search>
            <vone-search-dynamic
              table-search-key="testm-plan-detail"
              :model="formData"
              :table-ref="$refs['testm-plan-detail']"
              @getTableData="() => getNodeCaseDetail()"
            >
              <el-col :span="12">
                <el-form-item label="用例" prop="name">
                  <el-input
                    v-model="formData.name"
                    placeholder="请输入用例名称"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="优先级" prop="priority">
                  <vone-icon-select
                    v-model:value="formData.priority"
                    :data="prioritList"
                    filterable
                    clearable
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in prioritList"
                      :key="item.key"
                      :label="item.name"
                      :value="item.code"
                    >
                      <i
                        :class="`iconfont ${item.icon}`"
                        :style="{
                          color: item.color,
                          fontSize: '16px',
                          paddingRight: '6px',
                        }"
                      />
                      {{ item.name }}
                    </el-option>
                  </vone-icon-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="是否有执行人" prop="hasExecutor">
                  <el-select
                    v-model="formData.hasExecutor"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option label="是" value="0" />
                    <el-option label="否" value="1" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="执行人" prop="execBy">
                  <vone-remote-user v-model:value="formData.execBy" />
                </el-form-item>
              </el-col>
            </vone-search-dynamic>
          </template>
          <template v-slot:actions>
            <el-button
              :icon="elIconEditRelate"
              :disabled="
                planStateId == 2 || !$permission('testm_plan_relation')
              "
              @click="openTestCase"
              >关联用例</el-button
            >
            <el-button
              :icon="elIconEditUnrelate"
              :disabled="
                planStateId == 2 || !$permission('testm_plan_relation_cancel')
              "
              @click="unLink"
              >取消关联</el-button
            >
            <el-button
              :icon="ElIconVideoPlay"
              :disabled="planStateId != 1 || !$permission('testm_plan_excute')"
              @click="showTestDetails"
              >执行用例</el-button
            >
            <el-button
              :icon="ElIconVideoPlay"
              :disabled="planStateId != 1 || !$permission('testm_plan_excute')"
              @click="showBatchTest"
              >批量执行</el-button
            >
            <el-button
              :icon="elIconApplicationAppoint"
              :disabled="planStateId == 2 || !$permission('testm_plan_assign')"
              @click="setGroup"
              >批量指派</el-button
            >
          </template>
        </vone-search-wrapper>

        <div style="height: calc(100vh - 386px)">
          <vxe-table
            ref="planTable-detail"
            class="vone-vxe-table draggTable caseTable"
            border
            height="auto"
            show-overflow="tooltip"
            :loading="tableLoading"
            :empty-render="{ name: 'empty' }"
            :data="tableData.records"
            :column-config="{ minWidth: '120px' }"
            :checkbox-config="{ reserve: true }"
            row-id="id"
          >
            <vxe-column
              type="checkbox"
              width="36"
              fixed="left"
              align="center"
            />
            <vxe-column
              title="用例"
              field="name"
              min-width="320px"
              show-overflow-tooltip
            >
              <template v-slot="{ row }">
                <el-icon class="iconfont" style="color: #37cdde"
                  ><el-icon-application-view-list
                /></el-icon>
                <el-icon style="color: #3e7bfa"><el-icon-document /></el-icon>
                <span style="margin-left: 4px">{{ row.name }}</span>
              </template>
            </vxe-column>

            <vxe-column title="版本" field="version">
              <template v-slot="{ row }">
                {{ row.version }}
              </template>
            </vxe-column>
            <vxe-column title="优先级" field="priority" width="120">
              <template v-slot="{ row }">
                <vone-icon-select
                  v-model:value="row.priority"
                  :data="prioritList"
                  filterable
                  :no-permission="
                    planStateId == 2 || !$permission('testm_plan_usecase_eidt')
                  "
                  style="width: 100%"
                  @change="changePriority(row)"
                >
                  <el-option
                    v-for="item in prioritList"
                    :key="item.key"
                    :label="item.name"
                    :value="item.code"
                  >
                    <i
                      :class="`iconfont ${item.icon}`"
                      :style="{
                        color: item.color,
                        fontSize: '16px',
                        paddingRight: '6px',
                      }"
                    />
                    {{ item.name }}
                  </el-option>
                </vone-icon-select>
              </template>
            </vxe-column>
            <vxe-column
              title="执行人"
              field="execBy"
              width="120"
              show-overflow-tooltip
            >
              <template v-slot="{ row }">
                <span v-if="row.execBy && row.userData">
                  <vone-user-avatar
                    :avatar-path="row.userData.avatarPath"
                    :name="row.userData.name"
                  />
                </span>
              </template>
            </vxe-column>
            <vxe-column title="执行结果" width="120">
              <template v-slot="{ row }">
                <el-tag v-if="row.status === 'system'" :class="row.status"
                  >成功</el-tag
                >
                <el-tag v-else-if="row.status === 'smoke'" :class="row.status"
                  >失败</el-tag
                >
                <el-tag v-else-if="row.status === 'block'" :class="row.status"
                  >阻塞</el-tag
                >
                <el-tag v-else-if="row.status === 'skip'" :class="row.status"
                  >跳过</el-tag
                >
                <el-tag v-else class="untest">未测</el-tag>
              </template>
            </vxe-column>
            <vxe-column title="操作" fixed="right" align="left" width="120">
              <template #default="{ row }">
                <el-tooltip class="item" content="取消关联" placement="top">
                  <el-button
                    type="text"
                    :disabled="isDisabled(row)"
                    :icon="elIconApplicationDelete"
                    @click="deleteRow(row)"
                  />
                </el-tooltip>
              </template>
            </vxe-column>
          </vxe-table>
          <vone-pagination
            ref="pagination"
            :total="tableData.total"
            @update="getNodeCaseDetail"
          />
        </div>
      </el-col>
    </el-row>

    <relateCase
      v-model:value="releaseVisible"
      :library-id="$route.params.libraryId"
      @success="saveReleaseTreeAndCases"
    />
    <!-- 分组指派 -->
    <setGroupDialog
      v-bind="setGroupParam"
      v-if="setGroupParam.visible"
      v-model:value="setGroupParam.visible"
      :current-casekey="currentCasekey"
      @success="appointSuccess"
    />
    <!-- 批量执行弹窗 -->
    <el-dialog
      title="批量执行"
      :model-value="batchExcuteVisible"
      width="400px"
      append-to-body
      :before-close="onClose"
      :close-on-click-modal="false"
    >
      <el-row type="flex" align="middle" justify="center" class="excuteContent">
        <el-button
          type="success"
          :loading="excuting"
          :icon="elIconTipsCheckCircleFill"
          class="system"
          @click="saveCaseExecuteResult('system')"
          >通过</el-button
        >
        <el-button
          type="danger"
          :loading="excuting"
          :icon="elIconTipsCloseCircleFill"
          class="smoke"
          @click="saveCaseExecuteResult('smoke')"
          >失败</el-button
        >
        <el-button
          type="warning"
          :loading="excuting"
          :icon="elIconTipsMinusCircleFill"
          class="block"
          @click="saveCaseExecuteResult('block')"
          >阻塞</el-button
        >
        <el-button
          type="primary"
          :loading="excuting"
          :icon="elIconShijian1"
          class="skip"
          @click="saveCaseExecuteResult('skip')"
          >跳过</el-button
        >
      </el-row>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="batchExcuteVisible = false">取消</el-button>
        </span>
      </template>
    </el-dialog>
  </el-row>
</template>

<script>
import dayjs from "dayjs";
import storage from "store";

import {
  queryPlanById,
  executePlanCase,
  queryAllCaseNumberOfPlanTree,
} from "@/api/vone/testplan";
import {
  deletePlanReleaseCases,
  getPlanCasesList,
  getPlanExcuteDetail,
  savePlanReleaseCases,
  getTotalTreeByPlanId,
  deleteTestPlanCaseByTreeId,
} from "@/api/vone/testmanage/plan";
import { updatePrioritys } from "@/api/vone/testmanage/case";

import { catchErr } from "@/utils";

import process from "./components/progress.vue";
import relateCase from "./components/plan-release-case.vue";
import setGroupDialog from "./components/set-group.vue";

export default {
  components: {
    process,
    relateCase,
    setGroupDialog,
  },
  data() {
    return {
      treeCountMap: {},
      formData: {
        name: "",
        caseKey: "",
        priority: "",
        hasExecutor: "",
      },
      tableData: {
        records: [],
        total: 0,
      },
      filterText: "",
      tableLoading: false, // 表格加载
      leftLoading: false, // 左侧树加载
      originTree: [], // 原始树
      dataTree: [],
      selectedTable: [], // 选中的表格数据
      currentCasekey: "",
      menuNode: {},
      defaultProps: {
        children: "children",
        label: "name",
      },
      planData: {}, // 选择的计划数据
      planVisibile: false, // 新增计划弹窗
      userMap: {},
      progressData: {}, // 计划进度数据
      rateOfCoverage: 0,
      prioritList: [
        {
          name: "最高",
          code: 5,
          icon: "el-icon-icon-dengji-zuigao2",
          color: "#FA6A69",
        },
        {
          name: "较高",
          code: 4,
          icon: "el-icon-icon-dengji-jiaogao2",
          color: "#FA8669",
        },
        {
          name: "普通",
          code: 3,
          icon: "el-icon-icon-dengji-putong2",
          color: "var(--main-theme-color,#3e7bfa)",
        },
        {
          name: "较低",
          code: 2,
          icon: "el-icon-icon-dengji-jiaodi2",
          color: "#5ACC5E",
        },
        {
          name: "最低",
          code: 1,
          icon: "el-icon-icon-dengji-zuidi2",
          color: "#4ECF95",
        },
      ],
      releaseVisible: false,
      batchExcuteVisible: false, // 批量执行弹窗
      excuting: false, // 批量执行中
      setGroupParam: {
        visible: false,
      },
    };
  },
  computed: {
    planId() {
      return this.$route.params.planId;
    },
    planStateId() {
      return this.$route.query.stateId;
    },
    loginUser() {
      return storage.get("user");
    },
  },
  watch: {
    filterText(val) {
      // 监听的是左侧的计划列表,和下边filterNode方法配合
      this.$refs.treeGroupCase.filter(val);
    },
  },
  async created() {
    this.getPlanCaseStatus();
    this.getPlanDetail();
    await this.getTreeCount();
    this.getTreeData();
  },
  methods: {
    format_filter(val) {
      if (!val) return "";
      return dayjs(val).format("MM月DD日");
    },
    onClose() {
      this.batchExcuteVisible = false;
    },
    isDisabled() {
      return (
        this.planStateId == 2 || !this.$permission("testm_plan_relation_cancel")
      );
    },
    // 获取以关联的树id
    // async getRelateCase() {
    //   const res = await testPlanCaseTree({ planId: this.planId })
    //   this.treeData = res.data
    // },
    async disassociate(node, e) {
      await this.$confirm(`确定取消关联当前分组下的所有用例吗?`, "提示", {
        type: "warning",
      });
      const params = {
        planId: this.planId,
        treeId: e.id,
      };
      const res = await deleteTestPlanCaseByTreeId(params);
      if (!res.isSuccess) {
        this.$message.error(res.msg);
        return;
      }
      this.$message.success("取消关联成功");
      this.getPlanCaseStatus(); // 更新里程碑
      this.getNodeCaseDetail();
      await this.getTreeCount();
      this.getTreeData(this.menuNode);
    },
    async getTreeCount() {
      this.leftLoading = true;
      const res = await queryAllCaseNumberOfPlanTree(this.$route.params.planId);
      if (!res.isSuccess) {
        return;
      }
      this.treeCountMap = res.data;
    },
    async getTreeData(currentNode) {
      const [res, err] = await catchErr(getTotalTreeByPlanId(this.planId));
      this.leftLoading = false;
      if (err) return;
      if (res.isSuccess) {
        if (this.treeCountMap) {
          const setTreeNum = (children) => {
            children.map((child) => {
              child.num = this.treeCountMap[child.id]?.[0]?.count;
              if (child.children) {
                setTreeNum(child.children);
              }
            });
          };
          setTreeNum(res.data);
        }
        this.dataTree = res.data;
        this.menuNode = currentNode || res.data[0];
        this.currentCasekey = this.menuNode.id;

        this.$nextTick(() => {
          this.$refs.treeGroupCase.setCurrentKey(this.currentCasekey);
          // 查询右侧表格数据
          this.getNodeCaseDetail(this.currentCasekey);
        });
      }
    },
    // 查询测试计划数据
    async getPlanDetail() {
      const [res, err] = await catchErr(queryPlanById(this.planId));
      if (err) return;
      if (res.isSuccess) {
        this.planData = res.data;
      }
    },
    // 查询计划用例状态
    async getPlanCaseStatus() {
      const params = {
        planId: this.planId,
      };
      const [res, err] = await catchErr(getPlanExcuteDetail(params));
      if (err) return;
      if (res.isSuccess) {
        this.progressData = res.data;
        this.rateOfCoverage = res.data.echoMap.rateOfCoverage;
        const { blockingNum, skipNum, smokeNum, systemNum, undoNum } = res.data;
        this.progressData.totalSum =
          Number(blockingNum) +
          Number(skipNum) +
          Number(smokeNum) +
          Number(systemNum) +
          Number(undoNum);
      }
    },
    // 点击节点查询数据
    treeCaseClick(data) {
      this.currentCasekey = data.id;
      this.menuNode = data;
      this.getNodeCaseDetail();
    },
    // 模糊搜索定位节点
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    // 查人员
    async getUserList(userId, row) {
      if (this.userMap[userId]) {
        row["userData"] = this.userMap[userId];
        return;
      }
      const res = await this.$store.dispatch("user/getUserData", userId);
      if (!res.isSuccess) {
        return;
      }
      this.userMap[userId] = res.data;
      row["userData"] = res.data;
    },
    async deleteRow(row) {
      await this.$confirm("确定取消关联当前项吗？", "提示", {
        type: "warning",
        closeOnClickModal: false,
      });
      const currentNode = this.menuNode;
      const params = {
        planId: this.planId,
        testCaseIds: [row.testcaseId],
      };
      const [res, err] = await catchErr(deletePlanReleaseCases(params));
      if (err) return;
      if (!res.isSuccess) {
        this.$message.error(res.msg);
        return;
      }
      this.$message.success("取消关联成功");
      this.getPlanCaseStatus(); // 更新里程碑
      this.getNodeCaseDetail();
      await this.getTreeCount();
      this.getTreeData(currentNode);
    },
    // 表格选择
    selectChange(value) {
      this.selectedTable = value;
    },
    // 查询当前分组下详细用例
    async getNodeCaseDetail(nodeId) {
      if (!this.menuNode.id) return;
      const tableAttr = this.$refs.pagination?.pageObj || {
        current: 1,
        size: 20,
      };
      const params = {
        ...tableAttr,
        extra: {},
        model: {
          ...this.formData,
          planId: this.planId,
          treeId: nodeId || this.menuNode.id,
        },
      };
      this.tableLoading = true;
      const [res, err] = await catchErr(getPlanCasesList(params));
      this.tableLoading = false;
      if (err) return;
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      Array.isArray(res.data.records) &&
        res.data.records.forEach((ele) => {
          ele.execBy && this.getUserList(ele.execBy, ele);
        });
      this.tableData = res.data;
      this.tableData.total = res.data.total;
    },
    // 执行用例
    showTestDetails(row) {
      this.$router.push({
        path: `/test/plan/excute/${this.planId}`,
        query: {
          caseId: row ? row.id : "",
          bug: this.$route.query.bug,
        },
      });
    },
    // 批量执行
    showBatchTest() {
      this.selectedTable = this.getVxeTableSelectData("planTable-detail");
      if (this.selectedTable.length === 0) {
        this.$message.warning("请勾选批量执行的用例");
        return;
      }
      this.batchExcuteVisible = true;
    },
    async saveCaseExecuteResult(excute) {
      this.excuting = true;
      const params = this.selectedTable.map((v) => {
        return {
          expectedResult: v.echoMap && v.echoMap.result,
          id: v.testcaseId,
          version: v.version,
        };
      });
      const res = await executePlanCase(this.planId, excute, params);
      if (!res.isSuccess) {
        this.$message.error(res.msg);
        return;
      }
      this.$message.success("批量执行成功");
      this.excuting = false;
      this.batchExcuteVisible = false;
      this.getNodeCaseDetail();
      this.getPlanCaseStatus();
    },
    openTestCase() {
      this.releaseVisible = true;
    },
    // 取消关联
    async unLink() {
      this.selectedTable = this.getVxeTableSelectData("planTable-detail");
      if (this.selectedTable.length > 0) {
        await this.$confirm("确定取消关联吗?", "提示", {
          type: "warning",
        });
        const currentNode = this.menuNode;
        const params = {
          planId: this.planId,
          testCaseIds: this.selectedTable.map((item) => item.testcaseId),
        };
        const [res, err] = await catchErr(deletePlanReleaseCases(params));
        if (err) return;
        if (!res.isSuccess) {
          this.$message.error(res.msg);
          return;
        }
        this.$message.success("取消关联成功");
        this.getPlanCaseStatus(); // 更新里程碑
        this.getNodeCaseDetail();
        await this.getTreeCount();
        this.getTreeData(currentNode);
      } else {
        this.$message.warning("请勾选要取消关联的用例");
      }
    },
    // 分组指派
    setGroup() {
      this.selectedTable = this.getVxeTableSelectData("planTable-detail");
      if (!this.selectedTable.length) {
        this.$message.warning("请选择用例");
        return;
      }
      this.setGroupParam = { visible: true, selectData: this.selectedTable };
    },
    appointSuccess({ users }) {
      // 缓存用户信息
      users?.forEach((item) => {
        this.userMap[item.id] = this.userMap[item.id] || item;
      });
      this.getNodeCaseDetail();
    },
    // 保存关联的用例数据
    async saveReleaseTreeAndCases({ caseIds, treeMap, execBy }) {
      const currentNode = this.menuNode;
      const params = {
        createdBy: this.loginUser.id,
        execBy: execBy,
        connectTreeId: [], // treeIds
        caseAndTreeReturnVo: treeMap,
        planId: this.planId,
        testcaseIds: caseIds,
      };
      const [res, err] = await catchErr(savePlanReleaseCases(params));
      if (err) return;
      if (res.isSuccess) {
        this.$message.success("关联成功");
        this.getNodeCaseDetail();
        this.getPlanCaseStatus();
        await this.getTreeCount();
        this.getTreeData(currentNode);
      } else {
        this.$message.error(res.msg);
      }
    },
    // 修改优先级
    async changePriority(row) {
      const [res, err] = await catchErr(
        updatePrioritys({
          planId: row.planId,
          priority: row.priority,
          testcaseId: row.testcaseId,
        })
      );
      if (err) return;
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }
      this.$message.success("修改成功");
      this.getNodeCaseDetail();
    },
  },
};
</script>

<style lang="scss" scoped>
.planContainer {
  background-color: var(--main-bg-color, #fff);
  border: 1px solid #ebeef5;
  border-radius: 4px;
}
.progressContainer {
  border-radius: 4px;
  margin-bottom: 16px;
}
.left-aside {
  border-right: 1px solid #ebeef5;
}
.right-aside {
  padding: 16px;
}
.testTitle {
  height: 66px;
  padding: 16px;
  border-bottom: 1px solid #ebeef5;
}
.custom-tree {
  height: calc(100vh - 336px);
  padding: 16px 0 0 16px;
  overflow: auto;
  .custom-tree-node {
    // flex: 1;
    display: flex;
    // align-items: center;
    // justify-content: space-between;
    // width: fit-content;
    & > span {
      flex: 1 1 auto;
      display: flex;
      align-items: center;
    }
    .iconCls {
      margin-right: 3px;
    }

    .treeNode {
      // margin-left: 6px;
      flex: 1 1 auto;
    }
  }
  :deep(.el-tree-node > .el-tree-node__children) {
    overflow: unset;
  }
  .el-tree-node__content {
    position: relative;
    .operation-icon {
      opacity: 0;
      position: absolute;
      right: 0;
      padding-left: 5px;
    }
    &:hover {
      .operation-icon {
        opacity: 1;
        background-color: var(--hover-bg-color, #f5f6fa);
        outline: none;
      }
    }
  }
}
:deep(.el-tree-node__content) {
  /*// min-width: min-content;*/
}
.caseTable :deep() {
  .has-header-view {
    height: calc(100vh - 230px);
  }
  .pagination {
    max-height: 32px;
  }
}
.excuteContent {
  width: 100%;
  background-color: var(--main-bg-color);
}
.custom-theme-dark {
  .planContainer {
    background-color: #252933;
    border-color: #252933;
    .left-aside,
    .testTitle {
      border-color: #495266;
    }
  }
}
.system {
  color: #fff;
  background-color: #3cb540;
  border-color: #3cb540;
}
.smoke {
  color: #fff;
  background-color: #fa6b57;
  border-color: #fa6b57;
}
.block {
  color: #fff;
  background-color: #ffbf47;
  border-color: #ffbf47;
}
.skip {
  color: #fff;
  background-color: #bd7ffa;
  border-color: #bd7ffa;
}
.untest {
  color: #fff;
  background-color: #adb0b8;
  border-color: #adb0b8;
}
</style>
