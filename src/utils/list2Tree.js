/**
 * 数组转树形结构
 * @param {Array} data 列表数据
 * @param {string} childrenKey 树结构子节点的Key
 * @param {string} key 节点ID的Key
 * @param {string} parentKey 节点父ID的Key
 */
export function list2Tree(
  data = [],
  { childrenKey = 'children', key = 'id', parentKey = 'parentId' } = {}
) {
  const map = {}
  const root = []
  data.forEach(node => {
    map[node[key]] = node
    delete node[childrenKey]
  })

  data.forEach(node => {
    if (parentKey in node) {
      const parent = map[node[parentKey]]
      if (!parent) {
        root.push(node)
        return
      }
      parent[childrenKey] = parent[childrenKey] || []
      parent[childrenKey].push(node)
    } else {
      root.push(node)
    }
  })
  return root
}
