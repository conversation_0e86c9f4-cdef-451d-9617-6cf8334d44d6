<template>
  <div style="height: calc(100vh - 398px); position: relative">
    <vone-search-wrapper>
      <template v-slot:actions>
        <el-button type="primary" @click="add">新增</el-button>
      </template>
    </vone-search-wrapper>
    <div style="height: calc(100vh - 528px)">
      <vxe-table
        ref="trends-table"
        class="vone-vxe-table"
        height="auto"
        border
        auto-resize
        resizable
        :loading="tableLoading"
        show-overflow="tooltip"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <template>
          <vxe-column
            show-overflow-tooltip
            field="name"
            title="名称"
            fixed
            min-width="400"
            class-name="name_col"
          >
            <template v-slot="{ row }">
              <span
                v-if="!row.hasChildren"
                style="width: 10px; opacity: 0; display: inline-block"
              />
              {{ row.name }}
            </template>
          </vxe-column>
          <vxe-column field="description" title="描述" />
          <vxe-column title="操作" fixed="right" align="left" width="120">
            <template #default="{ row }">
              <el-tooltip class="item" content="新增" placement="top">
                <el-button
                  type="text"
                  :disabled="row.type !== 1 ? true : false"
                  :icon="elIconTipsPlusCircle"
                  @click="add(row)"
                />
              </el-tooltip>
              <el-divider direction="vertical" />

              <el-tooltip class="item" content="编辑" placement="top">
                <el-button
                  type="text"
                  :icon="elIconApplicationEdit"
                  @click="edit(row)"
                />
              </el-tooltip>
              <el-divider direction="vertical" />

              <el-tooltip class="item" content="删除" placement="top">
                <el-button
                  type="text"
                  :icon="elIconApplicationDelete"
                  @click="del(row)"
                />
              </el-tooltip>
            </template>
          </vxe-column>
        </template>
      </vxe-table>
    </div>

    <vone-pagination
      ref="pagination"
      :total="tableData.total"
      @update="getInitTableData"
    />

    <el-dialog
      title="新增"
      :model-value="addVisible"
      :close-on-click-modal="false"
      @close="close"
    >
      <el-form
        ref="addForm"
        :model="addForm"
        :rules="rules"
        label-position="top"
      >
        <el-form-item label="名称" prop="name">
          <el-input v-model="addForm.name" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="addForm.description" placeholder="请输入" />
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <div>
          <el-button @click="close">取 消</el-button>
          <el-button type="primary" @click="hold">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  getEngineJenkinsCloudList,
  addEngineJenkinsCloudList,
  delEngineJenkinsCloudList,
  editEngineJenkinsCloudList,
  getEngineJenkinsCloudListNoPage,
} from "@/api/vone/base/engine";
export default {
  props: {
    id: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      tableLoading: false,
      tableData: {},
      tableOptions: {
        isOperation: true,
        isSelection: true,
        operation: {
          isFixed: true,
          // 表格有操作列时设置
          label: "操作", // 列名
          width: "120", // 根据实际情况给宽度
          data: [
            {
              type: "icon", // 为icon则是图标
              label: "新增", // 功能名称
              hidden: (index, row) => {
                return row.type !== 1;
              },
              icon: "iconfont el-icon-tips-plus-circle", // icon class
              handler: this.add, // 操作事件
            },
            // 功能数组
            {
              type: "icon",
              label: "编辑",
              icon: "iconfont el-icon-application-edit",
              handler: this.edit,
            },
            {
              type: "icon", // 为icon则是图标
              label: "删除", // 功能名称
              icon: "iconfont el-icon-application-delete", // icon class
              handler: this.del, // 操作事件
            },
          ],
          // 更多操作按钮
          moreData: [],
        },
      },
      issueInfoParam: {
        // 详情
        visible: false,
      },
      addVisible: false,
      addForm: {},
      rules: {
        name: [{ required: true, message: "请输入名称" }],
      },
      addParam: false,
      node_had: {},
      resolve: null,
    };
  },

  mounted() {
    if (this.id) this.getInitTableData();
  },
  methods: {
    add(row) {
      if (!this.id) {
        this.$message.warning("请先保存JenkinsMaster引擎，再配置节点");
        return;
      }
      this.addVisible = true;
      this.addForm = {};
      this.addParam = true;
      this.addForm.type = row.type;
      this.addForm.id = row.id;
    },
    close() {
      this.addVisible = false;
      this.addParam = false;
    },
    edit(row) {
      this.addForm = { ...row };
      this.addVisible = true;
    },
    async hold() {
      try {
        await this.$refs.addForm.validate();
      } catch (e) {
        return;
      }
      if (this.addParam) {
        // 新增
        const params = {
          name: this.addForm.name,
          description: this.addForm.description,
          engineId: this.id,
          parentId: this.addForm.id || 0,
          type: this.addForm.type == 1 ? 2 : 1,
        };
        addEngineJenkinsCloudList(params).then((res) => {
          if (res.isSuccess) {
            this.addVisible = false;
            this.$message.success("新增成功");
            if (this.addForm.id) {
              this.reGetTableData();
            } else {
              this.getInitTableData();
            }
          } else {
            this.$message.warning(res.msg);
          }
        });
      } else {
        // 编辑
        editEngineJenkinsCloudList(this.addForm).then((res) => {
          if (res.isSuccess) {
            this.$message.success("编辑成功");
            if (this.addForm.type == 1) {
              this.getInitTableData();
            } else {
              this.reGetTableData();
            }
            this.addVisible = false;
          } else {
            this.$message.warning(res.msg);
          }
        });
      }
    },
    reGetTableData() {
      this.load(this.node_had, "", this.resolve);
    },
    load(tree, treeNode, resolve) {
      this.node_had = tree;
      this.resolve = resolve;
      getEngineJenkinsCloudListNoPage({
        engineId: this.id,
        parentId: tree.id,
      }).then((res) => {
        const list = res.data;
        resolve(list);
      });
    },
    async del(row) {
      await this.$confirm(`确定删除吗?`, "删除", {
        type: "warning",
        closeOnClickModal: false,
      });

      const { isSuccess, msg } = await delEngineJenkinsCloudList([row.id]);
      if (!isSuccess) {
        this.loading = false;
        this.$message.error(msg);
        return;
      }
      this.$message.success("删除成功");
      if (row.type == 1) {
        this.getInitTableData();
      } else {
        this.reGetTableData();
      }
    },
    // 初始化进入页面列表
    async getInitTableData() {
      this.tableLoading = true;
      let params = {};
      const tableAttr = this.$refs?.pagination?.pageObj || {
        current: 1,
        size: 20,
      };
      params = {
        ...tableAttr,
        extra: {},
        model: { engineId: this.id, parentId: 0 },
      };
      const res = await getEngineJenkinsCloudList(params);
      this.tableLoading = false;
      if (!res.isSuccess) {
        this.$message.warning(res.msg);
        return;
      }

      res.data.records.forEach((item) => {
        if (item.type == "1") {
          item.hasChildren = true;
        }
      });
      this.tableData = res.data;
    },
  },
};
</script>
