<template>
  <page-wrapper>
    <div>
      <el-alert
        title="新增租户步骤：1. 添加租户 2.点击操作列: 初始化连接 3.点击用户页面去新增一个超级管理员账号 4.将刚创建的超级管理员提供给租户，登录Vone"
        type="info"
        :closable="false"
      />
    </div>

    <el-row style="margin-top: 10px">
      <div>
        <el-input
          v-model="name"
          style="width: 200px; margin: 0px 10px"
          placeholder="企业编码"
        />
        <el-input
          v-model="code"
          style="width: 200px; margin: 0px 10px"
          placeholder="企业名称"
        />
        <el-date-picker
          v-model="time"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
        <el-button style="margin: 0px 10px" @click="searchTableClick">
          搜索
        </el-button>
        <el-button style="margin: 0px 10px"> 重置 </el-button>
        <el-button type="primary" style="margin: 0px 10px" @click="adddNew">
          添加
        </el-button>
      </div>
    </el-row>
    <div style="height: calc(100vh - 224px); margin-top: 16px">
      <vxe-table
        ref="tenant-table"
        class="vone-vxe-table"
        border
        height="auto"
        show-overflow="tooltip"
        :loading="tableLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ resizable: true, minWidth: 120 }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column title="企业编码" field="code" />
        <!-- <vxe-column title="企业名称" field="name" /> -->
        <vxe-column title="责任人" field="user" />
        <vxe-column title="类型" field="name" />
        <vxe-column title="状态" field="state" />
        <vxe-column title="有效期" field="time" />
        <vxe-column title="内置" field="readOnly" />
        <vxe-column title="创建时间" field="createTime" />
      </vxe-table>
    </div>
    <vone-pagination
      ref="pagination"
      :total="tableData.total"
      @update="getLogList"
    />

    <addDialog
      v-bind="addTenantParams"
      v-model:value="addTenantParams.visible"
      @success="refreshTable"
    />
  </page-wrapper>
</template>

<script>
import { apiBaseTenantDel, apiBaseTenantList } from '@/api/vone/base/tenant'
import addDialog from './addDialog.vue'
export default {
  components: {
    addDialog,
  },
  data() {
    return {
      tableLoading: false,
      tableData: {},
      addTenantParams: { visible: false },
      paramsData: {
        name: '',
        code: '',
        time: '',
      },
      name: '',
      code: '',
      time: '',
      // 表格配置项
      tableOptions: {
        isSelection: true, // 表格有多选时设置
        isOperation: true, // 表格有操作列时设置
        isIndex: false, // 列表序号
        operation: {
          isFixed: true, // 是否固定在右侧
          // 表格有操作列时设置
          label: '操作', // 列名
          width: '100', // 根据实际情况给宽度
          data: [
            // 功能数组
            {
              type: 'icon', // 为icon则是图标
              label: '编辑', // 功能名称
              icon: 'iconfont el-icon-application-edit', // icon class
              handler: this.editRow, // 操作事件
            },
            {
              type: 'icon', // 为icon则是图标
              label: '删除', // 功能名称
              icon: 'iconfont el-icon-application-delete', // icon class
              handler: this.delRow, // 操作事件
            },
          ],
          // 更多操作按钮,如果按钮超过3个，从第三个开始需要添加在moreData中
          moreData: [],
        },
      },
    }
  },
  mounted() {
    this.getLogList()
  },
  methods: {
    async getLogList() {
      this.tableLoading = true
      const pageObj = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
      const params = {
        ...pageObj,
        extra: {},
        model: { ...this.formData },
      }
      const res = await apiBaseTenantList(params)
      this.tableLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.tableData = res.data
    },
    searchTableClick() {},
    // 新增
    adddNew() {
      this.addTenantParams = { visible: true, title: '新增' }
    },
    // 编辑
    editRow(item) {
      this.addTenantParams = { visible: true, title: '编辑', id: item.id }
    },
    async delRow(item) {
      await this.$confirm(`确定删除【${item.name}】吗?`, '删除', {
        type: 'warning',
      })
      const res = await apiBaseTenantDel([item.id])
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success(res.msg)
      this.refreshTable(true)
    },
    // 刷新表格
    refreshTable(isReset) {
      this.$refs['tenant-table'].getTableData(isReset)
    },
  },
}
</script>
