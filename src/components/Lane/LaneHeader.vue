<template>
  <div class="lane-top" :style="{'border-color': item.kanbanColumnStatuses[0].echoMap.stateCode.color}">
    <div class="lane-top__header">
      <div class="lane-top__title">
        <template v-if="$slots.title">
          <slot name="title" />
        </template>
        <template v-else>
          <div
            class="lane-top__text"
            :style="{
              'border-color': item.kanbanColumnStatuses[0].echoMap.stateCode.color,
              color: item.kanbanColumnStatuses[0].echoMap.stateCode.color
            }"
          >
            {{ title }}
          </div>
        </template>
        <span
          class="lane-top__list_len"
          :style="{color: item.kanbanColumnStatuses[0].echoMap.stateCode.color}"
        >
          {{ list ? list.length : 0 }}
        </span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => { [] }
    },
    title: {
      type: String,
      default: ''
    },
    item: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
    }
  },
  computed: {
  },
  methods: {
  }
}
</script>
<style lang="scss" scoped>
.lane-top {
  display: flex;
  flex-direction: column;
  flex: 0 0 248px;
  border-radius: var(--border-radius-base);
  transition: var(--all-transition);
  // min-height: 65vh;
  box-shadow: var(--box-shadow-light);
	max-width: 248px;
  height: 50px;
  line-height: 42px;
  border: 1px solid #ADB0B8;
  border-top: 4px solid #ADB0B8;
  // margin-bottom: 6px;
  border-radius: 4px 4px 0 0;
  padding: 0 12px;
  & + & {
    margin-left: 16px;
  }
  &__ {
    &header {
      display: flex;
    }
    &title {
      flex: 1;
      display: flex;
      justify-items: center;
      align-items: center;
    }
    &text{
      height: 26px;
      line-height: 18px;
      padding: 4px 8px;
      border: 1px solid #ADB0B8;
      border-radius: 4px;
      font-size: 12px;
      text-align: center;
      color: #ADB0B8;
    }
    &list_len {
      flex: 1;
      border-radius: var(--border-radius-base);
      background-color: var(--border-color-hover);
      color: #ADB0B8;
      text-align: right;
      font-size: 16px;
    }
  }
}
</style>
