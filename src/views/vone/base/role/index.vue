<template>
  <page-wrapper>
    <vone-search-wrapper>
      <template v-slot:search>
        <el-tabs v-model="type" class="vone-tabs" style="margin-right: 12px">
          <el-tab-pane label="平台角色" name="platFormRole" />
          <el-tab-pane label="项目角色" name="projectRole" />
        </el-tabs>
        <vone-search-dynamic
          v-if="type == 'platFormRole'"
          ref="searchForm"
          table-search-key="baseRoleTable"
          :model="formData"
          :default-fileds="defaultFileds"
          show-basic
          :extra="extraData"
          @getTableData="getList"
        />
        <vone-search-dynamic
          v-else
          ref="searchForm"
          table-search-key="baseProjectRoleTable"
          :model="formData"
          show-basic
          :extra="extraData"
          :default-fileds="defaultFileds"
          @getTableData="getList"
        />
      </template>
      <template v-slot:actions>
        <el-button
          type="primary"
          :icon="elIconTipsPlusCircle"
          :disabled="!$permission('base_role_add')"
          @click="openAddRole"
        >
          新增
        </el-button>
        <el-dropdown trigger="click" @command="(e) => e && e()">
          <el-button class="btnMore"
            ><el-icon class="iconfont"><el-icon-application-more /></el-icon
          ></el-button>
          <template v-slot:dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="(item, index) in actions"
                :key="index"
                :icon="item.icon"
                :command="item.fn"
                :disabled="item.disabled"
              >
                {{ item.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
      <template v-slot:fliter>
        <vone-search-filter
          ref="searchFilter"
          :extra="extraData"
          :model="formData"
          :default-fileds="defaultFileds"
          @getTableData="getList"
        />
      </template>
    </vone-search-wrapper>
    <components
      :is="type"
      :ref="type == 'platFormRole' ? 'baseRoleTable' : 'baseProjectRoleTable'"
      :form-data="formData"
      :extra-data="extraData"
      :table-height="tableHeight"
    />
    <!-- 新增编辑弹窗 -->
    <add-or-edit
      v-if="dialogVisible"
      ref="editDialog"
      :now-row="nowRow"
      :type="type"
      v-model:dialog-visible="dialogVisible"
      :action-type="actionType"
      @success="getList"
    />
  </page-wrapper>
</template>

<script>
import addOrEdit from "./addOrEdit.vue";
import projectRole from "./table/projectRole.vue";
import platFormRole from "./table/platFormRole.vue";
import getTableHeight from "@/utils/search-filter";
export default {
  components: {
    addOrEdit,
    projectRole,
    platFormRole,
  },
  data() {
    return {
      extraData: {},
      defaultFileds: [
        {
          key: "name",
          name: "名称",
          type: {
            code: "INPUT",
          },
          placeholder: "请输入名称",
        },
      ],
      formData: {
        name: "",
      },
      type: "platFormRole",
      dialogVisible: false, // 新增和编辑弹窗
      actionType: "add",
      tableLoading: false,
      nowRow: {},
      actions: [
        {
          name: "批量删除",
          disabled: !this.$permission("base_role_delete"),
          fn: this.deleteAll,
        },
      ],
      tableHeight: `calc(100vh - 185px)`,
    };
  },
  watch: {
    formData: {
      deep: true,

      handler(val, oldval) {
        getTableHeight(val, this);
      },

      immediate: true,
    },
  },
  methods: {
    getList() {
      const ref =
        this.type == "platFormRole" ? "baseRoleTable" : "baseProjectRoleTable";
      this.$refs[ref]?.getRoleList(this.$refs.searchForm);
    },
    openAddRole() {
      this.dialogVisible = true;
      this.actionType = "add";
      this.nowRow = {};
    },
    // 批量删除
    async deleteAll() {
      const ref =
        this.type == "platFormRole" ? "baseRoleTable" : "baseProjectRoleTable";
      this.$refs[ref]?.deleteAll();
    },
  },
};
</script>
