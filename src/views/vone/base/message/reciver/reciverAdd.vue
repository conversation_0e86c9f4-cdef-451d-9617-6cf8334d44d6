<template>
  <el-dialog size="md" :title="title" v-model="visible" :before-close="onClose" :wrapper-closable="false" width="60%" :close-on-click-modal="false">
    <el-form ref="addForm" v-loading="loading" :model="addForm" :rules="rules" label-position="top">
      <el-row>
        <el-col :span="24">
          <el-form-item label="名称" prop="name">
            <el-input v-model="addForm.name" placeholder="请输入名称" />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="24">
          <el-form-item label="适用事件" prop="eventType">
            <el-select v-model="addForm.eventType" placeholder="请选择适用事件" filterable clearable>
              <el-option v-for="item in eventList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col> -->
        <el-col :span="24">
          <el-form-item label="业务通知对象" prop="eventNoticyList">
            <el-checkbox-group v-model="addForm.eventNoticyList">
              <el-checkbox v-for="(item, index) in eventUserList" :key="index" :label="item.value">{{ item.label }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="通用通知对象">
            <ReciverGrid ref="ReciverGrid" type="WORK_ITEM" :filter-data="editData" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="描述" prop="description">
            <el-input v-model.trim="addForm.description" type="textarea" :rows="2" placeholder="请输入描述" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-row slot="footer">
      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="saveUser(0)">确定</el-button>
    </el-row>

  </el-dialog>
</template>

<script>

import {
  apiMsgReceiveHandle,
  apiMsgReceiveInfo,
  apiBaseMsgEvent } from '@/api/vone/base/message'
import { batchQuery } from '@/api/vone/base/role'
import { apiBaseDictEnumList } from '@/api/vone/ouath/ouath'
import _ from 'lodash'

import ReciverGrid from './reciver-grid.vue'

export default {
  components: {
    ReciverGrid
  },
  props: {
    id: {
      type: String,
      default: null
    },
    title: {
      type: String,
      default: null
    },
    visible: {
      type: Boolean,
      default: false
    }
    // engineTypes: {
    //   type: Array,
    //   default: () => []
    // }
  },
  data() {
    return {
      engineTypes: [
        {
          id: 'USER',
          name: '平台人员'
        },
        {
          id: 'ROLE',
          name: '平台角色'
        }
      ],
      eventList: [],
      loading: false,
      saveLoading: false,
      save: '保存并添加下一个',
      rules: {
        name: [
          {
            required: true,
            message: '请输入长度不超过50个字符的名称',
            trigger: 'change'
          },
          {
            max: 50,
            message: '请输入长度不超过50个字符的名称',
            trigger: 'change'
          }
        ],
        engineMaintainers: [
          {
            required: true,
            message: '请选择引擎维护人',
            trigger: 'change',
            type: 'array'
          }
        ],
        // eventType: [
        //   {
        //     required: true,
        //     message: '请选择适用事件',
        //     trigger: 'change'
        //   }
        // ],

        type: [
          {
            required: true,
            message: '请选择类型',
            trigger: 'change'
          }
        ],

        description: [
          {
            required: false,
            max: 250,
            message: '请输入长度不超过250个字符的描述',
            trigger: 'change'
          }
        ]
      },
      addForm: {
        name: '',
        description: '',
        readonly: false,
        eventNoticyList: []
      },
      roleList: [],
      editData: [],
      eventUserList: []
    }
  },
  mounted() {
    this.getEventList()
    this.getRoleList()
    this.getEventUser()
    if (this.id) {
      this.getUserDetail()
    }
    this.$nextTick(() => {
      this.$refs.addForm.clearValidate()
    })
  },
  methods: {
    async getRoleList() {
      const res = await batchQuery()
      if (!res.isSuccess) {
        return
      }
      this.roleList = res.data
    },
    async getEventList() {
      const res = await apiBaseMsgEvent()
      if (!res.isSuccess) {
        return
      }
      this.eventList = res.data
    },
    async getEventUser() {
      const res = await apiBaseDictEnumList(['MsgReceiveType'])
      if (!res.isSuccess) {
        return
      }
      this.eventUserList = res.data.MsgReceiveType
    },
    onClose() {
      this.$emit('update:visible', false)
      this.$refs.addForm.resetFields()
    },
    async getUserDetail() {
      this.loading = true
      const res = await apiMsgReceiveInfo(this.id)
      this.loading = false

      if (!res.isSuccess) {
        return
      }

      this.addForm = res.data
      const bzid = res.data.ruleList.filter(r => r.ruleType
        .code == 'BIZ').map(r => r.bizId)

      this.$set(this.addForm, 'eventNoticyList', bzid)

      const role = res.data.ruleList.filter(r => r.ruleType.code == 'ROLE').map(r => r.bizId)

      const roleList = [
        {
          authType: 'ROLE',
          authIds: role
        }
      ]

      // const roleList =

      const user = res.data.ruleList.filter(r => r.ruleType.code == 'USER').map(r => r.bizId)
      const userList = [
        {
          authType: 'USER',
          authIds: user
        }
      ]
      this.editData = roleList.concat(userList)
    },

    async saveUser(type) {
      try {
        await this.$refs.addForm.validate()
      } catch (e) {
        return
      }

      const events = this.addForm.eventNoticyList.map(r => ({
        bizId: r,
        eventType: this.addForm.eventType,
        ruleType: 'BIZ'
      }))

      const conditions = this.$refs.ReciverGrid.conditions

      const arr = []
      conditions.forEach(element => {
        element.authIds.forEach(j => {
          arr.push({
            bizId: j,
            eventType: this.addForm.eventType,
            ruleType: element.authType
          })
        })
      })

      const ruleList = events.concat(arr)

      try {
        this.saveLoading = true

        const res = await apiMsgReceiveHandle({
          id: this.addForm.id,
          name: this.addForm.name,
          description: this.addForm.description,
          readonly: false,
          ruleList
        }, this.id ? 'put' : 'post')
        if (!res.isSuccess) {
          this.$message.error(res.msg)
          return
        }
        this.saveLoading = false
        this.$message.success(this.id ? '修改成功' : '保存成功')
        this.$emit('success')
        this.onClose()
      } catch (e) {
        this.saveLoading = false
        return
      }
    }
  }
}
</script>

<style  lang="scss" scoped >

.mb-10 {
  margin-bottom: 10px;
}
:deep(.el-dialog__body) {
  max-height: 520px;
  overflow-y: auto;
  overflow-x: hidden;
}
</style>

