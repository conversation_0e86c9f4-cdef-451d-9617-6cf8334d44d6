<template>
  <div class="mainbox">
    <el-tabs v-model="activeName" tab-position="left">
      <el-tab-pane label="用户管理" name="first">
        <span slot="label"><i class="iconfont el-icon-application-member" />基本设置</span>
        <el-form ref="form" :model="userForm" :rules="userFormRules" label-width="100px" @submitValid="saveInfo">
          <el-form-item label="登录账号">
            {{ userForm.loginName }}
          </el-form-item>
          <el-form-item label="头像" prop="icon" class="userbox">
            <el-popover placement="bottom" trigger="hover">
              <el-row class="popover">
                <template v-for="item in avatarList">
                  <el-col :key="item.name" :span="6">
                    <div class="userhead" @click="userForm.icon = item.name">
                      <UserAvatar :name="item.name" :size="50" />
                    </div>
                  </el-col>
                </template>
              </el-row>
              <span slot="reference">
                <UserAvatar :name="userForm.icon" :size="50" />
              </span>
            </el-popover>
            <el-upload class="upload-demo" action="https://jsonplaceholder.typicode.com/posts/" :before-upload="beforeAvatarUpload">
              <el-button size="small" type="primary">点击上传</el-button>
            </el-upload>
            <!-- <div @click="showDialog">上传头像</div> -->
          </el-form-item>
          <el-form-item label="用户名称" prop="name">
            <el-input v-model="userForm.name" placeholder="请输入用户名称" />
          </el-form-item>
          <el-form-item label="用户邮箱" prop="email">
            <el-input v-model="userForm.email" placeholder="请输入用户邮箱" />
          </el-form-item>
          <el-form-item label="手机号码" prop="phone">
            <el-input v-model="userForm.phone" placeholder="请输入手机号码" />
          </el-form-item>
          <el-form-item label="描述" prop="description">
            <el-input v-model="userForm.description" placeholder="请输入描述" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" native-type="submit" :v-loading="saveLoading">保存</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="配置管理" name="second">
        <span slot="label"><i class="iconfont el-icon-application-password-reset mr-2" /> 修改密码</span>
        <el-form ref="form" :model="passWordForm" :rules="passWordRules" label-width="100px" @submitValid="passWordSubmit">
          <el-form-item label="旧密码" prop="oldPwd">
            <el-input v-model="passWordForm.oldPwd" show-password placeholder="请输入旧密码" />
          </el-form-item>
          <el-form-item label="新密码" prop="newPwd">
            <el-input v-model="passWordForm.newPwd" show-password placeholder="请输入新密码" />
          </el-form-item>
          <el-form-item label="确认密码" prop="checkPass">
            <el-input v-model="passWordForm.checkPass" show-password placeholder="请输入确认密码" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" native-type="submit" :loading="passLoading">保存</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>
    <el-dialog title="上传头像" v-model="dialogVisible" width="40%" height="400" :before-close="handleClose" :close-on-click-modal="false">
      <div style="overflow: hidden">
        <vueCropper
          ref="cropper"
          style="height: 300px; width: 300px; float: left"
          :img="option.img"
          :info="option.info"
          :output-size="option.outputSize"
          :output-type="option.outputType"
          :can-move="option.canMove"
          :auto-crop="option.autoCrop"
          :auto-crop-width="option.autoCropWidth"
          :auto-crop-height="option.autoCropHeight"
          :fixed-box="option.fixedBox"
          :original="option.original"
          :info-true="option.infoTrue"
          :center-box="option.centerBox"
          :can-move-box="option.canMoveBox"
          :can-scale="option.canScale"
          :fixed="option.fixed"
          :fixed-number="option.fixedNumber"
          @realTime="realTime"
        />
        <div class="upload-preview">
          <img v-show="previews.url" :src="previews.url" :style="previews.img">
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { avatarList } from '@/assets/avatar/avatar'
import UserAvatar from './avatar.vue'
import { VueCropper } from 'vue-cropper'
export default {
  components: { UserAvatar, VueCropper },
  data() {
    return {
      avatarList,
      activeName: 'first',
      userForm: {
        loginName: '张三',
        icon: 'avatar1'
      },
      saveLoading: false,
      passLoading: false,
      passWordForm: {

      },
      userFormRules: {
        name: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
        email: [{ required: true, message: '请输入用户邮箱', trigger: 'blur' }]
      },
      passWordRules: {
        oldPwd: [
          {
            required: true,
            message: '请输入旧密码',
            trigger: 'change'
          },
          {
            pattern: '^(?=.*[A-Za-z])(?=.*\\d)(?=.*[$@$!%*#?&])[A-Za-z\\d$@$!%*#?&]{8,32}$',
            message: '请输入8-32位密码，至少包含1个字母，1个数字和1个特殊字符',
            trigger: 'change'
          }
        ],
        newPwd: [
          {
            required: true,
            message: '请输入新密码',
            trigger: 'change'
          },
          {
            pattern: '^(?=.*[A-Za-z])(?=.*\\d)(?=.*[$@$!%*#?&])[A-Za-z\\d$@$!%*#?&]{8,32}$',
            message: '请输入8-32位密码，至少包含1个字母，1个数字和1个特殊字符',
            trigger: 'change'
          },
          {
            validator: (r, v, c) => {
              if (v === this.passWordForm.oldPwd) {
                c('新密码与旧密码密码不能一致,请修改')
              }
              c()
            }
          }
        ],
        checkPass: [
          {
            required: true,
            message: '确认密码不允许为空'
          },
          {
            validator: (r, v, c) => {
              if (v !== this.passWordForm.newPwd) {
                c('确认密码与新密码不一致')
              }
              c()
            }
          }
        ]
      },
      dialogVisible: false,
      option: {
        img: '', // 裁剪图片的地址
        outputSize: 1, // 裁剪生成图片的质量(可选0.1 - 1)
        outputType: 'png', // 裁剪生成图片的格式（jpeg || png || webp）
        info: true, // 图片大小信息
        canScale: true, // 图片是否允许滚轮缩放
        autoCrop: true, // 是否默认生成截图框
        autoCropWidth: 200, // 默认生成截图框宽度
        autoCropHeight: 200, // 默认生成截图框高度
        fixed: true, // 是否开启截图框宽高固定比例
        fixedNumber: [1, 1], // 截图框的宽高比例
        full: false, // false按原比例裁切图片，不失真
        fixedBox: true, // 固定截图框大小，不允许改变
        canMove: false, // 上传图片是否可以移动
        canMoveBox: true, // 截图框能否拖动
        original: false, // 上传图片按照原始比例渲染
        centerBox: false, // 截图框是否被限制在图片里面
        height: true, // 是否按照设备的dpr 输出等比例图片
        infoTrue: false, // true为展示真实输出图片宽高，false展示看到的截图框宽高
        maxImgSize: 3000, // 限制图片最大宽度和高度
        enlarge: 1, // 图片根据截图框输出比例倍数
        mode: '230px 150px' // 图片默认渲染方式
      },
      previews: {}
    }
  },
  mounted() {
  },
  methods: {
    // 保存基本信息
    saveInfo() {

    },
    // 保存密码
    passWordSubmit() {

    },
    handleClose() {
      this.dialogVisible = false
    },
    beforeAvatarUpload(file) {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => {
        this.option.img = reader.result // base64
      }
      this.dialogVisible = true
    },
    realTime(data) {
      this.previews = data
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.el-tabs--left .el-tabs__header.is-left) {
  padding-right: 40px;
}
.mainbox {
  width: 800px;
  margin: 0 auto;
  padding-top: 40px;
}
.popover {
  width: 300px;
  text-align: center;
  .userhead {
    width: 50px;
    padding: 6px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    background-color: var(--main-bg-color,#fff);
    &:hover {
      background-color: var(--hover-bg-color, #f5f6fa);
    }
  }
}
:deep(.userbox .el-form-item__content) {
  width: 70px;
}
:deep(.cropper-view-box) {
  border-radius: 50%;
}
:deep(.cropper-face) {
  border-radius: 50%;
}
.cropper-crop-box {
  width: 300px !important;
}
.upload-preview {
  float: right;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  box-shadow: 0 0 4px #bbbbbb;
  overflow: hidden;
}
</style>
