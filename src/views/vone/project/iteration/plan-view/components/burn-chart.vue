<template>
  <!-- 燃尽图 -->
  <el-card class="clearfix">
    <template v-slot:header>
      <div>
        <span>迭代-燃尽图</span>

        <el-select
          v-model="burnType"
          style="float: right; padding: 3px 0; width: 20%"
          @change="changeType"
        >
          <el-option label="故事点" value="0" />
          <el-option label="工时" value="1" />
        </el-select>
      </div>
    </template>
    <vone-echarts :options="options" :height="'300px'" />

    <!-- <vone-empty v-else /> -->
  </el-card>
</template>

<script>
import { findBurnDownByPlanId } from '@/api/vone/project/iteration'
import * as echarts from 'echarts'
export default {
  props: {
    title: {
      type: String,
      default: '',
    },
    projectList: {
      type: Array,
      default: () => [],
    },
    name: {
      type: String,
      default: '名称',
    },
  },

  data() {
    return {
      options: {},
      chartData: null,
      burnType: '0',
      planList: [],
    }
  },
  mounted() {
    this.getOptions()
  },
  methods: {
    async getOptions() {
      await findBurnDownByPlanId(this.$route.params.planIs, {
        type: this.burnType,
      }).then((res) => {
        if (res.isSuccess) {
          this.chartData = res.data
        }
      })

      this.options = {
        color: ['#ADB0B8', '#64BEFA'],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          backgroundColor: '#fff',
          borderColor: 'none',
          extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);', // 附加阴影样式
          textStyle: {
            color: '#53565C',
          },
        },

        legend: {
          x: 'right', // 居右显示
          itemHeight: 12,
          itemWidth: 24,
          data: [
            {
              name: '计划',
              icon: 'rect',
            },
            {
              name: '实际',
              icon: 'rect',
            },
          ],
          textStyle: {
            // 图例文字的样式
            color: '#8A8F99',
          },
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: this.chartData.date,
            boundaryGap: false,
            axisLabel: {
              show: true,
              textStyle: {
                color: '#8A8F99', // 更改坐标轴文字颜色
              },
            },
            axisLine: {
              lineStyle: {
                color: '#EBEEF5', // 更改坐标轴颜色
              },
            },
          },
        ],
        yAxis: [
          {
            type: 'value',
            minInterval: 1,
            axisTick: {
              // y轴刻度线
              show: false,
            },
            axisLine: {
              show: false, // 不显示坐标轴轴线
            },
            splitLine: {
              // 网格线
              lineStyle: {
                type: 'dashed', // 设置网格线类型 dotted：虚线   solid:实线
                width: 1,
                color: '#EBEEF5',
              },
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#8A8F99', // 更改坐标轴文字颜色
              },
            },
          },
        ],
        series: [
          {
            name: '计划',
            type: 'line',
            // areaStyle: {
            //   normal: {
            //     // 前四个参数代表位置 左下右上，如下表示从上往下渐变色 紫色到暗蓝色，
            //     color: new echarts.graphic.LinearGradient(
            //       0, 0, 0, 1,
            //       [
            //         { offset: 0, color: 'rgba(111, 195, 138, 1)' },
            //         { offset: 1, color: 'rgba(111, 195, 138, 0)' }
            //       ]
            //     )
            //   }
            // },
            emphasis: {
              focus: 'series',
            },
            data: this.chartData.plan,
          },
          {
            name: '实际',
            type: 'line',
            areaStyle: {
              normal: {
                // 前四个参数代表位置 左下右上，如下表示从上往下渐变色 紫色到暗蓝色，
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: 'rgba(0, 136, 255, 0.5)' },
                  { offset: 1, color: 'rgba(0, 136, 255, 0)' },
                ]),
              },
            },
            emphasis: {
              focus: 'series',
            },
            data: this.chartData.reality,
          },
        ],
      }
    },
    changeType() {
      this.getOptions()
    },
  },
}
</script>

<style lang="scss" scoped>
.clearfix {
  font-weight: bold;
  :deep(.el-card__header) {
    border-bottom: none;
  }
}
</style>
