import request from '@/utils/axios-api'

// 项目
export function getProjectList(data) {
  return request({
    url: '/api/insight/insight/projectComponent/findProjectDetail',
    method: 'get',
    data
  })
}

// 组织
export function getOrgList(data) {
  return request({
    url: '/api/insight/insight/userComponent/orgsDetail',
    method: 'get',
    data
  })
}

// 组织查人
export function getOrgUser(data) {
  return request({
    url: '/api/insight/insight/userComponent/page',
    method: 'post',
    data
  })
}

// 饼图
export function getPie(data) {
  return request({
    url: '/api/insight/insight/taskComponent/findLeaderCountGroupBy',
    method: 'post',
    data
  })
}

// 查工作项
export function getWorkList(data) {
  return request({
    url: '/api/insight/insight/taskComponent/queryWorkItemPageByQuery',
    method: 'post',
    data
  })
}

// 查工作项不分页
export function getWorkListNoPage(data) {
  return request({
    url: '/api/insight/insight/taskComponent/queryWorkItem',
    method: 'post',
    data
  })
}
