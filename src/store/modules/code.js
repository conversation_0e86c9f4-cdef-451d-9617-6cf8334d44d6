const state = {
  repositoryData: {}, // 代码库信息
  commitMap: {} // 分支提交信息
}
const mutations = {
  // 保存当前代码库数据
  SAVE_REPOINFO(state, payload) {
    state.repositoryData = payload
    sessionStorage.setItem(payload.id, JSON.stringify(payload))
  },
  // 保存分支提交信息
  SAVE_COMMITS(state, payload) {
    const key = payload.codeRepositoryId + '_' + payload.branchName
    if (state.commitMap[key]) return
    state.commitMap[key] = payload.commit
    sessionStorage.setItem(key, JSON.stringify(payload.commit))
  }
}
const actions = {
  saveRepoInfo({ commit }, data) {
    commit('SAVE_REPOINFO', data)
  }
}
export default {
  namespaced: true,
  state,
  mutations,
  actions
}
