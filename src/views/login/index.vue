<template>
  <div class="login-container">
    <div class="left-part">
      <img style="width: 100%" src="@/assets/login.jpg" />
      <!-- <div class="videobox">
            <video style="width: 100%" autoplay loop class="fillWidth" muted>
              <source src="@/assets/login-video.mp4" type="video/mp4">
            </video>
          </div> -->
    </div>
    <div class="right-part">
      <div>
        <el-form
          ref="loginForm"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          auto-complete="on"
          label-position="left"
        >
          <div class="title-container">
            <div class="titlebox">
              <div style="display: flex; align-items: center">
                <!-- <div style="width: 43px; display: inline-block;"><img style="width: 100%" src="@/assets/logo.png"></div> -->
                <h1>Jetto NG 智测平台</h1>
              </div>
              <!-- <span>数字化研发效能平台 | 研发运营质效加速器 | 数字化转型赋能者</span> -->
            </div>
          </div>

          <!-- <div v-loading="licensesLoading"> -->
          <!-- <div v-if="licensesSuccess">
                  <div
                    v-if="
                      (licensesFailureTime - new Date()) / 1000 / 24 / 3600 > 0 &&
                        (licensesFailureTime - new Date()) / 1000 / 24 / 3600 <= 15
                    "
                    class="approach"
                  >
                    <i class="iconfont el-icon-tips-exclamation-circle" />
                    服务还有 {{
                      Math.ceil(
                        (licensesFailureTime - new Date().getTime()) / 1000 / 24 / 3600
                      )
                    }} 日到期
                    <a @click="showInfo">
                      查看详情
                    </a>
                  </div>
                </div>

                <div v-if="!licensesSuccess" class="pt-3">
                  <div class="invalid">
                    <i class="iconfont el-icon-tips-exclamation-circle" />
                    Licenses无效,请重新上传</div>
                  <div class="py-4 pb-5">
                    <el-upload v-model="licensesFile" class="upload-demo" action :limit="1" :multiple="false" :http-request="httpRequest" :before-upload="beforeUpload">
                      <el-button slot="trigger" type="text" size="small" icon="iconfont el-icon-edit-upload">选择文件</el-button>
                    </el-upload>
                  </div>
                  <el-button class="submitStyle" :loading="upLoading" type="primary" @click="uploadLicense">上传</el-button>
                </div> -->
          <div>
            <el-form-item prop="account" label="">
              <el-input
                ref="account"
                v-model="loginForm.account"
                placeholder="请输入您的账号"
                tabindex="1"
                auto-complete="off"
                @keyup.enter="handleLogin"
                :prefix-icon="Search"
              />
            </el-form-item>

            <el-form-item prop="password" label="">
              <el-input
                :key="passwordType"
                ref="password"
                v-model="loginForm.password"
                :type="passwordType"
                placeholder="请输入您的密码"
                name="password"
                tabindex="2"
                auto-complete="on"
                @keyup.enter="handleLogin"
                show-password
              >
                <template v-slot:suffix>
                  <i
                    :class="[
                      'iconfont',
                      passwordType === 'password'
                        ? 'iconfont el-icon-edit-invisible'
                        : 'iconfont el-icon-edit-visible',
                    ]"
                    @click="changeye"
                  />
                </template>
              </el-input>
            </el-form-item>
            <el-button
              :loading="loading"
              type="primary"
              class="submitStyle"
              @click.prevent="handleLogin"
              @keyup.enter="handleLogin"
              >登录</el-button
            >
          </div>
          <!-- </div> -->
        </el-form>
      </div>
      <div class="textStyle">
        <!-- © 捷科新一代智测平台 -->
      </div>
    </div>
  </div>
</template>

<script>
import { Search } from "@element-plus/icons-vue-vue";
import { login, getUserPermission } from "@/api/user";
import { setPermission, setExpiration } from "@/utils/auth";

// import { validUsername } from '@/utils/validate'
import _ from "lodash";

import { getUserDetail } from "@/api/vone/base/user";
// import * as ww from '@wecom/jssdk'
import { setTenant } from "@/utils/auth";
import { Base64 } from "js-base64";
export default {
  name: "Login",
  data() {
    return {
      loginForm: {
        tenantView: "",
        tenant: "",
        account: "",
        password: "",
      },
      loginRules: {
        account: [{ required: true, message: "请输入账号", trigger: "change" }],
        password: [
          { required: true, message: "请输入密码", trigger: "change" },
        ],
      },
      loading: false,
      passwordType: "password",
      redirect: undefined,
      activeIndex: "4",
      videoWidth: "900px",
      tableData: [],
      exportData: [],
      loginCode: "",
      showWxMs: false,
      isShowWx: false,
      account: "",
    };
  },
  watch: {
    $route: {
      deep: true,

      handler: function (route) {
        // 处理用户访问项目下菜单，退出之后重新登录跳转404问题,由于项目下权限是单独控制,只有点击具体项目后才能拿到路由信息,解决方案为,判断redirect来源,如果是从项目模块redirect过来的,默认跳转到项目首页
        const redirectForm = route.query && route.query.redirect;
        this.redirect = _.startsWith(redirectForm, "/project")
          ? "/project/view"
          : redirectForm;
        const url = this.$route.query;
        if (url.code) {
          this.loginCode = url.code;
          this.isShowWx = true;
          if (this.isPC()) {
            if (url.type == "wx") {
              this.handleLogin("wx");
            }
            const ww = window.wx;
            const wurl = window.location.href;
            this.wwUrl = wurl;
            ww.config({
              beta: true,
            });
            ww.ready(function () {});
            ww.invoke(
              "openDefaultBrowser",
              {
                url: wurl + "&type=wx", // 在默认浏览器打开redirect_uri，并附加code参数；也可以直接指定要打开的url，此时不会附带上code参数。
              },
              function (res) {
                if (res.err_msg != "openDefaultBrowser:ok") {
                  this.$message(res);
                  // 错误处理
                }
              }
            );
          } else {
            this.handleLogin("wx");
          }
        } else if (url.IDnumber && url.IDnumber) {
          this.isShowWx = true;
          this.loginCode = url.token;
          this.account = url.IDnumber;
          this.handleLogin("oa");
        }
      },

      immediate: true,
    },
  },
  mounted() {
    const url = this.$route.query;
    if (url.type) {
      if (url.type === "direct") {
        this.loginForm.account = url.n;
        this.loginForm.password = url.p;
        this.loginForm.grantType = "password";
        this.handleLogin();
      }
    }
    // this.getWidth()
    // this.resize()
  },
  methods: {
    goLogin() {
      this.isShowWx = false;
    },
    getWidth() {
      const windowWidth = window.innerWidth;
      if (windowWidth >= 1920) {
        this.videoWidth = "1230px";
      } else if (windowWidth >= 1440 && windowWidth < 1920) {
        this.videoWidth = "900px";
      } else {
        this.videoWidth = "750px";
      }
    },
    resize() {
      window.onresize = () => {
        const windowWidth = window.innerWidth;
        if (windowWidth >= 1920) {
          this.videoWidth = "1230px";
        } else if (windowWidth >= 1440 && windowWidth < 1920) {
          this.videoWidth = "900px";
        } else {
          this.videoWidth = "750px";
        }
      };
    },
    showPwd() {
      if (this.passwordType === "password") {
        this.passwordType = "";
      } else {
        this.passwordType = "password";
      }
      this.$nextTick(() => {
        this.$refs.password.focus();
      });
    },
    changeye() {
      this.passwordType = this.passwordType == "password" ? "text" : "password";
    },
    getResource() {
      getUserPermission().then(async (response) => {
        const { data, isSuccess } = response;
        if (!isSuccess) {
          return this.$message("获取用户权限失败，请重新登录");
        }

        this.$store.commit("user/set_permission", data.functionList);

        setPermission(data.functionList);
        this.$router.push({ path: this.redirect || "/" });
      });
    },
    /**
     * 判断是否是PC端
     * @returns {boolean}
     */
    isPC() {
      const userAgentInfo = navigator.userAgent;
      // eslint-disable-next-line no-array-constructor
      const agents = new Array(
        "Android",
        "iPhone",
        "SymbianOS",
        "Windows Phone",
        "iPad",
        "iPod"
      );
      let flag = true;
      for (let v = 0; v < agents.length; v++) {
        if (userAgentInfo.indexOf(agents[v]) > 0) {
          flag = false;
          break;
        }
      }
      return flag;
    },
    async handleLogin(type) {
      var param = {};
      if (type == "wx") {
        param = {
          code: this.loginCode,
          grantType: type,
        };
      } else if (type == "oa") {
        param = {
          code: this.loginCode,
          account: this.account,
          grantType: type,
        };
      } else {
        try {
          await this.$refs.loginForm.validate();
        } catch (e) {
          return;
        }
        this.loginForm.tenant = `${Base64.encode(this.loginForm.tenantView)}`;
        this.loginForm.tenant != "" && setTenant(this.loginForm.tenant);
        param = {
          ...this.loginForm,
          code: "",
          grantType: "password",
          refreshToken: "",
        };
        this.loading = true;
      }
      login(param)
        .then(async (res) => {
          const { data, isSuccess, msg } = res;
          if (isSuccess) {
            await this.$store.commit("user/set_token", data.token);
            let userDetail = {};
            await getUserDetail(data.userId).then((res1) => {
              if (res1.isSuccess) {
                if (!res1.data.userRoles) {
                  this.$message.warning(
                    "当前用户没有配置角色,请联系管理员配置角色权限"
                  );
                  return;
                }
                userDetail = res1.data;

                setExpiration(data.expiration);

                this.getResource();
              }
            });
            await this.$store.commit("user/set_user", {
              id: data.userId,
              userAccount: data.userAccount,
              name: data.userName,
              userName: data.userName,
              avatarId: data.avatarId,
              orgId: data.orgId,
              avatarPath: data.avatarPath,
              avatarType: data.avatarType,
              email: userDetail.email,
              userRoles: userDetail.userRoles,
            });
          } else {
            if (type) {
              this.wxLoginMs = msg;
              this.showWxMs = true;
            } else {
              this.$message.warning(msg);
            }
          }
          this.loading = false;
        })
        .catch(() => {
          if (type) {
            this.showWxMs = true;
          }
          this.loading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.left-part {
  width: 100%; /*// position: absolute;*/
  display: flex;
  align-items: center;
  height: 100%;
}
.right-part {
  background: #fff;
  position: absolute;
  width: 428px;
  height: 460px;
  border-radius: 16px;
  right: 20%;
  padding: 45px;
  background-color: rgba(255, 255, 255, 0.6);
  flex-grow: 0;
  flex-shrink: 0;
  .titlebox {
    h1 {
      width: 100%;
      font-size: 34px;
      line-height: 48px;
      font-weight: 500;
      text-align: center;
      margin: 0px;
    }
    span {
      color: var(--font-second-color);
      font-size: 13px;
      margin-top: 8px;
      // margin-left: 55px;
    }
  }
}
input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 1000px white inset !important;
}
:deep(.el-form .el-form-item) {
  margin-bottom: 24px;
}
.login-container {
  display: flex;
  position: relative;
  background: #f7f8fb;
  height: 100vh;
  overflow: hidden;
  justify-content: flex-end;
  align-items: center;
  .login-form {
    position: relative;
    width: 100%;
    // box-shadow: 0 4px 6px rgb(0 0 0 / 10%), 0 12px 20px rgb(38 38 38 / 12%);
    max-width: 100%;
    // padding: 0px 34px;
    margin: 0 auto;
    overflow: hidden;
    // margin-top: 30%;
  }
  .title-container {
    margin-bottom: 40px;
    text-align: right;
    .title {
      font-size: 34px;
      color: var(--font-main-color);
      font-weight: 500;
      line-height: 66px;
    }
  }
  :deep(.el-input__inner) {
    height: 40px;
    line-height: 40px;
  }
}
.textStyle {
  width: 470px;
  text-align: center;
  clear: both;
  position: absolute;
  bottom: 24px;
  font-size: 12px;
}
.submitStyle {
  width: 100%;
  margin-top: 67px;
  justify-content: center;
  line-height: 40px;
  height: 40px;
}
.licenses {
  font-size: 12px;
  margin-left: 10px;
  color: #909399;
}
:deep(.el-input__suffix) {
  display: flex;
  align-items: center;
}
.invalid {
  height: 38px;
  line-height: 38px;
  border: 1px solid #ffe07a;
  border-radius: 2px;
  background-color: #fffbe6;
  padding: 0 12px;
  color: #777f8e;
  display: flex;
  align-items: center;
  margin-bottom: 26px;
  i {
    color: #f7a500;
    margin-right: 6px;
  }
}
.approach {
  display: flex; /*// justify-content: ;*/
  align-items: center;
  justify-content: end;
  color: #f7a500;
  margin: 16px 0;
  i {
    margin-right: 3px;
  }
  a {
    margin-left: 6px;
    color: var(--main-theme-color);
  }
}
</style>
