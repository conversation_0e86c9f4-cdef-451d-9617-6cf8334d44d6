<template>
  <div class="itemBox">
    <div v-if="showItem" v-loading="infoLoading">
      <el-row class="h4Header" type="flex">
        <h4>
          {{ form.name }} <span>{{ `「 ${typeMap[form.type]} 」` }}</span>
        </h4>
      </el-row>

      <el-form :model="form" label-position="top" @submit.prevent>
        <el-row>
          <el-col :span="24">
            <el-form-item label="属性名" prop="name">
              <el-input v-model="form.name" />
            </el-form-item>
          </el-col>
          <el-col
            v-if="form.type == 'SELECT' || form.type == 'LINKED'"
            :span="24"
          >
            <el-form-item
              v-if="
                form.key == 'priorityCode' ||
                form.key == 'sourceCode' ||
                !form.isBuilt
              "
              label="默认值"
              prop="defaultValue"
            >
              <el-select
                v-model="form.defaultValue"
                placeholder="请选择"
                clearable
                filterable
                @change="saveInfo"
              >
                <el-option
                  v-for="item in form.options"
                  :key="item.id"
                  :label="item.name"
                  :value="!form.isBuilt ? item.id : item.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col v-if="form.type == 'INT'" :span="24" class="numberIten">
            <el-form-item label="默认值" prop="defaultValue">
              <el-input-number
                v-model="form.defaultValue"
                controls-position="right"
                @change="saveInfo"
              />
            </el-form-item>
          </el-col>
          <el-col
            v-if="
              form.type != 'USER' && form.type != 'ORG' && form.type != 'FILE'
            "
            :span="24"
          >
            <el-form-item label="提示语 (placeholder)" prop="placeholder">
              <el-input
                v-model="form.placeholder"
                placeholder="请输入提示语(placeholder)"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="必填项" prop="isRequired">
              <el-radio-group v-model="form.isRequired">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col
            v-if="form.type == 'INPUT' || form.type == 'TEXTAREA'"
            :span="24"
          >
            <el-form-item label="校验规则" prop="validator">
              <el-input
                v-model="form.validator"
                placeholder="请输入检验规则(正则表达式)"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="form.isRequired" :span="24">
            <el-form-item label="校验提示信息" prop="message">
              <el-input
                v-model="form.message"
                placeholder="请输入校验提示信息"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              v-if="form.type == 'DATE'"
              label="默认时间"
              prop="defaultValue"
            >
              <el-select
                v-model="form.defaultValue"
                placeholder="请选择"
                clearable
              >
                <el-option label="当前时间" value="now" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item
              v-if="
                form.type == 'SELECT' ||
                form.type == 'USER' ||
                form.type == 'ORG' ||
                form.type == 'PROJECTUSER'
              "
              label="多选"
              prop="multiple"
            >
              <el-radio-group v-model="form.multiple">
                <el-radio :disabled="form.isBuilt ? true : false" :label="true"
                  >是</el-radio
                >
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col v-if="form.type == 'INT'" :span="24" class="numberIten">
            <el-form-item label="精度(1~10)" prop="precision">
              <el-input-number
                v-model="form.precision"
                :min="1"
                :step="1"
                :max="10"
                controls-position="right"
                @change="saveInfo"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24" class="numberIten">
            <el-form-item label="排序" prop="sort">
              <el-input-number v-model="form.sort" controls-position="right" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="是否展示为筛选条件" prop="isSearch">
              <el-radio-group v-model="form.isSearch">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <el-col v-if="form.type != 'FILE' && form.type != 'QUOTE'" :span="24">
            <el-form-item label="是否支持导入" prop="isImport">
              <el-radio-group v-model="form.isImport">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col v-if="form.type != 'FILE'" :span="24">
            <el-form-item label="是否支持导出" prop="isExport">
              <el-radio-group v-model="form.isExport">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="24">
                <el-form-item label="列表是否支持排序" prop="sortable">
                  <el-radio-group v-model="form.sortable">
                    <el-radio :label="true">是</el-radio>
                    <el-radio :label="false">否</el-radio>

                  </el-radio-group>

                </el-form-item>
              </el-col> -->
          <!-- <el-col :span="24">
                <el-form-item label="字段列占比" prop="width">
                  <el-radio-group v-model="form.width">
                    <el-radio :label="'100'">10%</el-radio>
                    <el-radio :label="'200'">20%</el-radio>
                    <el-radio :label="'450'">45%</el-radio>
                  </el-radio-group>

                </el-form-item>
              </el-col> -->

          <el-col :span="24">
            <span class="options">
              <el-form-item
                v-if="!form.isBuilt && form.type == 'SELECT'"
                label="字段项"
                prop="options"
              >
                <a class="optionItem" @click="addOption">
                  <el-icon class="iconfont"><el-icon-tips-plus /></el-icon>
                  添加字段项</a
                >
                <template>
                  <el-row
                    v-for="(item, index) in form.options"
                    :key="index"
                    class="btnRow"
                  >
                    <el-col :span="19">
                      <el-input
                        v-model="item.name"
                        style="margin: 5px 0"
                        placeholder="请输入字段项"
                        @keyup.enter="saveItem(item)"
                      />
                    </el-col>
                    <el-col :span="4" class="editButton">
                      <a @click="delItem(index, item)">
                        <el-icon class="iconfont"
                          ><el-icon-application-delete
                        /></el-icon>
                      </a>
                    </el-col>
                  </el-row>
                </template>
              </el-form-item>
            </span>
          </el-col>
          <el-col v-if="form.type == 'LINKED'" :span="24">
            <el-form-item label="关联数据表" prop="relationShipsheet">
              <el-select
                v-model="form.relationShipsheet"
                placeholder="关联表"
                @change="changeLink"
              >
                <el-option
                  v-for="i in sheetOptions"
                  :key="i.id"
                  :label="i.name"
                  :value="i.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="form.type == 'QUOTE'">
          <el-col :span="24">
            <el-form-item label="引用的字段" prop="relationShipsheet">
              <el-select
                v-model="form.relationShipsheet"
                placeholder="引用表"
                @change="sheetChange"
              >
                <el-option
                  v-for="i in sheetOptions"
                  :key="i.id"
                  :label="i.name"
                  :value="i.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col style="border-top: 1px solid var(--el-divider)" :span="24">
            <el-form-item label="" prop="relationField">
              <el-select v-model="form.relationField" placeholder="引用字段">
                <el-option
                  v-for="i in fieldOptions"
                  :key="i.id"
                  :label="i.name"
                  :value="i.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="查找条件" prop="queryCriteriaA">
              <el-select v-model="form.queryCriteriaA" placeholder="属性类型">
                <el-option
                  v-for="i in fieldOptions"
                  :key="i.id"
                  :label="i.name"
                  :value="i.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col style="border-top: 1px solid var(--el-divider)" :span="24">
            <el-form-item label="" prop="queryCriteriaB">
              <el-select v-model="form.queryCriteriaB" placeholder="">
                <el-option label="等于" value="equal" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col style="border-top: 1px solid var(--el-divider)" :span="24">
            <el-form-item class="formlable" label="" prop="queryCriteriaC">
              <el-select v-model="form.queryCriteriaC" placeholder="属性类型">
                <el-option
                  v-for="i in customList.filter((e) => e.type == 'LINKED')"
                  :key="i.key"
                  :label="i.name"
                  :value="i.key"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <!-- <el-form :model="formConfig" label-position="top">
            <el-row>
              <el-col v-if="form.type == 'LINKED'" :span="24">
                <el-form-item label="关联数据表" prop="relationShipsheet">
                  <el-select v-model="form.relationShipsheet" placeholder="属性类型">
                    <el-option v-for="i in sheetOptions" :key="i.id" :label="i.name" :value="i.id" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form> -->
    </div>
    <vone-empty v-else />
  </div>
</template>

<script>
import {
  TipsPlus as ElIconTipsPlus,
  ApplicationDelete as ElIconApplicationDelete,
} from "@element-plus/icons-vue";
import {
  $on,
  $off,
  $once,
  $emit,
} from "../../../../../../utils/gogocodeTransfer";
const typeMap = {
  USER: "平台人员",
  PROJECTUSER: "项目人员",
  ORG: "组织机构",
  FILE: "文件",
  SELECT: "下拉框",
  INPUT: "输入框",
  INT: "数字",
  TEXTAREA: "文本域",
  DATE: "日期选择器",
  LINKED: "关联类型",
  QUOTE: "引用类型",
  LINK: "超链接",
};

import { getSourceById, getSourceList } from "@/api/vone/base/source";

import { pick } from "lodash";
export default {
  components: {
    ElIconTipsPlus,
    ElIconApplicationDelete,
  },
  props: {
    typeClassify: {
      type: String,
      default: undefined,
    },
    itemInfo: {
      type: Object,
      default: () => {},
    },
    showItem: {
      type: Boolean,
      default: false,
    },
    formId: {
      type: String,
      default: undefined,
    },
    customList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      form: {
        options: [],
        precision: 1,
      },
      isEdit: false,
      optionEdit: false,
      infoLoading: false,
      saveLoading: false,
      sheetOptions: [],
      fieldOptions: [],
      formConfig: [],
      typeMap,
      flagchange: 0, // 默认值
    };
  },
  watch: {
    itemInfo: {
      handler: function (val) {
        if (this.showItem) {
          this.getInfo();
        }
      },
      deep: true,
      immediate: true,
    },
    form: {
      handler(val) {
        if (this.flagchange > 0) {
          // 表单中的值发生了改变
          this.saveInfo();
        }
        this.flagchange++;
      },
      deep: true,
    },
  },
  mounted() {
    this.getSheetOptions();
  },
  methods: {
    async getSheetOptions() {
      const res = await getSourceList();
      if (res.isSuccess) {
        this.sheetOptions = res.data;
      }
    },
    changeLink() {
      this.form["defaultValue"] = "";
      $emit(this, "changeLink", this.form);
      this.$forceUpdate();
    },
    sheetChange(e) {
      this.form["relationField"] = null;
      this.getFieldOptions(e);
    },
    async getFieldOptions(id) {
      const res = await getSourceById(id);
      if (res.isSuccess) {
        this.fieldOptions = res.data.fields;
      }
    },
    changeEdit() {
      this.isEdit = !this.isEdit;
      this.optionEdit = false;
    },
    async getInfo() {
      this.infoLoading = true;

      this.form = { ...this.itemInfo };

      this.infoLoading = false;
      if (this.form && this.form.options && this.form.options.length) {
        this.form.options.forEach((element) => {
          element.itemEdit = false;
        });
      }

      if (!this.form.isBuilt && !this.form.options) {
        this.form["options"] = [];
      }
      if (this.form.type == "QUOTE") {
        this.getFieldOptions(this.form.relationShipsheet);
      }
    },
    addOption(index) {
      this.optionEdit = true;

      this.form.options.push({
        name: "",
        id: JSON.stringify(Date.now()),
        itemEdit: true,
      });
    },
    optionsEdit(index, item) {
      item["itemEdit"] = true;
    },
    async delItem(index, item) {
      await this.$confirm(`确认删除选项【${item.name}】吗?`, "删除", {
        type: "warning",
        customClass: "delConfirm",
        showClose: false,
      });
      this.form.options.splice(index, 1);
      if (item.id == this.form.defaultValue) {
        this.form["defaultValue"] = "";
      }
    },
    saveItem(item) {
      this.saveInfo();
      item["itemEdit"] = false;
    },
    saveInfo() {
      const list = [
        "placeholder",
        "multiple",
        "validator",
        "message",
        "defaultTime",
        "sortable",
        "width",
        "precision",
        "relationShipsheet",
        "relationField",
        "queryCriteriaA",
        "queryCriteriaB",
        "queryCriteriaC",
        "defaultValue",
      ];
      // 如果是自定义下拉框类型的字段才保存options信息
      if (!this.form.isBuilt && this.form.type == "SELECT") {
        list.push("options");
      }

      const otherProperty = { ...pick(this.form, list) };

      this.form["config"] = JSON.stringify(otherProperty);
      // this.$set(this.form, 'id', this.formId)
      const normal = {
        ...pick(this.form, [
          "id",
          "isBuilt",
          "name",
          "type",
          "isRequired",
          "sort",
          "customFormId",
          "key",
          "config",
          "isImport",
          "isExport",
          "isSearch",
        ]),
      };
      $emit(this, "success", normal);
    },
  },
  emits: ["changeLink", "success"],
};
</script>

<style lang="scss" scoped>
.h4Header{position:sticky;top:0;background:#fff;z-index:99;/*// box-shadow: 1px 1px 15px var(--input-border-color);*/h4 {
    padding-left: 12px;
  }
  span{
    color: var(--auxiliary-font-color);
    font-size: 12px;
  }}:deep(.el-form .el-form-item__label){width:100%;height:36px;line-height:36px;background-color:var(--node-cildren-bg-color);/*// margin-bottom: 5px;*/padding:0 16px;color:var(--auxiliary-font-color)}.options{:deep(.el-form-item__label) {
    position: relative;
  }
  .optionItem {
    position: absolute;
    top: -35px;
    right: 2%;
    color: var(--main-theme-color);
  }}.editButton{text-align:right;margin-top:4px;a {
    color: var(--main-theme-color);

    display: none;
  }}.btnRow:hover a{display:inline-block}.itemBox{margin-left:10px;border:1px solid var(--el-divider);border-radius:5px;height:100%;overflow-y:auto;overflow-x:hidden;padding-bottom:20px;:deep(.el-form-item__content) {
    // padding-left: 22px;
    // padding-right: 10px;
    padding: 4px 18px;
  }

  .numberIten {
    :deep(.el-input-number) {
      border: 1px solid #ced1d9;
    }
    :deep(.el-input-inner:hover) {
      background: none;
    }
    :deep(.el-input-inner:facus) {
      background: none;
    }
    :deep(.el-input-number__decrease) {
      background-color: #fff;
      border-left: #ced1d9;
      color: #ced1d9;
    }
    :deep(.el-input-number__increase) {
      color: #ced1d9;
      background-color: #fff;
      border-right: #ced1d9;
    }
  }}.el-col-24{:deep(.el-input--small .el-input__inner) {
    border: none;
    background: none;
  }

  // :deep(.el-input__inner:focus) {
  //   background: var(--col-hover-bg);
  // }
  // :deep(.el-input__inner:hover) {
  //   background: var(--col-hover-bg);
  // }}:deep(.el-form-item){margin-bottom:0}
</style>
