<template>
  <div>
    <vone-search-wrapper style="margin-bottom: 16px;">
      <template slot="custom">
        <span class="strong">状态</span>
      </template>
      <template slot="moreSearch">
        <el-input
          slot="moreSearch"
          v-model="formData.name"
          style="width:300px"
          placeholder="搜索名称"
          prefix-icon="el-icon-search"
          @input="searchChange"
        />
      </template>
      <template slot="actions">
        <el-button type="primary" icon="iconfont el-icon-tips-plus-circle" size="small" :disabled="!$permission('alm_state_add')" @click="clickAddRow">新增</el-button>
        <el-dropdown trigger="click" @command="(e) => e && e()">
          <el-button class="btnMore"><i class="iconfont el-icon-application-more " /></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="(item, index) in actions" :key="index" :icon="item.icon" :command="item.fn" :disabled="item.disabled">
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
    </vone-search-wrapper>
    <div :style="{height: tableHeight}">
      <vxe-table
        ref="issue-status-table"
        class="vone-vxe-table"
        border
        height="auto"
        show-overflow="tooltip"
        :loading="pageLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ resizable: true, minWidth: 120 }"
        :checkbox-config="{checkMethod:selectable}"
        row-id="id"
      >
        <vxe-column type="checkbox" width="37" align="center" fixed="left" />
        <vxe-column title="名称" field="name" fixed="left">
          <template slot-scope="scope">
            <span v-if="scope.row.color" :style="{ border:`1px solid ${ scope.row.color}`,color:`${scope.row.color}`}" class="tagCustom">{{ scope.row.name }}</span>
            <div v-else>{{ scope.row.name }}</div>
          </template>
        </vxe-column>
        <vxe-column title="标识" field="code" />
        <vxe-column title="阶段" field="stageCode">
          <template slot-scope="scope">
            <span v-if="scope.row.stageCode && scope.row.echoMap && scope.row.echoMap.stageCode">
              {{ scope.row.echoMap.stageCode.name }}
            </span>
            <span v-else>{{ scope.row.stageCode }}</span>
          </template>
        </vxe-column>
        <vxe-column title="描述" field="description" />
        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <template>
              <el-tooltip class="item" content="编辑" placement="top">
                <el-button type="text" :disabled="!$permission('alm_state_edit')" icon="iconfont el-icon-application-edit icon_click" @click="editClickRow(row)" />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="删除" placement="top">
                <el-button type="text" :disabled="row.readonly || !$permission('alm_state_delete')" icon="iconfont el-icon-application-delete" @click="deleteRow(row)" />
              </el-tooltip>
            </template>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <vone-pagination ref="pagination" :total="tableData.total" @update="getTableData" />
    <!-- 新增状态 -->
    <el-dialog :title="title" width="40%" v-model="dialogFormVisible" :close-on-click-modal="false" :before-close="onClose">
      <el-form ref="statusForm" :rules="rules" :model="statusForm">
        <el-form-item label="名称" prop="name">
          <el-input v-model="statusForm.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="标识" prop="code">
          <el-input v-model="statusForm.code" placeholder="请输入标识" :disabled="title == '编辑状态'" />
        </el-form-item>
        <el-row :gutter="12">
          <el-col :span="12">

            <el-form-item label="颜色" prop="color">
              <el-input v-model="statusForm.color" type="color" placeholder="请选择颜色" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="阶段" prop="stageCode">
              <el-select v-model="statusForm.stageCode" placeholder="请选择阶段">
                <el-option
                  v-for="item in stageList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="描述" prop="description">
          <el-input v-model="statusForm.description" placeholder="请输入描述" type="textarea" />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="sureAdd">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { apiProjectmWorkflowStatus, apiAlmStatusAdd, apiAlmStageNoPage,
  apiAlmStateDel,
  apiAlmStateGetById } from '@/api/vone/base/work-flow'
import _ from 'lodash'

export default {
  data() {
    return {
      rules: {
        name: [
          {
            required: true,
            message: '请输入长度不超过20个字符的名称',
            trigger: 'change'
          },
          {
            pattern: '^[a-zA-Z0-9_\\u4e00-\\u9fa5]{1,20}$',
            message: '请输入不超过20位由中文、字母、数字或者下划线组成的名称'
          },
          {
            max: 20,
            message: '请输入长度不超过20个字符的名称',
            trigger: 'change'
          }
        ],
        code: [
          {
            required: true,
            message: '请输入长度不超过20个字符的标识',
            trigger: 'change'
          },
          {
            pattern: '^\\w{1,20}$',
            message: '请输入不超过20位由字母、数字或者下划线组成的标识'
          }
        ],
        stageCode: [
          {
            required: true,
            message: '请选择阶段',
            trigger: 'change'
          }
        ],
        color: [
          {
            required: true,
            message: '请选择颜色',
            trigger: 'blur'
          }
        ],
        description: [
          {
            max: 100,
            message: '请输入长度不超过100个字符的描述',
            trigger: 'change'
          }
        ]
      },
      stageList: [],
      saveLoading: false,
      statusForm: {
        color: '#000'
      },
      dialogFormVisible: false,
      pageLoading: false,
      tableData: {},
      title: '',
      formData: {
        name: ''
      },
      actions: [
        {
          name: '批量删除',
          disabled: !this.$permission('alm_state_delete'),
          fn: this.deleteAll
        }
      ]
    }
  },
  computed: {
    tableHeight() {
      const height = (this.extraData?.height || 0) + 'px'
      return `calc(${this.$reduceTableHeight} - ${height})`
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    searchChange: _.debounce(function(e) {
      this.getTableData()
    }, 1000),
    selectable({ row }) {
      if (row.readonly) {
        return false
      } else {
        return true
      }
    },
    onClose() {
      this.dialogFormVisible = false
      this.$refs.statusForm.resetFields()
    },

    getStatus(index, row) {
      return this.tableData.records[index].readonly || !this.$permission('alm_state_edit')
    },
    getDel(index, row) {
      return this.tableData.records[index].readonly || !this.$permission('alm_state_delete')
    },
    async getStageList() {
      const res = await apiAlmStageNoPage(
      )
      if (!res.isSuccess) {
        return
      }
      this.stageList = res.data
    },
    async getTableData() {
      this.pageLoading = true
      const pageObj = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
      const params = {
        ...pageObj,
        extra: {},
        model: { ...this.formData }
      }
      const res = await apiProjectmWorkflowStatus(params)
      this.pageLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
      }
      this.tableData = res.data
    },
    clickAddRow() {
      this.title = '新增状态'
      this.dialogFormVisible = true
      this.getStageList()
    },
    editClickRow(row) {
      this.getStageList()
      this.title = '编辑状态'
      this.dialogFormVisible = true
      this.getInfo(row.id)
    },
    // 状态详情
    async getInfo(id) {
      const res = await apiAlmStateGetById(id)
      this.pageLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
      }
      this.statusForm = res.data
    },
    async sureAdd() {
      try {
        await this.$refs.statusForm.validate()
      } catch (e) {
        return
      }

      if (this.title == '编辑状态') {
        this.sureEdit()
        return
      }
      try {
        this.saveLoading = true
        const res = await apiAlmStatusAdd(
          this.statusForm, 'POST'
        )
        this.saveLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.$message.success('操作成功')
        this.onClose()
        this.getTableData()
      } catch (e) {
        this.saveLoading = false
      }
    },
    async sureEdit() {
      try {
        this.saveLoading = true
        const res = await apiAlmStatusAdd(
          this.statusForm, 'PUT'
        )
        this.saveLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.$message.success('操作成功')
        this.onClose()
        this.getTableData()
      } catch (e) {
        this.saveLoading = false
      }
    },

    // 状态删除
    async deleteRow(row) {
      await this.$confirm(`确定删除【${row.name}】吗？`, '删除', {
        type: 'warning',
        closeOnClickModal: false,
        customClass: 'delConfirm',
        showClose: false
      })
      this.pageLoading = true
      try {
        const res = await apiAlmStateDel([row.id])
        this.pageLoading = false
        if (!res.isSuccess) {
          this.$message.error(res.msg)
        }
        this.$message.success('删除成功')
        this.getTableData()
      } catch (e) {
        this.pageLoading = false
      }
    },
    // 批量删除
    async deleteAll() {
      const selectData = this.getVxeTableSelectData('issue-status-table')
      if (!selectData.length) {
        this.$message.warning('请选择要删除的数据')
        return
      }

      try {
        await this.$confirm(`是否删除 ${selectData.length} 个数据?`, '批量删除', {
          type: 'warning',
          customClass: 'delConfirm',
          showClose: false
        })
        this.pageLoading = true
        const selectId = this.getVxeTableSelectData('issue-status-table').map(r => r.id)
        const res = await apiAlmStateDel(selectId)
        this.pageLoading = false
        if (!res.isSuccess) {
          this.$message.error(res.msg)
          return
        }
        this.$message.success('删除成功')
        this.getTableData()
      } catch (e) {
        this.pageLoading = false
      }
    }
  }

}
</script>

<style lang="scss" scoped>
.strong{
  font-size: 16px;
  font-weight: 500;
  color: #2C2E36;
}
</style>
