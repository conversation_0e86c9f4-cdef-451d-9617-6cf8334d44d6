import request from '@/utils/axios-api'

/**
 * @desc 查询所有角色
 */
export function apiBaseRoleList(data) {
  return request({
    url: '/api/base/base/role/page',
    method: 'POST',
    data
  })
}

/**
 * @desc 新增角色
 */
export function addRole(data) {
  return request({
    url: '/api/base/base/role',
    method: 'POST',
    data
  })
}
/**
 * @desc 查询角色信息
 */
export function searchRoleDetail(data) {
  return request({
    url: '/api/base/base/role/' + data,
    method: 'GET'
  })
}
/**
 * @desc 修改角色
 */
export function editRole(data) {
  return request({
    url: '/api/base/base/role',
    method: 'PUT',
    data
  })
}
export function editRoleApi(data) {
  return request({
    url: '/api/base/base/role/all',
    method: 'PUT',
    data
  })
}
/**
 * @desc 删除角色
 */
export function delRole(data) {
  return request({
    url: '/api/base/base/role',
    method: 'DELETE',
    data
  })
}

/**
 * @desc 修改所有字段
 */
export function editAllField(data) {
  return request({
    url: '/api/base/base/role/all',
    method: 'PUT',
    data
  })
}
/**
 * @desc 检验角色编码
 */
export function checkRoleCode(data) {
  return request({
    url: '/api/base/base/role/check',
    method: 'GET',
    data
  })
}
/**
 * @desc 根据角色编码查询用户ID
 */
export function searchUserIdByRole(data) {
  return request({
    url: '/api/base/base/role/codes',
    method: 'GET',
    data
  })
}
/**
 * @desc 导出Excel
 */
export function exportExcel(data) {
  return request({
    url: '/api/base/base/role/export',
    method: 'POST',
    data
  })
}
/**
 * @desc 导入Excel
 */
export function importExcel(data) {
  return request({
    url: '/api/base/base/role/import',
    method: 'POST',
    data
  })
}
/**
 * @desc 预览Excel
 */
export function previewExcel(data) {
  return request({
    url: '/api/base/base/role/preview',
    method: 'POST',
    data
  })
}
/**
 * @desc 批量查询
 */
export function batchQuery(data) {
  return request({
    url: '/api/base/base/role/query',
    method: 'POST',
    data
  })
}
/**
 * @desc 查询角色拥有的资源id集合
 */
export function searchRoleResIds(data) {
  return request({
    url: '/api/base/base/role/roleAuthorityList',
    method: 'GET',
    data
  })
}
/**
 * @desc 给角色配置权限
 */
export function saveRolePermission(data) {
  return request({
    url: '/api/base/base/role/saveRoleAuthority',
    method: 'POST',
    data
  })
}
/**
 * @desc 给用户分配角色
 * @param {string} roleId 角色id
 * @param {array} userIdList 用户列表
 */
export function saveRoleForUser(data) {
  return request({
    url: '/api/base/base/role/saveUserRole',
    method: 'POST',
    data
  })
}
/**
 * @desc 查询角色的用户
 */
export function searchRoleUsers(data) {
  return request({
    url: '/api/base/base/role/userList',
    method: 'GET',
    data
  })
}
/**
 * @func
 * @desc 查询角色已关联用户信息
 */
export function getRoleUsers(roleId) {
  return request({
    url: `/api/base/base/user/role/${roleId}`,
    method: 'get'
  })
}
/**
 * @func
 * @desc 查询所有用户实体信息
 */
export function getAllUsersInfo(params) {
  return request({
    url: '/api/base/base/user/findAll',
    method: 'get',
    data: params
  })
}
/**
 * @func
 * @desc 查询用户关联菜单和资源
 */
export function getroleAuthority(roleId) {
  return request({
    url: `/api/base/base/roleAuthority/${roleId}`,
    method: 'get'
  })
}

/**
 * @desc 根据菜单id查询功能
 * @param {string} roleId 角色id
 * @param {array} userIdList 用户列表
 */
export function apiBaseMuenFunction(data) {
  return request({
    url: '/api/base/base/function/page',
    method: 'POST',
    data
  })
}
/**
 * @desc 查询所有菜单和功能树
 */
export function apiAllMenuFuncTree() {
  return request({
    url: '/api/base/base/menu/menuFunctionTree',
    method: 'post'
  })
}

/**
 * @func
 * @desc 查询角色关联的用户和用户组
 */
export function apiBaseRoleUser(roleId) {
  return request({
    url: `/api/base/base/role/userIdOrUserGroupId?roleId=` + roleId,
    method: 'get'

  })
}

/**
 * @func
 * @desc 角色信息
 */
export function apiBaseRoleGetInfo(id) {
  return request({
    url: `/api/base/base/role/${id}`,
    method: 'get'
  })
}

// 验证角色是否可以修改
export function checkRole(id) {
  return request({
    url: `/api/alm/alm/projectRole/getProjectUserRole/` + id,
    method: 'get'
  })
}
// 查询当前登录用户的菜单资源树
export function getCurrentMenuTree(id) {
  return request({
    url: `/api/base/base/menu/currentMenuFunctionTree`,
    method: 'post'
  })
}
// 获取项目角色
export function queryProjectRole(data) {
  return request({
    url: `/api/alm/alm/projectRole/query`,
    method: 'post',
    data
  })
}
