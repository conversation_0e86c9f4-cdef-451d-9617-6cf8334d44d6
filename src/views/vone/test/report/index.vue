<!-- 测试报告table -->
<template>
  <page-wrapper>
    <header class="toolbar">

      <div class="right_bar">
        <el-button type="primary" icon="iconfont el-icon-tips-plus-circle" :disabled="!$permission('testm_reports_add')" @click="openAddDialog">生成报告</el-button>
      </div>
    </header>
    <div :style="{height: $tableHeight}">
      <vxe-table
        ref="project-report-table"
        class="vone-vxe-table draggTable"
        border
        height="auto"
        show-overflow="tooltip"
        :loading="pageLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth:'120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column field="testReportName" title="报告名称" show-overflow-tooltip>
          <template v-slot="{ row }">
            <span>{{ row.testReportName }}</span>
            <span v-if="row.createTime">_{{ row.createTime | formatVal }}</span>
          </template>
        </vxe-column>
        <vxe-column field="plan" title="测试计划" width="360" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.planId && scope.row.echoMap">
              <span v-for="(item,index) in scope.row.echoMap.planName" :key="index">
                {{ item?item:'' }} </span>
            </span>
            <span v-else>{{ scope.row.planId }}</span>
          </template>
        </vxe-column>
        <vxe-column title="创建人" width="150">
          <template v-slot="{ row }">
            <span v-if="row.createdBy && row.echoMap&&row.echoMap.createBy">
              <vone-user-avatar :avatar-path="row.echoMap.createBy.avatarPath" :name="row.echoMap.createBy.name" />
            </span>

          </template>
        </vxe-column>
        <vxe-column field="createTime" title="创建时间" width="165" sortable show-overflow-tooltip>
          <template v-slot="{ row }">
            {{ row.createTime }}
          </template>
        </vxe-column>
        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <template>
              <el-tooltip class="item" content="查看" placement="top">
                <el-button type="text" :disabled="!$permission('testm_reports_query')" icon="iconfont el-icon-edit-visible" @click="visiteDetail(row)" />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="删除" placement="top">
                <el-button type="text" :disabled="!$permission('testm_reports_del')" icon="iconfont el-icon-application-delete" @click="delRow(row)" />
              </el-tooltip>
            </template>
          </template>
        </vxe-column>
      </vxe-table>
      <vone-pagination ref="pagination" :total="tableData.total" @update="getPlanReports" />
    </div>

    <!-- 生成报告 -->
    <relateCase ref="relateCase" :visible.sync="addCaseVisible" @success="saveProCases" />
    <!-- 报告详情 -->
    <reportDetail :visible.sync="detailVisible" :report-data="detailData" />
  </page-wrapper>
</template>

<script>
import dayjs from 'dayjs'
import storage from 'store'

import { addtestReport, deltestReport } from '@/api/vone/testreport'
import { listtestReport } from '@/api/vone/testreport'

import reportDetail from './reportDetail.vue'
import relateCase from './relate-case'

import { catchErr } from '@/utils'

export default {
  components: {
    reportDetail,
    relateCase
  },
  filters: {
    formatVal(val) {
      if (!val) return ''
      return dayjs(val).format('YYYY-MM-DD')
    }
  },
  data() {
    return {
      addCaseVisible: false,
      pageLoading: false,
      tableData: { records: [], total: 0 },
      detailVisible: false, // 报告详情
      detailData: {}, // 查看报告数据
      userMap: {}
    }
  },
  computed: {
    loginUser() {
      return storage.get('user')
    }
  },
  mounted() {
    this.getPlanReports()
  },
  methods: {
    // 生成报告
    async saveProCases(list, planId, name) {
      const params = {
        createdBy: this.loginUser.id,
        planIds: list.map(v => v.id),
        product: true,
        testReportName: name
      }
      const [res, err] = await catchErr(addtestReport(params))
      if (err) return
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$refs.relateCase.btnLoading = false
      this.$refs.relateCase.onClose()
      this.getPlanReports()
    },
    // 查人员
    async getUserList(userId, tableIdxs = []) {
      if (this.userMap[userId]) {
        tableIdxs.forEach(idx => {
          this.$set(this.tableData.records[idx], 'userData', this.userMap[userId])
        })
        return
      }
      const res = await this.$store.dispatch('user/getUserData', userId)
      if (res.isSuccess) {
        this.userMap[userId] = res.data
        tableIdxs.forEach(idx => {
          this.$set(this.tableData.records[idx], 'userData', this.userMap[userId])
        })
      }
    },
    // 查询报告列表
    async getPlanReports() {
      this.pageLoading = true
      const tableAttr = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
      const params = {
        ...tableAttr,
        extra: {},
        model: {
          product: true
        }
      }

      const [res, err] = await catchErr(listtestReport(params))

      this.pageLoading = false
      if (err) return
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.tableData = res.data
      this.tableData.total = res.data.total

      // 保存表格相同用户index
      const tableUserMap = {}
        res.data?.records?.forEach((ele, i) => {
          if (ele.leadingBy) {
            const userIds = tableUserMap[ele.leadingBy] ?? []
            tableUserMap[ele.leadingBy] = [...userIds, i]
          }
        })
        for (const key in tableUserMap) {
        // 请求用户信息并更新表格
          this.getUserList(key, tableUserMap[key])
        }
    },
    // 查看报告
    async visiteDetail(row) {
      this.detailVisible = true
      this.detailData = row
    },
    // 删除报告
    async delRow(row) {
      await this.$confirm(`确认删除【${row.testReportName + (row.createTime ? '_' + row.createTime.split(' ')[0] : '')}】吗?`, '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: 'delConfirm',
        showClose: false,
        type: 'warning'
      })
      const [res, err] = await catchErr(deltestReport([row.id]))
      if (err) return
      if (res.isSuccess) {
        this.$message.success('删除成功')
        this.getPlanReports()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 生成报告
    openAddDialog() {
      this.addCaseVisible = true
    }
  }
}
</script>
<style lang='scss' scoped>
.toolbar {
  display: flex;
  justify-content: right;
  align-items: center;
  height: 48px;
  margin: -16px -16px 16px -16px;
  padding: 0 16px;
  border-bottom: 1px solid #f2f3f5;
}
</style>
