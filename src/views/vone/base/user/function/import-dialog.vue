<template>
  <div>
    <el-dialog
      :title="`${title}导入`"
      width="40%"
      v-model:value="visible"
      :before-close="onClose"
      :close-on-click-modal="false"
    >
      <div v-if="showError == 0" class="invalid init">
        <el-icon class="iconfont"><el-icon-tips-exclamation-circle /></el-icon>
        平台限制{{ importData.limit || 0 }}条用户,当前可导入用户数{{
          importData.importUser || 0
        }}条
      </div>
      <!-- 全部成功 -->
      <div v-else-if="showError == 1" class="invalid success">
        <el-icon class="iconfont"><el-icon-tips-check-circle /></el-icon>
        {{ importData.successMsg }}
      </div>
      <!-- 成功加失败 -->
      <div v-else-if="showError == 2" class="invalid init">
        <el-icon class="iconfont"><el-icon-tips-check-circle /></el-icon>
        {{ importData.successMsg }}
        <el-button
          size="small"
          type="text"
          :loading="loading"
          @click.stop="downloadFun"
          >下载错误文件</el-button
        >
      </div>

      <section class="sectionLoad">
        <div class="flexRow">
          <span>
            <strong> 下载导入模版 </strong>
            <p>根据模版完善表格内容</p>
          </span>
          <span>
            <el-button :icon="elIconEditDownload" @click="getDownload">
              下载模板
            </el-button>
          </span>
        </div>
      </section>

      <section class="sectionLoad">
        <div class="upload">
          <el-upload
            style="width: 100%"
            class="upload-demo"
            drag
            action
            :limit="1"
            :auto-upload="true"
            :multiple="false"
            :http-request="httpRequest"
            :before-upload="beforeUpload"
          >
            <div class="el-upload__text">
              <el-icon><el-icon-upload /></el-icon
              >将文件拖到此处，或<em>点击上传</em>文件
              <br />

              <span> 只能上传xls类型文件 </span>
            </div>
          </el-upload>
        </div>
      </section>

      <template v-slot:footer>
        <div class="dialog-footer">
          <el-button @click="onClose">取消</el-button>
          <el-button
            v-if="!showError"
            type="primary"
            :loading="upLoading"
            @click="submit"
            >确定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from "../../../../../utils/gogocodeTransfer";

import { downloadTemplate, apiImport } from "@/api/common/index";

import { download } from "@/utils";
import { getMaxUserNum } from "@/api/vone/base/user";

export default {
  components: {
    // errorImport
  },
  props: {
    visible: {
      type: Boolean,
      default: true,
    },
    id: {
      type: String,
      default: undefined,
    },
    title: {
      type: String,
      default: undefined,
    },
    url: {
      type: String,
      default: undefined,
    },
    importUrl: {
      type: String,
      default: undefined,
    },
    type: {
      type: String,
      default: undefined,
    },
    data: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      importData: {},
      showError: 0,
      form: {},
      upLoading: false,
      hasError: false,
      dialogFormVisible: false,
      errorImportParam: { visible: false },

      info: {},
      loading: false,
    };
  },
  mounted() {
    this.getMaxUserNum();
  },
  methods: {
    async getMaxUserNum() {
      const res = await getMaxUserNum();
      if (!res.isSuccess) {
        return this.$message.warning(res.msg);
      }
      // this.$set(res.data, 'thisUserNum', Number(res.data.thisUserNum))

      const limit = Number(res.data.maxUserNum);
      const user = Number(res.data.thisUserNum);
      const userIn = limit - user;
      const importUser = userIn > 0 ? userIn : 0;

      this.importData["limit"] = limit;
      this.importData["importUser"] = importUser;
    },
    onClose() {
      $emit(this, "update:visible", false);
      if (this.showError) {
        return;
      }
      this.$refs.form.resetFields();
    },
    httpRequest(file) {
      this.form["file"] = file.file;
    },
    // 下载下载批量用户导入模板
    async getDownload() {
      // 项目下需要传项目id,其它地方不需要(缺陷，任务模板下载的url，projectId传参特殊)
      try {
        download(
          `${this.title}批量导入模版.xls`,
          await downloadTemplate(this.url, {
            projectId: !this.type ? this.$route.params.id : null,
          })
        );
      } catch (e) {
        this.$message.error("模板下载失败");
        return;
      }
    },
    async submit() {
      try {
        this.upLoading = true;
        const param = {
          file: this.form.file,
          ...this.data,
        };
        const res = await apiImport(this.importUrl, param);
        this.upLoading = false;
        if (!res.isSuccess) {
          this.$message.warning(res.msg);
          return;
        }

        if (res.extra) {
          this.info = res;
          this.showError = 2;
          this.importData.successMsg = res.msg;
        } else {
          $emit(this, "success");
          this.showError = 1;
          this.importData.successMsg = res.msg;
        }
      } catch (e) {
        this.upLoading = false;
      }
    },
    // 文件验证规则
    beforeUpload(file) {
      const isXls = file.name.substr(file.name.lastIndexOf(".") + 1) === "xls";
      // const size = file.size / 1024 <= 5
      if (!isXls) {
        this.$message.error("只支持xls类型文件!");
      }
      // if (!size) {
      //   this.$message.error('最大支持5M的文件!')
      // }

      return isXls;
    },
    downloadFun() {
      this.loading = true;

      const blob = this.dataURLtoBlob(this.info.extra.fileStream);
      const link = document.createElement("a");
      link.style.display = "none";
      link.href = URL.createObjectURL(blob);
      link.setAttribute("download", this.info.extra.fileName);
      document.body.appendChild(link);
      link.click();

      this.loading = false;
    },
    dataURLtoBlob(base64Str) {
      var bstr = atob(base64Str);
      var n = bstr.length;
      var u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      // 下载的是excel格式的文件
      return new Blob([u8arr], { type: "application/vnd.ms-excel" });
    },
  },
  emits: ["update:visible", "success"],
};
</script>

<style lang="scss" scoped>
.invalid {
  height: 38px;
  border-radius: 2px;
  line-height: 38px;
  padding: 0 12px;
  color: #777f8e;
  display: flex;
  align-items: center; /*// margin-bottom: 26px;*/
  i {
    color: #f7a500;
    margin-right: 6px;
  }
}
.sectionLoad {
  margin-top: 10px;
  background-color: #f7f7f7;
  border: 1px solid #d6dae0;
  padding: 12px;
  border-radius: 4px;
  .flexRow {
    display: flex;
    justify-content: space-between;
    align-items: center;
    p {
      font-size: 12px;
      color: var(--font-second-color);
    }
  }
  .upload {
    text-align: center;
  }
}
.el-icon-upload {
  font-size: 20px; /*// vertical-align: bottom;*/
}
:deep(.el-upload) {
  width: 100% !important;
}
:deep(.el-upload-dragger) {
  width: 100% !important;
}
.init {
  border: 1px solid #ffe07a;
  background-color: #fffbe6;
}
.success {
  background-color: #e1faed;
  border: 1px solid #6ee6b2;
}
.error {
  background-color: #fff1f0;
  border: 1px solid #ffafad;
  i {
    color: #db2c3a;
  }
}
</style>
