<template>
  <div>
    <el-card style="margin-bottom: 10px">
      <el-row>
        <el-col :span="12">
          <div class="name">{{ ruleForm.name }}</div>
        </el-col>
      </el-row>

      <el-form :model="ruleForm" class="formBox">
        <el-row>
          <el-col :span="8">
            <el-form-item label="CI/CD类型">
              <span v-if="ruleForm.type">{{ ruleForm.type.desc }}</span>
              <span v-else>--</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="维护人">
              <span
                v-if="
                  ruleForm.responsible &&
                  ruleForm.echoMap &&
                  ruleForm.echoMap.responsible
                "
              >
                <vone-user-avatar
                  :avatar-path="ruleForm.echoMap.responsible.avatarPath"
                  :name="ruleForm.echoMap.responsible.name"
                />
              </span>
              <span v-else> {{ ruleForm.responsible }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="应用类型">
              <span v-if="ruleForm.programType">{{
                ruleForm.programType.desc
              }}</span>
              <span v-else>--</span>
            </el-form-item>
          </el-col>

          <el-col v-if="ruleForm.type && ruleForm.type.code != 'CD'" :span="8">
            <el-form-item label="仓库地址" prop="adress">
              {{ ruleForm.adress }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="更新时间">
              {{ ruleForm.updateTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="分支策略">
              <span
                v-if="
                  ruleForm.branchingStrategyKey &&
                  ruleForm.echoMap.branchingStrategyKey
                "
                >{{ ruleForm.echoMap.branchingStrategyKey.name }}</span
              >
              <span v-else>--</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属机构">
              <el-tooltip
                v-if="ruleForm.orgId && ruleForm.echoMap.orgId"
                effect="dark"
                :content="ruleForm.echoMap.orgId.name"
                placement="top-end"
              >
                <span>{{ ruleForm.echoMap.orgId.name }}</span>
              </el-tooltip>
              <span v-else>--</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="环境">
              <el-tag
                v-for="(item, index) in regionList"
                :key="index"
                style="margin-right: 5px"
              >
                {{ item }}</el-tag
              >
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="应用描述">
              {{ ruleForm.description }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <vone-server-graph :graph-data="graphData" />
  </div>
</template>

<script>
import {
  apiBaseSelectBaseDatagById,
  apiBuniesSelectWarehouseBySystemId,
  apiCmdbServerGraph,
} from '@/api/vone/cmdb/server'
import { apiCmdbSelectModuleBySystemId } from '@/api/vone/cmdb/application'

export default {
  data() {
    return {
      gitData: {}, // 代码库信息
      projectInfo: [], // 项目信息
      regionList: [], // 环境标签数据
      ruleForm: {
        echoMap: {},
        type: {},
      },
      graphData: {},
    }
  },
  created() {
    this.getBaseInfo()
    this.getTopoGraphInfo()
  },
  methods: {
    // 查询仓库回显数据
    async getGitData(systemId) {
      const res = await apiBuniesSelectWarehouseBySystemId(systemId)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.gitData = res.data
    },
    // 查询工程回显数据
    async getProjectInfo(systemId) {
      const res = await apiCmdbSelectModuleBySystemId(systemId)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.projectInfo = res.data
    },
    // 查询基本信息
    async getBaseInfo() {
      const { systemId } = this.$route.params
      await this.getGitData(systemId)
      await this.getProjectInfo(systemId)
      const res = await apiBaseSelectBaseDatagById(systemId)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      // 环境标签
      this.regionList = res.data.applicationEnvs
        ? res.data.applicationEnvs.map((r) => r.echoMap.envKey.code)
        : []

      this.ruleForm = res.data
      if (this.gitData) {
        const path = this.gitData.echoMap.engineId.engineUrl
        const fullPath =
          `${path}/${this.ruleForm.codeRepPath}` +
          (this.projectInfo.length ? `/${this.projectInfo[0].name}.git` : '')
        this.ruleForm['adress'] = fullPath
      } else {
        this.ruleForm['adress'] = '--'
      }
    },
    // 查询拓扑图数据
    async getTopoGraphInfo() {
      const res = await apiCmdbServerGraph(this.$route.params.systemId)
      if (res.isSuccess) {
        this.graphData = res.data
      }
    },
  },
}
</script>
