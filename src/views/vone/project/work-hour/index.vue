<template>
  <page-wrapper>
    <el-tabs
      v-model="tabActive"
      class="vone-tab-line"
      style="margin: -16px -16px 0px -16px"
    >
      <el-tab-pane
        v-for="item in types"
        :key="item.key"
        :label="item.tab"
        :name="item.key"
        :disabled="item.disabled"
      />
      <template>
        <component :is="tabActive" v-if="tabActive" />
      </template>
    </el-tabs>
  </page-wrapper>
</template>

<script>
import submit from './components/submit.vue'
import confirm from './components/confirm.vue'
export default {
  components: {
    submit,
    confirm,
  },
  data() {
    return {
      tabActive: 'submit',
      types: [
        {
          key: 'submit',
          tab: '工时填报',
        },
        {
          key: 'confirm',
          tab: '工时确认',
        },
      ],
    }
  },
}
</script>
