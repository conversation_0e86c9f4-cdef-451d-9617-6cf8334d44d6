import request from '@/utils/axios-api'

/**
 * @desc 查询所有产品
 * @param {object}
 */
export function apiProductData(data) {
  return request({
    url: `/api/product/product/productInfo/page`,
    method: `post`,
    data
  })
}
// 查看产品类型
export function apiProductType(data) {
  return request({
    url: `/api/product/product/productType/query`,
    method: `post`,
    data
  })
}
// 获取机构
export function getOrgData(data) {
  return request({
    url: '/api/base/base/org/tree',
    method: 'get'
  })
}
// 操作产品
export function operationProduct(data, method) {
  return request({
    url: `/api/product/product/productInfo`,
    method: method,
    data
  })
}

// 根据产品id查询详情
export function getProductDetail(productId) {
  return request({
    url: `/api/product/product/productInfo/${productId}`,
    method: `get`
  })
}
// 根据产品版本单体查询
export function getProductDetailbyId(id) {
  return request({
    url: `/api/product/product/productVersion/${id}`,
    method: `get`
  })
}
// 根据产品关联产品
export function ProductConnect(data) {
  return request({
    url: `/api/productset/productProductset/saveProduct`,
    method: `post`,
    data
  })
}
// 根据产品取消关联产品
export function ProductNoConnect(data) {
  return request({
    url: `/api/productset/productProductset/deletePorductProductset`,
    method: `post`,
    data
  })
}

// 根据产品id查询版本
export function getProductVersion(data) {
  return request({
    url: `/api/product/product/productVersion/page`,
    method: `post`,
    data
  })
}
// 操作版本
export function operationVersion(data, method) {
  return request({
    url: `/api/product/product/productVersion`,
    method: method,
    data
  })
}

// 模块列表
export function getModule(data) {
  return request({
    url: `/api/product/product/productModuleFunction/tree`,
    method: 'post',
    data
  })
}
// 模块列表非树形
export function getModuleList(data) {
  return request({
    url: `/api/product/product/productModuleFunction/query`,
    method: `post`,
    data
  })
}
// 操作模块
export function operationModule(data, method) {
  return request({
    url: `/api/product/product/productModuleFunction`,
    method: method,
    data
  })
}
// 查询可以关联的服务应用
export function searchAppForProduct(data) {
  return request({
    url: '/api/product/product/productApplication/selectApplicationaAll',
    method: 'post',
    data
  })
}

// 查询服务应用列表
export function searchApp(data) {
  return request({
    url: '/api/product/product/productApplication/page',
    method: 'post',
    data
  })
}
// 操作服务应用
export function operationApp(data, productId, method) {
  return request({
    url: `/api/product/product/productApplication/${productId}`,
    method: method,
    data
  })
}
/**
 * @desc 查询所有产品--不分页
 * @param {string} type
 */
export function apiProductNoPage(data) {
  return request({
    url: `/api/product/product/productInfo/query`,
    method: `post`,
    data
  })
}

// 查询产品版本计划
export function getProductVersionList(data) {
  return request({
    url: `/api/product/product/productVersion/query`,
    method: `post`,
    data
  })
}
// 查询产品的缺陷趋势
export function findTrendByProductId(productId) {
  return request({
    url: `/api/alm/alm/bug/findTrendByProductId/${productId}`,
    method: 'get'
  })
}
// 查询产品的缺陷趋势
export function getModuleTreeByProId(data) {
  return request({
    url: '/api/product/product/productModuleFunction/tree',
    method: 'get',
    data
  })
}
// 获取产品关联的服务应用数量及模块数量、功能点数量
export function getcount(params) {
  return request({
    url: '/api/product/product/productInfo/getApplicationCountAndModuleFunctionCount',
    method: 'get',
    data: params
  })
}
// 查询产品授权信息
export function productOrg(id, data) {
  return request({
    url: `/api/product/product/productOrg/${id}`,
    method: 'get',
    data
  })
}
// 修改产品授权信息
export function productOrgPut(data) {
  return request({
    url: '/api/product/product/productOrg',
    method: 'put',
    data
  })
}
// 查询产品集授权信息
export function productsetOrg(id, data) {
  return request({
    url: `/api/productset/productset/org/${id}`,
    method: 'get',
    data
  })
}
// 修改产品集授权信息
export function productSetOrgPut(data) {
  return request({
    url: '/api/productset/productset/org',
    method: 'put',
    data
  })
}
// 查询产品关联的产品list
export function queryListByProductId(data) {
  return request({
    url: `/api/product/product/productInfo/queryListByProductId`,
    method: 'post',
    data
  })
}
// 查询产品关联的产品list
export function queryFiveProduct(data) {
  return request({
    url: `/api/product/product/productInfo/queryFiveProduct`,
    method: 'post',
    data
  })
}
// 查询产品拓扑
export function getProductMessage(params) {
  return request({
    url: `/api/product/product/productInfo/getProductMessage`,
    method: 'get',
    data: params
  })
}
// 查询产品用户故事
export function getMapList(productId, productVersionId, data) {
  return request({
    url:
      `/api/alm/alm/requirement/getRequirementGroupByProductIdAndProductVersionId/${productId}?productVersionId=` +
      productVersionId,
    method: 'post',
    data
  })
}

// 查询产品迭代ist
export function projectPlan(data) {
  return request({
    url: `/api/alm/alm/projectPlan/page`,
    method: 'post',
    data
  })
}
// 项目计划状态统计
export function projectPlanState(data) {
  return request({
    url: `/api/alm/alm/projectPlan/state`,
    method: 'post',
    data
  })
}

// 查询版本路线图
export function productVersionCircuit(productId) {
  return request({
    url: `/api/product/product/productVersion/${productId}/circuit`,
    method: 'get'
  })
}
// 项目计划状态统计
export function queryViewByTime(data) {
  return request({
    url: `/api/alm/alm/bug/queryViewByTime`,
    method: 'post',
    data
  })
}
// 查询可关联的项目
export async function getAssociableProjectInfo(productId, data) {
  return request({
    url: `/api/alm/projectProduct/getAssociableProjectInfo/${productId}`,
    method: 'get',
    data
  })
}
// 批量添加关联项目
export async function setBatchSave(data) {
  return request({
    url: `/api/alm/projectProduct/batchSave`,
    method: 'post',
    data
  })
}
// 批量取消关联项目
export function batchDelete(data) {
  return request({
    url: `/api/alm/projectProduct/batchDelete`,
    method: 'DELETE',
    data
  })
}
/**
 * 获取产品下的版本功能模块
 */
export function getVersionModuleTreeByProductId(productId) {
  return request({
    url: `/api/product/product/productVersion/tree/${productId}`,
    method: 'post'
  })
}

/**
 * 获取版本功能模块的单体查询
 */
export function getVersionModuleInfo(id) {
  return request({
    url: `/api/product/product/productModuleFunction/${id}`,
    method: 'get'
  })
}

/**
 * 导出excel
 */
export function getExcelExport(productId) {
  return request({
    url: `/api/product/product/version/modlueFunction/excel/export/${productId}`,
    method: 'get'
  })
}
/**
 * 下载版本功能模块导入模板
 */
export function getDownloadImportTemplate() {
  return request({
    url: `/api/product/product/version/modlueFunction/excel/downloadImportTemplate`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json; charset=UTF-8'
    },
    responseType: 'blob' // 在请求中加上这一行，特别重要
  })
}
/**
 * 导入excel
 */
export function getExcelImport(productId, data) {
  return request({
    url: `/api/product/product/version/modlueFunction/excel/import/${productId}`,
    method: 'post',
    data: requestUtils.fileFormData(data)
  })
}
