<template>
  <div class="kanban_columns">
    <div class="kanban_columns-status">
      <div class="title">工作项状态</div>
      <div class="kanban_columns-status-body">
        <el-collapse v-model="active" accordion>
          <el-collapse-item
            v-for="item in workType"
            :key="item.id"
            :name="item.code"
          >
            <template v-slot:title>
              <i
                :class="['iconfont', item.icon]"
                :style="{ color: item.color }"
              />
              {{ item.classify.desc + "/" + item.name }}
            </template>
            <draggable
              class="drag_list-group"
              :list="statusMap[item.code]"
              group="statusCols"
              :data-code="item.code"
              :clone="(original) => cloneStatus(original, item)"
              :move="(e) => checkMove(e, item)"
              @start="dragstart"
              @end="(e) => dragend(e, item)"
            >
              <span
                v-for="ele in statusMap[item.code]"
                :key="ele.id"
                class="drag_list-item"
                :style="{ color: ele.color }"
              >
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="ele.name"
                  placement="left"
                >
                  <span>{{ ele.name }}</span>
                </el-tooltip>
              </span>
            </draggable>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
    <div class="kanban_columns-body">
      <div class="kanban_columns-scroll">
        <draggable
          class="kanban_columns-drags"
          :class="{ move_to: dragging }"
          :list="statusColumns"
          group="columns"
          @end="dragColumn"
        >
          <div v-for="item in statusColumns" :key="item.id" class="scroll_item">
            <header class="kanban_columns-title">
              <el-input
                v-if="exsitMap[item.id]"
                ref="editTitle"
                v-model="item.name"
                placeholder="请输入"
                @blur="editColumnTitle(item)"
              />
              <template v-else>
                <span>{{ item.name }}</span>
                <el-popover
                  ref="columnPopper"
                  placement="bottom"
                  trigger="click"
                  popper-class="column-options"
                >
                  <div class="options">
                    <span style="cursor: pointer" @click="editTitle(item)">
                      <el-icon class="iconfont"
                        ><el-icon-application-rename
                      /></el-icon>
                      重命名
                    </span>
                    <span style="cursor: pointer" @click="deleteColumn(item)">
                      <el-icon class="iconfont"
                        ><el-icon-application-delete
                      /></el-icon>
                      删除
                    </span>
                  </div>
                  <template v-slot:reference>
                    <el-icon class="iconfont more"
                      ><el-icon-application-more
                    /></el-icon>
                  </template>
                </el-popover>
              </template>
            </header>
            <div class="scroll_body">
              <draggable
                class="scroll_body-drags"
                :class="{ move_to: dragging && moveId === item.id }"
                :list="item.statuses"
                :data-id="item.id"
                group="statusCols"
                :move="(e) => checkMove(e)"
                @start="dragstart"
                @end="(e) => dragend(e)"
              >
                <div
                  v-for="ele in item.statuses"
                  :key="ele.id + '_' + ele.status.code"
                  class="scroll_body-item"
                >
                  <span class="scroll_body-title">
                    <span
                      class="scroll_body-status"
                      :style="{ color: ele.status.color }"
                      >{{ ele.status.name }}</span
                    >
                    <span class="scroll_body-name">{{ ele.name }}</span>
                  </span>
                  <el-icon class="iconfont del"><el-icon-tips-close /></el-icon>
                </div>
              </draggable>
            </div>
          </div>
        </draggable>
      </div>
      <span class="kanban_columns-title add" @click="addColumn">
        <el-icon><el-icon-plus /></el-icon>
        新增列
      </span>
    </div>
  </div>
</template>

<script>
import {
  ApplicationRename as ElIconApplicationRename,
  ApplicationDelete as ElIconApplicationDelete,
  ApplicationMore as ElIconApplicationMore,
  TipsClose as ElIconTipsClose,
  Plus as ElIconPlus,
} from "@element-plus/icons-vue";
import {
  $on,
  $off,
  $once,
  $emit,
} from "../../../../../../utils/gogocodeTransfer";
import { uuid } from "vue-uuid";
import draggable from "vuedraggable";
export default {
  components: {
    draggable,
    ElIconApplicationRename,
    ElIconApplicationDelete,
    ElIconApplicationMore,
    ElIconTipsClose,
    ElIconPlus,
  },
  props: {
    workType: {
      type: Array,
      default: () => [],
    },
    savedColumns: {
      type: Array,
      default: () => [],
    },
    statusMap: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      active: "",
      statusColumns: [],
      exsitMap: {},
      extraItem: {},
      increase: false,
      dragging: false,
      moveId: "",
    };
  },
  watch: {
    workType(val, old) {
      this.calcExtra(val, old);
      if (!this.increase) this.deleteHeaderColumns(this.extraItem);
    },
    savedColumns(val) {
      this.statusColumns = [...val];
    },
  },
  mounted() {
    // 更新列数据
    this.calcExtra(this.workType, []);
    this.statusColumns = [...this.savedColumns];
  },
  methods: {
    cloneStatus(original, item) {
      return { ...item, status: original };
    },
    dragstart(evt) {
      this.dragging = true;
      this.active = evt?.item?._underlying_vm_?.code;
    },
    dragend(evt, item) {
      const { to, from } = evt;
      if (from === to) return;
      const state = evt.item._underlying_vm_;
      const dragType = to.dataset.code
        ? "pull"
        : from.dataset.code
        ? "push"
        : null;
      const statusId =
        dragType === "pull"
          ? from.dataset.id
          : dragType === "push"
          ? to.dataset.id
          : null;
      this.dragging = false;

      const exsitId = item
        ? item.id + "_" + state.status.code
        : state.id + "_" + state.status.code;
      if (to.className.indexOf("drag_list-group") > -1) {
        // 设置左侧工作项状态值
        this.statusMap[state.code] = this.statusMap[state.code].map(
          (v) => v.status || v
        );
      }
      if (dragType) {
        const col = this.statusColumns.find((v) => v.id === statusId);
        // 设置拖拽的列存在状态
        col.exsited[exsitId] = dragType === "push";
      } else {
        const fromCol = this.statusColumns.find(
          (v) => v.id === from.dataset.id
        );
        const toCol = this.statusColumns.find((v) => v.id === to.dataset.id);
        fromCol.exsited[exsitId] = false;
        toCol.exsited[exsitId] = true;
      }
      $emit(this, "updateCols", this.statusColumns);
    },
    checkMove(evt, item) {
      const { id } = evt.to.dataset;
      this.moveId = id;
      if (item) {
        const activeId = item.id + "_" + evt.draggedContext.element.code;
        const col = this.statusColumns.find((col) => col.exsited[activeId]);
        if (col) return false;
      }
      return true;
    },
    // 设置默认显示列
    refreshColumns() {
      const stateList = this.statusMap[this.extraItem.code];
      stateList.length > 0 && this.addHeaderColumns(stateList, this.extraItem);
    },
    calcExtra(news, olds) {
      this.increase = news.length > olds.length;
      if (news.length === 0) {
        this.statusColumn = [];
        this.extraItem = olds[0] || {};
        return;
      }
      this.active = news[news.length - 1]?.code;
      // 区分当前操作是新增或删除
      const [moreList, lessList] = this.increase ? [news, olds] : [olds, news];

      // 筛选出操作的项
      const extraItem =
        lessList.length === 0
          ? moreList[0]
          : moreList.find((val) => !lessList.find((v) => v.id === val.id));

      this.extraItem = extraItem;
      const stateList = this.statusMap[this.extraItem.code];
      if (stateList && this.increase)
        this.addHeaderColumns(stateList, this.extraItem);
    },
    // 新增工作项类型
    addHeaderColumns(stateList, extra) {
      // 不存在列
      if (this.statusColumns.length === 0) {
        this.statusColumns = stateList.map((v) => {
          const obj = {
            ...v,
            id: v.id || uuid.v1(),
            exsited: { [extra.id + "_" + v.code]: true },
            statuses: [
              {
                status: v,
                stateCode: v.code,
                typeCode: extra.code,
                classifyCode: extra.classify.code,
                ...extra,
              },
            ],
          };
          this.exsitMap[obj.id] = false;
          return obj;
        });
      } else {
        stateList.map((val) => {
          // 查询是否存在同名列
          const item = this.statusColumns.find(
            (item) => item.name === val.name
          );
          const obj = {
            status: val,
            stateCode: val.code,
            typeCode: extra.code,
            classifyCode: extra.classify.code,
            ...extra,
          };
          const exsitId = extra.id + "_" + val.code;
          // 末尾直接添加一列
          if (!item) {
            const col = {
              ...val,
              id: val.id || uuid.v1(),
              exsited: { [exsitId]: true },
              statuses: [obj],
            };
            this.statusColumns = [...this.statusColumns, col];
            this.exsitMap[col.id] = false;
          } else {
            // 当前项是否存在
            if (!item.exsited[exsitId]) {
              item.exsited[exsitId] = true;
              item.statuses = [...item.statuses, obj];
            }
          }
        });
      }
      $emit(this, "updateStatusCols", { [this.extraItem.code]: [] });
      $emit(this, "updateCols", this.statusColumns);
    },
    deleteHeaderColumns(extra) {
      const exsitId = extra.id;
      const recycle = [];
      this.statusColumns = this.statusColumns.filter((col) => {
        const keys = Object.keys(col.exsited);
        // 删除的状态值保存回原状态map中
        const finded = col.statuses.find((v) => v.id === exsitId);
        finded && recycle.push(finded.status);
        // 当前列存在，移除工作项类型
        if (keys.some((key) => key.indexOf(exsitId) > -1)) {
          col.exsited = keys
            .filter((key) => key.indexOf(exsitId) == -1)
            .reduce((acc, cur) => (acc[cur] = true) && acc, {});
          col.statuses = col.statuses.filter((v) => v.id != exsitId);
          return col.statuses.length > 0;
        } else {
          return true;
        }
      });
      $emit(this, "updateCols", this.statusColumns);
      $emit(this, "updateStatusCols", { [extra.code]: recycle });
    },
    addColumn() {
      const obj = {
        id: uuid.v1(),
        name: "标题",
        exsited: {}, // 当前已加入的工作项状态id
        statuses: [], // 当前列保存的工作项状态
      };
      this.statusColumns.push(obj);
      this.exsitMap[obj.id] = false;
      $emit(this, "updateCols", this.statusColumns);

      this.$nextTick(() => {
        const scrollBody = document.querySelector(".kanban_columns-drags");
        scrollBody?.scrollTo({
          left: scrollBody.scrollWidth || 0,
          behavior: "smooth",
        });
        setTimeout(() => {
          this.editTitle(obj);
        }, 500);
      });
    },
    dragColumn() {
      $emit(this, "updateCols", this.statusColumns);
    },
    deleteStatus(item, ele) {
      const exsitId = ele.id + "_" + ele.status.code;
      item.exsited[exsitId] = false;
      item.statuses = item.statuses.filter(
        (val) => val.id + "_" + val.status.code != exsitId
      );
      this.active = ele.code;
      $emit(this, "updateStatusCols", { [ele.code]: [ele.status] });
      $emit(this, "updateCols", this.statusColumns);
    },
    editTitle(item) {
      this.exsitMap[item.id] = true;
      this.$nextTick(() => {
        this.$refs.editTitle?.[0]?.focus();
      });
    },
    editColumnTitle(item) {
      this.exsitMap[item.id] = false;
      $emit(this, "updateCols", this.statusColumns);
    },
    deleteColumn(item) {
      this.statusColumns = this.statusColumns.filter(
        (val) => val.id != item.id
      );
      const statuses = item.statuses.reduce((acc, cur) => {
        acc[cur.code] = acc[cur.code]
          ? [...acc[cur.code], cur.status]
          : [cur.status];
        return acc;
      }, {});
      $emit(this, "updateStatusCols", statuses);
      $emit(this, "updateCols", this.statusColumns);
    },
  },
  emits: ["updateCols", "updateStatusCols"],
};
</script>

<style lang="scss" scoped>
.kanban_columns {
  display: flex;
  height: 288px;
  margin-top: 12px;
  &-status {
    width: 210px;
    height: 100%;
    border: 1px solid #eaecf0;
    border-radius: 4px 0 0 4px;
    &-body {
      height: calc(100% - 40px);
      padding: 8px;
      overflow-y: overlay;

      :deep() {
        .el-collapse-item__header {
          height: 36px;
          line-height: 36px;
          margin-bottom: 8px;
          padding: 8px 0 8px 12px;
          border-radius: 2px;
          border: 1px solid #eaecf0;
          background-color: #f7f8fa;
          i {
            margin-right: 4px;
          }
        }
      }
    }
  }
  &-body {
    display: flex;
    gap: 0 8px;
    max-width: calc(100% - 210px);
    padding-right: 12px;
  }
  &-drags {
    display: flex;
    gap: 0 8px;
    overflow: auto;
    height: 100%;
    .scroll_ {
      &item {
        height: 100%;
        width: 180px;
      }
      &body {
        height: calc(100% - 40px);
        padding: 8px;
        overflow: auto;
        background-color: #fafafa;
        &-item {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          width: 100%;
          padding: 8px;
          background: #ffffff;
          border-radius: 2px;
          cursor: move;
          // &:not(:last-child) {
          //   margin-bottom: 8px;
          // }

          i {
            cursor: pointer;
            &:hover {
              color: #ed3e3e;
            }
          }
        }
        &-title {
          display: flex;
          align-items: center;
          height: 24px;
          font-size: 12px;
          font-weight: 400;
          color: #838a99;
        }
        &-status {
          display: flex;
          align-items: center;
          height: 100%;
          padding: 0 8px;
          white-space: nowrap;
          border: 1px solid currentColor;
          border-radius: 2px 0 0 2px;
          &.done {
            color: #2cc750;
            background-color: #d7fada;
          }
          &.process {
            color: #42aaff;
            background-color: #e6f7ff;
          }
          &.notstart {
            color: #8c8c8c;
            background-color: #f0f0f0;
          }
        }
        &-name {
          display: flex;
          align-items: center;
          height: 100%;
          padding: 0 8px;
          white-space: nowrap;
          border: 1px solid #f2f3f5;
          border-radius: 0 2px 2px 0;
        }
      }
    }
  }
  &-scroll {
    display: flex;
    gap: 0 8px;
    width: calc(100% - 100px);
    overflow: auto;
  }
  &-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    width: 180px;
    padding: 8px 16px;
    background: #f5f5f5;
    border-radius: 4px;
    cursor: grab;
    i.more {
      width: 24px;
      height: 24px;
      line-height: 24px;
      text-align: center;
      border-radius: 4px;
      color: #6b7385;
      &:hover {
        background: #eaecf0;
      }
    }
    &.add {
      width: 100px;
      justify-content: left;
      color: #6b7385;
      i {
        margin-right: 4px;
      }
    }
  }
  .title {
    padding: 8px 16px;
    height: 40px;
    font-weight: 500;
    color: #1d2129;
    border-bottom: 1px solid #eaecf0;
  }
}
.scroll_body {
  &-drags {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    width: 100%;
    height: 100%;
    .del {
      color: #6b7385;
    }
  }
}
.drag_list-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  width: 100%;
  height: 100%;
  min-height: 86px;
  &.move_to {
    background-color: #f0f7ff;
    border: 2px dashed var(--main-theme-color);
  }
}
.drag_list-item {
  padding: 0 8px;
  height: 24px;
  border: 1px solid currentColor;
  border-radius: 2px;
  cursor: grab;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
::-webkit-scrollbar-thumb {
  border: none;
}
:deep(.el-collapse-item__wrap) {
  border: none;
}
</style>

<style lang="scss">
.column-options {
  min-width: 100px;
  .options {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
}
</style>
