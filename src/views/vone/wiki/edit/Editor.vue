<template>
  <div class="amEditorVue2">
    <am-loading :loading="loading">
      <am-toolbar v-if="engine" :engine="engine" :items="items" />
      <div :class="['editor-wrapper']">
        <!--大纲-->
        <div v-if="outlineData.length > 0" class="data-toc-wrapper">
          <div class="data-toc-title">大纲</div>
          <div v-for="(item,index) in outlineData" :key="index" class="data-toc">
            <a
              key="{index}"
              :href="'#'+item.id"
              :class="'data-toc-item data-toc-item-'+item.depth"
            >
              {{ item.text }}
            </a>
          </div>
        </div>
        <!--内容-->
        <div class="editor-container">
          <div class="editor-content">
            <div ref="container" />
          </div>
        </div>
        <!--评论-->
        <!-- <CommentLayer
          ref="comment"
          :editor="engine"
          :member="member || {
            avatar: 'https://cdn-image.aomao.com/10016/avatar/2020/04/17/1587113793-da092550-5b12-477e-b229-631908d0ac2b.png',
            name: 'test',
            uuid: 'test'
          }"
          :on-update="onCommentRequestUpdate"
        /> -->
      </div>
      <!-- <div ref="view"></div> -->
    </am-loading>
  </div>
</template>
<script>
import { faker } from '@faker-js/faker'
import Engine, { $, isHotkey, Parser } from '@aomao/engine'
import AmLoading from './Loading.vue'
import { Outline } from '@aomao/plugin-heading'
import AmToolbar from '@/components/EditorAomao/toolbar/components/toolbar.vue'
import items from './constant'
import { plugins, cards, pluginConfig } from './config'
import * as Y from 'yjs'
import { withYjs, YjsEditor, YCursorEditor } from '@aomao/plugin-yjs'
import { WebsocketProvider } from '@aomao/plugin-yjs-websocket'
import storage from 'store'
// import CommentLayer from './comment/index.vue'

let provider = null
export default {
  components: {
    AmLoading,
    AmToolbar
    // CommentLayer
    // MapModal,
  },
  props: {
    docContent: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: true,
      engine: null,
      view: null,
      items: items || [],
      value: {},
      saveTimeout: null,
      connected: false,
      yjs: {
        url: 'ws://172.16.10.171:8088',
        id: this.$route.params.docId
      },
      member: {},
      members: {}, // 光标成员列表
      outlineData: [],
      doc: null,
      defaultValue: null
    }
  },
  watch: {
    docContent: {
      handler: function(val) {
        if (val) {
          this.$nextTick(() => {
            this.engine && this.engine.setValue(JSON.parse(val)?.value)
            this.defaultValue = JSON.parse(val)
          })
        } else {
          this.$nextTick(() => {
            this.engine && this.engine.setValue('')
            this.defaultValue = null
          })
        }
      },
      deep: true,
      immediate: true
    },
    connected: {
      handler: function(val) {
        if (val) {
          if (this.engine) {
            YjsEditor.connect(this.engine)
            this.watchCursor()
          }
        }
      }
      // immediate: true,
    },
    '$route.params.docId': {
      handler: function(val) {
        // this.yjs.id = val
        // this.$nextTick(async() => {
        // this.doc = new Y.Doc()
        // this.member = this.getMember()
        // await this.initEditor()
        // await this.createProvider()
        // await this.creatConnect()
        // await this.watchCursor()
        // })
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.doc = new Y.Doc()
    this.member = this.getMember()
    this.$nextTick(async() => {
      this.initEditor()
      // 创建 Yjs WebsocketProvider 对象
      this.createProvider()
      this.creatConnect()
      this.watchCursor()
    })

    document.addEventListener('keydown', this.docKeydown)
    this.$once('hook:beforeDestroy', () => {
      document.removeEventListener('keydown', this.docKeydown)
    })
    // this.yjs.id = this.$route.params.docId
  },
  destroyed() {
    if (this.engine) this.engine.destroy()
  },
  methods: {
    getMember() {
      const userInfo = storage.get('user')
      const member = {
        name: userInfo.name,
        avatar: userInfo.avatarPath,
        color: faker.color.rgb()
      }
      localStorage.setItem('member', JSON.stringify(member))
      return member
    },
    // 创建 Yjs WebsocketProvider 对象
    createProvider() {
      // 如果全局变量 yjs 不存在，或者 yjs.url 和 yjs.id 不存在，则返回
      if (!this.yjs.url || !this.yjs.id) {
        return
      }
      // 创建 Yjs WebsocketProvider 对象
      provider = new WebsocketProvider(this.yjs.url, this.yjs.id, this.doc, {
        connect: false
      })
      // 监听 Yjs WebsocketProvider 对象状态事件
      provider.on('status', this.handleStatus)
      // 在组件被卸载时，移除事件监听器
      this.$once('hook:beforeDestroy', () => {
        if (provider) {
          provider = null
        }
      })
    },
    // Yjs WebsocketProvider 对象状态事件处理器
    handleStatus(event) {
      const { status } = event
      if (status === 'connected') {
        this.connected = true
      } else if (status === 'connecting') {
        this.loading = true
      } else if (status === 'disconnected') {
        this.connected = false
      }
      this.loading = false
    },
    // 定义保存的方法
    async save() {
      if (!this.engine) return
      // const filterValue = this.engine.command.executeMethod(
      //   "mark-range",
      //   "action",
      //   "comment",
      //   "filter",
      //   value
      // );
      const value = this.engine?.model.toValue() || ''
      const filterValue = this.engine.comment
        ? this.engine.current.command.executeMethod(
          'mark-range',
          'action',
          'comment',
          'filter',
          value
        )
        : { value, paths: [] }
      return filterValue
    },
    autoSave() {
      if (this.saveTimeout) clearTimeout(this.saveTimeout)
      this.saveTimeout = setTimeout(() => {
        // const value = this.engine?.model.toValue() || ''
        this.$emit('saveDoc')
      }, 60000)
    },
    userSave() {
      if (!this.engine) return
      this.engine.model
        .toValueAsync(undefined, (pluginName, card) => {
          console.log(`${pluginName} 正在等待...`, card?.getValue())
        })
        .then((value) => {
          this.save(value)
        })
        .catch((data) => {
          console.log('终止保存：', data.name, data.card?.getValue())
        })
    },
    docKeydown() {
      if (!this.engine) return
      if (isHotkey('mod+s', event)) {
        event.preventDefault()
        this.userSave()
      }
    },
    initEditor() {
      // 容器加载后实例化编辑器引擎
      const container = this.$refs.container
      if (container) {
        // 实例化引擎
        const engine = new Engine(container, {
          // 启用的插件
          plugins,
          // 启用的卡片
          cards,
          // 所有的卡片配置
          config: pluginConfig
        })
        // 设置显示成功消息UI，默认使用 console.log
        engine.messageSuccess = (type, msg, ...args) => {
          this.$message.success(msg)
        }
        // 设置显示错误消息UI，默认使用 console.error
        engine.messageError = (type, error, ...args) => {
          this.$message.error(error)
        }
        // 设置显示确认消息UI，默认无
        engine.messageConfirm = (type, msg, ...args) => {
          return (
            new Promise() <
            boolean >((resolve, reject) => {
              this.$confirm({
                content: msg,
                onOk: () => resolve(true),
                onCancel: () => reject()
              })
            })
          )
        }
        // 卡片最大化时设置编辑页面样式
        engine.on('card:maximize', () => {
          $('.editor-toolbar').css('z-index', '9999').css('top', '55px')
        })
        engine.on('card:minimize', () => {
          $('.editor-toolbar').css('z-index', '').css('top', '')
        })
        // 监听编辑器值改变事件
        engine.on('change', () => {
          if (this.loading) return
          // 自动保存，非远程更改，触发保存
          const value = engine.model.toValue()
          this.autoSave(value)
          this.updateOutline()
        })
        // engine.on('select', () => { // 显示评论图标
        //   this.$refs.comment.getMarkRange()
        //   console.log('engine', engine)
        // })

        window.engine = engine
        window.Parser = Parser
        this.engine = engine
      }
    },
    // 创建连接
    creatConnect() {
      if (!this.engine) return
      // 设置编辑器值
      const value = this.defaultValue
        ? this.defaultValue.path && this.defaultValue.paths.length > 0
          ? this.engine.current.command.executeMethod(
            'mark-range',
            'action',
            'comment',
            'wrap',
            this.defaultValue.paths,
            this.defaultValue.value
          )
          : this.defaultValue.value
        : null
      // 连接到协作服务端，demo文档
      const handleCustomMessage = (message) => {
        const { action } = message
        if (value && action === 'initValue') {
          this.engine.setValue(value)
          this.engine.history.clear()
        }
      }
      if (this.yjs && provider) {
        provider.connect()
        provider.on('customMessage', handleCustomMessage)
        const sharedType = this.doc.get('content', Y.XmlElement)
        withYjs(this.engine, sharedType, provider.awareness, {
          data: this.member
        })
      } else {
        // 非协同编辑，设置编辑器值，异步渲染后回调
        this.engine.setValue(value, (count) => {
        })
      }

      this.$once('hook:beforeDestroy', () => {
        YjsEditor.disconnect(this.engine)
      })
    },
    updateOutline() {
      const outline = new Outline()
      const container = this.$refs.container
      const data = outline.getFromDom(container)
      this.outlineData = data || []
    },
    // 监听光标事件
    watchCursor() {
      // 获取编辑器引擎对象
      const engine = this.engine
      // 如果编辑器引擎对象不存在，则返回
      if (!engine) return
      // 定义光标位置变化的事件处理器
      const handleCursorChange = (event) => {
        // 获取添加的光标和删除的光标
        const { added, removed } = event
        // 处理添加的光标
        if (added.length > 0) {
          added.forEach((id) => {
            const newMember = YCursorEditor.cursorState(engine, id)
            if (newMember?.data) {
              this.$set(this.members, id, newMember.data)
            }
          })
          return { ...this.members }
        }
        // 处理删除的光标
        if (removed.length > 0) {
          removed.forEach((id) => {
            this.$delete(this.members, id)
          })
          return { ...this.members }
        }
      }
      // 监听编辑器的光标位置变化事件
      YCursorEditor.on(engine, 'change', handleCursorChange)
      // 在组件被卸载时，移除事件监听器
      this.$once('hook:beforeDestroy', () => {
        YCursorEditor.off(engine, 'change', handleCursorChange)
      })
    },
    // 评论
    // 广播通知更新评论列表吧
    onCommentRequestUpdate() {
      // otClient.current?.broadcast('updateCommentList');
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep li:not(.cdx-list__item) {
  list-style: auto;
}
#app {
  padding: 0;
}
#nav {
  position: relative;
}

.editor-toolbar {
  // position: fixed;

  width:100%;
  background: #ffffff;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.02);
  z-index: 1000;
}
.editor-wrapper {
  position: relative;
  width: 100%;
  // min-width: 1440px;
}

.editor-container {
  background: #fafafa;
  background-color: #fafafa;
  padding: 16px 0px;
  height: calc(100vh - 110px);
  width: 100%;
  margin: 0 auto;
  overflow: auto;
  position: relative;
}

.editor-content {
  position: relative;
  width: calc(100% - 220px);
  margin-left: 210px;
  background: #fff;
  border: 1px solid #f0f0f0;
  height: calc(100vh - 142px);
  overflow: hidden;
  overflow-y: auto;
}

.editor-content .am-engine {
  padding: 40px 20px
}
.data-toc-wrapper {
    position: absolute;
    top: 17px;
    width: 210px;
    padding: 16px;
    z-index: 888;
    background: #fff;
    height: calc(100vh - 142rem);
    overflow-y: auto;
}

.data-toc-title {
    position: relative;
    margin-bottom: 10px;
    font-size: 14px;
    font-weight: 700;
    padding: 0 2px 8px;
    border-bottom: 1px solid #e8e8e8;
}

.data-toc{
    overflow: auto;
    // height: calc(100vh - 209px);
}
.data-toc .data-toc-item {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    font-size: 12px;
    line-height: 20px;
    color: inherit;
  }
  .data-toc .data-toc-item-active,
  .data-toc .data-toc-item:hover,
  .data-toc .data-toc-item:focus {
    color: var(--main-theme-color);
  }
  .data-toc .data-toc-item-2 {
    padding-left: 16px;
  }
  .data-toc .data-toc-item-3 {
    padding-left: 32px;
  }
  .data-toc .data-toc-item-4 {
    padding-left: 48px;
  }
  .data-toc .data-toc-item-5 {
    padding-left: 64px;
  }
  .data-toc .data-toc-item-6 {
    padding-left: 80px;
  }
</style>
