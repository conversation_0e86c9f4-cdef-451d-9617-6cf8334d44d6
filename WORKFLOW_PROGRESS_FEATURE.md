# WorkFlow组件进度显示功能实现文档

## 功能概述

为WorkFlow组件新增了节点耗时显示和当前节点高亮功能，当`showProgress`为true时，会在工作流节点上显示耗时信息，并高亮显示当前节点。

## 实现架构

### 1. 核心文件结构

```
src/components/WorkFlow/
├── index.vue                    # 主组件，集成进度渲染器
├── node/progress/
│   └── draw.js                  # 进度节点渲染器
└── utils/
    └── timeFormatter.js         # 时间格式化工具
```

### 2. 主要功能模块

#### 2.1 进度渲染器 (`node/progress/draw.js`)

**功能特性：**
- 自定义BPMN节点渲染器
- 支持当前节点高亮显示
- 支持节点耗时信息显示
- 兼容所有BPMN节点类型

**核心方法：**
- `canRender()` - 判断是否可以渲染该元素
- `drawShape()` - 绘制节点形状
- `_addCurrentNodeHighlight()` - 添加当前节点高亮效果
- `_addSpendTimeDisplay()` - 添加耗时显示

#### 2.2 时间格式化工具 (`utils/timeFormatter.js`)

**提供的函数：**
- `formatTime(seconds)` - 将秒数转换为时分秒格式
- `formatTimeShort(seconds)` - 简化格式的时间转换
- `isValidTime(time)` - 验证时间数据有效性
- `getSpendTimeText(spendTime, useShortFormat)` - 获取完整的耗时显示文本

## 使用方法

### 1. 基本用法

在使用WorkFlow组件时，设置`showProgress`属性为true：

```vue
<vone-work-flow
  :xml="xml"
  :show-progress="true"
  :properties-props="{ width: 250 }"
/>
```

### 2. 数据格式要求

节点数据需要包含以下字段：

```javascript
{
  showProgress: true,        // 是否显示进度信息
  spendTime: 3661,          // 耗时（秒）
  currentNode: true,        // 是否为当前节点
  // ... 其他节点属性
}
```

### 3. 在change-status组件中的使用

参考`src/views/vone/project/common/change-status/index.vue`中的实现：

```javascript
// 在getFlowInfo方法中设置节点数据
nodes.map((item) => {
  if (this.showProgress) {
    item.showProgress = true;
    item.spendTime = this.spendTimes.find(
      (r) => r.nodeType == item.stateCode
    )?.spendTime;
    item.spendTime = item.spendTime ? item.spendTime : 0;
    item.currentNode = this.currentNode.nodeType == item.stateCode;
  }
});
```

## 视觉效果

### 1. 当前节点高亮

- **外层边框**：橙色虚线边框，带动画效果
- **内层边框**：橙色实线边框
- **动画**：虚线边框滚动动画，2秒循环

### 2. 耗时显示

- **位置**：节点下方居中显示
- **样式**：蓝色背景，白色文字，圆角矩形
- **字体**：系统字体，10px，中等粗细
- **阴影**：轻微阴影效果

### 3. 时间格式示例

```
0秒 → "耗时: 0s"
30秒 → "耗时: 30s"
90秒 → "耗时: 1m30s"
3661秒 → "耗时: 1h1m"
```

## 技术特点

### 1. 代码严谨性

- **类型检查**：使用`isValidTime()`验证时间数据
- **边界处理**：处理无效、负数、非数字等异常情况
- **错误容错**：当数据无效时显示默认值

### 2. 可扩展性

- **模块化设计**：渲染器和工具函数分离
- **配置灵活**：支持简化和完整两种时间格式
- **样式可定制**：CSS样式集中管理，易于修改

### 3. 性能优化

- **按需渲染**：只在`showProgress`为true时加载渲染器
- **轻量级实现**：使用原生SVG绘制，无额外依赖
- **缓存友好**：时间格式化结果可缓存

### 4. 兼容性

- **BPMN标准**：兼容所有标准BPMN节点类型
- **浏览器支持**：支持现代浏览器的SVG特性
- **框架集成**：与现有Vue3+Element Plus架构无缝集成

## 代码风格

### 1. 符合项目规范

- 使用项目统一的代码格式
- 遵循Vue3 Composition API风格
- 保持与现有组件的一致性

### 2. 注释完整

- 每个函数都有详细的JSDoc注释
- 关键逻辑有行内注释说明
- 提供使用示例

### 3. 错误处理

- 完善的参数验证
- 优雅的降级处理
- 清晰的错误信息

## 扩展建议

### 1. 功能扩展

- 支持更多时间格式（如天、周）
- 添加节点状态颜色映射
- 支持自定义高亮样式

### 2. 性能优化

- 实现虚拟滚动（大量节点时）
- 添加渲染缓存机制
- 优化SVG绘制性能

### 3. 用户体验

- 添加hover提示详细信息
- 支持点击节点查看详情
- 添加进度动画效果

## 测试建议

1. **功能测试**：验证不同时间值的显示效果
2. **边界测试**：测试无效数据的处理
3. **性能测试**：测试大量节点时的渲染性能
4. **兼容性测试**：验证不同浏览器的显示效果
5. **集成测试**：确保与现有功能无冲突
