<template>
  <el-card class="vone-echarts-card referEven" shadow="never">
    <template v-slot:header>
      <span class="title">测试计划列表</span>
      <el-radio-group v-model="activeName" size="mini" @change="handleClick">
        <el-radio-button :label="0">未进行</el-radio-button>
        <el-radio-button :label="1">进行中</el-radio-button>
        <el-radio-button :label="2">已完成</el-radio-button>
      </el-radio-group>
    </template>
    <vone-echarts :options="options" height="300px" />
  </el-card>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import dayjs from 'dayjs'
import { getTestPlansOverView } from '@/api/vone/project/overview'

export default {
  data() {
    const now = dayjs().format('YYYY-MM-DD')
    return {
      activeName: 0,
      options: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          formatter: '{b0}<br/>' + now + '<br/>进度: {c0}%',
        },
        grid: {
          left: 72,
          right: '4%',
          bottom: '10%',
        },
        xAxis: {
          type: 'value',
          min: 0,
          max: 100,
          splitLine: {
            show: true,
            lineStyle: {
              color: '#EBEEF5',
              type: 'dashed',
            },
          },
        },
        yAxis: {
          type: 'category',
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            width: 68,
            overflow: 'truncate',
          },
          data: [],
        },
        series: [
          {
            name: 'process',
            type: 'bar',
            emphasis: {
              focus: 'series',
            },
            data: [],
          },
        ],
      },
    }
  },
  mounted() {
    this.fetchPlansOverViewData()
    $on(this.$bus, 'refreshChart', () => {
      this.changeTheme()
    })
  },
  methods: {
    handleClick() {
      this.fetchPlansOverViewData()
    },
    changeTheme() {
      // 是否是暗色主题
      const theme = document.body.className.indexOf('dark') > -1

      this.options.xAxis.splitLine.lineStyle.color = theme
        ? '#333947'
        : '#EBEEF5'
    },
    async fetchPlansOverViewData() {
      const projectId = this.$route.params.id

      // stateId 0未开始 1进行中 2已完成
      const res = await getTestPlansOverView(projectId, this.activeName)
      if (res.isSuccess) {
        const names = []
        const progress = []
        if (res.data?.length > 0) {
          res.data.map((item) => {
            names.push(item.name)
            const { blockingNum, skipNum, smokeNum, systemNum, undoNum } =
              item.echoMap.testReport
            const total =
              +blockingNum + +skipNum + +smokeNum + +systemNum + +undoNum
            const rate = total > 0 ? systemNum / total : 0
            progress.push(rate.toFixed(2) * 100)
          })
        }
        this.options.yAxis.data = names
        this.options.series[0].data = progress
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.referEven {
  height: 100%;
  padding: 12px 0;
}
:deep() {
  .el-card__header {
    padding: 0;
    height: 26px;
    line-height: 26px;
    border: none;
    display: flex;
    width: 100%;
    font-weight: bold;
  }
  .el-card__body {
    height: 320px;
  }
}
.title {
  padding-left: 16px;
  font-weight: 700;
  font-size: 14px;
  flex: 1;
}
.planTabs {
  :deep(.el-tabs__header) {
    margin-bottom: 0;
  }
}
.baseMessage {
  height: 280px;
  overflow-y: auto;
  li {
    line-height: 30px;
    height: 30px;
    margin: 5px 0;
  }
}
.delay {
  min-width: 80px;
  background-color: #fa6b57;
  text-align: center;
  font-size: 10px;
  margin-top: 1%;
  color: #fff;
}
</style>
