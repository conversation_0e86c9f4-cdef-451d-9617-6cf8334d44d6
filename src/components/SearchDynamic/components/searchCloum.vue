<template>
  <div>
    <el-dropdown
      :hide-on-click="false"
      trigger="click"
      placement="bottom-start"
      @command="(e) => e && e()"
    >
      <span class="label">
        <span class="weak-title">表头</span>
        显示
        <el-icon class="iconfont el-icon--right"
          ><el-icon-direction-down
        /></el-icon>
      </span>
      <template v-slot:dropdown>
        <el-dropdown-menu class="column-el-dropdown">
          <div class="drag-group">
            <span
              >表头设置
              <el-tooltip
                content="固定列不允许禁用、拖拽及移动位置"
                placement="top"
              >
                <el-icon
                  class="iconfont"
                  style="font-size: 14px; padding-left: 2px"
                  ><el-icon-tips-info-circle
                /></el-icon>
              </el-tooltip>
            </span>
            <a @click="resetColumns">恢复默认</a>
          </div>
          <draggable
            v-model:value="columns"
            class="drag-wrap"
            handle=".mover"
            animation="300"
            :move="dragMove"
            @end="dragEnd"
          >
            <transition-group tag="span">
              <el-dropdown-item
                v-for="col in columns"
                v-show="col.type != 'checkbox' && col.title != '操作'"
                :key="col.field || col.id"
              >
                <div class="item-wrap">
                  <div class="drag-item">
                    <span v-if="col.fixed" class="disable-move">
                      <el-icon class="iconfont"
                        ><el-icon-icon-tuodong
                      /></el-icon>
                    </span>
                    <span v-else class="mover">
                      <el-icon class="iconfont"
                        ><el-icon-icon-tuodong
                      /></el-icon>
                    </span>
                    <span>{{ col.title }}</span>
                  </div>
                  <el-switch
                    v-model="col.visible"
                    active-color="#3E7BFA"
                    inactive-color="#BCC2CB"
                    :disabled="col.fixed != undefined"
                    :class="['drag-switch', !col.visible ? 'switch-off' : '']"
                    @change="(val) => operationColum(val, col.field)"
                  />
                </div>
              </el-dropdown-item>
            </transition-group>
          </draggable>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script>
import {
  DirectionDown as ElIconDirectionDown,
  TipsInfoCircle as ElIconTipsInfoCircle,
  IconTuodong as ElIconIconTuodong,
} from "@element-plus/icons-vue";
import _, { cloneDeep } from "lodash";
import Draggable from "vuedraggable";
export default {
  components: {
    Draggable,
    ElIconDirectionDown,
    ElIconTipsInfoCircle,
    ElIconIconTuodong,
  },
  name: "ShowColumn",
  props: {
    // columns: {
    //   type: Array,
    //   default: () => []
    // },
    valueObj: {
      type: Object,
      default: () => {},
    },
    // vxe-table 组件对象
    tableRef: {
      type: Object,
      default: () => null,
    },
    // 隐藏表格列keys
    hideColumns: {
      type: Array,
      default: () => [],
    },
    // 展示表格列keys
    showColumnsData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      columns: [],
      groupingItem: {
        name: "不分组",
        value: "none",
      },
    };
  },
  watch: {
    tableRef: {
      deep: true,

      handler(val, oldVal) {
        if (val != oldVal) {
          if (!this.columns.length) {
            this.$nextTick(() => {
              this.getTableColumns();
            });
          }
        }
      },

      immediate: true,
    },
  },
  methods: {
    // 获取表格列
    getTableColumns() {
      if (!this.columns.length) {
        const tableColum =
          (this.tableRef && this.tableRef.getTableColumn()?.fullColumn) || [];
        if (this.hideColumns.length > 0) {
          tableColum.forEach((item) => {
            item.visible = !this.hideColumns.includes(item.field);
          });
        }
        if (this.showColumnsData.length > 0) {
          tableColum.forEach((item) => {
            item.visible = this.showColumnsData.includes(item.field);
          });
        }
        this.originColumns = cloneDeep(tableColum);
        this.columns = tableColum;
        this.tableRef && this.tableRef.refreshColumn();
      }
    },
    // 拖拽排序结束
    dragEnd(e) {
      if (e.oldIndex == e.newIndex) return;
      // 刷新列
      this.$nextTick(() => {
        this.tableRef?.loadColumn(this.columns);
      });
    },
    // 拖动过程中的元素校验
    dragMove(e) {
      // 设置fixed属性的列不允许停靠
      if (e?.relatedContext?.element?.fixed != undefined) {
        return false;
      }
    },
    // 重置列
    resetColumns() {
      this.columns = cloneDeep(this.originColumns);
      this.tableRef?.loadColumn(this.columns);
    },
    // 操作列字段显示/隐藏
    operationColum(val, col) {
      // 获取表格所有的列
      const tableColum = this.tableRef.getTableColumn()?.fullColumn || [];
      // 获取未选中的列
      const hideColumn = tableColum
        .filter((item) => !item.visible)
        ?.map((itm) => itm.field);
      this.formJson.list = hideColumn;
      // 只要操作列，显示保存按钮
      this.showSaveAs = true;
      // 刷新列
      this.tableRef.refreshColumn();
    },
  },
};
</script>

<style lang="scss" scoped>
@use "../index.scss" as *;
</style>

<style lang="scss">
@use "../index-global.scss" as *;
</style>
