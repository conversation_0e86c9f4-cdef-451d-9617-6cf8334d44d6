<template>
  <!-- 用户访问量 -->
  <vone-echarts-card :title="formInfo.title" :name="formInfo.name">
    <template v-slot:header>
      <div class="clearfix">
        <span>{{ formInfo.title }}</span>
      </div>
    </template>
    <vone-echarts :options="options" />
  </vone-echarts-card>
</template>

<script>
import { queryUserPageview } from '@/api/vone/weidget'
export default {
  props: {
    formInfo: {
      type: Object,
      default: null,
    },
  },

  data() {
    return {
      chartData: null,
      options: {},
    }
  },
  mounted() {
    this.getOptions()
  },
  methods: {
    async getOptions() {
      const res = await queryUserPageview()
      if (res.isSuccess) {
        this.chartData = res.data
      }
      this.options = {
        color: ['#f6a600', '#3d7bfa'],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          backgroundColor: '#fff',
          borderWidth: 0,
          extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);', // 附加阴影样式
          textStyle: {
            color: '#53565C',
          },
        },

        legend: {
          icon: 'circle',
          x: 'right', // 居右显示
          itemHeight: 8,
          itemWidth: 8,
          borderRadius: 8,
          data: [
            {
              name: '访客数(UV)',
              icon: 'circle',
            },
            {
              name: '访问次数',
              icon: 'circle',
            },
          ],
          textStyle: {
            // 图例文字的样式
            color: '#777F8E',
          },
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            data: res.data?.date || [],
            axisLabel: {
              show: true,
              textStyle: {
                color: '#777F8E', // 更改坐标轴文字颜色
              },
            },
            axisLine: {
              lineStyle: {
                color: '#EBEEF5', // 更改坐标轴颜色
              },
            },
          },
        ],
        yAxis: [
          {
            type: 'value',
            minInterval: 1,
            axisTick: {
              // y轴刻度线
              show: false,
            },
            axisLine: {
              show: false, // 不显示坐标轴轴线
            },
            splitLine: {
              // 网格线
              lineStyle: {
                type: 'dashed', // 设置网格线类型 dotted：虚线   solid:实线
                width: 1,
                color: '#EBEEF5',
              },
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#777F8E', // 更改坐标轴文字颜色
              },
            },
          },
        ],
        series: [
          {
            name: '访客数(UV)',
            type: 'line',
            emphasis: {
              focus: 'series',
            },
            data: res.data?.loginCount,
          },
          {
            name: '访问次数',
            type: 'line',
            // areaStyle: {
            //   // 前四个参数代表位置 左下右上，如下表示从上往下渐变色 紫色到暗蓝色，
            //   color: new echarts.graphic.LinearGradient(
            //     0, 0, 0, 1,
            //     [
            //       { offset: 0, color: 'rgba(100, 190, 250, 0.16)' },
            //       { offset: 1, color: 'rgba(100, 190, 250, 0.16)' }
            //     ]
            //   )
            // },
            emphasis: {
              focus: 'series',
            },
            data: res.data?.optCount,
          },
        ],
      }
    },
  },
}
</script>
